{"name": "qportal", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev_build": "vue-cli-service build --mode dev", "qa_build": "vue-cli-service build --mode qa", "stage_build": "vue-cli-service build --mode stage", "dev_sync": "aws s3 sync ./dist s3://dev.qportal.io", "dev_invalidate": "aws cloudfront create-invalidation --distribution-id EKRXK0TNL3ZSZ --paths /*", "dev_deploy": "npm run dev_sync && npm run dev_invalidate", "dev_release": "npm run dev_build && npm run dev_deploy", "qa_sync": "aws s3 sync ./dist s3://qa-qportal.io", "qa_invalidate": "aws cloudfront create-invalidation --distribution-id EBZKVEZ0PGRKS --paths /*", "qa_deploy": "npm run qa_sync && npm run qa_invalidate", "qa_release": "npm run qa_build && npm run qa_deploy", "stage_sync": "aws s3 sync ./dist s3://stage-qportal.io", "stage_invalidate": "aws cloudfront create-invalidation --distribution-id EYR6VDZBY5ZRQ --paths /*", "stage_deploy": "npm run stage_sync && npm run stage_invalidate", "stage_release": "npm run stage_build && npm run stage_deploy", "sync": "aws s3 sync ./dist s3://qportal.io", "invalidate": "aws cloudfront create-invalidation --distribution-id EBDKHQNZT19NL --paths /*", "deploy": "npm run sync && npm run invalidate", "release": "npm run build && npm run deploy"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.2.1", "@ckeditor/ckeditor5-upload": "^41.2.1", "@ckeditor/ckeditor5-vue2": "^3.0.1", "axios": "^0.21.1", "chart.js": "^2.9.3", "chartjs-plugin-datalabels": "^0.7.0", "core-js": "^3.6.5", "jsonp": "^0.2.1", "jspdf": "^2.3.1", "jspdf-autotable": "^3.5.14", "konva": "^8.0.4", "libphonenumber-js": "^1.9.17", "moment": "^2.29.4", "printd": "^1.5.0", "tiptap": "^1.32.2", "tiptap-extensions": "^1.35.2", "vue": "^2.6.11", "vue-advanced-cropper": "^1.7.0", "vue-cal": "^3.10.1", "vue-chartkick": "^0.6.1", "vue-konva": "^2.1.7", "vue-msal-browser": "^2.0.0", "vue-qrcode-component": "^2.1.1", "vue-router": "^3.5.1", "vue-web-cam": "^1.9.0", "vue2-google-maps": "^0.10.7", "vuedraggable": "^2.24.3", "vuetify": "^2.4.0", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "^1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.4.0", "vue-svg-loader": "^0.16.0", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
import { mapActions } from "vuex";
import moment from "moment";
import { mapGetters } from "vuex";

const common = {
    watch: {
        $route(to) {
            document.title = to.meta.title + " - QPortal" || "QPortal";
        },
    },
    data() {
        return {
            cardStyle: {
                border: "2px solid #A5A5A5FF",
            },
        };
    },
    computed: {
        ...mapGetters({
            checkWritePermission: "checkWritePermission",
            checkDeletePermission: "checkDeletePermission",
            checkExportPermission: "checkExportPermission",
            checkReadPermission: "checkReadPermission",
            checkBackfillPermission: "checkBackfillPermission",
            currencyCode: "currencyCode",
        }),
    },

    methods: {
        ...mapActions(["showSnack", "showOverlayLoader"]),
        showSuccess(message) {
            this.showSnack({
                text: message,
                type: "success",
                timeout: 10000,
            });
        },
        showError(message) {
            this.showSnack({
                text: message,
                type: "error",
                timeout: 10000,
            });
        },
        showInfo(message, timeout) {
            this.showSnack({
                text: message,
                type: "info",
                timeout: timeout == null ? 10000 : timeout,
            });
        },
        showLoader(message) {
            this.showOverlayLoader({
                text: message,
                show: true,
            });
        },
        hideLoader() {
            this.showOverlayLoader({
                text: "",
                show: false,
            });
        },
        downloadFile(response, name, type = "") {
            const blob = new Blob([response.data], {
                type: response.data.type,
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            response.headers["content-disposition"];
            let fileName = "";
            if (type == "pdf") {
                fileName = `${name}_${moment().format("YYYYMMDDHHmmss")}.pdf`;
            } else if (type == "zip") {
                fileName = `${name}_${moment().format("YYYYMMDDHHmmss")}.zip`;
            } else {
                fileName = `${name}_${moment().format("YYYYMMDDHHmmss")}.xlsx`;
            }
            link.setAttribute("download", fileName);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);
        },

        printFile(response) {
            let fileType = response.data.type ?
                response.data.type :
                "application/pdf";
            const blob = new Blob([response.data], {
                type: fileType,
            });
            var blobURL = URL.createObjectURL(blob);
            const iframe = document.createElement("iframe");
            document.body.appendChild(iframe);
            iframe.style.display = "none";
            iframe.src = blobURL;
            iframe.onload = function() {
                setTimeout(function() {
                    iframe.focus();
                    iframe.contentWindow.print();
                }, 1);
            };
        },

        downloadReport(response, name, type = "") {
            const blob = new Blob([response.data], {
                type: response.data.type,
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            response.headers["content-disposition"];
            let fileName = "";
            if (type == "pdf") {
                // fileName = `${name}_${moment().format("YYYYMMDDHHmmss")}.pdf`;
                fileName = `${name}.pdf`;
            }
            else if (type == "SUN") {
                // Handle TXT download
                fileName = `${name}.SUN`;
            }
            else if (type == "txt") {
                // Handle TXT download
                fileName = `${name}.txt`;
            }
            else {
                // fileName = `${name}_${moment().format("YYYYMMDDHHmmss")}.xlsx`;
                fileName = `${name}.xlsx`;
            }
            link.setAttribute("download", fileName);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);
        },
        openFile(path) {
            window.open(this.s3BucketURL + path, "_blank");
        },
        errorChecker(error) {
            var response = error;
            console.log(error);
            this.hideLoader();
            if (error.response) {
                response = error.response;
            }
            let that = this;
            if (
                response &&
                response.request &&
                response.request.responseType == "blob"
            ) {
                let fr = new FileReader();
                fr.onload = function(e) {
                    response = { data: JSON.parse(e.target.result) };
                    that.showMessageFromError(response);
                };
                fr.readAsText(response.data);
            } else {
                this.showMessageFromError(response);
            }
        },
        showMessageFromError(response) {
            setTimeout(() => {
                let message =
                    typeof response.data != "undefined" &&
                    typeof response.data.message != "undefined" ?
                    response.data.message :
                    "Unknown error occurred.";

                this.showError(message);
                this.hideLoader();
            });
        },
        findFirstPage() {
            if (
                this.$store.getters.checkReadPermission(
                    this.$modules.general.dashboard.slug
                )
            ) {
                this.$router.push("/dashboard", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.facility.schedule.slug
                )
            ) {
                this.$router.push("/schedule", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.events.schedule.slug
                )
            ) {
                this.$router.push("/event-schedule", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.workshops.schedule.slug
                )
            ) {
                this.$router.push("/schedule", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.facility.management.slug
                )
            ) {
                this.$router.push("/facilities", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.events.management.slug
                )
            ) {
                this.$router.push("/events", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.workshops.management.slug
                )
            ) {
                this.$router.push("/workshops", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.clients.customers.slug
                )
            ) {
                this.$router.push("/customers", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.trainers.management.slug
                )
            ) {
                this.$router.push("/trainers", () => {});
            } else if (
                this.$store.getters.checkReadPermission(this.$modules.sales.graph.slug)
            ) {
                this.$router.push("/sales", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.sales.credits.slug
                )
            ) {
                this.$router.push("/credits", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.sales.refunds.slug
                )
            ) {
                this.$router.push("/refunds", () => {});
            } else if (
                this.$store.getters.checkReadPermission(this.$modules.sales.void.slug)
            ) {
                this.$router.push("/cancellations", () => {});
            } else if (
                this.$store.getters.checkReadPermission(this.$modules.memberships.schedule.slug)
            ) {
                this.$router.push("/memberships/attendance-log", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.settings.profile.slug
                )
            ) {
                this.$router.push("/settings/profile", () => {});
            } else if (
                this.$store.getters.checkReadPermission(
                    this.$modules.kiosk.facility.slug
                )
            ) {
                this.$store.dispatch("switchToKiosk", true).then(() => {
                    let venue = this.$store.getters.venueInfo;
                    if(venue && venue.kiosk_domain){
                        window.location.href = `https://${venue.kiosk_domain}?token=${this.$store.getters.token}&login_url=${window.location.origin}`;
                    }else{
                        window.location.href = `${process.env.VUE_APP_KIOSK_DOMAIN}?token=${this.$store.getters.token}&login_url=${window.location.origin}`;
                    }
                });
            } else {
                this.$router.push("/forbidden", () => {});
            }
        },
        getTaxVariation(type, taxPercentage, amount) {
            if (amount) {
                if (type == "post") {
                    let taxAmount = parseFloat(
                        (parseFloat(amount) / (100 + parseFloat(taxPercentage))) *
                        parseFloat(taxPercentage)
                    );
                    return {
                        tax: taxAmount,
                        price: parseFloat(amount) - taxAmount,
                        total_price: parseFloat(amount),
                    };
                } else {
                    let taxAmount = parseFloat(
                        parseFloat(amount) * (parseFloat(taxPercentage) / 100)
                    );
                    return {
                        tax: taxAmount,
                        price: parseFloat(amount),
                        total_price: parseFloat(amount) + taxAmount,
                    };
                }
            }
        },
        logIt(data) {
            console.log(data);
        },
        sanitizeHTML(inputHTML) {
            const allowedTags = [
                    'b', 'i', 'a', 'em', 'strong', 'p', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                    'img', 's', 'u', 'code', 'table', 'tbody', 'thead', 'td', 'tr', 'sub', 'sup', 'figure',
                    'figcaption', 'tfoot', 'th', 'iframe', 'blockquote', 'div', 'pre', 'span', 'br'
                ];
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = inputHTML;

            // Recursively clean the nodes
            function cleanNodes(element) {
                const childNodes = Array.from(element.childNodes);
                for (const node of childNodes) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // If the tag is not allowed, remove it
                        if (!allowedTags.includes(node.tagName.toLowerCase())) {
                            console.log('removed the tag > ',node.tagName.toLowerCase());
                            node.remove();
                        } else {
                            // Clean child nodes recursively
                            cleanNodes(node);
                        }
                    } else if (node.nodeType !== Node.TEXT_NODE) {
                        // Remove non-text nodes (comments, scripts, etc.)
                        node.remove();
                    }
                }
            }

            cleanNodes(tempDiv);
            return tempDiv.innerHTML;
        },
       parseMarkdownText(inputText) {
            if (!inputText) {
                return inputText
            }
            const regex = /\[([^[]*)]\(([^)]*)\)/g
            return inputText.replace(regex, '<a href="$1" target="_blank" class="underline italic external-link" onclick="(e)=>{e.stopPropagation()}">$2</a>')
        },
        utf8ToBase64 (str) {
            const utf8Bytes = new TextEncoder().encode(str)
            return btoa(String.fromCharCode(...utf8Bytes))
        },
        createFileFromImageUrl(imageUrl, fileName,type) {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async (resolve, reject) => {
                if (type === 'image') {
                    const img = new Image();
                    img.crossOrigin = 'Anonymous';

                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);

                        canvas.toBlob((blob) => {
                            if (blob) {
                                const file = new File([blob], fileName, { type: blob.type });
                                resolve(file);
                            } else {
                                reject(new Error('Failed to create file from image.'));
                            }
                        }, 'image/jpeg'); // Specify the MIME type here
                    };

                    img.onerror = (error) => {
                        reject(new Error('Failed to load image: ' + error.message));
                    };
                    img.src = imageUrl;

                } else if (type === 'video') {
                    try {
                        const response = await fetch(imageUrl);
                        const blob = await response.blob();

                        const file = new File([blob], fileName, { type: blob.type });
                        resolve(file);
                    } catch (error) {
                        reject(new Error('Failed to load video: ' + error.message));
                    }
                } else {
                    reject(new Error('Unsupported type: ' + type));
                }
            });
        },

    },
};

export default common;

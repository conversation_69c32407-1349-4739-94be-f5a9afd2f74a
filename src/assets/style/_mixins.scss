@import "variables";

@mixin active($color:white,$background:$neon) {
  color: $color !important;
  background: $background;
}

@mixin background($background) {
  background-color: $background;
}

@mixin tab($color:$blue){
  border-radius: 4px !important;
  box-shadow: $boxShadow;
  width: max-content;

  .v-btn, .v-tab {
    &::before {
      background: none !important;
      box-shadow: none !important;
    }
    letter-spacing: 1px; /* Adjust the value as needed */
    text-transform: unset !important;
    &--active {
      background: #fff !important;
      color: $color !important;
      border-bottom: 2px solid $color !important;
      border-bottom-color: $color !important;
      font-weight: 700;
    }
    border: none;
    border-color: unset !important;
    padding: 1.25rem !important;
    background: transparent !important;
    height: auto !important;
    //color: rgba(17, 42, 70, 0.6) !important;
    font-size: 0.875rem;

  }

  .v-tabs-slider {
    @include background($color);
  }
}



@mixin tab_container($color:$blue){
  .v-btn{
    font-family: "Inter", sans-serif;
    font-size: 1rem !important;
    font-weight: 400;
    letter-spacing: -1px; /* Adjust the value as needed */
    text-transform: capitalize;
    .v-btn__content{
      opacity: 1 !important;
    }

  }
  .active_container_tab {
    &::before {
      background: none !important;
    }
    .v-btn__content{
      opacity: 1 !important;
    }
    color: $color !important;
    border-bottom: 2px solid $color;
    font-weight: 600;

  }
  .svg-stroke-icon{
    svg{
      stroke: $color !important;
      fill: none !important;
      opacity: 1 !important;
    }

  }
  .svg-fill-icon{
    svg{
      stroke: $color !important;
      fill: $color !important;
      opacity: 1;

    }
  }

}
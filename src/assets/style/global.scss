@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import "variables";
@import "mixins";
@import "table";
@import "utility";
@import "memberships";
@import "shop";

html,
body, .v-application {
  font-weight: 400;
  font-size: 14px;
  font-family: 'Inter', sans-serif !important;
  @media screen and (max-width: 640px) {
    &{
      font-size: 14px;
    }
  }

  @media screen and (min-width: 1900px) {
    &{
      font-size: 16px;
    }
  }
}

td,
th {
  border: 0px solid grey !important;
  background: transparent;
  box-shadow: none;
  padding: 0px;
  margin: 0px;
}

a {
  text-decoration: none;
}

.v-icon {
  cursor: pointer;
}

::-webkit-scrollbar {
  width: 5px;
  height: 6px;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #00b0af;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00b0af;
}

.container {
  padding: 2rem;
  padding-top: 1rem;
}

.search_column .v-text-field__details {
  margin-bottom: 0 !important;
}

.header_title {
  font-size: 20px;
  font-weight: 600;
  color: #062b47;
}

.headline {
  font-weight: bold;
  color: #fff;
  background: #4FAEAF;
}

.blue-text {
  color: #062b47 !important;
  text-transform: none;
}


.black-text {
  color: #111111 !important;
  text-transform: none;
}




.pointer {
  cursor: pointer;
}

/* .v-main {
    background-color: #edf9ff;
}

*/

.add_btn {
  margin: 0 auto;
  text-align: center;
  padding: 9px;
}

.titles {
  text-align: left;
  color: #066a8c;
  font-size: 14px;
  padding: 3px 0 10px 10px;
  font-weight: bolder;
}
.sub_titles {
  text-align: left;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  padding: 3px 0 10px 10px;
}

.q-titles {
  text-align: left;
  color: #112a46;
  font-size: 14px;
  padding: 3px 0 10px 10px;
  font-weight: bolder;
}

.no-background {
  background: none;
}

.reciept_details {
  background: rgb(0, 182, 230);
  background: linear-gradient(
                  0deg,
                  rgba(0, 182, 230, 1) 0%,
                  rgba(3, 88, 119, 1) 100%
  );
}

.reciept_details_body {
  background: #f0f0f0;
}

.reciept_details_body .head {
  text-align: center;
  color: black;
}

.reciept_details .head {
  text-align: center;
}

.reciept_table {
  width: 70%;
  margin: 20px auto;
}

.reciept_table tr td {
  width: 50%;
  text-align: left;
  color: #fff;
  padding: 3px;
}

.reciept_details_table {
  width: 99%;
  margin: 20px auto;
}

.reciept_details_table tr td {
  width: 50%;
  text-align: left;
  color: #000;
  padding: 3px;
}

.reciept_right {
  text-align: right;
}

.reciept_left {
  text-align: left;
}

.reciept_title {
  font-size: 12px;
  color: #002a89;
}

.reciept_details_item_table {
  width: 99%;
  margin: 20px auto;
}

.reciept_details_item_table,
.reciept_details_item_table th,
.reciept_details_item_table td {
  border: 1px solid rgba(163, 163, 163, 0.534) !important;
  border-collapse: collapse;
}

.reciept_details_item_table tr td {
  font-size: 12px;
  text-align: center;
  padding: 3px;
}

.reciept_details_item_table tr th {
  text-align: center;
  padding: 3px;
}

.reciept_details_total_table {
  text-align: right;
  width: 100%;
}

.reciept_details_total_table tr td {
  padding: 6px;
}

.reciept_details_total_table .space {
  width: 186px;
}

.head {
  padding: 8px 10px;
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}

.avatharImg {
  width: 100px;
  height: 100px;
  margin: auto;
  border-radius: 100px;
}

.profile_txt {
  text-align: center;
  font-size: 20px;
  color: #00b0af;
}

.text {
  padding: 10px;
  color: #868585;
  font-size: 16px;
  line-height: 25px;
}

.form_bg {
  background: #edf9ff;
  border-radius: 5px;
}

.red-color {
  background-color: #840000 !important;
  text-transform: none;
  color: #fff !important;
}

.default-color {
  background-color: #272727 !important;
  text-transform: none;
  color: #fff !important;
}

.green-color {
  background-color: #4caf50 !important;
  text-transform: none;
  color: #fff !important;
}

.teal-color {
  background-color: #00b0af !important;
  text-transform: none;
}

.black-color {
  background-color: #1b1b1b !important;
  text-transform: none;
}

.blue-color {
  background-color: #062b47 !important;
  text-transform: none;
}

.light-blue-color {
  background-color: rgba(17, 42, 70, 0.05) !important;
}

.yellow-color {
  background-color: #cea800 !important;
  border-color: rgb(92, 78, 2);
  color: #000000 !important;
  text-transform: none;
}

.brown-color {
  background: #7a6d50 !important;
  text-transform: capitalize;
}

.text-blue {
  color: #112A46 !important;
}
.text-red {
  color: #E50000 !important;
}

.text-dark-gray {
  color: rgba(86, 92, 104, 1) !important;
}

.text-light-gray{
  color: rgba(0, 0, 0, 0.40) !important;
}

.text-light-black {
  color: rgba(0, 0, 0, 0.60) !important;
}

.text-thin-black {
  color: rgba(51, 51, 51, 0.4) !important;
}
.text-thin-gray {
  color: #999999 !important;
}

.bounce-enter-active {
  animation: bounce-in 0.5s;
}

.bounce-leave-active {
  animation: bounce-in 0.5s reverse;
}

button.outlined-button {
  border: 1px solid #112a45;
  text-transform: none;
}

button.outlined-button.dark-back {
  background: #112A45;
}

.btn-sm-outline-red {
  color: #F02D3A !important;
  border: 1px solid #F02D3A;
  height: 24px !important;
  width: 86px;
}

.close-btn {
  color: #112A45;
  border: 1px solid #112A45;
  border-radius: 4px;
}

.d-btn {
  background-color: #112A45;
  color: #FFFFFF !important;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px !important;
  line-height: 17px;
  text-align: center;
  padding: 0px 14px !important;
}

.text-underline {
  text-decoration: underline;
}

@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter, .slide-fade-leave-to
  /* .slide-fade-leave-active below version 2.1.8 */
{
  transform: translateX(10px);
  opacity: 0;
}

.theme--light.v-tabs-items {
  background-color: transparent !important;
  width: 100%;
}

.button_navigation {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.button_navigation .v-item--active {
  background-color: #062b48 !important;
  color: #fff !important;
}

.date_button_navigation {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.date_button_navigation .v-item--active {
  background-color: #062b48 !important;
  color: #fff !important;
}

.hoverImage {
  background-color: rgba(54, 54, 54, 0.342);
}

.text_field1 {
  border-bottom-right-radius: 0px !important;
  border-top-right-radius: 0px !important;
}

.text_field2 {
  border-bottom-left-radius: 0px !important;
  border-top-left-radius: 0px !important;
}

.total_bg td {
  background: #edf9ff;
}

.grand_total_bg td {
  background: #dfeefd;
}

.sticky_bottom {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  z-index: 1;
}
.head_bg td {
  background: #c4ffd8;
}

.btn_bg {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
  cursor: pointer;
}

.salesBtn {
  padding: 5px;
  font-size: 14px;
  font-weight: bold;
  color: #066a8c;
  cursor: pointer;
}

.v-select__selections span {
  font-weight: 400 !important;
}

.v-select__selections input {
  font-weight: 400 !important;
}

.per-page .v-input__append-inner {
  margin-top: 14px !important;
}

.per-page .v-label {
  top: 14px !important;
}

.per-page .v-label--active {
  top: 18px !important;
}

.listing_table tbody tr:hover {
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -webkit-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -moz-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  z-index: 1;
}

.listing_table {
  width: 100%;
  table-layout: fixed;
}

.five {
  width: 5%;
}

.ten {
  width: 10%;
}

.twenty {
  width: 20%;
}

.ellipsis {
  max-width: 115px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-small {
  max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.slot-pos {
  position: relative;
}

.slot-pre-fill {
  background-color: #2c3b46;
  width: 100%;
  position: absolute;
  bottom: 0;
}

.facility-name_header {
  max-width: 165px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.container-80 {
  max-width: 80%;
  display: inline-block;
}

.text-white {
  color: #ffffff !important;
}

.ellipsis-card {
  max-width: 290px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.font-16{
  font-size: 16px !important;
}
.font-14{
  font-size: 14px !important;
}
.font-12{
  font-size: 12px !important;
}
.font-18{
  font-size: 18px !important;
}
.support-status{
  padding: 5px 15px;
  white-space: nowrap;
}
.support-status.open{
  border-radius: 2px;
  background: #D34C46;
  color: white;
}
.support-status.resolved{
  border-radius: 2px;
  background: #112A46;
  color: white;
}

.table-striped {
  tr:nth-child(2n) {
    background: #E9F1F6B2;
  }
}
.table-bordered {
  border-collapse: collapse;
 td {
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
  }
  tbody > tr:first-child > td {
    border-top: none;
  }
}


.table-td-bottom-bordered{
  border-collapse: collapse;
  td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  }
}

.bg-transparent {
  background: transparent !important;
}


.q-autocomplete {
  border: none !important;

  fieldset {
    border-color: rgba(220, 220, 220, 1) !important;
    border-radius: 0.25rem;
  }
  &:not(.shadow-0) fieldset{
    box-shadow: $boxShadow;
    border: none !important;
  }
  border-radius: 4px;
  background: #fff;

}

.q-autocomplete-rounded {
  border: none !important;

  fieldset {
    border-color: rgba(220, 220, 220, 1) !important;
    border-radius: 1rem;
  }
  &:not(.shadow-0) fieldset{
    box-shadow: $boxShadow;
    border: none !important;
  }
  border-radius: 1rem !important;
  background: #fff;

}

.q-tab-autocomplete{
  fieldset {
    border-radius: 0rem;
    border-color: transparent;
  }
  border-radius: 0px;
  .v-select__selection{
    color: #4FAEAF; ;
  }
  .mdi-chevron-down{
    color: #4FAEAF; ;
  }

}


.mobile_no_field{
  &.v-text-field.v-text-field--enclosed:not(.v-text-field--rounded) > .v-input__control > .v-input__slot, .v-text-field.v-text-field--enclosed .v-text-field__details{
    padding: 0px !important;
  }
  fieldset {
    border-radius: 4px 0px 0px 4px ;
  }
}
.mobile_no_field_number{
  border-radius: 0px 4px 4px 0px !important; ;
}



.q-text-field {
  border: none !important;
  border-radius: 0.25rem;
  border-color: rgba(220, 220, 220, 0.5);
  //min-height: 40px !important;
  //max-height: 48px !important;
  .v-input__slot fieldset{
    border-color: rgba(220, 220, 220, 1);
  }
  &:not(.shadow-0){
    box-shadow: $boxShadow;
  }
  .v-input__control, .v-input__slot {
    //min-height: 36px !important;
  }
}

.q-text-field-rounded {
  border: none !important;
  border-radius: 1rem;
  border-color: rgba(220, 220, 220, 0.5);
  //min-height: 40px !important;
  //max-height: 48px !important;
  .v-input__slot fieldset{
    border-color: rgba(220, 220, 220, 1);
  }
  &:not(.shadow-0){
    box-shadow: $boxShadow;
  }
  .v-input__control, .v-input__slot {
    //min-height: 36px !important;
  }
}



.q-text-fields {
  border: none !important;
  border-radius: 0.25rem;
  border-color: rgba(220, 220, 220, 0.5);
  &:not(.shadow-0){
    //box-shadow: $boxShadow;
  }
  .v-input__control, .v-input__slot {
    min-height: 43px !important;
    //border-color: rgba(220, 220, 220, 0.8);


  }
  .v-input__slot fieldset{
    border-color: rgba(220, 220, 220, 1);
  }
  .v-text-field__slot input{
    color: rgba(86, 92, 104, 1);
    font-weight: 500;
    font-size: 12px;
  }
  .v-input__prepend-inner{
    margin-top: 12px !important;
  }
  .v-input__icon--prepend-inner{
    .v-icon{
      //font-size: 18px;
    }
  }

  .v-text-field__prefix{
    color: rgba(86, 92, 104, 1);
    font-weight: 500;
    font-size: 12px;
    max-height: 20px
  }

}

.event-month-name {
  font-weight: 600;
  color: $blue;
}

.v-divider {
  border-color: #F1F1F1;
}


.v-dialog input,
.v-dialog textarea,
.v-dialog select,
.v-dialog  .v-select__selections{
  font-size: 14px;
  color: black;
  font-weight: 500;
}


.v-dialog{
  box-shadow: unset !important;
  label{
    color: #565C68;
    font-size: 12px
  }

  .theme--light.v-input{
    color: black !important;
  }
}

.active_tab_fill_icon{
  svg {
    fill: $neon !important;
    stroke: none !important;
  }
  border-bottom: 1px solid;
}

.active_tab_stroke_icon{
  svg {
    stroke: $neon !important;
    fill: none !important;
  }
  border-bottom: 1px solid;
}

.no-hover-effect:hover {
  &,&::before{
    background-color: inherit !important; /* Prevent background color change on hover */
    color: inherit !important; /* Prevent text color change on hover */
  }
}

.q-auto-complete-text-ellipsis {
  white-space: nowrap; /* Prevents the text from wrapping */
  text-overflow: ellipsis; /* Adds ellipsis (...) to indicate text overflow */
  max-width: 85%;
  overflow: hidden;
}


.mobile_auto_complete_hide_anchor{
  .v-input__append-inner{
    display: none;
  }
}

.qp-tab-nav{
  background-color: #FFFFFF !important;
  max-height: 48px !important;
  border-radius: 2px !important;
}

.qp-tab-nav-is-active{
  color: #112A46 !important;
  font-weight: 600 !important;
  svg{
    opacity: 1 !important;
  }
}


.academy_schedule_days{
  background: none !important;

  .v-btn {
    border:1px solid #EFEDED !important;
    border-radius:4px !important;
    background-color: #FFFFFF !important;
    min-width: 60px !important;
  }
  .v-btn__content{
    color: rgba(0, 0, 0, 0.70) !important;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: capitalize;
  }
  & button{
    .v-btn__content{
      font-weight: 600 !important;
    }
  }
}

.wrap-text{
  word-break: break-all;
  white-space: normal;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.w-fit{
  width: fit-content;
}

.title-text{
  color: #112A46;
  font-size: 16px;
  font-weight: 600;
}

@import "common";
@import "dashboard";
@import "events";


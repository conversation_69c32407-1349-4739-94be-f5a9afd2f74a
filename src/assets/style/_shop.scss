
.cart {
  .cart-header {
    border-radius: 0.75rem;
    border: 1px solid #E8E8E8;
    background: #FFF;
    padding: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    //display: flex;
    align-items: center;
    gap: 0.25rem;

    span.count {
      background: #F8FAFB;
      padding: 0.125rem 0.25rem;
      border-radius: 100%;
      font-size: 0.625rem;
      min-width: 1.5rem;
      display: inline-block;
      text-align: center;
      height: 24px;
      line-height: 22px;
    }
    span.view-offer {
      padding: 0.125rem 0.25rem;
      font-size: 0.625rem;
      display: inline-block;
      height: 24px;
      line-height: 22px;
      float: right;
      cursor: pointer;
    }
  }
}

.cart-holder {
  position: fixed;
  right: 0;
  top: 60px;
  width: 20%;
  height: 89.5vh;
  z-index: 6;

  .cart-preview {
    position: sticky;
    width: 100%;
    right: 0;
    top: 65px;
    height: 98%;
    bottom: 0;
    z-index: 5;

    .cart-items {
      height: 70vh;
      overflow-y: auto;
    }
  }
}

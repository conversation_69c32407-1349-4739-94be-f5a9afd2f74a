/* Margin on all sides */
@import "variables";
@import "mixins";

.bordered {
  border: 1px solid #EAEAEA !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 0.75rem !important;
}

.m-4 {
  margin: 1rem !important;
}

.m-5 {
  margin: 1.25rem !important;
}

.m-6 {
  margin: 1.5rem !important;
}

.m-8 {
  margin: 2rem !important;
}

.m-auto {
  margin: auto;
}

/* Margin in specific directions */
.m-t-0 {
  margin-top: 0 !important;
}

.m-t-1 {
  margin-top: 0.25rem !important;
}

.m-t-2 {
  margin-top: 0.5rem !important;
}

.m-t-3 {
  margin-top: 0.75rem !important;
}

.m-t-4 {
  margin-top: 1rem !important;
}

.m-t-5 {
  margin-top: 1.25rem !important;
}

.m-t-6 {
  margin-top: 1.5rem !important;
}

.m-t-8 {
  margin-top: 2rem !important;
}

.m-t-auto {
  margin-top: auto;
}

.m-r-0 {
  margin-right: 0 !important;
}

.m-r-1 {
  margin-right: 0.25rem !important;
}

.m-r-2 {
  margin-right: 0.5rem !important;
}

.m-r-3 {
  margin-right: 0.75rem !important;
}

.m-r-4 {
  margin-right: 1rem !important;
}

.m-r-5 {
  margin-right: 1.25rem !important;
}

.m-r-6 {
  margin-right: 1.5rem !important;
}

.m-r-8 {
  margin-right: 2rem !important;
}

.m-r-auto {
  margin-right: auto;
}

.m-b-0 {
  margin-bottom: 0 !important;
}

.m-b-1 {
  margin-bottom: 0.25rem !important;
}

.m-b-2 {
  margin-bottom: 0.5rem !important;
}

.m-b-3 {
  margin-bottom: 0.75rem !important;
}

.m-b-4 {
  margin-bottom: 1rem !important;
}

.m-b-5 {
  margin-bottom: 1.25rem !important;
}

.m-b-6 {
  margin-bottom: 1.5rem !important;
}

.m-b-8 {
  margin-bottom: 2rem !important;
}

.m-b-auto {
  margin-bottom: auto;
}

.m-l-0 {
  margin-left: 0 !important;
}

.m-l-1 {
  margin-left: 0.25rem !important;
}

.m-l-2 {
  margin-left: 0.5rem !important;
}

.m-l-3 {
  margin-left: 0.75rem !important;
}

.m-l-4 {
  margin-left: 1rem !important;
}

.m-l-5 {
  margin-left: 1.25rem !important;
}

.m-l-6 {
  margin-left: 1.5rem !important;
}

.m-l-8 {
  margin-left: 2rem !important;
}

.m-l-auto {
  margin-left: auto;
}

/* Horizontal Margin */
.m-x-0 {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.m-x-1 {
  margin-left: 0.25rem !important;
  margin-right: 0.25rem !important;
}

.m-x-2 {
  margin-left: 0.5rem !important;
  margin-right: 0.5rem !important;
}

.m-x-3 {
  margin-left: 0.75rem !important;
  margin-right: 0.75rem !important;
}

.m-x-4 {
  margin-left: 1rem !important;
  margin-right: 1rem !important;
}

.m-x-5 {
  margin-left: 1.25rem !important;
  margin-right: 1.25rem !important;
}

.m-x-6 {
  margin-left: 1.5rem !important;
  margin-right: 1.5rem !important;
}

.m-x-8 {
  margin-left: 2rem !important;
  margin-right: 2rem !important;
}

.m-x-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Vertical Margin */
.m-y-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.m-y-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.m-y-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.m-y-3 {
  margin-top: 0.75rem !important;
  margin-bottom: 0.75rem !important;
}

.m-y-4 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.m-y-5 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}

.m-y-6 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.m-y-8 {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
}

.m-y-auto {
  margin-top: auto;
  margin-bottom: auto;
}

/* Negative Margin */
.n-m-0 {
  margin: 0 !important;
}

.n-m-1 {
  margin: -0.25rem !important;
}

.n-m-2 {
  margin: -0.5rem !important;
}

.n-m-3 {
  margin: -0.75rem !important;
}

.n-m-4 {
  margin: -1rem !important;
}

.n-m-5 {
  margin: -1.25rem !important;
}

.n-m-6 {
  margin: -1.5rem !important;
}

.n-m-8 {
  margin: -2rem !important;
}

/* Negative Margin in specific directions */
.n-m-t-0 {
  margin-top: 0 !important;
}

.n-m-t-1 {
  margin-top: -0.25rem !important;
}

.n-m-t-2 {
  margin-top: -0.5rem !important;
}

.n-m-t-3 {
  margin-top: -0.75rem !important;
}

.n-m-t-4 {
  margin-top: -1rem !important;
}

.n-m-t-5 {
  margin-top: -1.25rem !important;
}

.n-m-t-6 {
  margin-top: -1.5rem !important;
}

.n-m-t-8 {
  margin-top: -2rem !important;
}

.n-m-r-0 {
  margin-right: 0 !important;
}

.n-m-r-1 {
  margin-right: -0.25rem !important;
}

.n-m-r-2 {
  margin-right: -0.5rem !important;
}

.n-m-r-3 {
  margin-right: -0.75rem !important;
}

.n-m-r-4 {
  margin-right: -1rem !important;
}

.n-m-r-5 {
  margin-right: -1.25rem !important;
}

.n-m-r-6 {
  margin-right: -1.5rem !important;
}

.n-m-r-8 {
  margin-right: -2rem !important;
}

.n-m-b-0 {
  margin-bottom: 0 !important;
}

.n-m-b-1 {
  margin-bottom: -0.25rem !important;
}

.n-m-b-2 {
  margin-bottom: -0.5rem !important;
}

.n-m-b-3 {
  margin-bottom: -0.75rem !important;
}

.n-m-b-4 {
  margin-bottom: -1rem !important;
}

.n-m-b-5 {
  margin-bottom: -1.25rem !important;
}

.n-m-b-6 {
  margin-bottom: -1.5rem !important;
}

.n-m-b-8 {
  margin-bottom: -2rem !important;
}

.n-m-l-0 {
  margin-left: 0 !important;
}

.n-m-l-1 {
  margin-left: -0.25rem !important;
}

.n-m-l-2 {
  margin-left: -0.5rem !important;
}

.n-m-l-3 {
  margin-left: -0.75rem !important;
}

.n-m-l-4 {
  margin-left: -1rem !important;
}

.n-m-l-5 {
  margin-left: -1.25rem !important;
}

.n-m-l-6 {
  margin-left: -1.5rem !important;
}

.n-m-l-8 {
  margin-left: -2rem !important;
}

/* Horizontal Negative Margin */
.n-m-x-0 {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.n-m-x-1 {
  margin-left: -0.25rem !important;
  margin-right: -0.25rem !important;
}

.n-m-x-2 {
  margin-left: -0.5rem !important;
  margin-right: -0.5rem !important;
}

.n-m-x-3 {
  margin-left: -0.75rem !important;
  margin-right: -0.75rem !important;
}

.n-m-x-4 {
  margin-left: -1rem !important;
  margin-right: -1rem !important;
}

.n-m-x-5 {
  margin-left: -1.25rem !important;
  margin-right: -1.25rem !important;
}

.n-m-x-6 {
  margin-left: -1.5rem !important;
  margin-right: -1.5rem !important;
}

.n-m-x-8 {
  margin-left: -2rem !important;
  margin-right: -2rem !important;
}

/* Vertical Negative Margin */
.n-m-y-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.n-m-y-1 {
  margin-top: -0.25rem !important;
  margin-bottom: -0.25rem !important;
}

.n-m-y-2 {
  margin-top: -0.5rem !important;
  margin-bottom: -0.5rem !important;
}

.n-m-y-3 {
  margin-top: -0.75rem !important;
  margin-bottom: -0.75rem !important;
}

.n-m-y-4 {
  margin-top: -1rem !important;
  margin-bottom: -1rem !important;
}

.n-m-y-5 {
  margin-top: -1.25rem !important;
  margin-bottom: -1.25rem !important;
}

.n-m-y-6 {
  margin-top: -1.5rem !important;
  margin-bottom: -1.5rem !important;
}

.n-m-y-8 {
  margin-top: -2rem !important;
  margin-bottom: -2rem !important;
}

/* Padding on all sides */
.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 0.75rem !important;
}

.p-4 {
  padding: 1rem !important;
}

.p-5 {
  padding: 1.25rem !important;
}

.p-6 {
  padding: 1.5rem !important;
}

.p-8 {
  padding: 2rem !important;
}

/* Padding in specific directions */
.p-t-0 {
  padding-top: 0 !important;
}

.p-t-1 {
  padding-top: 0.25rem !important;
}

.p-t-2 {
  padding-top: 0.5rem !important;
}

.p-t-3 {
  padding-top: 0.75rem !important;
}

.p-t-4 {
  padding-top: 1rem !important;
}

.p-t-5 {
  padding-top: 1.25rem !important;
}

.p-t-6 {
  padding-top: 1.5rem !important;
}

.p-t-8 {
  padding-top: 2rem !important;
}

.p-r-0 {
  padding-right: 0 !important;
}

.p-r-1 {
  padding-right: 0.25rem !important;
}

.p-r-2 {
  padding-right: 0.5rem !important;
}

.p-r-3 {
  padding-right: 0.75rem !important;
}

.p-r-4 {
  padding-right: 1rem !important;
}

.p-r-5 {
  padding-right: 1.25rem !important;
}

.p-r-6 {
  padding-right: 1.5rem !important;
}

.p-r-8 {
  padding-right: 2rem !important;
}

.p-b-0 {
  padding-bottom: 0 !important;
}

.p-b-1 {
  padding-bottom: 0.25rem !important;
}

.p-b-2 {
  padding-bottom: 0.5rem !important;
}

.p-b-3 {
  padding-bottom: 0.75rem !important;
}

.p-b-4 {
  padding-bottom: 1rem !important;
}

.p-b-5 {
  padding-bottom: 1.25rem !important;
}

.p-b-6 {
  padding-bottom: 1.5rem !important;
}

.p-b-8 {
  padding-bottom: 2rem !important;
}

.p-l-0 {
  padding-left: 0 !important;
}

.p-l-1 {
  padding-left: 0.25rem !important;
}

.p-l-2 {
  padding-left: 0.5rem !important;
}

.p-l-3 {
  padding-left: 0.75rem !important;
}

.p-l-4 {
  padding-left: 1rem !important;
}

.p-l-5 {
  padding-left: 1.25rem !important;
}

.p-l-6 {
  padding-left: 1.5rem !important;
}

.p-l-8 {
  padding-left: 2rem !important;
}

/* Horizontal and Vertical Padding */
.p-x-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.p-x-1 {
  padding-left: 0.25rem !important;
  padding-right: 0.25rem !important;
}

.p-x-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.p-x-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.p-x-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.p-x-5 {
  padding-left: 1.25rem !important;
  padding-right: 1.25rem !important;
}

.p-x-6 {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}

.p-x-8 {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

.p-y-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.p-y-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.p-y-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.p-y-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

.p-y-4 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.p-y-5 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

.p-y-6 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.p-y-8 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}


.text-xxs {
  font-size: 0.65rem !important;
}
.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-base {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.text-6xl {
  font-size: 4rem;
}

.font-thin {
  font-weight: 100;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}
.text_capitalize{
  text-transform:capitalize;
}
/* Row Gap (Y-Axis) */
.gap-y-0 {
  row-gap: 0;
}

.gap-y-1 {
  row-gap: 0.25rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-5 {
  row-gap: 1.25rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

.gap-y-10 {
  row-gap: 2.5rem;
}

.gap-y-12 {
  row-gap: 3rem;
}

.gap-y-16 {
  row-gap: 4rem;
}

.gap-y-20 {
  row-gap: 5rem;
}

.gap-y-24 {
  row-gap: 6rem;
}

.gap-y-32 {
  row-gap: 8rem;
}

/* Column Gap (X-Axis) */
.gap-x-0 {
  column-gap: 0;
}

.gap-x-1 {
  column-gap: 0.25rem;
}

.gap-x-2 {
  column-gap: 0.5rem;
}

.gap-x-3 {
  column-gap: 0.75rem;
}

.gap-x-4 {
  column-gap: 1rem;
}

.gap-x-5 {
  column-gap: 1.25rem;
}

.gap-x-6 {
  column-gap: 1.5rem;
}

.gap-x-8 {
  column-gap: 2rem;
}

.gap-x-10 {
  column-gap: 2.5rem;
}

.gap-x-12 {
  column-gap: 3rem;
}

.gap-x-16 {
  column-gap: 4rem;
}

.gap-x-20 {
  column-gap: 5rem;
}

.gap-x-24 {
  column-gap: 6rem;
}

.gap-x-32 {
  column-gap: 8rem;
}

.bg-blue {
  background: #112A46 !important;
}
.bg-red {
  background: #E50000 !important;
}
.border-blue {
  border-color: #112A46 !important;
}
.bg-light-neon {
  background: #F8FAFB !important;
}

.bg-neon {
  background: #4FAEAF !important;

  &.opacity-70 {
    background: rgba(233, 241, 246, 0.7) !important;
  }
  &.opacity-10{
    background-color: rgba(79, 174, 175, 0.10) !important;
  }
}

.text-neon {
  color: #4FAEAF !important;
}

.text-black{
    color: black !important;
}
.text-gray2{
  color: #A6A6A6 !important;
}
.rounded-2 {
  border-radius: 0.5rem !important;
}

.rounded-3 {
  border-radius: 0.75rem !important;
}

.rounded-4 {
  border-radius: 1rem !important;
}

.rounded-5 {
  border-radius: 1.25rem !important;
}

.rounded-6 {
  border-radius: 1.5rem !important;
}

.rounded-7 {
  border-radius: 1.75rem !important;
}

.rounded-8 {
  border-radius: 2rem !important;
}

.shadow-0 {
  box-shadow: unset !important;
}

.shadow {
  box-shadow: 0 8px 24px 0 rgba(70, 76, 136, 0.20) !important;
}

.shadow-2 {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1) !important;
}

.border-0 {
  border: unset !important;
}

.border-bottom {
  border-bottom: 1px solid #F1F1F1;
}


.border-top {
  border-top: 1px solid #F1F1F1;
}

.min-w-full {
  min-width: 100%;
}

.w-full{
  width: 100%;
}

.bg-white {
  background: #fff !important;
}

.h-full{
  height: 100% !important;
}


.q-btn-secondary {
  border-radius: 0.25rem;
  border: 1px solid $neon;
  background: rgba(79, 174, 175, 0.10);

  &.active {
    @include active;
  }
}

.q-btn-secondary-outlined {
  border-radius: 0.25rem;
  border: 1px solid $neon;
  .v-btn__content{
    color: $neon !important;
  }
  &.active {
    .v-btn__content{
      color: white !important;
    }
    @include active;
  }
}

.bg-ghost-white {
  @include background(#FAFCFF)
}


.export-button {
  background: rgba(247, 247, 247, 1);
  border-radius: 0.25rem;
  padding: 0.75rem !important;
  font-weight: 600 !important;
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch
}

.svg-stroke-primary {
  svg {
    &,*{
      stroke: $blue !important;
      fill: none !important;
    }
  }
}

.svg-stroke-white {
  svg {
    stroke: white !important;
    fill: none !important;
  }
}
.svg-stroke-neon {
  svg {
    &,* {
      stroke: $neon !important;
      fill: none !important;
    }
  }
}
.svg-stroke-red {
  svg {
    stroke:#E50000 !important;
    fill: none !important;
  }
}
.svg-fill-red {
  svg {
    fill:#E50000 !important;
  }
}
.svg-stroke-black {
  svg {
    stroke: black !important;
    fill: none !important;
  }
}
.svg-fill-primary {
  svg {
    fill: $blue !important;
    stroke: none !important;
  }
}
.svg-fill-white {
  svg {
    fill: white !important;
    stroke: none !important;
  }
}
.svg-fill-neon {
  svg {
    fill: $neon !important;
    stroke: none !important;
    stroke-width: 1.5;
  }
}

.svg-fill-red {
  svg {
    &,* {
      fill: #E50000 !important;
      stroke: none !important;
      stroke-width: 1.5;
    }
  }
}


.active-svg-stroke-neon {
  border-bottom: 1px solid black;
  svg {
    stroke: $neon !important;
    fill: none !important;
  }
}
.max-w-50{
 max-width: 50%;
}
.max-68{
  max-width: 68rem !important;
}
.max-36{
  max-width: 36rem !important;
}
.bg-cyan{
    background: #F8FAFB !important;
}
.relative{
    position: relative;
}
.absolute{
    position: absolute;
}
.min-h-none{
  min-height: unset !important;
}

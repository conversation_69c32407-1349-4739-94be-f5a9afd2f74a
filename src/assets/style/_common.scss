@import "mixins";

.tr-rounded {
  th:first-child, td:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  th:last-child, td:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

.tr-neon {
  td, th {
    background: #4FAEAF !important;
  }

  &.opacity-70 {
    td, th {
      background: rgba(233, 241, 246, 0.7) !important;
    }
  }


}

.v-progress-linear__content {
  justify-content: flex-start !important;
  padding-left: 10px;
}

.new-pagination {
  padding-top: 1rem;
  padding-bottom: 1rem;

  button {
    box-shadow: none !important;
    border-radius: 0.5rem !important;
  }
}

.border-collapse {
  border-collapse: collapse !important;
}

.q-tabs {
  @include tab;
}
.q-tabs-secondary {
  @include tab($neon);
}

.tab_container-blue {
  @include tab_container($blue);
}

.tab_container-neon {
  @include tab_container($blue);
}

.table {
  width: 100%;

  th {
    font-weight: 500;
    //min-width: 150px;
    font-size: 1rem;
    padding: 1.25rem !important;
    vertical-align: unset !important;
    text-align: left !important;

    & > div:first-child {
      margin-bottom: 5px;
    }
  }

  td {
    font-weight: 400;
    font-size: 1rem;
    padding: 0.5rem 1.25rem;
  }
}

.w-200 {
  width: 200px !important;
  max-width: 200px !important;
}

.w-150 {
  width: 150px !important;
  max-width: 150px !important;
}

.q-btn-edit {
  border-radius: 0.25rem;
  background: rgba(17, 42, 70, 0.10);
}

.q-btn-delete {
  border-radius: 0.25rem;
  background: rgba(229, 0, 0, 0.1) !important;
  color: #E50000 !important;
}

label {
  color: $blue;
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.stepper-card {
  border-radius: 0.75rem !important;
  border: 1px solid #EAEAEA !important;
  background: #FFF !important;

  .v-stepper__header {
    height: unset !important;
  }

  .v-stepper__step__step {
    width: 2.5rem !important;
    height: 2.5rem !important;
    border-radius: 0.5625rem !important;
    background: rgba(0, 0, 0, 0.05) !important;
    color: rgba(0, 0, 0, 0.6) !important;
  }

  .v-stepper__step--active .v-stepper__step__step {
    background: rgba(79, 174, 175, 0.10) !important;
    color: #4FAEAF !important;
  }
}

.custom-radio {
  .mdi-radiobox-marked {
    color: $neon !important;
    caret-color: $neon !important;
  }

  &:not(.border-0){
    border: 1px solid rgba(242, 242, 242, 1);
  }
  padding: 0.5rem 1rem;
  height: 40px !important;

  &.v-item--active {
    border-radius: 0.25rem;
    background: rgba(79, 174, 175, 0.05);
    &:not(.border-0){
      border: 2px solid $neon !important;
    }
  }

  &.v-radio--is-disabled {
    border: 1px solid rgba(0, 0, 0, 0.1) !important;

    label {
      color: rgba(86, 92, 104, 1) !important;
    }
  }
}

.custom-radio-group {
  min-height: 40px !important;

  .v-input--radio-group__input {
    display: flex;
    column-gap: 14px !important;
    flex-wrap: nowrap !important;
  }

  .v-radio {
    margin-right: 0px !important;
  }

  &.v-input--selection-controls {
    margin-top: 0px;
    padding-top: 0px;
  }

}

.qp-custom-checkbox {
  border: 1px solid #DCDCDC;
  padding: 0.5rem 1rem;
  width: auto !important;
  border-radius: 0.25rem;
  &:not(.no-height){
    height: 40px;
  }
  .v-input__control {
    align-items: center !important;
    text-align: center !important;

    input[type="checkbox"] {
      border-radius: 10px;
    }
  }

  &.v-input--selection-controls:not(.mt-1):not(.mt-2):not(.mt-3):not(.mt-4):not(.mt-5) {
    margin-top: 8px !important;
  }

  .v-input__slot {
    width: auto !important;
  }

  &.v-input--is-label-active {
    color: $neon !important;
    border: 1px solid $neon !important;

    label {
      color: $neon !important;
    }

    background: rgba(79, 174, 175, 0.05);

  }

  .mdi-checkbox-marked {
    &.primary--text {
      background-color: rgba(79, 174, 175, 0.05) !important;
      color: $neon !important;
      //caret-color: $neon !important;
    }
  }
}

.ck-powered-by {
  display: none !important;
}

.tabs{
  .tab:not(:last-child) {
    &:after {
      border-right: 1px solid #DCDCDC;
      content: " ";
      margin-left: 1rem;
      margin-right: 1rem;
    }
  }
}

.summary-table {

  border-collapse: collapse;
  width: 100%;
  margin: 0 auto;
  font-weight: 400;
  font-size: 14px;

  th {
    font-size: 1rem !important;
    border-bottom: 0 !important;
    font-weight: 500 !important;
    opacity: 1 !important;
    padding: 0.75rem;
  }

  td {
    font-size: 1rem !important;
    height: 48px;
    padding: 0.75rem;
    font-weight: 400;
    border-bottom: thin solid rgba(0, 0, 0, 0.05) !important;
    opacity: 1 !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }


}
.q-menu{
 box-shadow: $boxShadow;
  .v-list-item{
    padding: 0.25rem 1rem;
    font-size: 1rem;
    font-weight: 400;
    color: $blue;
    border-radius: 0.25rem;
    min-width: 12rem;
    min-height: 26px;
    &:hover{
      background-color: $neon-100;
      .v-list-item__title{
        color: $neon !important;
      }
    }
  }
}

.download-btn{
  border-radius: 0.25rem;
  border: 1px solid $neon;
  background: #4FAEAF1A;
  color: $neon;
  padding: 0.5rem;
}
.show-on-hover{
  button{
    display: none !important;
  }
}

/** Membership Schedule New Screen Checkin Checkout START **/
.membership-schedule-new{
    .calender-navi {
        width: 420px;
        text-align: center;
        margin: 0 auto;
    }
}
.membership-header-cell{
    .c-booking-btn {
        width: 180px;
        font-weight: 400;
        font-size: 10px;
        line-height: normal;
    }
    .c-booking-btn span {
        width: 50% !important;
        display: inline-block;
        font-weight: 600;
    }
    .header_name {
        padding-top: 12px;
        font-size: 14px;
    }
    .header_name.Time {
        padding-top: 20px;
        text-align: center;
    }
}
.membership-schedule-cell {
    &.ms-border-bottom {
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    &.hoverstyle {
      background: rgba(79, 174, 175, 0.10);
      cursor: pointer;
    }
    .checkin-checkout-div span {
        display: inline;
    }
    .checkin-checkout-div {
        width: 100%;
        display: table;
    }
    .cc-row {
        float: left;
        width: 50%;
    }
    .cc-row span {
        display: block;
    }
    span.checkout-count {
        color: #E50000;
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        padding-top: 4px;
    }
    span.checkin-count {
        color: #093;
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }
    .cc-row .checkin,.cc-row .checkout {
        color: rgba(17, 42, 70, 0.70);
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
    }
}
.member-checkin-checkout-popup{
    span.header {
        text-align: center;
        width: 100%;
        display: block;
        padding-top: 8px;
        color: #000;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0.2px;
    }
    .close-icon-btn {
        padding-top:  8px;
        text-align: right;
    }
    .pop-heading {
        text-align: center;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0.2px;
        padding: 10px 0px;
    }
    .pop-heading.checkin {
        background: rgba(0, 153, 51, 0.10);
        color: #093;
    }
    .pop-heading.checkout {
        background: rgba(229, 0, 0, 0.10);
        color: #E50000;
    }
    
    .members-list {
        width: 100%;
        padding: 10px 10px;
        color: #000;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0.2px;
        display: table;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    .members-list:hover{
        cursor: pointer;
        background: #f5f5f5;
    }
    .members-list.checkin{
        border-right: 1px solid rgba(0, 0, 0, 0.05);
    }
    .members-list.checkout{
        border-left: 1px solid rgba(0, 0, 0, 0.05); 
    }
    
    .members-list .member-img {
        float: left;
        width: 15%;
        text-align: center;
    }
    
    .members-list .member-content {
        float: left;
        width: 80%;
        padding: 8px 8px;
    }
    
    .members-list .member-content .time {
        color: rgba(0, 0, 0, 0.40);
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px; /* 166.667% */
        letter-spacing: 0.2px;
    }
    .members-area{
        min-height: 400px;
        max-height: 550px;
        overflow-y: auto;
    }
    .member-content .name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
/*** Membership Schedule New Screen CheckIn CheckOut END ***/
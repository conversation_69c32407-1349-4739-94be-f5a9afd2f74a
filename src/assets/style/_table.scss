.user-wrapper {
  margin: 0px auto 20px auto;
  min-height: 320px;
  width: 100%;
}
.md-layout-item {
  padding-right: 15px;
  padding-left: 15px;
}

.header_title {
  font-size: 20px;
  padding: 15px 0;
  font-weight: 600;
  color: #062b47;
}

.md-card.md-theme-default,
.md-card.md-theme-default .md-card-expand .md-card-actions {
  background-color: #fff;
  background-color: var(--md-theme-default-background, #fff);
}

.md-card.md-theme-default {
  color: rgba(0, 0, 0, 0.87);
  color: var(
    --md-theme-default-text-primary-on-background,
    rgba(0, 0, 0, 0.87)
  );
}

.md-card {
  display: inline-block;
  position: relative;
  width: 100%;
  margin: 25px 0;
  overflow: unset;
  -webkit-box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.14);
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.14);
  border-radius: 3px;
  color: rgba(0, 0, 0, 0.87);
  background: #fff;
}

.md-card {
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  position: relative;
  z-index: 1;
  border-radius: 2px;
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition-property: color, background-color;
  will-change: color, background-color;
}

.md-card [data-background-color],
.md-card [data-background-color] a {
  color: #fff;
}

.md-card .md-tabs.md-warning .md-tabs-navigation,
.md-card [data-background-color="orange"] {
  /* background: linear-gradient(60deg,#062b48,#00b1b0);
             -webkit-box-shadow: 0 12px 20px -10px rgba(255,152,0,.28), 0 4px 20px 0 rgba(0,0,0,.12), 0 7px 8px -5px rgba(255,152,0,.2);
       box-shadow: 0 12px 20px -10px rgba(255,152,0,.28), 0 4px 20px 0 rgba(0,0,0,.12), 0 7px 8px -5px rgba(255,152,0,.2);*/
  background-color: #066a8c;
}

.md-card .md-card-header {
  -webkit-box-shadow: 0 10px 30px -12px rgba(0, 0, 0, 0.42),
    0 4px 25px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
  box-shadow: 0 10px 30px -12px rgba(0, 0, 0, 0.42),
    0 4px 25px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
  margin: -20px 15px 0;
  border-radius: 3px;
  padding: 15px;
  background-color: #066a8c;
}

.md-card-header {
  padding: 16px;
}

.md-card .md-card-content {
  padding: 15px 20px;
}

.md-card .md-card-table{
  padding-bottom: 0px !important;
}

.md-card-content:last-of-type {
  padding-bottom: 24px;
}

.md-card-header + .md-card-content {
  padding-top: 0;
}

.md-card-content {
  padding: 16px;
  font-size: 14px;
  line-height: 22px;
}

.md-content.md-theme-default {
  background-color: #fff;
  background-color: var(--md-theme-default-background, #fff);
  color: rgba(0, 0, 0, 0.87);
  color: var(
    --md-theme-default-text-primary-on-background,
    rgba(0, 0, 0, 0.87)
  );
}

.md-table {
  display: flex;
  flex-flow: column wrap;
  overflow-x: auto;
}

.md-table.md-theme-default .md-table-alternate-header,
.md-table.md-theme-default .md-table-content {
  background-color: #fff;
  background-color: var(--md-theme-default-background, #fff);
}

.md-content.md-theme-default {
  background-color: #fff;
  background-color: var(--md-theme-default-background, #fff);
  color: rgba(0, 0, 0, 0.87);
  color: var(
    --md-theme-default-text-primary-on-background,
    rgba(0, 0, 0, 0.87)
  );
}

.md-table .md-table-content {
  flex: 1;
  overflow-x: auto;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.md-table table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  overflow: hidden;
}

.md-table.md-theme-default .md-table-head {
  color: rgba(0, 0, 0, 0.54);
  color: var(--md-theme-default-text-accent-on-background, rgba(0, 0, 0, 0.54));
}

.md-table-head {
  padding: 0;
  position: relative;
  font-size: 12px;
  line-height: 16px;
  text-align: left;
}

.md-ripple {
  -webkit-mask-image: none;
  overflow: visible;
}

.md-table-head-container {
  height: auto;
  padding: 0;
}

.md-table-head-container,
.md-table-head-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  //text-align: center;
}

.md-table-head-container {
  padding: 8px 0 0;
}

.md-ripple {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 5;
  overflow: hidden;
  -webkit-mask-image: radial-gradient(circle, #fff 100%, #000 0);
}

.md-table-head-label {
  padding-right: 32px;
  padding-left: 24px;
  display: inline-block;
  position: relative;
  line-height: 28px;
  font-size: 16px;
  //color: #FFFFFF;
  color: #000000;
  font-weight: 400;
}

.md-table-head-container,
.md-table-head-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.md-table-row {
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition-property: background-color, font-weight;
  will-change: background-color, font-weight;
  border: 1px solid #f3f3f3;
}

.md-table.md-theme-default .md-table-row td {
  border-top-color: rgba(0, 0, 0, 0.12);
  border-top-color: var(
    --md-theme-default-divider-on-background,
    rgba(0, 0, 0, 0.12)
  );
}

.md-table .md-table-row td {
  border-top-color: rgba(0, 0, 0, 0.06) !important;
  text-align: left;
}

.md-table-row td:nth-child(1) {
  text-align: left !important;
}

tbody .md-table-row td {
  border-top: 1px solid;
}

.md-table-cell {
  padding: 2px 1px;
  font-size: 14px;
}

.md-table-cell {
  height: 48px;
  position: relative;
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 13px;
  line-height: 18px;
}

.md-table-cell-container {
  padding: 0;
}

.md-table-cell-container {
  padding: 6px 1.25rem;
}

.search-select {
  margin: 0 13px;
}

.customer_det {
  float: left;
  width: 50%;
}

.user_date {
  font-weight: bold;
  color: #0c6486;
}

.search_customer .v-text-field {
  padding-top: 0px;
  margin-top: 0px;
}

.search_customer .v-input__slot {
  margin-bottom: 0px;
}

.table_borders {
  -webkit-box-shadow: 0 0px 0px -12px rgba(0, 0, 0, 0.42),
    0 4px 25px 0 rgba(0, 0, 0, 0.12), 0 2px 0px -5px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0px 0px -12px rgba(0, 0, 0, 0.42),
    0 4px 25px 0 rgba(0, 0, 0, 0.12), 0 2px 0px -5px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}

.v-text-field--filled > .v-input__control > .v-input__slot {
  min-height: 32px !important;
}

.history_table {
  margin-top: 20px;
  font-size: 14px;
  width: 100%;
}

.history_table tr td {
  padding: 15px 10px;
  color: #000000;
  background-color: #f3f3f3;
}

.icon_table {
  width: 100%;
}

.icon_table tr td {
  width: 100%;
  padding: 0 10px;
}

/* Table */

/* Customer -*/

.ageBox {
  min-height: 300px;
  min-height: 264px;
  max-width: 193px;
  top: 72px;
  left: 5px;
  -webkit-transform-origin: left top;
  transform-origin: left top;
  z-index: 8;
  background: #fff;
  padding: 10px 5px;
  z-index: 999;
}

.age_text {
  height: 50px;
}

.ageBox .v-label,
.search_column .v-label {
  font-size: 13px;
  font-weight: 100;
}

.search_column .v-input .v-label {
  font-size: 13px;
  font-weight: 400;
}

.ageBox .v-list__tile {
  height: 35px;
}

.search_column .v-input__control {
  min-height: 32px !important;
}

.search_column {
  //width: 95%;
  width: 100%;
  margin: 0 auto;
}

.search_column .v-input__slot {
  margin-bottom: 0px;
}

.search_column .v-text-field .v-text-field__details {
  margin-bottom: 0px !important;
}

.search_column .v-input .v-label {
  font-weight: 300;
  font-size: 13px;
}
th:nth-child(1) .md-table-head-container,
.md-table-head-label {
  text-align: left;
}
a {
  text-decoration: none;
}

.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 1rem !important;
}

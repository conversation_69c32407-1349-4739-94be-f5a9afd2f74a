/** Trainer Schedule New Screen START **/
.ts-new{
  .calender-navi {
    width: 420px;
    text-align: center;
    margin: 0 auto;
  }
}
.ts-new .calender-navi {
  width: 420px;
  text-align: center;
  margin: 0 auto;
}
.ts-header-cell{
  .c-booking-btn {
    width: 180px;
    font-weight: 400;
    font-size: 10px;
    line-height: normal;
  }
  .c-booking-btn span {
    width: 50% !important;
    display: inline-block;
    font-weight: 600;
  }
  .header_name {
    padding-top: 12px;
    font-size: 14px;
  }
  .header_name.Time {
    padding-top: 20px;
    text-align: center;
  }
}
.ts-schedule-cell {
  &.ms-border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
  &.hoverstyle {
    background: rgba(79, 174, 175, 0.10);
    cursor: pointer;
  }
  .checkin-checkout-div span {
    display: inline;
  }
  .checkin-checkout-div {
    width: 100%;
    display: table;
  }
  .cc-row {
    float: left;
    width: 50%;
  }
  .cc-row span {
    display: block;
  }
  span.checkout-count {
    color: #E50000;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    padding-top: 4px;
  }
  span.checkin-count {
    color: #093;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .cc-row .checkin,.cc-row .checkout {
    color: rgba(17, 42, 70, 0.70);
    font-family: Inter;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
/*** Membership Schedule New Screen CheckIn CheckOut END ***/
<template>
  <v-container>
    <ApprovalTabs :currentTab="tab" @gotoTab="gotoTab" />
    <div class="md-card shadow-0 rounded-2 bordered mt-4">
      <FacilityApprovals :refresh="refresh" v-if="tab === 'facility'" />
      <EventsApprovals :refresh="refresh" v-if="tab === 'events'" />
      <MembershipApprovals :refresh="refresh" v-if="tab === 'memberships'" />
    </div>
  </v-container>
</template>

<script>
import ApprovalTabs from "@/components/Approvals/ApprovalTabs.vue";
import FacilityApprovals from "@/components/Approvals/FacilityApprovals.vue";
import EventsApprovals from "@/components/Approvals/EventsApprovals.vue";
import MembershipApprovals from "@/components/Approvals/MembershipApprovals.vue";

export default {
  name: "Approvals",
  components: {MembershipApprovals, EventsApprovals, FacilityApprovals, ApprovalTabs},
  mounted() {
    this.setTab()
  },
  data() {
    return {
      tab: '',
      refresh:false,
    }
  },
  methods:{
    gotoTab(tab) {
      this.tab = tab;
    },
    setTab() {
      if (this.checkReadPermission(this.$modules.facility.schedule.slug)) {
        this.tab = 'facility'
      } else if (this.checkReadPermission(this.$modules.events.schedule.slug)) {
        this.tab = 'events'
      } else if (this.checkReadPermission(this.$modules.memberships.dashboard.slug)) {
        this.tab = 'memberships'
      }
      // else if (this.checkReadPermission(this.$modules.trainers.dashboard.slug)) {
      //   this.tab = 'trainers'
      // } else if (this.checkReadPermission(this.$modules.workshops.schedule.slug)) {
      //   this.tab = 'academy'
      // } else if (this.checkReadPermission(this.$modules.pos.dashboard.slug)) {
      //   this.tab = 'retail'
      // }
    }
  }
}
</script>

<style scoped>

</style>
<template>
  <v-container fluid>
    <ServiceRequestTopTab :activeTab="activeTab" @setActiveTab="setActiveTab" @setPerPage="setPerPage" @filterByRequestTypeTab="filterByRequestTypeTab" @filterByStatus="filterByStatus" @filterByPartnerName="filterByPartnerName"/>
    <v-divider class="mt-2 mb-2"/>
    <ServiceRequestConfiguration v-if="JSON.stringify(['configurations']) == JSON.stringify(activeTab)" />
    <template v-else>
      <v-row>
        <v-col cols="12" v-for="serviceRequest in serviceRequests" :key="serviceRequest.id" lg="4" md="6" sm="12" xl="3">
          <ServiceRequestCard :serviceRequest="serviceRequest"/>
        </v-col>
      </v-row>
      <v-pagination
          v-if="totalPages"
          v-model="page"
          :length="totalPages"
          class="new-pagination"

      ></v-pagination>
      <h3 class="text-center mt-12" v-if="serviceRequests.length == 0">
        No Request Found
      </h3>
    </template>
  </v-container>
</template>
<script>
import ServiceRequestTopTab from "@/components/ServiceRequest/ServiceRequestTopTab.vue";
import ServiceRequestCard from "@/components/ServiceRequest/ServiceRequestCard.vue";
import ServiceRequestConfiguration from "@/components/ServiceRequest/ServiceRequestConfiguration.vue";
export default {
  props: {},
  components: {ServiceRequestConfiguration, ServiceRequestTopTab,ServiceRequestCard },
  data() {
    return {
      page: 1,
      totalPages: 1,
      perPage: 12,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      viewLeadId: null,
      isResponseModalVisible: false,
      isStatusModalVisible: false,
      serviceRequests: [],
      selectedServiceRequestTypeIds: null,
      selectedStatusIds: null,
      filterName: null,
      activeTab:[3,4],
    };
  },
  computed: {},
  mounted() {
    this.activeTab = [3,4];
    this.filterByRequestTypeTab(this.activeTab);
  },
  watch: {
    page() {
      this.filterByRequestTypeTab(this.activeTab);
    },
  },
  methods: {
    setActiveTab(data){
      this.activeTab = data;
    },
    setPerPage(data){
      this.perPage = data;
      this.loadServiceRequestLogs();
    },
    filterByRequestType(requestTypeIds){
      if(requestTypeIds.length > 0){
        this.selectedServiceRequestTypeIds = requestTypeIds.join(",");
      }else{
        this.selectedServiceRequestTypeIds = null;
      }
      this.loadServiceRequestLogs();
    },
    filterByRequestTypeTab(requestTypeIds){
      if(requestTypeIds.length > 0){
        this.selectedServiceRequestTypeIds = requestTypeIds.join(",");
      }else{
        this.selectedServiceRequestTypeIds = null;
      }
      this.activeTab = requestTypeIds;
      this.loadServiceRequestLogs();
    },
    filterByStatus(statusIds){
      if(statusIds.length > 0){
        this.selectedStatusIds = statusIds.join(",");
      }else{
        this.selectedStatusIds = null;
      }
      this.loadServiceRequestLogs();
    },
    loadServiceRequestLogs(){
      let url = "";
      if(this.selectedServiceRequestTypeIds){
        url+=`&request_type_ids=${this.selectedServiceRequestTypeIds}`;
      }
      if(this.selectedStatusIds){
        url+=`&status_ids=${this.selectedStatusIds}`;
      }
      if(this.filterName){
        url+=`&filter_name=${this.filterName}`;
      }
      this.showLoader('Loading Events');
      this.$http
          .get(`venues/service-request/logs?page=${this.page}&per_page=${this.perPage}&${url}`)
          .then((response) => {
            if (response.status === 200 && response.data.status == true) {
              this.serviceRequests = response.data.data;
              this.totalPages = response.data.total_pages;
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          })
          .finally(() => {
            this.hideLoader();
          });
    },
    filterByPartnerName(searchName){
      this.filterName = searchName;
      this.loadServiceRequestLogs();
    }
  },
};
</script>

<style scoped lang="scss">
.v-card {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
  margin-right: 8px !important;
  margin-left: 8px !important;
}
.rounded-5 {
  border-radius: 1.25rem !important;
}
</style>

<template>
  <v-container fluid>
    <v-row class="">
      <v-col cols="12" lg="12" sm="12"><BackButton :handler="goBack"/></v-col>
    </v-row>
    <v-row>
      <v-col cols="12" lg="8" md="6" sm="12" xl="9">
        <v-row class="ml-2">
          <v-col cols="12" md="10"><p class="text-xl m-0 font-bold">Academy Details</p></v-col>
          <v-col cols="12" md="2" class="text-center pr-5">
            <span :class="`btn btn-sm mr-0 status-badge ${getStatusText}`">{{ getStatusText }}</span>
          </v-col>
        </v-row>
        <v-card class="rounded-2 m-2">
          <v-card-text v-if="academy && academy.request_body">
            <v-row class="ml-3 mr-3 mt-3">
              <v-col cols="12">
                <v-row class="opacity-70 general-heading rounded-2">
                  <v-col cols="12" md="9"><span>General Information</span></v-col>
                  <v-col cols="12" md="3 text-right">
                    <span class="font-12 text-base">Timestamp: {{ academy.updated_timestamp | timeStamp }}</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="10">
                <v-row>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Academy Name</span>
                      <span class="d-block font-semibold">{{ academy.request_body.name }}</span>
                    </div>
                  </v-col>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Academy Type</span>
                      <span class="d-block font-semibold">{{ academy.request_body.is_public == 1?'Online':'Offline' }}</span>
                    </div>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Start and End Date</span>
                      <span class="d-block font-semibold">{{ getScheduleDate() }}</span>
                    </div>
                  </v-col>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Service</span>
                      <span class="d-block font-semibold">{{  academy.request_body.venue_service_name?academy.request_body.venue_service_name:academy.request_body.venue_service_id }}</span>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="2">
                <div class="academy-image"><v-img :aspect-ratio="1" :src="imageSrc" class="rounded-2"></v-img></div>
              </v-col>
              <v-col cols="12" md="12" v-if="academy.request_body.description">
                <div class="mb-3">
                  <span class="d-block">Description</span>
                  <span class="d-block font-semibold">{{ academy.request_body.description }}</span>
                </div>
              </v-col>
            </v-row>
            <v-divider class="mt-2 mb-2"></v-divider>
            <v-row v-if="academy && academy.request_body && academy.request_body.programs">
              <v-col v-for="(program, index) in academy.request_body.programs" :key="index" class="pointer" md="4" @click="selectProgram(index)">
                <div :class="selectedSlider === index ? 'program_widget_active' :'program_widget' " class="rounded-5 mb-4 " style="">
                  <v-card-text>
                    <div class="d-flex justify-space-between  align-center mb-1">
                      <p class="white--text text-capitalize font-bold p-0 m-0">{{ program.name || "Program name" }}</p>
                      <v-btn icon><TickIcon class="svg_fill_gray"/></v-btn>
                    </div>
                    <div class="p-4" style="background-color: #FFFFFF; border-radius: 12px">
                      <div class="d-flex justify-space-between border-bottom">
                        <p class="black-text text-capitalize font-semibold">Capacity: {{ program.capacity || 20 }}</p>
                        <p class="black-text text-capitalize font-semibold">Duration : {{ program.duration || 60 }} min</p>
                      </div>
                      <div class="d-flex justify-space-between mt-2">
                        <p class="m-0 p-0">Trainers<br/> {{ Number(totalTrainerCounts(program) || 0) | numberFormatter }} </p>
                        <p class="m-0 p-0" v-if="program.pricing">Products <br/>{{ Number(program.pricing.length || 0) | numberFormatter }} </p>
                      </div>
                    </div>
                  </v-card-text>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="schedules && schedules.length">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Program Schedules</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                        <tr class="opacity-70 tr-neon tr-rounded ">
                          <th class="black-text">Date Range</th>
                          <th class="black-text">Days</th>
                          <th class="black-text">Timings</th>
                          <th class="black-text">Trainers</th>
                          <th class="black-text">Facility</th>
                        </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(sc,index) in schedules" :key="index">
                            <td>{{ sc.start_date | dateformat }} - {{ sc.end_date | dateformat }}</td>
                            <td>{{ getWeekdays(sc.weekdays) }}</td>
                            <td>{{ getTimeSlot(sc) }}</td>
                            <td>{{ sc.trainer_names?sc.trainer_names:sc.trainer_ids }}</td>
                            <td style="max-width: 200px">{{ sc.location && sc.location != "null"?sc.location:sc.facility_name }}</td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="selectedProgram && selectedProgram.pricing">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Tickets</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                        <tr class="opacity-70 tr-neon tr-rounded ">
                          <th class="black-text">Ticket Name</th>
                          <th class="black-text">Frequency</th>
                          <th class="black-text">Price</th>
                          <th class="black-text">Type</th>
                        </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(ticket,sIndex) in selectedProgram.pricing" :key="sIndex">
                            <td>{{ ticket.name }}</td>
                            <td>{{ ticket.frequency }}</td>
                            <td>{{ parseFloat(ticket.total_price) | toCurrency }}</td>
                            <td>{{ ticket.package_type == "2"?("Sessions: "+ticket.sessions):"Program" }}</td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="academy && academy.request_body.documents && academy.request_body.documents.length > 0">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Documents</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                        <tr class="opacity-70 tr-neon tr-rounded ">
                          <th class="black-text">Document Name</th>
                          <th class="black-text">File</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(doc,docIndex) in academy.request_body.documents" :key="docIndex">
                          <td>{{ doc.name }}</td>
                          <td><span v-if="doc.file_path"> <v-icon color="cyan" title="View document" @click="openFile(doc.file_path)" >mdi-download-box</v-icon></span></td>
                        </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="academy && academy.request_body.custom_fields">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Additional Fields</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                        <tr class="opacity-70 tr-neon tr-rounded ">
                          <th class="black-text">Name</th>
                          <th class="black-text">Value</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(cf,index) in academy.request_body.custom_fields" :key="index">
                          <td>{{ cf.field_name }}</td>
                          <td>
                            <span v-if="cf.field_type !== 'File'">{{ cf.field_value  }}</span>
                            <span v-else> <v-icon color="cyan" title="Document" v-if="cf.field_value" @click="openFile(cf.field_value)">mdi-download-box</v-icon></span>
                          </td>
                        </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
        <span class="font-12 text-base ml-2" v-if="academy && academy.request_body && academy.status_id != 32">
          <ServiceRequestAction :requestData="academy" @reload="goBack"/>
        </span>
      </v-col>
      <v-col cols="12" lg="4" md="6" sm="12" xl="3" v-if="academy && academy.logs && academy.logs.length > 0 && academy.status_id != 34">
        <v-row class="mt-5">
          <v-col cols="12">
            <v-card class=" shadow rounded-2 m-2">
              <v-card-title>Activity Log</v-card-title>
              <v-divider />
              <v-card-text class="pa-0" v-if="academy && academy.logs">
                <ServiceRequestLogTimeline :logs="academy.logs" />
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import ServiceRequestAction from "@/components/ServiceRequest/ServiceRequestAction.vue";
import BackButton from "@/components/Common/BackButton.vue";
import ServiceRequestLogTimeline from "@/components/ServiceRequest/ServiceRequestLogTimeline.vue";
import TickIcon from '@/assets/images/misc/tick-circle.svg';
import moment from "moment/moment";
export default {
  components: {
    ServiceRequestAction,
    BackButton,TickIcon,ServiceRequestLogTimeline,
  },
  data() {
    return {
      levels: [],
      requestId: null,
      academy: null,
      pWidth: 400,
      tab: "details",
      params: {venue_service_ids: [], product_ids: [], product_type_ids: [4]},
      customerFormDialoge: false,
      orderId: null,
      order_id_for_payment: null,
      refresh: true,
      currentProgramIndex: 0,
      reload: false,
      orderIds: null,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      customerSessionFormDialoge: false,
      selectedProgram: null,
      schedules:[],
      selectedSlider: 0,
    };
  },
  mounted() {
    if (typeof this.$route.params.data != "undefined") {
      let data = JSON.parse(atob(this.$route.params.data));
      this.requestId = parseInt(data.id);
      if (this.requestId) {
        this.getRequestDetails();
      }
    }
    if (this.$store.getters.getWeekdays.status === false) {
      this.$store.dispatch("loadWeekdays");
    }
  },
  computed:{
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
    imageSrc(){
      return this.academy?.request_body?.image_path ? this.s3BucketURL + this.academy?.request_body?.image_path : require('@/assets/images/default_images/event_default.jpg');
    },
    getStatusText(){
      if(this.academy) {
        if (this.academy.status_id === 33) {
          return "approved";
        } else if (this.academy.status_id === 11) {
          return "pending";
        } else if(this.academy.status_id === 31){
          return "amendment";
        }else if(this.academy.status_id === 32){
          return "rejected";
        }else if(this.academy.status_id === 34){
          return "draft";
        }else if(this.academy.status_id === 35) {
          return "cancellation requested";
        }else if(this.academy.status_id === 13){
          return "cancelled";
        }
      }
      return "";
    },
  },
  methods: {
    goBack(){
      this.$router.push({name: "ServiceRequestLogs"});
    },
    getWeekdays(daysString) {
      let dayName = "";
      let days = JSON.parse(daysString);
      days.forEach((element) => {
        let findValue = this.$store.getters.getWeekdays.data.find((y) => y.bit_value == element);
        if (findValue) {
          dayName = dayName.concat(findValue.name.substring(0, 3), ",");
        }
      });
      return dayName.slice(0, -1);
    },
    getTimeSlot(data) {
      console.log(data,'data')
      let date = '';
      if (data.start_time && data.start_time != 'null'){
        date = moment(data.start_time, "HH:mm").format("h:mm a") + " To ";
      }
      if (data.end_time && data.end_time != 'null'){
        date += moment(data.end_time, "HH:mm").format("h:mm a");
      }
      return date;
    },
    getRequestDetails() {
      this.showLoader("Loading");
      this.$http
          .get("venues/service-request/logs/" + parseInt(this.requestId))
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              this.academy = response.data.data;
              if(this.academy && this.academy.request_body && this.academy.request_body.programs)
              this.selectedProgram = this.academy.request_body.programs[0];
              this.setSchedulesData();
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getScheduleDate(){
      const rb = this.academy.request_body;
      if(rb && this.academy.request_type_id === 4){
        const formatDate = (dateString) => {
          const date = new Date(dateString);
          const options = { day: 'numeric', month: 'short', year: 'numeric' };
          return date.toLocaleDateString('en-GB', options);
        };
        let displayDate = "";
        const start = formatDate(rb.start_date);
        const end = formatDate(rb.end_date);
        displayDate = `${start} - ${end}`;
        return displayDate;
      }
      return "";
    },
    selectProgram(index) {
      this.selectedSlider = index;
      this.selectedProgram = this.academy.request_body.programs[index];
      this.setSchedulesData();
    },
    totalTrainerCounts(program){
      let trainerIds = [];
      if(program && program.date_ranges && program.date_ranges.length > 0){
        program.date_ranges.forEach(dr => {
          if( dr.schedules && dr.schedules.length > 0){
            dr.schedules.forEach(sc => {
              if (sc.trainer_ids && sc.trainer_ids.length>0) {
                sc.trainer_ids.forEach((tId) => {
                  if (!trainerIds.includes(tId)) {
                    trainerIds.push(tId) // Add the ID if it doesn't exist
                  }
                })
              }
            })
          }
        })
      }
      return trainerIds.length;
    },
    setSchedulesData(){
      if(this.selectedProgram && this.selectedProgram.date_ranges){
        let scheduleData = [];
        this.selectedProgram.date_ranges.forEach((dRange) => {
          dRange.schedules.forEach((schedule) => {
            let obj = schedule;
            obj.start_date = dRange.start_date;
            obj.end_date = dRange.end_date;
            obj.facility_id = obj.facility_id === "null"?null:obj.facility_id;
            obj.facility_name = obj.facility_name === "null"?null:obj.facility_name;
            obj.is_external = obj.is_external !== "false";
            obj.isLoading = obj.isLoading !== "false";
            scheduleData.push(obj);
          });
        });
        this.schedules = [...new Set(scheduleData)];
      }
    }
  },
};
</script>

<style scoped>
.general-heading{
  background-color: rgb(240, 245, 249);
  margin-top: 5px;
  font-weight: 600;
  font-size: 16px;
  color: #000;
}
.status-badge {
  padding: 8px 8px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  display: block;
  text-transform: capitalize;
  border-radius: 5px;
  font-weight: 600;
}
.status-badge.approved{
  background: #B6F0B5;
  color: #113206;
}
.status-badge.pending{
  background: #FFCDB3;
  color: #471E08;
}
.status-badge.rejected{
  background: #FFBAB6;
  color: #4C1008;
}
.status-badge.amendment {
  background: dimgrey;
}
.status-badge.draft{
  background: #D2D2D2;
  color: #000000;
}
.status-badge.cancellation{
  background: pink;
  color: #000000;
}
.status-badge.cancelled{
  background: black;
  color: #fff;
}
.tab_active {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
}
.program_widget_active {
  background-color: #4FAEAF;
  .program_text_white {
    p {
      color: #fff !important;
    }
  }
}
</style>

<template>
  <v-container fluid>
    <v-row class="">
      <v-col cols="12" lg="12" sm="12"><BackButton :handler="goBack"/></v-col>
    </v-row>
    <v-row>
      <v-col cols="12" lg="8" md="6" sm="12" xl="9">
        <v-row class="ml-2">
          <v-col cols="12" md="10"><p class="text-xl m-0 font-bold">Event Details</p></v-col>
          <v-col cols="12" md="2" class="text-center pr-5">
            <span :class="`btn btn-sm mr-0 status-badge ${getStatusText}`">{{ getStatusText }}</span>
          </v-col>
        </v-row>
        <v-card class="rounded-2 m-2">
          <v-card-text v-if="event && event.request_body">
            <v-row class="ml-3 mr-3 mt-3">
              <v-col cols="12">
                <v-row class="opacity-70 general-heading rounded-2">
                  <v-col cols="12" md="9"><span>General Information</span></v-col>
                  <v-col cols="12" md="3 text-right">
                    <span class="font-12 text-base">Timestamp: {{ event.updated_timestamp | timeStamp }}</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="10">
                <v-row>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Event Name</span>
                      <span class="d-block font-semibold">{{ event.request_body.name }}</span>
                    </div>
                  </v-col>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Event Type</span>
                      <span class="d-block font-semibold">{{ event.request_body.is_membership_only == 1?'Member Only':'Public' }}</span>
                    </div>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Start and End Date</span>
                      <span class="d-block font-semibold">{{ getScheduleDate() }}</span>
                    </div>
                  </v-col>
                  <v-col>
                    <div class="mb-3">
                      <span class="d-block">Service</span>
                      <span class="d-block font-semibold">{{  event.request_body.venue_service_name }}</span>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="2">
                <div class="event-image"><v-img :aspect-ratio="1" :src="imageSrc" class="rounded-2"></v-img></div>
              </v-col>
              <v-col cols="12" md="12" v-if="event.request_body.description">
                <div class="mb-3">
                  <span class="d-block">Description</span>
                  <span class="d-block font-semibold">{{ event.request_body.description }}</span>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="event && event.request_body.event_schedules">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Event Schedule</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                          <tr class="opacity-70 tr-neon tr-rounded ">
                            <th class="black-text">Schedule</th>
                            <th class="black-text">Start Date</th>
                            <th class="black-text">Start Time</th>
                            <th class="black-text">End Date</th>
                            <th class="black-text">End Time</th>
                            <th class="black-text">Location</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(sc,index) in event.request_body.event_schedules" :key="index">
                            <td>{{ sc.schedule_name }}</td>
                            <td>{{ sc.start_date | dateformat }}</td>
                            <td>{{ sc.start_time }}</td>
                            <td>{{ sc.end_date | dateformat }}</td>
                            <td>{{ sc.end_time }}</td>
                            <td style="max-width: 200px">{{ sc.location && sc.location != "null"?sc.location:sc.facility_name }}</td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="event && event.request_body.tickets">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Tickets</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                          <tr class="opacity-70 tr-neon tr-rounded ">
                            <th class="black-text">Ticket Name</th>
                            <th class="black-text">Type</th>
                            <th class="black-text">Quantity</th>
                            <th class="black-text">Price</th>
                            <th class="black-text">Access Type</th>
                            <th class="black-text">Online Enable</th>
                            <th class="black-text">Image</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(ticket,index) in event.request_body.tickets" :key="index">
                            <td>{{ ticket.name }}</td>
                            <td>{{ ticket.type === "I"?"Individual":ticket.type==="C"?"Couple":"Group" }}</td>
                            <td>{{ ticket.quantity }}</td>
                            <td>{{ ticket.total_price | toCurrency }}</td>
                            <td>{{ ticket.enable_multi_access?"Multi Access":"Single Access" }}</td>
                            <td>{{ ticket.enable_online_booking?"Yes":"No" }}</td>
                            <td>
                              <span v-if="ticket.image"> <v-icon color="cyan" title="Ticket Image" @click="openFile(ticket.image)" >mdi-download-box</v-icon></span>
                            </td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="event && event.request_body.lineups">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Lineups</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                        <tr class="opacity-70 tr-neon tr-rounded ">
                          <th class="black-text">Name</th>
                          <th class="black-text">Designation</th>
                          <th class="black-text">Start Date</th>
                          <th class="black-text">Start Time</th>
                          <th class="black-text">End Time</th>
                          <th class="black-text">Description</th>
                          <th class="black-text">Image</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(linup,index) in event.request_body.lineups" :key="index">
                          <td>{{ linup.name }}</td>
                          <td>{{ linup.designation }}</td>
                          <td>{{ linup.start_date | dateformat }}</td>
                          <td>{{ linup.start_time | timeFormat }}</td>
                          <td>{{ linup.end_time | timeFormat }}</td>
                          <td style="width: 300px"><span class="line-clamp-1">{{ linup.description }}</span></td>
                          <td>
                            <span v-if="linup.image_path"> <v-icon color="cyan" title="View Image" @click="openFile(linup.image_path)" >mdi-download-box</v-icon></span>
                          </td>
                        </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="event && event.request_body.documents && event.request_body.documents.length > 0">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Documents</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                        <tr class="opacity-70 tr-neon tr-rounded ">
                          <th class="black-text">Document Name</th>
                          <th class="black-text">File</th>
                        </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(doc,docIndex) in event.request_body.documents" :key="docIndex">
                            <td>{{ doc.name }}</td>
                            <td><span v-if="doc.file_path"> <v-icon color="cyan" title="View document" @click="openFile(doc.file_path)" >mdi-download-box</v-icon></span></td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="event && event.request_body.custom_fields">
            <v-row>
              <v-col cols="12">
                <v-card class="shadow rounded-2 m-2">
                  <v-card-title class="font-semibold font-14">Additional Fields</v-card-title>
                  <v-card-text>
                    <v-simple-table >
                      <template v-slot:default>
                        <thead>
                          <tr class="opacity-70 tr-neon tr-rounded ">
                            <th class="black-text">Name</th>
                            <th class="black-text">Value</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(cf,index) in event.request_body.custom_fields" :key="index">
                            <td>{{ cf.field_name }}</td>
                            <td>
                              <span v-if="cf.field_type !== 'File'">{{ cf.field_value  }}</span>
                              <span v-else> <v-icon color="cyan" title="Document" v-if="cf.field_value" @click="openFile(cf.field_value)">mdi-download-box</v-icon></span>
                            </td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
        <span class="font-12 text-base ml-2" v-if="event && event.request_body && event.status_id != 32">
          <ServiceRequestAction :requestData="event" @reload="goBack" />
        </span>
      </v-col>
      <v-col cols="12" lg="4" md="6" sm="12" xl="3" v-if="event && event.logs && event.logs.length > 0 && event.status_id != 34">
        <v-row class="mt-5">
          <v-col cols="12">
            <v-card class=" shadow rounded-2 m-2">
              <v-card-title class="pt-2 pb-2 font-semibold font-14">Activity Log</v-card-title>
              <v-divider />
              <v-card-text v-if="event && event.logs && event.status_id != 34" class="pa-0">
                <ServiceRequestLogTimeline :logs="event.logs" />
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import BackButton from "@/components/Common/BackButton.vue";
import ServiceRequestLogTimeline from "@/components/ServiceRequest/ServiceRequestLogTimeline.vue";
import ServiceRequestAction from "@/components/ServiceRequest/ServiceRequestAction.vue";
export default {
  components: {
    BackButton,ServiceRequestLogTimeline,ServiceRequestAction
  },
  data() {
    return {
      levels: [],
      requestId: null,
      event: null,
      pWidth: 400,
      tab: "details",
      params: {venue_service_ids: [], product_ids: [], product_type_ids: [4]},
      customerFormDialoge: false,
      orderId: null,
      order_id_for_payment: null,
      refresh: true,
      currentProgramIndex: 0,
      reload: false,
      orderIds: null,
      customerSessionFormDialoge: false,
    };
  },
  mounted() {
    if (typeof this.$route.params.data != "undefined") {
      let data = JSON.parse(atob(this.$route.params.data));
      this.requestId = parseInt(data.id);
      if (this.requestId) {
        this.getRequestDetails();
      }
    }
  },
  computed:{
    imageSrc(){
      return this.event?.request_body?.image_path ? this.s3BucketURL + this.event?.request_body?.image_path : require('@/assets/images/default_images/event_default.jpg');
    },
    getStatusText(){
      if(this.event) {
        if (this.event.status_id === 33) {
          return "approved";
        } else if (this.event.status_id === 11) {
          return "pending";
        }else if(this.event.status_id === 31){
          return "amendment";
        }else if(this.event.status_id === 32){
          return "rejected";
        }else if(this.event.status_id === 34){
          return "draft";
        }else if(this.event.status_id === 35) {
          return "cancellation requested";
        }else if(this.event.status_id === 13){
          return "cancelled";
        }
      }
      return "";
    },
  },
  methods: {
    goBack(){
      this.$router.push({name: "ServiceRequestLogs"});
    },
    getRequestDetails() {
      this.showLoader("Loading");
      this.$http
          .get("venues/service-request/logs/" + parseInt(this.requestId))
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              this.event = response.data.data;
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getScheduleDate(){
      const rb = this.event.request_body;
      if(rb && this.event.request_type_id === 3 && rb.event_schedules){
        const result = rb.event_schedules.reduce(
            (acc, schedule) => {
              if (!acc.smallestStartDate || schedule.start_date < acc.smallestStartDate) {
                acc.smallestStartDate = schedule.start_date;
              }
              if (!acc.largestEndDate || schedule.end_date > acc.largestEndDate) {
                acc.largestEndDate = schedule.end_date;
              }
              return acc;
            },
            { smallestStartDate: null, largestEndDate: null }
        );
        // Format dates to 'DD MMM'
        const formatDate = (dateString) => {
          const date = new Date(dateString);
          const options = { day: 'numeric', month: 'short', year: 'numeric' };
          return date.toLocaleDateString('en-GB', options);
        };
        let displayDate = "";
        if (result.smallestStartDate === result.largestEndDate) {
          displayDate = formatDate(result.smallestStartDate);
        } else {
          const start = formatDate(result.smallestStartDate);
          const end = formatDate(result.largestEndDate);
          displayDate = `${start} - ${end}`;
        }
        // Console Output
        return displayDate;
      }
      return "";
    },


  },
};
</script>

<style scoped>
.general-heading{
  background-color: rgb(240, 245, 249);
  margin-top: 5px;
  font-weight: 600;
  font-size: 16px;
  color: #000;
}
.status-badge {
  padding: 8px 8px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  display: block;
  text-transform: capitalize;
  border-radius: 5px;
  font-weight: 600;
}
.status-badge.approved{
  background: #B6F0B5;
  color: #113206;
}
.status-badge.pending{
  background: #FFCDB3;
  color: #471E08;
}
.status-badge.rejected{
  background: #FFBAB6;
  color: #4C1008;
}
.status-badge.amendment {
  background: dimgrey;
}
.status-badge.draft{
  background: #D2D2D2;
  color: #000000;
}
.status-badge.cancellation{
  background: pink;
  color: #000000;
}
.status-badge.cancelled{
  background: black;
  color: #fff;
}
.tab_active {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
}
</style>

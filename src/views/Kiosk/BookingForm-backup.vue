<template>
  <v-container>
    <v-card elevation="12">
      <v-card-title class="headline">
        Booking Form <v-spacer></v-spacer
        ><v-btn @click="goToKiosk()" text
          ><v-icon left>mdi-backburger</v-icon> Back</v-btn
        ></v-card-title
      >
      <v-card-text class="form_bg" v-if="homePage">
        <div class="pt-8 mb-8">
          <h3 class="font-weight-bold text-center pt-5">
            Enter you registered mobile number to book a slot!
          </h3>
          <v-row align="center" justify="center" class="mt-2">
            <v-col cols="12" xs="12" sm="4" md="4">
              <mobile-field
                v-model="bookingForm.mobile"
                label="Mobile No*"
                @validated="setCustomerData($event, 'existing')"
              ></mobile-field>

              <div
                class="v-messages theme--light error--text text-center"
                style="margin-top: -5px; margin-bottom: 10px"
                role="alert"
                v-if="notRegisteredMessage"
              >
                Entered mobile number is not registered with
                {{ $store.getters.venueInfo.name }}. Please verify your input or
                register as a new customer.
              </div>
            </v-col>
          </v-row>
          <v-row no-gutters style="margin-top: -20px">
            <v-spacer></v-spacer>
            <v-col xs="12" sm="2" md="2" class="text-center">
              <v-btn
                class="ma-2 white--text blue-color text-center"
                text
                dark
                :disabled="bookingDisableFlag"
                @click="submit('direct')"
                >Book Now</v-btn
              >
            </v-col>
            <v-spacer></v-spacer>
          </v-row>
        </div>

        <v-divider></v-divider>
        <div class="mt-4 mb-10">
          <h3 class="font-weight-bold text-center pt-5">
            Are you visiting first time or not registered your mobile number?
          </h3>
          <v-row no-gutters>
            <v-spacer></v-spacer>
            <v-col xs="12" sm="2" md="2" class="text-center">
              <v-btn
                class="ma-2 white--text blue-color"
                text
                @click="
                  (homePage = false), (userRegForm = true), (customer = {})
                "
                >Register Now</v-btn
              >
            </v-col>
            <v-spacer></v-spacer>
          </v-row>
        </div>
      </v-card-text>

      <v-form v-if="userRegForm" ref="form" v-model="valid">
        <v-card-text>
          <div class="form">
            <div class="titles">Customer Details</div>
            <v-divider></v-divider>
            <v-row>
              <v-col cols="12" sm="6" md="6">
                <input type="hidden" v-model="customer.id" />
                <v-text-field
                  v-model="customer.first_name"
                  outlined
                  background-color="#fff"
                  label="First Name*"
                  required
                  :rules="[(v) => !!v || 'First name is required']"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <input type="hidden" v-model="customer.id" />
                <v-text-field
                  v-model="customer.last_name"
                  outlined
                  background-color="#fff"
                  label="Last Name"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <mobile-field
                  v-model="customer.mobile"
                  label="Mobile Number*"
                  @validated="setCustomerData($event, 'new')"
                ></mobile-field>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <v-text-field
                  v-model="customer.email"
                  label="Email*"
                  outlined
                  background-color="#fff"
                  required
                  :rules="[
                    (v) => !!v || 'Email is required',
                    (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                  ]"
                ></v-text-field>
              </v-col>
              <v-col md="6">
                <date-of-birth v-model="customer.dob"> </date-of-birth>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <v-autocomplete
                  v-model="customer.country_id"
                  :items="countries"
                  label="Nationality"
                  item-value="id"
                  item-text="name"
                  outlined
                  background-color="#fff"
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <v-select
                  v-model="customer.gender"
                  :items="['Male', 'Female']"
                  label="Gender"
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  required
                ></v-select>
              </v-col>
            </v-row>
          </div>
        </v-card-text>
      </v-form>

      <v-divider></v-divider>
      <v-card-actions v-if="!homePage">
        <v-spacer></v-spacer>
        <v-btn class="ma-2 white--text red-color" @click="reset()" text
          >Reset</v-btn
        >
        <v-btn class="ma-2 white--text blue-color" @click="submit()" text
          >Confirm booking</v-btn
        >
      </v-card-actions>
    </v-card>
  </v-container>
</template>

<script>
import constantValue from "@/utils/constants";

export default {
  components: {},
  data() {
    return {
      customer: {},
      constant: constantValue,
      venueServiceId: null,
      homePage: true,
      userRegForm: false,
      facilityBookingForm: false,
      notRegisteredMessage: false,
      bookingDisableFlag: true,
      bookingForm: {},
      facility: {
        open_days: [],
        facility_game_formations: [],
        product_categories: [],
        child_facilities: [],
      },
      valid: false,
      perCapacity: 0,
      loading: false,
    };
  },
  created() {},
  watch: {},
  mounted() {
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$route.params.data) {
      let data = JSON.parse(atob(this.$route.params.data));
      this.facility = data;
      console.log('hhhh',data);
      this.venueServiceId = data.venue_service_id;
      this.initializeFacilityServices();
    }
  },
  computed: {
    countries() {
      return this.$store.getters.getCountries.data;
    },
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
    venueSerives() {
      return this.$store.getters.getSportsService;
    },
    taxTypes() {
      return this.$store.getters.getTaxTypes.data;
    },
  },
  methods: {
    reset() {
      this.customer = {};
    },
    gameFormations() {
      return this.$store.getters.getGameFormationsByVenueServiceId(
        this.venueServiceId
      );
    },
    surfaces() {
      return this.$store.getters.getFacilitySurfaceByVenueServiceId(
        this.venueServiceId
      );
    },
    goToKiosk() {
      if (this.homePage == true)
        this.$router.push({ name: "KioskDashboard" }, () => {});
      if (this.userRegForm == true) {
        this.homePage = true;
        this.userRegForm = false;
      }
    },
    setCustomerData(mobile, type) {
      if (this.loading) return;
      this.showLoader("Checking");
      this.loading = true;
      this.$http
        .get(`venues/customers?mobile=${mobile}`)
        .then((response) => {
          if (response.status == 200) {
            // this.isSearchLoading = false;
            this.hideLoader();
            this.loading = false;
            let data = response.data.data;
            if (data.length == 0) {
              this.notRegisteredMessage = true;
            } else {
              this.notRegisteredMessage = false;
              this.bookingDisableFlag = false;
              this.customer = data[0];
              if (type == "new") {
                this.homePage = true;
                this.userRegForm = false;
                this.showError("Customer exist with same mobile number");
              }
              this.$forceUpdate();
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    initializeFacilityServices() {
      if (this.gameFormations().length == 0) {
        this.$store
          .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
          .then(() => {
            this.$nextTick(() => {
              this.$forceUpdate;
            });
          });
      }

      if (this.surfaces().length == 0) {
        this.$store
          .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
          .then(() => this.$forceUpdate);
      }
    },

    submit(type) {
      if (type != "direct" && !this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }

      this.showLoader();
      var formData = new FormData();

      for (let key in this.facility) {
        if (key != "facility_rentals") {
          formData.append(`${key}`, this.facility[key]);
        }
      }

      this.facility.facility_rentals.forEach((element, index) => {
        for (let key in element) {
          formData.append(`products[${index}][${key}]`, element[key]);
          formData.append(`products[${index}][rental]`, true);
        }
      });
      formData.append(`attendance_count`, 1);
          
      for (let key in this.customer) {
        if (this.customer[key] != null && key != "id") {
          formData.append(`${key}`, this.customer[key]);
        }
      }


      this.$http
        .post(`venues/facilities/bookings`, formData, {
          headers: {
            "Content-Type": "multipart/form-data; boundary=${form._boundary}",
          },
        })
        .then((response) => {
          this.hideLoader();
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Booking successfully added");
            this.$router.push({
              name: "KioskDashboard",
            });
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
  },
};
</script>

<style scoped></style>

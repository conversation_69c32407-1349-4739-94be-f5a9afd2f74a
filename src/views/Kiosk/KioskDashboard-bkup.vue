<template>
  <v-container>
    <h2 class="ml-3 blue-text">Services</h2>
    <v-row>
      <v-col md="3" v-for="service in services" :key="service.id">
        <v-card
          color="white"
          @click="gotoBookingForm(service), (facility = {})"
        >
          <div class="facility-icons">
            <view-image :image="service.image_path" class="box-image"></view-image>
          </div>

          <h3 class="text-center pb-3">{{ service.service_name }}</h3>
        </v-card>
      </v-col>
    </v-row>

    <v-dialog
      v-model="isCheckFacilityAvailablity"
      scrollable
      persistent
      width="70%"
    >
      <v-form ref="form" v-model="valid" lazy-validation>
        <v-card elevation="1" tile>
          <div class="titles pt-4">Facility Details</div>
          <v-card-text class="pb-0">
            <v-row>
              <v-col cols="12" sm="12" md="12">
                <v-row>
                  <!-- <v-col cols="12" sm="6" md="6" v-if="perCapacity == 1">
                    <v-select
                      :items="weekdays"
                      label="Opening Days*"
                      multiple
                      v-model="facility.open_days"
                      outlined
                      validate-on-blur
                      background-color="#fff"
                      item-value="bit_value"
                      item-text="name"
                      :rules="[(v) => v.length > 0 || 'Opening Days required']"
                    >
                    </v-select>
                  </v-col> -->

                  <!-- <v-col :md="perCapacity == 1 ? 3 : 6"> -->
                  <v-col :md="perCapacity == 0?4:6">
                    <v-select
                      v-model="facility.is_outdoor"
                      :items="[
                        { id: '1', name: 'Outdoor' },
                        { id: '0', name: 'Indoor' },
                      ]"
                      item-value="id"
                      item-text="name"
                      label="Outdoor/Indoor*"
                      required
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                      :rules="[(v) => !!v || 'Ground Type is required']"
                      @change="getAvailablity()"
                    ></v-select>
                  </v-col>
                  <v-col :md="perCapacity == 0?4:6">
                    <v-select
                      v-model="facility.facility_surface_id"
                      :items="surfaces()"
                      item-value="id"
                      item-text="name"
                      label="Surface*"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                    ></v-select>
                  </v-col>
                  <v-col md="4" v-if="perCapacity == 0">
                    <v-select
                      v-model="facility.facility_game_formation_id"
                      :items="gameFormations()"
                      item-value="id"
                      item-text="name"
                      label="Game Formation*"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                      @change="getAvailablity()"
                    ></v-select>
                  </v-col>
                  <v-col cols="12" sm="4" md="4">
                    <date-field
                      v-model="facility.date"
                      label="Date*"
                      :rules="[(v) => !!v || 'Date is required']"
                      @change="getStartTime(), getAvailablity()"
                      :backFill="
                        checkBackfillPermission($modules.kiosk.facility.slug)
                      "
                    >
                    </date-field>
                  </v-col>
                  <v-col cols="12" sm="4" md="4">
                    <v-select
                      v-model="facility.start_time"
                      :items="startTime"
                      label="Start Time*"
                      item-text="text"
                      item-value="value"
                      :rules="[(v) => !!v || 'Opening Time Required']"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                      @change="getAvailablity()"
                    ></v-select>
                  </v-col>
                  <v-col
                    cols="12"
                    sm="4"
                    md="4"
                    v-if="this.endTime.length != 0"
                  >
                    <v-select
                      v-model="facility.end_time"
                      :items="endTime"
                      item-text="formatted"
                      item-value="time"
                      label="End Time*"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                    ></v-select>
                  </v-col>
                  <v-col cols="12" sm="12" md="12" v-if="isTimeSlotAvailable">
                    <div
                      class="v-messages theme--light error--text text-center"
                      style="margin-top: -20px; margin-bottom: 10px"
                      role="alert"
                    >
                      No Facility Available for this time.
                    </div>
                  </v-col>
                </v-row>
              </v-col>
              <v-spacer></v-spacer>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              class="ma-2 white--text red-color"
              @click="isCheckFacilityAvailablity = false"
              text
              >Close</v-btn
            >
            <v-btn
              class="ma-2 white--text blue-color"
              @click="checkAvailablity()"
              text
              >Submit</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
  </v-container>
</template>
<script>
import constantValue from "@/utils/constants";
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      constant: constantValue,
      venueServiceId: null,
      isCheckFacilityAvailablity: false,
      isTimeSlotAvailable: false,
      valid: false,
      services: [],
      facility: {},
      perCapacity: 0,
      timeIncrement: 0,
      minBookingTime: 0,
      endTime: [],
      startTime: [],
    };
  },
  computed: {
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
  },
  mounted() {
    this.getServices();
  },
  methods: {
    async initializeFacilityServices() {
      if (this.gameFormations().length == 0) {
        this.showLoader("Loading");
        await this.$store
          .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
          .then(() => {
            this.$nextTick(() => {
              this.hideLoader();
              this.$forceUpdate;
            });
          });
      }
      this.isCheckFacilityAvailablity = true;
    },
    gameFormations() {
      return this.$store.getters.getGameFormationsByVenueServiceId(
        this.venueServiceId
      );
    },
    surfaces() {
      return this.$store.getters.getFacilitySurfaceByVenueServiceId(
        this.venueServiceId
      );
    },
    getServices() {
      this.$http
        .get("venues/kiosk")
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.services = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    gotoBookingForm(service) {
      this.venueServiceId = service.venue_service_id;
      this.perCapacity = service.per_capacity;
      this.timeIncrement = service.time_increment;
      this.minBookingTime = service.min_booking_time;
      this.getStartTime();
      this.initializeFacilityServices();
    },
    getStartTime() {
      if (
        moment(this.facility.date).format("Y-M-D") ==
        moment(new Date()).format("Y-M-D")
      ) {
        this.startTime = this.constant.TIMINGS.slice(
          new Date().getHours() + 1,
          24
        );
      } else {
        this.startTime = this.constant.TIMINGS.slice(0, 24);
      }
    },
    getAvailablity() {
      this.endTime = [];
      if (!this.facility.start_time) {
        return;
      }
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      this.isTimeSlotAvailable = false;
      let url = `?venue_service_id=${this.venueServiceId}&date=${this.facility.date}&start_time=${this.facility.start_time}&increment=${this.timeIncrement}&min_booking_time=${this.minBookingTime}&per_capacity=${this.perCapacity}&is_outdoor=${this.facility.is_outdoor}`;

      if (
        this.facility.facility_game_formation_id &&
        this.facility.facility_game_formation_id != null
      ) {
        url += `&game_formation_id=${this.facility.facility_game_formation_id}`;
      }
      if (
        this.facility.facility_surface_id &&
        this.facility.facility_surface_id != null
      ) {
        url += `&facility_surface_id=${this.facility.facility_surface_id}`;
      }
      this.showLoader("Checking Availablity.....");
      this.$http
        .get("venues/kiosk/facility-availability" + url)
        .then((response) => {
          if (response.data.status == true) {
            this.facility.venue_service_id = this.venueServiceId;
            this.facility.facility_id = response.data.data.id;
            this.facility.facility_rentals =
              response.data.data.facility_rentals;
            this.endTime = response.data.data.end_times;

            this.hideLoader();
          } else {
            // this.showError("No Facility Available for this time");
            this.isTimeSlotAvailable = true;
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    checkAvailablity() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (this.facility.facility_rentals.length > 0) {
        this.facility.facility_rentals[0].quantity = this.endTime.find(
          (y) => y.time == this.facility.end_time
        ).quantity;
      }

      this.$router.push({
        name: "BookingForm",
        params: {
          data: btoa(JSON.stringify(this.facility)),
        },
      });
    },
  },
};
</script>
<style>
.box-image > div.v-image__image{
  background-size: contain;
}
</style>
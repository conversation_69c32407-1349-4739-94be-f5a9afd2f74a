<template>
  <v-container>
    <v-card elevation="12">
      <div class="d-flex justify-space-around headline pa-4">
        <div class="pitch">Service: {{ service_name }}</div><v-spacer></v-spacer>
        <div class="pitch" v-if="perCapacity == 0">
          Price
          {{ bookingForm.total_price | toCurrency }}
          <span
            v-if="
              bookingForm.discount != null &&
                bookingForm.price != bookingForm.discount.actual_price
            "
            class="text-decoration-line-through"
          >
            {{ bookingForm.discount.actual_total | toCurrency }}</span
          >
        </div>
        <div class="pitch" v-else>
          Total {{ grandTotal() | toCurrency }}
        </div>
        <v-spacer></v-spacer>
      <v-btn @click="goToKiosk()" text class="white--text blue-color"><v-icon left>mdi-backburger</v-icon> Back</v-btn>
      </div>
      <v-form v-if="userRegForm" ref="form" v-model="valid">
        <v-card-text>
          <v-card
            color="#edf9ff"
            style="border: 1px #ccc solid"
            class="pa-4"
            outlined
          >
            <v-row>
              <v-col>
                <div class="titles">Booking Details</div>
              </v-col>
              <v-spacer></v-spacer>
            </v-row>
            <v-divider></v-divider>
            <v-row class="mt-0">
              <v-col cols="12" sm="6" md="3">
              <v-select
                  v-model="facility.id"
                  :items="facility_list"
                  item-value="id"
                  item-text="name"
                  label="Facilities*"
                  required
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  :rules="[(v) => !!v || 'Facility is required']"
                  @change="getFacilityUtils()"
                ></v-select>
              </v-col>
              <v-col sm="6" md="3">
                <date-field
                  label="Booking date"
                  outlined
                  background-color="#fff"
                  readonly
                  :value="bookingForm.date"
                  :dayName="true"
                  disabled
                >
                </date-field>
              </v-col>
              <v-col sm="6" md="3">
                <v-select
                  v-model="bookingForm.start_time"
                  :items="startTimes"
                  label="Start Time*"
                  item-text="text"
                  item-value="value"
                  :rules="[(v) => !!v || 'Opening Time Required']"
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  :value="timeFormat(bookingForm.start_time)"
                  @change="getFacilityUtils()"
                ></v-select>
                <!-- <v-text-field
                  label="Start Time"
                  hint="Start Time"
                  outlined
                  background-color="#fff"
                  readonly
                  :value="timeFormat(bookingForm.start_time)"
                >
                </v-text-field> -->
              </v-col>
              <v-col sm="6" md="3" >
                <v-select
                  v-if="endTimes.length != 0"
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  label="End time"
                  background-color="#fff"
                  :items="endTimes"
                  item-text="formatted"
                  item-value="time"
                  readonly
                  @change="getRentalProducts"
                  v-model="bookingForm.end_time"
                  :rules="[(v) => !!v || 'End time is required']"
                >
                </v-select>
              </v-col>
            </v-row>
          </v-card>
          <ProductSelection
            v-if="bookingForm.end_time && bookingForm.facility_id"
            :key="refreshComponent"
            :products="bookingForm.products"
            :categories="categories"
            :categoriesList="categoriesList"
            :taxTypes="taxTypes"
            :productCatId="productCategoryId"
            :venueServiceId="venueServiceId"
            :productCombinations="productCombinations"
            :deletedProducts="deletedProducts"
            :currentOrderProducts="currentOrderProducts"
            :repeatId="repeatId"
            @chooseRentalCombination="chooseRentalCombination"
            @deleteProduct="deleteProduct"
            @setCurrentOrderProducts="setCurrentOrderProducts"
            @removeProduct="
              (productIndex) => removeProduct(productIndex)
            "
            @setCustomerProduct="
              (data) => setCustomerProduct(null, data)
            "
          />
          <v-card
            color="#edf9ff"
            style="border: 1px #ccc solid"
            class="pa-4"
            outlined
            v-if="bookingForm.end_time && bookingForm.facility_id && bookingForm.products && bookingForm.products.length > 0"
          >
            <v-row>
              <v-col>
                <div class="titles">Customer Details</div>
              </v-col>
              <v-spacer></v-spacer>
            </v-row>
            <v-divider></v-divider>
            <v-row class="mt-0">
              <v-col cols="12" sm="4" md="4">
                <mobile-field
                  v-model="bookingForm.mobile"
                  label="Mobile Number*"
                  @validated="setCustomerData($event)"
                ></mobile-field>
              </v-col>
              <v-col cols="12" sm="4" md="4">
                <v-text-field
                  v-model="bookingForm.name"
                  outlined
                  background-color="#fff"
                  label="Name*"
                  :readonly="bookingForm.customer_id > 0"
                  required
                  :rules="[(v) => !!v || 'Name is required']"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="4" md="4">
                <v-text-field
                  v-model="bookingForm.email"
                  label="Email*"
                  outlined
                  background-color="#fff"
                  :readonly="bookingForm.customer_id > 0"
                  required
                  :rules="[
                    (v) => !!v || 'Email is required',
                    (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                  ]"
                ></v-text-field>
              </v-col>
              <v-card
                v-for="(addOns, index) in bookingCustomersAddons"
                :key="index"
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-5 mt-5 mb-6"
                outlined
              >
              <v-spacer></v-spacer>
              <v-row class="mt-0">
                <v-col cols="12" sm="4" md="4">
                  <mobile-field
                    v-model="bookingForm.mobile"
                    label="Mobile Number*"
                    @validated="setCustomerData($event)"
                  ></mobile-field>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <v-text-field
                    v-model="bookingForm.name"
                    outlined
                    background-color="#fff"
                    label="Name*"
                    :readonly="bookingForm.customer_id > 0"
                    required
                    :rules="[(v) => !!v || 'Name is required']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <v-text-field
                    v-model="bookingForm.email"
                    label="Email*"
                    outlined
                    background-color="#fff"
                    :readonly="bookingForm.customer_id > 0"
                    required
                    :rules="[
                      (v) => !!v || 'Email is required',
                      (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                    ]"
                  ></v-text-field>
                </v-col>
              </v-row>
              
              <!-- <customer-booking-form
                :addOn="1"
                :perCapacity="perCapacity"
                :promotions="promotions"
                :venue_service_id="venue_service_id"
                :singleBookingForm="addOns"
                :id="id"
                :order_id="order_id"
                :countries="countries"
                :companies="companies"
                :idProofTypes="idProofTypes"
                :disablePromotion="disablePromotion"
                @removeCustomerAdd="
                  (data) => removeAddonCustomer(index, null)
                "
                @setCustomerData="
                  (data) => setCustomerDataAddon(data, index)
                "
                @customerTypeChange="customerTypeChange($event, null)"
              /> -->
          </v-card>
                
            </v-row>
          </v-card>
        </v-card-text>
      </v-form>

      <v-divider></v-divider>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn class="ma-2 white--text red-color" @click="goToKiosk()" text
          >Close</v-btn
        >
        <v-btn class="ma-2 white--text blue-color" @click="submit()" text
          >Confirm booking</v-btn
        >
      </v-card-actions>
    </v-card>
  </v-container>
</template>

<script>
import constantValue from "@/utils/constants";
import moment from "moment";
import ProductSelection from "@/components/Schedule/Facility/ProductSelection";

export default {
  components: {ProductSelection},
  data() {
    return {
      id: 0,
      refreshComponent: 0,
      categories: [],
      categoriesList: [
        { name: "All", id: null },
      ],
      productCategoryId: null,
      productCombinations: [],
      deletedProducts: [],
      currentOrderProducts: [],
      repeatId: null,
      customer: {},
      constant: constantValue,
      venueServiceId: null,
      homePage: true,
      userRegForm: true,
      bookingForm: {
        date: moment(new Date()).format("Y-M-D"),
        start_time: "", end_time: "",
        attendance: false,
        attendance_count: 1,
        opt_marketing: false,
        total_price: 0,
        price: 0,
        discount: null,
        promotion_code: null,
      },
      bookingFormTotal: 0,
      bookingFormAdded: 1,
      bookingCustomersAddons: [],
      facility: {
        id: null,
        name: "",
        open_days: [],
        facility_game_formations: [],
        product_categories: [],
        child_facilities: [],
      },
      pastTime: false,
      bookingWithOpenProduct: false,
      facility_id : "",
      service_name: "",
      valid: false,
      totalParticipants: 0,
      bookedCapacity: 1,
      capacity: 0,
      perCapacity: 0,
      attendanceCustomersTotal: [0],
      attendanceCustomerAdded: [1],
      attendanceCustomersAddons: [[]],
      attendanceSwitch: false,
      editFlag: false,
      timeIncrement: 0,
      minBookingTime: 0,
      loading: false,
      startTimes:[],
      endTimes: [],
      facility_list: [],
    };
  },
  created() {},
  watch: {},
  mounted() {
    // if (this.$store.getters.getCountries.status == false) {
    //   this.$store.dispatch("loadCountries");
    // }
    if (this.$route.params.data) {

      let service = JSON.parse(atob(this.$route.params.data));
      console.log('sss',service);
      // this.facility = service;
      this.service_name = service.service_name;
      this.venueServiceId = service.venue_service_id;
      this.perCapacity = service.per_capacity;
      this.timeIncrement = service.time_increment;
      this.minBookingTime = service.min_booking_time;
      this.bookingForm.venue_service_id = service.venue_service_id;
      this.bookingForm.per_capacity = service.per_capacity;
      this.initializeFacilityServices();
      this.getStartTime();
    }
    console.log('bbbbbb',this.$route.params.data);
  },
  computed: {
    countries() {
      return this.$store.getters.getCountries.data;
    },
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
    venueSerives() {
      return this.$store.getters.getSportsService;
    },
    taxTypes() {
      return this.$store.getters.getTaxTypes.data;
    },
  },
  methods: {
    close() {
      
    },
    gameFormations() {
      return this.$store.getters.getGameFormationsByVenueServiceId(
        this.venueServiceId
      );
    },
    surfaces() {
      return this.$store.getters.getFacilitySurfaceByVenueServiceId(
        this.venueServiceId
      );
    },
    goToKiosk() {
      if (this.homePage == true)
        this.$router.push({ name: "KioskDashboard" }, () => {});
      if (this.userRegForm == true) {
        this.homePage = true;
        this.userRegForm = false;
      }
    },
    setCustomerData(mobile) {
      if (this.loading) return;
      this.showLoader("Checking");
      this.loading = true;
      this.$http
        .get(`venues/customers?mobile=${mobile}`)
        .then((response) => {
          if (response.status == 200) {
            // this.isSearchLoading = false;
            this.hideLoader();
            this.loading = false;
            let data = response.data.data;
            console.log(data);
            if (data.length) {
              this.$set(this.bookingForm, "email", data[0].email);
              this.$set(this.bookingForm, "customer_id", data[0].id);
              this.$set(this.bookingForm, "first_name", data[0].first_name);
              this.$set(this.bookingForm, "last_name", data[0].last_name);
              this.$set(this.bookingForm, "name", data[0].name);
              this.$set(this.bookingForm, "customer_type", "normal");
            } else {
              this.$set(this.bookingForm, "email", "");
              this.$set(this.bookingForm, "customer_id", "");
              this.$set(this.bookingForm, "first_name", "");
              this.$set(this.bookingForm, "last_name", "");
              this.$set(this.bookingForm, "name", "");
              this.$set(this.bookingForm, "customer_type", "");
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    initializeFacilityServices() {
      if (this.gameFormations().length == 0) {
        this.$store
          .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
          .then((response) => {
            let data_facility = response.data.data.facilities;
            this.facility_list = data_facility;
            this.$nextTick(() => {
              this.$forceUpdate;
            });
          });
      }

      if (this.surfaces().length == 0) {
        this.$store
          .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
          .then(() => this.$forceUpdate);
      }
    },

    submit(type) {
      if (type != "direct" && !this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (! this.bookingForm.facility_id || !this.bookingForm.venue_service_id) {
        this.showError("Please select facility");
        return;
      }
      if (!this.bookingForm.first_name && this.bookingForm.name) { 
        let name = this.bookingForm.name.replace(/\s\s+/g, " ");
        name = name.trim();
        let index = name.indexOf(" ");
        this.bookingForm.first_name = name;
        if (index != -1) {
          this.bookingForm.first_name = name.substr(0, index).trim();
          this.bookingForm.last_name = name.substr(index + 1).trim();
        } else {
          this.bookingForm.last_name = "";
        }
      }
      if (this.perCapacity == 1) {
        console.log(this.bookingForm.products);
        let count = this.bookingFormTotal;
        if (this.capacity < count) {
          this.showError("Trying to book more than available capacity");
          return;
        }
        console.log("count");
        console.log(count);
        console.log("this.capacity");
        console.log(this.capacity);
        // return;
      }
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields to continue");
        return;
      }
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add atleast one product");
        return;
      }
      this.showLoader();
      var formData = new FormData();
      for (let key in this.bookingForm) {
        if (
          this.bookingForm[key] != null &&
          !Array.isArray(this.bookingForm[key]) &&
          typeof this.bookingForm[key] != "object"
        ) {
          formData.append(`${key}`, this.bookingForm[key]);
        } else if (Array.isArray(this.bookingForm[key])) {
          this.bookingForm[key].forEach((data, index) => {
            if (!Array.isArray(data)) {
              for (let innerKey in data) {
                if (Array.isArray(data[innerKey])) {
                  if (
                    innerKey != "start_times" &&
                    innerKey != "end_times" &&
                    innerKey != "times"
                  ) {
                    data[innerKey].forEach((deepData, deepIndex) => {
                      formData.append(
                        `${key}[${index}][${innerKey}][${deepIndex}]`,
                        typeof deepData == "object"
                          ? JSON.stringify(deepData)
                          : deepData
                      );
                    });
                  }
                } else {
                  formData.append(
                    `${key}[${index}][${innerKey}]`,
                    data[innerKey]
                  );
                }
              }
            } else if (Array.isArray(data)) {
              data.forEach((innerData, innerIndex) => {
                formData.append(`${key}[${index}][${innerIndex}]`, innerData);
              });
            } else {
              formData.append(
                `${key}[${index}]`,
                typeof data == "object" ? JSON.stringify(data) : data
              );
            }
          });
        }
      }

      formData.append(`attendance_count`, 1);
      formData.append(`customer_type`, 'normal');
       
          
      for (let key in this.customer) {
        if (this.customer[key] != null && key != "id") {
          formData.append(`${key}`, this.customer[key]);
        }
      }
      this.$http
        .post(
          `venues/facilities/bookings`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data; boundary=${form._boundary}",
            },
          }
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            let data = response.data.data;
            this.hideLoader();
            this.$emit("booked", data.order_id);
            this.bookingFormTotal = 0;
            this.showSuccess("Booking added successfully");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    timeFormat(time) {
      return moment(time, "HH:mm:ss").format("hh:mm a");
    },
    getStartTime() {
      if (
        moment(this.bookingForm.date).format("Y-M-D") ==
        moment(new Date()).format("Y-M-D")
      ) {
        this.startTimes = this.constant.TIMINGS.slice(
          new Date().getHours() + 1,
          24
        );
      } else {
        this.startTimes = this.constant.TIMINGS.slice(0, 24);
      }
    },
    getAvailablity() {
      this.endTimes = [];
      if (!this.bookingForm.start_time) {
        return;
      }
      if (this.bookingForm.date && this.bookingForm.start_time && this.facility.id) {
        this.isTimeSlotAvailable = false;
        let url = `?venue_service_id=${this.venueServiceId}&date=${this.bookingForm.date}&start_time=${this.bookingForm.start_time}&increment=${this.timeIncrement}&min_booking_time=${this.minBookingTime}&per_capacity=${this.perCapacity}`;

        if (
          this.facility.facility_game_formation_id &&
          this.facility.facility_game_formation_id != null
        ) {
          url += `&game_formation_id=${this.facility.facility_game_formation_id}`;
        }
        if (
          this.facility.facility_surface_id &&
          this.facility.facility_surface_id != null
        ) {
          url += `&facility_surface_id=${this.facility.facility_surface_id}`;
        }
        if (
          this.facility.id &&
          this.facility.id != null
        ) {
          url += `&facility_id=${this.facility.id}`;
        }
        this.showLoader("Checking Availablity");
        this.$http
          .get("venues/kiosk/facility-availability" + url)
          .then((response) => {
            if (response.data.status == true) {
                  this.hideLoader();
          
              this.facility.venue_service_id = this.venueServiceId;
              this.facility.id = response.data.data.id;
              this.bookingForm.facility_id = response.data.data.id;
              this.facility.facility_rentals =
                response.data.data.facility_rentals;
              this.endTimes = response.data.data.end_times;
              this.bookingForm.end_time = this.endTimes[0].time;
              this.bookingForm.quantity = this.endTimes[0].quantity
              console.log(this.endTimes);
              this.$forceUpdate();

            } else {
              this.showError("Facility not available for this time");
              this.facility.id = null;
              this.bookingForm.facility_id = null;
              this.bookingForm.end_time = null;
              this.isTimeSlotAvailable = true;
              this.$forceUpdate();
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      } else {
        this.showError("Please fill facility, date and start time");
        return;  
      }
     
    },
    checkAvailablity() {
      if (this.facility.facility_rentals.length > 0) {
        this.facility.facility_rentals[0].quantity = this.endTime.find(
          (y) => y.time == this.facility.end_time
        ).quantity;
      }
    },
    getRentalProducts() {
      if (this.bookingForm.date && this.bookingForm.start_time && this.bookingForm.end_time && this.facility.id) {
        this.$http
          .get(
            `venues/facilities/bookings/rentals?start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&facility_id=${this.facility.id}&date=${this.date}`
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              this.productCombinations = data;
              if (data.length) {
                this.changeRentalProducts(data[0]);
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      }
    },
    grandTotal() {
      var totalPrice = 0;
      if (this.bookingForm.products) {
        totalPrice = this.bookingForm.products.reduce(
          (a, b) => a + parseFloat(b.total_price),
          0
        );
      }
      if (this.attendanceCustomers && this.attendanceCustomers.length) {
        this.attendanceCustomers.map((item) => {
          if (item.products) {
            if (item.products.length) {
              totalPrice += item.products.reduce(
                (a, b) => a + parseFloat(b.total_price),
                0
              );
            }
          }
        });
      }
      return totalPrice;
    },
    chooseRentalCombination() {
      this.showCombinationDialog = true;
    },
    setCurrentOrderProducts(data) {
      this.currentOrderProducts = data;
      this.$forceUpdate();
    },
    removeProduct(pIndex, index = null) {
      if (index === null) {
        let data = this.bookingForm.products[pIndex];
        if (this.bookingForm.products[pIndex].inventory_enable) {
          if (data.order_item_id) {
            this.deletedProducts.push(data.order_item_id);
          }
          let products = this.categories.find(
            (item) => item.id == data.category_id
          ).products;
          if (products) {
            products.forEach((el) => {
              if (el.id == data.product_id) {
                el.sales -= data.quantity;
              }
            });
          }
        }
        this.bookingForm.products.splice(pIndex, 1);
        if (this.bookingForm.repeats && this.bookingForm.repeats.length > 0) {
          this.bookingForm.repeats.forEach((repeat) => {
            let findIndex = repeat.products.findIndex(
              (x) => x.id == data.product_id
            );
            if (findIndex != -1) {
              repeat.products.splice(pIndex, 1);
            }
          });
        }

        if (data.rental == false && this.repeatId) {
          let findIndex = this.currentOrderProducts.findIndex(
            (x) => x.product_id == data.product_id
          );
          if (findIndex != null) {
            this.currentOrderProducts.splice(findIndex, 1);
          }
        }
        this.$forceUpdate();
        this.bookingForm.total_price = this.bookingForm.products.reduce(
          (a, b) => a + parseFloat(b.total_price),
          0
        );
        if (this.bookingForm.promotion_code != null) {
          this.verifyBenefit("promotion");
        }
        if (this.bookingForm.card_number != null) {
          this.verifyBenefit("membership");
        }
        this.refreshComponent++;
        this.updateBookingFormQuantity();
        this.$forceUpdate();
      } else {
        let data = this.attendanceCustomers[index].products[pIndex];
        if (this.attendanceCustomers[index].products[pIndex].inventory_enable) {
          if (data.order_item_id) {
            this.deletedProducts.push(data.order_item_id);
          }
          let products = this.categories.find(
            (item) => item.id == data.category_id
          ).products;
          if (products) {
            products.forEach((el) => {
              if (el.id == data.product_id) {
                el.sales -= data.quantity;
              }
            });
          }
        }
        this.attendanceCustomers[index].products.splice(pIndex, 1);
        if (this.bookingForm.repeats && this.bookingForm.repeats.length > 0) {
          this.bookingForm.repeats.forEach((repeat) => {
            let findIndex = repeat.products.findIndex(
              (x) => x.id == data.product_id
            );
            if (findIndex != -1) {
              repeat.products.splice(pIndex, 1);
            }
          });
        }

        if (data.rental == false && this.repeatId) {
          let findIndex = this.currentOrderProducts.findIndex(
            (x) => x.product_id == data.product_id
          );
          if (findIndex != null) {
            this.currentOrderProducts.splice(findIndex, 1);
          }
        }

        this.attendanceCustomers[index].total_price = this.attendanceCustomers[
          index
        ].products.reduce((a, b) => a + parseFloat(b.total_price), 0);
        if (this.attendanceCustomers[index].promotion_code != null) {
          this.verifyBenefit("promotion", index);
        }
        if (this.attendanceCustomers[index].card_number != null) {
          this.verifyBenefit("membership", index);
        }
        this.updateAttendanceFormQuantity(index);
        this.refreshCustomersAttendance(index);
      }

      this.$forceUpdate();
    },
    setCustomerProduct(index = null, products) {
      if (index === null) {
        this.bookingForm.products = products;
        if (this.bookingForm.promotion_code != null) {
          this.verifyBenefit("promotion");
        }
        if (this.bookingForm.card_number != null) {
          this.verifyBenefit("membership");
        }

        this.bookingForm.total_price = products.reduce(
          (a, b) => a + parseFloat(b.total_price),
          0
        );
        this.refreshComponent++;
        this.updateBookingFormQuantity();
      } else {
        this.attendanceCustomers[index].products = products;
        if (this.attendanceCustomers[index].promotion_code != null) {
          this.verifyBenefit("promotion", index);
        }
        if (this.attendanceCustomers[index].card_number != null) {
          this.verifyBenefit("membership", index);
        }

        this.attendanceCustomers[index].total_price = products.reduce(
          (a, b) => a + parseFloat(b.total_price),
          0
        );
        this.updateAttendanceFormQuantity(index);
      }

      this.$forceUpdate();
    },
    deleteProduct(data) {
      this.deletedProducts = data;
    },
    getFacilityUtils() {
      if (!this.bookingForm.start_time) {
        return;
      }
      
      this.endTimes = [];
      if (this.bookingForm.date && this.bookingForm.start_time && this.facility.id) {
        this.showLoader("Checking Availability");
        this.$http
          .get(
            `venues/facilities/bookings/utils?facility_id=${this.facility.id}&date=${this.bookingForm.date}&start_time=${this.bookingForm.start_time}&increment=${this.timeIncrement}&min_booking_time=${this.minBookingTime}&booking_id=${this.id}&per_capacity=${this.perCapacity}&venue_service_id=${this.venueServiceId}`
          )
          .then((response) => {
            this.hideLoader();
            if (response.status == 200 && response.data.status == true) {
              let data = response.data.data;
              if (data.length == 0) {
                this.showError(response.data.message);
                this.resetBookingForm();
                return;
              }
              this.bookingWithOpenProduct = false;
              this.capacity = 0;
              if (
                data.is_enable_per_day_capacity &&
                data.capacity &&
                data.total_attendance
              ) {
                const remainingCapacity = data.capacity - data.total_attendance;
                this.capacity = remainingCapacity >= 1 ? remainingCapacity : 0;
              } else {
                this.capacity = data.capacity;
              }

              this.productCombinations = [];
              this.endTimes = data.end_times;
              this.bookingForm.start_time = data.start_time;
              this.bookingForm.end_time = data.end_time;
              this.bookingForm.facility_id = this.facility.id;
              let start_time = moment(this.start_time, "HH:mm:ss").format(
                "HH:mm:ss"
              );
              let currentTime = moment(new Date()).format("HH:mm:ss");

              if (
                moment(start_time, "HH:mm:ss").isBefore(
                  moment(currentTime, "HH:mm:ss")
                )
              ) {
                this.pastTime = true;
              } else {
                this.pastTime = false;
              }
              this.categories = data.categories;
              this.categoriesList = [
                { name: "All", id: null },
                // { name: "Open Product", id: -1 },
                ...this.categories,
              ];
              if (
                data.facility_rentals.length > 0 &&
                this.id == 0 &&
                this.perCapacity == 0
              ) {
                let rentalProducts = data.default_products;
                this.bookingForm.price = 0;
                this.bookingForm.total_price = 0;
                this.bookingForm.products = [];
                rentalProducts.forEach((rentalProduct) => {
                  this.bookingForm.price += rentalProduct.price;
                  this.bookingForm.total_price +=
                    rentalProduct.price +
                    (rentalProduct.quantity >= 1
                      ? rentalProduct.tax_amount *
                      parseFloat(rentalProduct.quantity)
                      : rentalProduct.tax_amount);
                  this.bookingForm.products.push({
                    product_id: rentalProduct.id,
                    product_type_id: rentalProduct.product_type_id,
                    price: rentalProduct.price,
                    name: rentalProduct.name,
                    tax: rentalProduct.tax_amount,
                    category_id: rentalProduct.category_id,
                    rental: true,
                    product_price: rentalProduct.product_price_when_overlapping
                      ? rentalProduct.product_price_when_overlapping
                      : rentalProduct.product_price,
                    quantity: rentalProduct.quantity,
                    total_price:
                      rentalProduct.price +
                      (rentalProduct.quantity >= 1
                        ? rentalProduct.tax_amount *
                        parseFloat(rentalProduct.quantity)
                        : rentalProduct.tax_amount),
                  });
                });
              } else {
                this.$http
                  .get(
                    `venues/facilities/bookings/participants?facility_id=${this.facility.id}&start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&date=${this.bookingForm.date}`
                  )
                  .then((participans_response) => {
                    this.hideLoader();
                    if (
                      participans_response.status == 200 &&
                      participans_response.data.status == true
                    ) {
                      const data_participans_response =
                        participans_response.data.data;
                      this.totalParticipants = data_participans_response.reduce(
                        (a, b) => a + parseFloat(b.attendance),
                        0
                      );
                      this.bookedCapacity += this.totalParticipants;
                      if (this.bookedCapacity >= this.capacity) {
                        this.attendanceSwitch = false;
                      } else {
                        this.attendanceSwitch = true;
                      }
                      this.editFlag = false;
                    }
                  });
                this.bookingForm.products = [];
                this.bookingForm.price = 0;
                this.bookingForm.total_price = 0;
              }
              if (this.perCapacity == 1) {
                if (data.facility_rentals[0]) {
                  this.categoriesList.push({
                    name: "Tickets",
                    id: data.facility_rentals[0].category_id,
                  });
                }
              }
              if (data.facility_rentals.length) {
                if (data.facility_rentals[0]) {
                  let rentalProducts = {
                    id: data.facility_rentals[0].category_id,
                    name: this.perCapacity == 1 ? "Tickets" : "Rentals",
                    products: [
                      ...data.facility_rentals.map((item) => {
                        item.id = item.product_id;
                        item.rental = true;
                        return item;
                      }),
                    ],
                  };
                  this.categories.push(rentalProducts);
                }
              }
              // this.bookingForm.opt_marketing = false;
              this.$refs.form.resetValidation();
              this.hideLoader();
              this.$forceUpdate();
            } else {
              this.showError("Facility not available for this time");
               this.resetBookingForm();
            }
          })
          .catch((error) => {
            console.log('ddddd');
            this.errorChecker(error);
            this.resetBookingForm();
          });
      }  else {
        this.showError("Please fill facility, date and start time");
        return;  
      }
    },
    updateBookingFormQuantity() {
      let old = this.bookingFormTotal;
      let max = this.bookingForm.products.reduce(
        (acc, num) =>
          acc +
          (num.rental == true
            ? parseInt(num.quantity) * parseInt(num.participant_count)
            : 0),
        0
      );
      console.log("max");
      console.log(max);
      if (old > max && max < this.bookingFormAdded) {
        this.bookingFormAdded = max + 1;
      }
      this.bookingFormTotal = max;
    },
    addBookingFormCustomer() {
      this.bookingCustomersAddons.push({
        attendance: false,
        attendance_count: 1,
        opt_marketing: false,
        total_price: 0,
        price: 0,
        discount: null,
        promotion_code: null,
      });
      this.bookingFormAdded++;
    },
    resetBookingForm() {
      this.facility.id = null;
      this.bookingForm.facility_id = null;
      this.bookingForm.end_time = null;
      this.isTimeSlotAvailable = true;
      this.refreshComponent++;
    }

  },
};
</script>


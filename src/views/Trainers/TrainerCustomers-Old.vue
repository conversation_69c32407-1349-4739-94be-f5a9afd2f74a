<template>
  <v-container fluid>
    <v-card flat rounded color="#3e4c55" height="200px" width="100%">
      <v-btn small @click="gotoTrainer" absolute right top>
        <v-icon small>mdi-backburger</v-icon>Back
      </v-btn>
      <v-hover v-slot:default="{ hover }" open-delay="200">
        <v-btn
            small
            absolute
            bottom
            right
            :dark="hover"
            @click="
            confirmDelete({
              id: trainerId,
              status_id: trainer.status_id,
              flag: true,
            })
          "
            :color="trainer.status_id == 1 ? 'error' : 'success'"
        >
          <v-icon small>
            {{ trainer.status_id == 1 ? "mdi-account-cancel" : "mdi-account" }}
          </v-icon>
          {{ trainer.status_id == 1 ? "Deactivate" : "Activate" }}
        </v-btn>
      </v-hover>
      <v-row>
        <v-spacer></v-spacer>
        <v-col md="2" class="ml-4">
          <view-image
              style="border: 2px #062b48 solid; border-radius: 5px"
              :image="trainer.profile_image ? trainer.profile_image : ''"
              defaultImage="user"
              :height="150"
              contain
          ></view-image>
        </v-col>
        <v-col md="6" class="d-flex align-center white--text">
          <v-row no-gutters>
            <v-col md="12">
              <div class="pl-6">
                <div class="display-2">
                  {{ trainer.first_name }} {{ trainer.last_name || "" }}
                </div>
                <div class="display-1">{{ trainer.title }}</div>
              </div>
            </v-col>
            <v-col class="pl-6" md="5">
              <div>
                <v-btn class="mt-2" small block color="#008483" dark>
                  Email:
                  <span style="text-transform: none">
                    {{ trainer.email }}
                  </span>
                </v-btn>
                <v-btn class="mt-2" small block color="#008483" dark
                >Phone:{{ trainer.mobile }}</v-btn
                >
              </div>
            </v-col>
            <v-col class="pl-6" md="5">
              <div>
                <v-btn class="mt-2" small block color="#008483" dark
                >Students:{{ Number(trainer.students) }}</v-btn
                >
                <v-btn class="mt-2" small block color="#008483" dark
                >Revenue:{{ Number(trainer.sales) | toCurrency }}</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-col>
        <v-spacer></v-spacer>
      </v-row>
    </v-card>
    <v-card
        :class="
        `pa-2 mt-6 ${trainer.training_services.length == 0 ? 'pb-6' : ''}`
      "
        v-if="trainer.venue_services && trainer.venue_services.length > 0"
    >
      <div class="header_title">
        <v-row no-gutters>
          <v-col md="3" class="pl-3">Packages</v-col>
          <v-spacer></v-spacer>
          <v-col md="2">
            <div
                style="float: right"
                v-if="
                checkWritePermission($modules.trainers.management.slug) &&
                  trainer.status_id == 1
              "
            >
              <v-btn
                  dark
                  v-if="trainer.training_services.length < 3"
                  color="#00b0af"
                  @click="addTrainerService"
              >
                <v-icon small>mdi-plus-circle</v-icon>
                <span class="pl-2">Package</span>
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </div>
      <v-row>
        <v-spacer></v-spacer>
        <v-col
            v-for="(trainerService, ts) in trainer.training_services"
            :key="`tr_pc_${ts}`"
            md="4"
        >
          <v-card flat tile color="transparent">
            <v-menu offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                    fab
                    dark
                    x-small
                    absolute
                    top
                    right
                    color="#00b0af"
                    style="margin-top: 45px; margin-right: -25px"
                    v-bind="attrs"
                    v-on="on"
                >
                  <v-icon small>mdi-dots-vertical</v-icon>
                </v-btn>
              </template>
              <v-list dense>
                <v-list-item @click="editPackageCategory(ts)">
                  <v-list-item-icon>
                    <v-icon small>mdi-pencil</v-icon>
                  </v-list-item-icon>
                  <v-list-item-title>Edit</v-list-item-title>
                </v-list-item>
                <v-list-item
                    v-if="trainer.training_services.length > 1"
                    @click="deleteTrainerService(ts, 1)"
                >
                  <v-list-item-icon>
                    <v-icon small>mdi-delete</v-icon>
                  </v-list-item-icon>
                  <v-list-item-title>Delete</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>

            <div class="titles text-center">
              <br v-if="trainerService.venue_service_id == null" />
              {{
                trainerService.venue_service_id != null
                    ? trainerService.name
                    : ""
              }}
            </div>
            <v-simple-table fixed-header height="250px">
              <template v-slot:default>
                <thead>
                <tr>
                  <th class="tableHeader">Package Name</th>
                  <th class="tableHeader">Session</th>
                  <th class="tableHeader">Duration</th>
                  <th class="tableHeader">Type</th>
                  <th class="tableHeader">Price (Inc.VAT)</th>
                </tr>
                </thead>
                <tbody>
                <tr
                    v-for="(trainerPackage,
                    tp) in trainerService.training_packages"
                    :key="tp"
                >
                  <td>{{ trainerPackage.name }}</td>
                  <td>{{ trainerPackage.sessions }}</td>
                  <td>{{ trainerPackage.duration }} mins</td>
                  <td>
                    {{
                      trainerPackage.package_type == "I"
                          ? "Individual"
                          : trainerPackage.package_type == "C"
                              ? "Couple"
                              : "Group"
                    }}
                  </td>
                  <td>{{ trainerPackage.total_price | toCurrency }}</td>
                </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-card>
        </v-col>
        <v-col
            v-for="trainerService in trainer.global_packages"
            :key="`g_${trainerService.id}`"
            md="4"
        >
          <div class="titles text-center">
            <br v-if="trainerService.venue_service_id == null" />
            {{
              trainerService.venue_service_id != null ? trainerService.name : ""
            }}
            (Global)
          </div>
          <v-simple-table fixed-header height="250px">
            <template v-slot:default>
              <thead>
              <tr>
                <th class="tableHeader">Package Name</th>
                <th class="tableHeader">Sessions</th>
                <th class="tableHeader">Duration</th>
                <th class="tableHeader">Type</th>
                <th class="tableHeader">Price (Inc.VAT)</th>
              </tr>
              </thead>
              <tbody>
              <tr
                  v-for="(trainerPackage,
                  tp) in trainerService.training_packages"
                  :key="tp"
              >
                <td>{{ trainerPackage.name }}</td>
                <td>{{ trainerPackage.sessions }}</td>
                <td>{{ trainerPackage.duration }} mins</td>
                <td>
                  {{
                    trainerPackage.package_type == "I"
                        ? "Individual"
                        : trainerPackage.package_type == "C"
                            ? "Couple"
                            : "Group"
                  }}
                  <span v-if="trainerPackage.package_type == 'G'"
                  ><br />({{ trainerPackage.people }})</span
                  >
                </td>
                <td>{{ trainerPackage.total_price | toCurrency }}</td>
              </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-col>
        <v-spacer></v-spacer>
      </v-row>
    </v-card>

    <v-row class="mt-5">
      <v-tabs
          v-model="tab"
          background-color="transparent"
          centered
          light
          slider-color="transparent"
          icons-and-text
          active-class="tab_active"
      >
        <v-tab href="#details" @click="showCalender = false">
          <span style="min-width: 170px">Students</span>
          <v-icon color="#066a8c">mdi-account-supervisor</v-icon>
        </v-tab>
        <v-tab href="#sales" @click="showCalender = false">
          <span style="min-width: 170px">Sales</span>
          <v-icon class="chart_ic" color="#066a8c"></v-icon>
        </v-tab>
        <v-tab href="#analysis" @click="showCalender = false">
          <span style="min-width: 170px">Customer Analysis</span>
          <v-icon color="#066a8c">mdi-chart-pie</v-icon>
        </v-tab>
        <v-tab href="#trainer_calender" @click="showCalender = true">
          <span style="min-width: 170px">Trainer Schedules</span>
          <v-icon color="#066a8c">mdi-calendar</v-icon>
        </v-tab>
        <v-tab
            v-if="checkReadPermission($modules.trainers.availability.slug)"
            href="#trainer_availability"
            @click="showCalender = false"
        >
          <span style="min-width: 170px">Availability</span>
          <v-icon color="#066a8c">mdi-archive-clock-outline</v-icon>
        </v-tab>
      </v-tabs>
    </v-row>
    <v-row no-gutters class="mt-6">
      <v-tabs-items v-model="tab">
        <v-tab-item value="details">
          <trainer-customer-section
              :trainerId="trainerId"
              :trainer="trainer"
              @reloadTrainer="trainerDetails(trainerId, 'view')"
              :trainerLevels="levels"
          />
        </v-tab-item>

        <v-tab-item value="sales">
          <trainer-sales-graph
              :trainerId="salesTrainerId"
              :exportPermission="
              checkExportPermission($modules.trainers.management.slug)
            "
          ></trainer-sales-graph>
        </v-tab-item>
        <v-tab-item value="analysis">
          <trainer-customer-chart
              :params="params"
              :showProductType="false"
              :exportPermission="
              checkExportPermission($modules.trainers.management.slug)
            "
              stackType="all"
              :reload="pieReload"
          ></trainer-customer-chart>
        </v-tab-item>
        <v-tab-item value="trainer_calender">
          <trainer-calender v-if="showCalender" :trainerId="trainerId">
          </trainer-calender>
        </v-tab-item>
        <v-tab-item value="trainer_availability">
          <trainer-availability
              :trainerId="trainerId"
              :trainer="trainer"
              :trainerLevels="levels"
          >
          </trainer-availability>
        </v-tab-item>
      </v-tabs-items>
    </v-row>

    <v-dialog
        v-model="trainerPackageDialog"
        @input="cancelPackageEdit"
        scrollable
        width="50%"
    >
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-title class="headline">Trainer Packages</v-card-title>
          <v-card-text class="form_bg">
            <v-container>
              <v-card
                  class="pt-3 pl-2 pr-2 mb-3"
                  elevation="0"
                  :style="cardStyle"
                  v-if="trainer.training_services[index] != null"
              >
                <v-row>
                  <v-col md="6">
                    <v-autocomplete
                        v-model="
                        trainer.training_services[index].venue_service_id
                      "
                        :items="venueServices"
                        clearable
                        label="Select Service*"
                        item-text="name"
                        item-value="venue_service_id"
                        outlined
                        background-color="#fff"
                        required
                        :rules="[(v) => !!v || 'Service is required']"
                    >
                    </v-autocomplete>
                  </v-col>
                  <v-col md="6">
                    <v-autocomplete
                        @change="taxChange(trainer.training_services[index])"
                        v-model="trainer.training_services[index].tax_type_id"
                        :items="taxTypes"
                        clearable
                        label="Tax type*"
                        item-text="text"
                        item-value="value"
                        outlined
                        background-color="#fff"
                        required
                        :rules="[(v) => !!v || 'Tax type is required']"
                    >
                    </v-autocomplete>
                  </v-col>
                </v-row>
                <v-card
                    v-for="(training_package, tp) in trainer.training_services[
                    index
                  ].training_packages"
                    :key="tp"
                    outlined
                    class="mb-7"
                >
                  <v-container>
                    <v-row>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn
                              v-if="
                              trainer.training_services[index].training_packages
                                .length > 1
                            "
                              absolute
                              top
                              right
                              v-bind="attrs"
                              v-on="on"
                              color="red"
                              class="mt-1"
                              @click="warningPackageRemove(index, tp)"
                              fab
                              x-small
                              dark
                          >
                            <v-icon>mdi-delete</v-icon>
                          </v-btn>
                        </template>
                        Delete
                      </v-tooltip>
                      <v-col md="4">
                        <v-text-field
                            label="Name*"
                            required
                            v-model="training_package.name"
                            outlined
                            background-color="#fff"
                            :rules="[(v) => !!v || 'Name is required']"
                        ></v-text-field>
                      </v-col>
                      <v-col md="4">
                        <v-select
                            label="Duration"
                            required
                            v-model="training_package.duration"
                            hint="Session duration (default 1 hr)"
                            :items="getMinBookingTimes(index)"
                            outlined
                            :menu-props="{ bottom: true, offsetY: true }"
                            background-color="#fff"
                        ></v-select>
                      </v-col>
                      <v-col md="4">
                        <v-text-field
                            label="Sessions*"
                            required
                            :rules="[
                            (v) => !!v || 'Session is required',
                            (v) => !isNaN(v) || 'Session must be Number',
                          ]"
                            v-model="training_package.sessions"
                            outlined
                            background-color="#fff"
                        ></v-text-field>
                      </v-col>
                      <v-col md="4">
                        <v-select
                            label="Type*"
                            required
                            v-model="training_package.package_type"
                            outlined
                            :menu-props="{ bottom: true, offsetY: true }"
                            background-color="#fff"
                            :items="packageTypes"
                            @change="setPackagePeople(index, tp)"
                            item-text="name"
                            item-value="type"
                            :rules="[(v) => !!v || 'Type is required']"
                        ></v-select>
                      </v-col>
                      <v-col md="4" v-if="training_package.package_type == 'G'">
                        <v-text-field
                            label="People*"
                            required
                            v-model="training_package.people"
                            :rules="[
                            (v) => !!v || 'No persons is required',
                            (v) => !isNaN(v) || 'Price must be Number',
                          ]"
                            outlined
                            background-color="#fff"
                        ></v-text-field>
                      </v-col>
                      <v-col md="4">
                        <v-text-field
                            label="Price (Pre Tax)*"
                            @change="
                            calculateTaxVariation(
                              $event,
                              trainer.training_services[index],
                              training_package,
                              'pre'
                            )
                          "
                            required
                            :rules="[
                            (v) => !!v || 'Price is required',
                            (v) => !isNaN(v) || 'Price must be Number',
                          ]"
                            v-model="training_package.pre_tax_price"
                            outlined
                            :prefix="currencyCode"
                            background-color="#fff"
                        ></v-text-field>
                      </v-col>
                      <v-col md="4">
                        <v-text-field
                            label="Price (Post Tax)*"
                            @change="
                            calculateTaxVariation(
                              $event,
                              trainer.training_services[index],
                              training_package,
                              'post'
                            )
                          "
                            required
                            :rules="[
                            (v) => !!v || 'Price is required',
                            (v) => !isNaN(v) || 'Price must be Number',
                          ]"
                            v-model="training_package.total_price"
                            outlined
                            :prefix="currencyCode"
                            background-color="#fff"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card>
                <div class="add_btn pb-4" style="margin-top: -30px">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                          v-bind="attrs"
                          v-on="on"
                          color="teal"
                          fab
                          x-small
                          dark
                          @click="addTrainerPackage(index)"
                      >
                        <v-icon small>mdi-plus-circle</v-icon>
                      </v-btn>
                    </template>
                    Add
                  </v-tooltip>
                </div>
              </v-card>
            </v-container>
          </v-card-text>

          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
                class="ma-2 white--text blue-color"
                @click="cancelPackageEdit"
            >Close</v-btn
            >

            <v-btn
                class="ma-2 white--text teal-color"
                @click="addOrEditTrainer($event, true)"
            >{{
                trainer.training_services[index] &&
                trainer.training_services[index].id != null
                    ? "Update"
                    : "Save"
              }}</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>

    <confirm-model
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="confirmModel.id = null"
    ></confirm-model>
  </v-container>
</template>

<script>
import TrainerCustomerSection from "./TrainerTabs/TrainerCustomerSection";
import TrainerSalesGraph from "./TrainerTabs/TrainerSalesGraph";
import TrainerCustomerChart from "@/components/Chart/CustomerPieCharts";
import trainer from "@/mixins/trainer";
import TrainerCalender from "./TrainerTabs/TrainerCalender.vue";
import TrainerAvailability from "./TrainerTabs/TrainerAvailability.vue";
// v-if="trainer.training_services[index] != null
export default {
  components: {
    TrainerSalesGraph,
    TrainerCustomerSection,
    TrainerCustomerChart,
    TrainerCalender,
    TrainerAvailability,
  },
  mixins: [trainer],
  data() {
    return {
      tab: "details",
      trainerId: null,
      image: null,
      trainer: {
        training_services: [{ venue_service_id: null }],
        deleted_packages: [],
        deleted_services: [],
      },
      trainerPackageDialog: false,
      valid: false,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      index: 0,
      params: { venue_service_ids: [], product_ids: [], product_type_ids: [5] },
      packageTypes: [
        { type: "I", name: "Individual" },
        { type: "C", name: "Couple" },
        { type: "G", name: "Group" },
      ],
      salesTrainerId: null,
      levels: [],
      pieReload: true,
      showCalender: false,
      // timeSlotList: [],
    };
  },
  created() {
    if (typeof this.$route.params.data != "undefined") {
      this.trainerId = parseInt(atob(this.$route.params.data));
      this.trainerDetails(this.trainerId, "view");
      // this.loadAvailability();
    }
  },
  watch: {
    tab: {
      immediate: true,
      handler(val) {
        if (val == "sales") {
          this.salesTrainerId = this.trainerId;
        } else {
          this.salesTrainerId = null;
        }
        if (val == "analysis") {
          this.pieReload = true;
        } else {
          this.pieReload = false;
        }
      },
    },
  },
  mounted() {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$store.getters.getTaxTypes.status == false) {
      this.$store.dispatch("loadTaxTypes");
    }
  },
  computed: {
    countries() {
      return this.$store.getters.getCountries.data;
    },
    venueServices() {
      return this.$store.getters.getVenueServices.data;
    },
    taxTypes() {
      return this.$store.getters.getTaxTypes.data;
    },
  },
  methods: {
    editPackageCategory(index) {
      this.index = index;
      this.trainerPackageDialog = true;
    },
    cancelPackageEdit() {
      this.trainerDetails(this.trainerId, "view");
      this.trainerPackageDialog = false;
    },
    gotoTrainer() {
      this.$router.push({
        name: "Trainers",
      });
    },
    getLevels() {
      let url = this.trainer.venue_service_ids.map(
          (id, index) => `&venue_service_ids[${index}]=${id}`
      );
      this.$http
          .get(`venues/general/color-codes/trainer?q=1&${url}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              if (data.length) {
                this.levels = [];
                this.levels.push({ divider: true });
                this.levels.push({ header: "General" });
                this.levels.push({ divider: true });
                this.levels.push({ id: "All", name: "All" });
                this.levels.push({
                  id: -1,
                  name: "Not Applied",
                  color_code: "#555",
                });
                let service = data[0].service;
                this.levels.push({ divider: true });
                this.levels.push({ header: service });
                this.levels.push({ divider: true });
                data.forEach((level) => {
                  if (level.service != service) {
                    this.levels.push({ divider: true });
                    this.levels.push({ header: level.service });
                    this.levels.push({ divider: true });
                    service = level.service;
                  }
                  this.levels.push(level);
                });
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getPackages() {
      this.$http
          .get(
              `venues/trainers/packages${
                  this.trainerId != null ? "?trainer_id=" + this.trainerId : ""
              }`
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              if (data.length) {
                let productIds = [];
                data.forEach((product) => {
                  productIds.push(product.product_id);
                });
                this.params.product_ids = productIds;
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getMinBookingTimes(index) {
      let duration = [];
      let venueServiceId = this.trainer.training_services[index]
          .venue_service_id;
      const venueService = this.venueServices.find(
          (item) => item.venue_service_id == venueServiceId
      );
      const timeIncrement = venueService ? venueService.time_increment : null;
      if (timeIncrement) {
        let hour = 0;
        let index = 0;
        while (hour < 10) {
          index = index + 1;
          let time = timeIncrement * index;
          hour = parseInt(time / 60);
          let min =
              time / 60 - hour > 0 ? Math.round((time / 60 - hour) * 60) : 0;
          let text = hour > 0 ? `${hour} Hour ` : "";
          text += min > 0 ? `${min} Min` : "";
          duration.push({ value: time, text: text });
        }
      }
      return duration;
    },

    calculateTaxVariation(amount, training_service, training_package, type) {
      let taxTypeId = training_service.tax_type_id;
      let taxPercentage = 0;
      if (taxTypeId) {
        taxPercentage = this.taxTypes.find((item) => item.value == taxTypeId)
            .percentage;
      }
      const priceData = this.getTaxVariation(type, taxPercentage, amount);
      training_package.price = priceData.price;
      training_package.pre_tax_price = priceData.price.toFixed(2);
      training_package.total_price = priceData.total_price;
      this.$forceUpdate();
    },

    taxChange(training_service) {
      training_service.training_packages.forEach((training_package) => {
        if (training_package.price) {
          this.calculateTaxVariation(
              training_package.price,
              training_service,
              training_package,
              "pre"
          );
        } else if (training_package.total_price) {
          this.calculateTaxVariation(
              training_package.total_price,
              training_service,
              training_package,
              "post"
          );
        }
      });
    },

    viewStudents(data) {
      console.log("vs", data);
    },
    // loadAvailability() {
    //   this.$http
    //     .get(
    //       `venues/trainers/availability${
    //         this.trainerId != null ? "/" + this.trainerId : ""
    //       }`
    //     )
    //     .then((response) => {
    //       if (response.status == 200) this.timeSlotList = response.data.data;
    //     })
    //     .catch((error) => {
    //       this.errorChecker(error);
    //     });
    // },
  },
};
</script>

<style scoped>
.tab_active {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
}

.flat-table {
  display: block;
  overflow: auto;
  width: auto;
  border-radius: 10px;
}
.flat-table th {
  background-color: rgb(4, 148, 184);
  color: white;
  font-weight: normal;
  padding: 8px 12px;
  text-align: center;
}
.flat-table td {
  background-color: #eeeeee;
  color: #6f6f6f;
  padding: 8px 12px;
}
</style>

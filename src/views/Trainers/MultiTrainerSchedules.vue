<template>
  <v-container fluid>
    <TrainerTopTabs
        :add-trainer="false"
        :venue-service-ids="venueServiceIds"
        :venue-services="venueServices"
        @changeVenueService="changeVenueService"
        @update:venueServiceIds="(value)=>venueServiceIds=value"
    />
    <v-divider class="mt-4" style="border-color: rgba(17, 42, 70, 0.14) !important;"/>
    <div class="md-card md-theme-default mt-8 shadow rounded-5 ts-new pa-0 calendar-wrapper">
      <div class="md-card-content">
        <v-row>
          <v-col sm="11" class="align-center">
            <div class="calender-navi">
              <v-row no-gutters>
                <v-col sm="1" class="text-lg-center mr-1">
                  <v-btn
                      fab
                      x-small
                      color="white"
                      @click="prevDate"
                  >
                    <v-icon dark>mdi-menu-left</v-icon>
                  </v-btn>
                </v-col>
                <v-col sm="8" class="text-lg-center">
                  <date-field
                      v-model="date"
                      :buttonAndText="true"
                      :dayName="true"
                      :back-fill="true"
                      @change="changeDate"
                  >
                  </date-field>
                </v-col>
                <v-col sm="1" class="text-lg-center ml-1">
                  <v-btn
                      fab
                      color="white"
                      x-small
                      @click="nextDate"
                  >
                    <v-icon dark>mdi-menu-right</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </v-col>
          <v-spacer></v-spacer>
          <v-col sm="1">
            <div class="mx-2">
              <v-hover v-slot:default="{ hover }">
                <v-btn
                    style="height: 40px; border: 1px solid #dcdcdc !important"
                    :elevation="hover ? 2 : 0"
                    :color="hover ? 'teal' : 'white'"
                    @click="getTrainerSchedules"
                >
                  <v-icon>mdi-refresh</v-icon>
                </v-btn>
              </v-hover>
            </div>
          </v-col>
        </v-row>
<!--        <v-row no-gutters>-->
<!--          &lt;!&ndash; Time Column &ndash;&gt;-->
<!--          <v-col cols="1" class="time-column">-->
<!--            <div class="trainer-header">Booking Slots</div>-->
<!--            <div-->
<!--                class="time-label"-->
<!--                v-for="(time,index) in timeSlots"-->
<!--                :key="index"-->
<!--                :style="{ height: slotHeight + 'px' }"-->
<!--            >-->
<!--              {{ time.formated_start_time }} - {{ time.formated_end_time }}-->
<!--            </div>-->
<!--          </v-col>-->
<!--          &lt;!&ndash; Trainer Columns &ndash;&gt;-->
<!--          <v-col-->
<!--              v-for="trainer in trainersData"-->
<!--              :key="trainer.id"-->
<!--              class="trainer-column"-->
<!--              cols="1"-->
<!--          >-->
<!--            <div class="trainer-header">{{ trainer.name }}</div>-->
<!--            <div class="trainer-schedule" :style="{ height: totalHeight + 'px' }">-->
<!--              &lt;!&ndash; All Time Slots &ndash;&gt;-->
<!--              <div-->
<!--                  v-for="(time, index) in timeSlots"-->
<!--                  :key="`slot_${trainer.id}_${index}`"-->
<!--                  class="slot"-->
<!--                  :style="{ height: slotHeight + 'px' }"-->
<!--                  :class="{ 'unavailable': !isSlotAvailable(trainer, time) }"-->
<!--                  @click.stop="openActionPopup(trainer, time)"-->
<!--              >-->
<!--                <div v-if="isSlotAvailable(trainer,time)" class="slot-plus" @click.stop="openActionPopup(trainer, time)">-->
<!--                  <v-icon small color="grey lighten-1">mdi-plus</v-icon>-->
<!--                </div>-->
<!--              </div>-->
<!--              <div v-for="(sch, idx) in trainer.schedules" :key="`ts_${idx}`" class="booking" :style="getBookingStyle(sch, idx)" @click.stop="handleProgramClick(trainer,sch)">-->
<!--                {{ sch.program_name }}-->
<!--              </div>-->
<!--            </div>-->
<!--          </v-col>-->
<!--        </v-row>-->
        <div class="trainer-scroll-wrapper">
          <div class="trainer-row-no-wrap">

            <!-- Time Column -->
            <div class="time-column">
              <div class="trainer-header">Booking Slots</div>
              <div
                  class="time-label"
                  v-for="(time,index) in timeSlots"
                  :key="index"
                  :style="{ height: slotHeight + 'px' }"
              >
                {{ time.formated_start_time }} - {{ time.formated_end_time }}
              </div>
            </div>

            <!-- Trainer Columns -->
            <div
                class="trainer-column"
                v-for="trainer in trainersData"
                :key="trainer.id"
            >
              <div class="trainer-header">{{ trainer.name }}</div>
              <div class="trainer-schedule" :style="{ height: totalHeight + 'px' }">
                <div
                    v-for="(time, index) in timeSlots"
                    :key="`slot_${trainer.id}_${index}`"
                    class="slot"
                    :style="{ height: slotHeight + 'px' }"
                    :class="{ 'unavailable': !isSlotAvailable(trainer, time) }"
                    @click.stop="openActionPopup(trainer, time)"
                >
                  <div v-if="isSlotAvailable(trainer, time)" class="slot-plus" @click.stop="openActionPopup(trainer, time)">
                    <v-icon small color="grey lighten-1">mdi-plus</v-icon>
                  </div>
                </div>
                <div
                    v-for="(sch, idx) in trainer.schedules"
                    :key="`ts_${idx}`"
                    class="booking"
                    :style="getBookingStyle(sch, idx)"
                    @click.stop="handleProgramClick(trainer, sch)"
                >
                  {{ sch.program_name }}
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
    <div class="mt-4">
      <TrainerScheduleLegends />
    </div>
    <TrainerCustomerTooltipDialog
        :selectedEvent="selectedEvent"
        :selectedOpen="selectedOpen"
        @close="closeTrainerCustomerTooltipDialog"
        @getTrainerSchedules="getTrainerSchedules"
    />
    <AddProgram
        v-if="selectedTrainer"
        :addProgramDialog="addProgramDialog"
        :trainer="selectedTrainer"
        @close="closeProgramDialoag"
        @programAdded="programAdded"
    />
    <BookWorkshopTrainerProgram
        :date="date"
        :addParticipantDialog="addParticipantDialog"
        :trainer="selectedTrainer"
        :workshopData="workshopData"
        @complete="customerschedulecompleted"
        @close="closeAddParticipantDialog"
    />
    <CustomerAttendanceModal
      v-if="selectedSlotData"
      :showParticipantDialog="showParticipantDialog"
      :trainer="selectedTrainer"
      :slotData="selectedSlotData"
      @addStudent="addStudentModal"
      @close="closeShowParticipantDialog"
      @reloadSchedule="reloadSchedule"
    />
    <order-details
        :id="order_id_for_payment"
        :ids="orderIds"
        @close="order_id_for_payment = null; orderIds = null;"
        @paymentDone="paymentDone"
    ></order-details>
    <TrainerActionDialog
        :openActionDialog="openActionDialog"
        @close="closeActionPopup"
        @openSelectedPopup="openSelectedPopup"
    />
    <CustomerForm
        v-if="customerFormDialoge"
        :customerFormDialoge="customerFormDialoge"
        :trainerId="currentTrainerId"
        page="multi_trainer_schedule"
        @close="closeCustomerDialog"
        @complete="updateCustomerDetails"
    />
    <TrainerScheduleSessionFormNew
        v-if="openScheduleSessionDialog"
        :openScheduleSessionDialog="openScheduleSessionDialog"
        :date="date"
        :selectedTrainer="selectedTrainer"
        :selectedTime="selectedTime"
        @close="closeScheduleSessionDialog"
        @complete="scheduleComplete"
    />
  </v-container>
</template>

<script>
import moment from "moment";
//import HeaderCell from "@/components/Workshop/Schedules/HeaderCell.vue";
//import TimeColumn from "@/components/Workshop/Schedules/TimeColumn.vue";
//import DataColumn from "@/components/Workshop/Schedules/DataColumn.vue";
import AddProgram from "@/components/Workshop/Schedules/AddProgram.vue";
import BookWorkshopTrainerProgram from "@/components/Workshop/Schedules/BookWorkshopTrainerProgram.vue";
import CustomerAttendanceModal from "@/components/Workshop/Schedules/CustomerAttendanceModal.vue";
import TrainerTopTabs from "@/views/Trainers/TrainerTabs/TrainerTopTabs.vue";
import OrderDetails from "../../components/Order/OrderDetails.vue";
import TrainerCustomerTooltipDialog from "@/views/Trainers/TrainerCustomerTooltipDialog.vue";
import TrainerActionDialog from "./TrainerActionDialog.vue";
import TrainerScheduleLegends from "./TrainerScheduleLegends.vue"
import CustomerForm from "./CustomerForm.vue";
import TrainerScheduleSessionFormNew from "./TrainerScheduleSessionFormNew.vue";
export default {
  components: {
    TrainerActionDialog,
    OrderDetails,
    CustomerAttendanceModal,
    BookWorkshopTrainerProgram,
    AddProgram,TrainerTopTabs,
    TrainerCustomerTooltipDialog,
    TrainerScheduleLegends,
    CustomerForm,
    TrainerScheduleSessionFormNew,
    // DataColumn, HeaderCell, TimeColumn,
  },
  data() {
    return {
      backfillAllowed: false,
      date: moment().format("YYYY-MM-DD"),
      weekDays: [],
      increment: 60,
      height: 4800,
      tpopup: false,
      selectedDate: moment().format("YYYY-MM-DD"),
      selectedTime: null,
      trainersData: [],
      timeSlots: [],
      timeSlots2: [],
      selectedData: {},
      userModel: {userID: null, type: "details"},
      venueServiceIds: [],
      trainerStatus: 'active',
      addProgramDialog: false,
      addParticipantDialog: false,
      showParticipantDialog:false,
      workshopData: null,
      selectedTrainer: null,
      selectedSlotData: null,
      trainers: [],
      startTime: "00:00:00",
      endTime: "23:59:00",
      dynamicHeight: 0,
      orderId: null,
      order_id_for_payment: null,
      orderIds: null,
      slotHeight: 60,
      dialogVisible: false,
      selectedGroup: [],
      maxVisible: 2,
      selectedOpen: false,
      openActionDialog: false,
      customerFormDialoge:false,
      openScheduleSessionDialog:false,
      selectedEvent: {},
      currentTrainerId: null,
    }
  },
  mounted() {
    console.log("this.$modules.trainers.management.slug",this.$modules.trainers.sales.slug);
    this.backfillAllowed = this.checkBackfillPermission(this.$modules.trainers.sales.slug);
    console.log("backfillAllowed",this.backfillAllowed);
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices").then(() => {
        this.venueServiceIds = this.venueServices.map((item) => item.venue_service_id);
        this.getTrainerSchedules()
      });
    } else {
      this.venueServiceIds = this.venueServices.map(
          (item) => item.venue_service_id
      );
      this.getTrainerSchedules()
    }
    this.generateWeekDays();
    this.calculateContainerHeight();
    this.generateSlotTiming();
  },

  computed: {
    venueServices() {
      return this.$store.getters.getVenueServices.data.filter(
          (service) => service.name != "POS"
      );
    },
    minuteHeight() {
      return this.slotHeight / this.increment;
    },
    // totalHeight() {
    //   return this.timeSlots.length * this.slotHeight;
    // },
    totalHeight() {
      return 1440 * this.minuteHeight;
    }
  },
  watch: {
    venueServices(venuS, old) {
      if (!old.length && venuS.length) {
        this.venueServiceIds = this.venueServices.map(
            (item) => item.venue_service_id
        );
      }
    }
  },
  methods: {
    toMinutes(timeStr) {
      const [h, m] = timeStr.split(":").map(Number);
      return h * 60 + m;
    },
    isSlotAvailable(trainer, slotTime) {
      const mins = this.toMinutes(slotTime.start_time);
      const isInAvailability = trainer._availableMinutes?.has(mins);
      if (!isInAvailability) return false;

      const now = moment(); // Current local time
      const selectedDate = moment(this.date, "YYYY-MM-DD");
      const slotStart = moment(`${this.date} ${slotTime.start_time}`, "YYYY-MM-DD HH:mm:ss");

      // If selected date is today
      if (selectedDate.isSame(now, 'day')) {
        // Block slot if it's in the past and backfill is not allowed
        if (slotStart.isBefore(now) && !this.backfillAllowed) {
          return false;
        }
      }

      // If selected date is in the past
      if (selectedDate.isBefore(now, 'day')) {
        if (!this.backfillAllowed) return false;
      }

      // Otherwise, slot is allowed
      return true;
    },
    preprocessOverlaps(schedules, maxVisible = 2) {
      schedules.sort((a, b) => a.start_time.localeCompare(b.start_time));
      const result = [];
      schedules.forEach((curr, index) => {
        const currStart = this.toMinutes(curr.start_time);
        // Include ALL previous ones in the same group
        const group = schedules.filter(s => {
          const start = this.toMinutes(s.start_time);
          return start === currStart;
        });
        curr._overlapGroup = group;
        curr._isVisible = index < maxVisible;
        curr._isSummary = index === maxVisible;
        result.push(curr);
      });

      return result;
    },
    getBookingStyle(sch,index) {
      console.log(index);
      const top = this.getTop(sch.start_time);
      const height = this.getHeight(sch.start_time, sch.end_time);
      console.log("height",height);
      const overlapIndex = sch._overlapGroup.indexOf(sch);
      const totalOverlap = sch._overlapGroup.length;

      const widthPercent = 100 / Math.min(totalOverlap, this.maxVisible);
      const leftPercent = widthPercent * overlapIndex;
      let bgColor = sch._isSummary ? "#ffffff" : "#112A46";
      if(sch.type === "trainer"){
        bgColor = "#06b4b3";
      }
      return {
        position: "absolute",
        top: `${top}px`,
        height: `${height}px`,
        left: `${leftPercent}%`,
        width: `${widthPercent}%`,
        backgroundColor: bgColor,
        color: "white",
        fontSize: "12px",
        padding: "2px 4px",
        borderRadius: "4px",
        boxSizing: "border-box",
        border: sch._isSummary ? "1px dashed #aaa" : "none",
        textAlign: "center",
        zIndex: 2,
        lineHeight:`${height}px`,
        whiteSpace: "nowrap",
        textOverflow: "ellipsis",
        overflow: "hidden",
      };
    },
    getTop(time) {
      const [h, m] = time.split(":").map(Number);
      const totalMins = h * 60 + m;
     // return (totalMins / 60) * this.slotHeight;
      return totalMins * this.minuteHeight;
    },
    // getHeight(start, end) {
    //   const [sh, sm] = start.split(":").map(Number);
    //   const [eh, em] = end.split(":").map(Number);
    //   const durationMins = eh * 60 + em - (sh * 60 + sm);
    //   return (durationMins / 60) * this.slotHeight;
    // },
    getHeight(start, end) {
      const [sh, sm] = start.split(":").map(Number);
      const [eh, em] = end.split(":").map(Number);
      const durationMins = (eh * 60 + em) - (sh * 60 + sm);
      return durationMins * this.minuteHeight;
    },

    calculateContainerHeight(){
      const start = this.convertToMinutes("00:00:00");
      const end = this.convertToMinutes("23:59:00");
      this.dynamicHeight  = ((end - start));
    },
    convertToMinutes(time) {
      const [hours, minutes] = time.split(":").map(Number);
      return hours * 60 + minutes;
    },
    openMoreDialog(trainerId, startTime) {
      const trainer = this.trainersData.find((t) => t.id === trainerId);
      if (!trainer) return;
      const group = trainer.schedules.find(
          (s) => s.start_time === startTime && s._overlapGroup
      )._overlapGroup;

      this.selectedGroup = group;
      this.dialogVisible = true;
    },
    gotoPage(route) {
      this.$router.push(route);
    },
    nextDate() {
      let amount = 1;
      this.date = moment(this.date).add(amount, "days").format("YYYY-MM-DD");
      this.generateWeekDays();
      this.getTrainerSchedules();
    },
    prevDate() {
      let amount = 1;
      this.date = moment(this.date).subtract(amount, "days").format("YYYY-MM-DD");
      this.generateWeekDays();
      this.getTrainerSchedules();
    },
    changeDate() {
      this.generateWeekDays();
      this.getTrainerSchedules();
    },
    getFormattedDate(date) {
      return moment(date, "YYYY-MM-DD").format("ddd Do");
    },
    generateWeek(startingDate) {
      const daysOfWeek = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      const result = [];

      // Convert the starting date string to a Date object
      const startDate = new Date(startingDate);

      // Generate the list of weekdays starting from the given date
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);
        const day = daysOfWeek[currentDate.getDay()];
        result.push({
          day: day,
          date: currentDate.toISOString().split("T")[0],
        });
      }
      return result;
    },
    generateWeekDays() {
      this.weekDays = [];
      this.weekDays = this.generateWeek(this.date);
    },
    getTrainerSchedules() {
      let url = `from_date=${this.date}&status=${this.trainerStatus}`;
      this.showLoader("Loading Trainers");

      if (this.venueServiceIds.length > 0) {
        this.venueServiceIds.map((service, index) => {
          url += `&venue_service_id[${index}]=${service}`;
        });
      }
      this.showLoader("Loading");
      this.$http
          .get(`venues/workshops/trainers/schedules?${url}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.trainersData = response.data.data;
              this.trainersData.forEach((trainer) => {
                const availability = trainer.availabilities.find(
                    (a) => a.selected_date === this.date
                );
                if (availability) {
                  const start = this.toMinutes(availability.start_time);
                  const end = this.toMinutes(availability.end_time);
                  // create a Set of available minute slots (e.g., [600, 601, ..., 1140])
                  trainer._availableMinutes = new Set();
                  for (let i = start; i < end; i++) {
                    trainer._availableMinutes.add(i);
                  }
                } else {
                  trainer._availableMinutes = new Set(); // no availability = all slots disabled
                }
                trainer.schedules = this.preprocessOverlaps(trainer.schedules || [], this.maxVisible);
              });
              // console.log(this.trainersData);
            }
            this.hideLoader();
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getTotalCount(date) {
      let checkIn = 0;
      let checkOut = 0;
      if (date && Object.keys(this.trainersData).length) {
        this.logs[date] && Object.keys(this.logs[date]).forEach((key) => {
          const item = this.logs[date][key];
          checkIn += item.total_check_in;
          checkOut += item.total_check_out;
        })
      }
      return {check_in: checkIn, check_out: checkOut};
    },
    onScroll() {
      const refElement = this.$refs.schedule;
      if (refElement) {
        const scrollLeft = refElement.scrollLeft;
        const scrollTop = refElement.scrollTop;
        this.$refs.scheduleTimer.scrollTop = scrollTop;
        this.$refs.scheduleHeader.scrollLeft = scrollLeft;
      }
    },
    openParticipantPopup(data) {
      this.tpopup = true;
      this.selectedData.date = data.date;
      this.selectedData.hour = data.hour;
      this.selectedData.slots = data.slots;
      if (this.timeSlots && this.timeSlots.length > 0) {
        this.selectedData.time = this.timeSlots.find(slot => slot.hour === data.hour).time;
      }
    },
    closeModal() {
      this.userModel.userID = null;
      this.tpopup = false;
    },
    showUserModal(userId) {
      this.userModel.userID = userId;
      this.userModel.type = "details";
    },
    changeVenueService() {
      this.getTrainerSchedules();
    },
    toggle() {
      this.$nextTick(() => {
        if (this.venueServices.length == this.venueServiceIds.length) {
          this.venueServiceIds = [];
        } else {
          this.venueServiceIds = this.venueServices.map(
              (item) => item.venue_service_id
          );
          this.$forceUpdate;
        }
      });
    },
    icon() {
      if (this.venueServices.length == this.venueServiceIds.length)
        return "mdi-close-box";
      if (this.venueServiceIds.length > 0) return "mdi-minus-box";
      return "mdi-checkbox-blank-outline";
    },
    addProgramModalShow(trainer){
      if(moment(this.date).isBefore(moment(), 'day')){
        this.showError("Program cannot be added for past dates.");
      }else{
        this.selectedTrainer = {id: trainer.id, name: trainer.name, user_id: trainer.user_id};
        this.addProgramDialog = true;
      }
    },
    showParticipantModal(trainer,childData){
      this.selectedTrainer = {id: trainer.id, name: trainer.name, user_id: trainer.user_id};
      this.selectedSlotData = childData.program;
      this.showParticipantDialog = true;
    },
    addParticipantModalShow(trainer,childData){
      this.selectedTrainer = {id: trainer.id, name: trainer.name, user_id: trainer.user_id};
      this.selectedSlotData = childData.program;
      this.workshopData = {...this.selectedSlotData};
      this.addParticipantDialog = true;
    },
    addStudentModal(){
      this.showParticipantDialog = false;
      this.workshopData = {...this.selectedSlotData};
      this.addParticipantDialog = true;
    },
    closeProgramDialoag(){
      this.addProgramDialog = false;
      this.selectedTrainer = null;
    },

    programAdded(){
      this.addProgramDialog = false;
      this.selectedTrainer = null;
      this.reloadSchedule();

    },
    closeAddParticipantDialog(){
      this.addParticipantDialog = false;
      this.workshopData = null;
      this.selectedTrainer = null;
    },
    closeShowParticipantDialog(){
      this.workshopData = null;
      this.selectedTrainer = null;
      this.showParticipantDialog = false;
    },
    reloadSchedule(){
      this.workshopData = null;
      this.selectedTrainer = null;
      this.showParticipantDialog = false;
      this.getTrainerSchedules();
    },
    getProgramsForSlot(trainer, slot) {
      // Map time slots to the trainer's programs.
      const [start, end] = slot.split(" - ");
      return trainer.programs.filter((program) =>
          program.schedules.some(
              (schedule) => this.isOverlapping(schedule.start, schedule.end, start, end)
          )
      );
    },
    isOverlapping(scheduleStart, scheduleEnd, slotStart, slotEnd) {
      const toMinutes = (time) => {
        const [hours, minutes] = time.match(/(\d+):(\d+)/).slice(1).map(Number);
        const isPM = time.includes("PM") && hours !== 12;
        return (isPM ? hours + 12 : hours) * 60 + minutes;
      };
      const s1 = toMinutes(scheduleStart);
      const e1 = toMinutes(scheduleEnd);
      const s2 = toMinutes(slotStart);
      const e2 = toMinutes(slotEnd);
      return s1 < e2 && s2 < e1;
    },
    isProgramInSlot(program, slot) {
      const programStart = new Date(program.start_time);
      const programEnd = new Date(program.end_time);
      const slotStart = slot.start;
      const slotEnd = slot.end;
 // Check if the program overlaps with the slot
      return programStart < slotEnd && programEnd > slotStart;
    },
    formatTime(date) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },
    generateSlotTiming() {
      this.timeSlots = [];
      const totalDayMinutes = 1440;
      //const divisions = totalDayMinutes / this.increment;
      const startTimeMoment = moment(this.startTime, "HH:mm:ss");
      const endTimeMoment = moment(this.endTime, "HH:mm:ss");
      let data = [];
      for (let index = 0; index < totalDayMinutes; index += this.increment) {
        let start = moment().startOf("day").add(index, "minutes");
        let end = start.clone().add(this.increment, "minutes");
        if(end.format('HH:mm:ss') == "00:00:00"){
          end = start.clone().add(this.increment - 1, "minutes");
        }


        if (start.isSameOrAfter(startTimeMoment) && end.isSameOrBefore(endTimeMoment)) {
          const element = {
            id: index,
            start_time: start.format("HH:mm:ss"),
            end_time: end.format("HH:mm:ss"),
            formated_start_time: start.format("hh:mm a"),
            formated_end_time: end.format("hh:mm a"),
            height: 60,
            fixed: true,
          };
          data.push(element);
        }
      }
      this.timeSlots = [...data];
    },
    paymentDone() {
      this.order_id_for_payment = null;
      this.getTrainerSchedules();
    },
    customerschedulecompleted(data) {
      this.addParticipantDialog = false;
      this.selectedTrainer = null;
      this.selectedTime = null;
      this.workshopData = null;
      if (data) {
        this.order_id_for_payment = data.order_id;
      }
    },
    handleProgramClick(trainer, program) {
      if(program.type === "workshop") {
        this.onClickSlot('addProgramParticipant', trainer, program);
      }else if(program.type === "trainer"){
        this.onClickSlot('showTrainerParticipantToolTip', trainer, program);
      }
    },
    onClickSlot(action, trainer,program) {
      let d = {start_time: program.start_time, program: program, action: action, is_open_participants: true};
      if (action === "purchaseTrainerPackage") {
        console.log('Emitting purchaseTrainerPackage', d);
      } else if (action === "addProgramParticipant") {
        console.log('Emitting addParticipant/showParticipant', d);
        this.showParticipantModal(trainer,d);
      }else if(action === "showTrainerParticipantToolTip"){
        console.log("program",program)
        this.selectedOpen = true;
        this.selectedEvent = program;
      }
    },
    closeTrainerCustomerTooltipDialog(){
      this.selectedOpen = false;
      this.selectedEvent = null;
    },
    openActionPopup(trainer,time){
      console.log(trainer);
      console.log(time);
      this.currentTrainerId = trainer.id;
      this.selectedTrainer = {id: trainer.id, name: trainer.name, user_id: trainer.user_id};
      this.selectedTime = time;
      this.openActionDialog = true;
    },
    closeActionPopup(){
      this.openActionDialog = false;
      this.currentTrainerId = null;
      this.selectedTrainer = null;
      this.selectedTime = null;
    },
    openSelectedPopup(action){
      if(action === "buy_package"){
        this.customerFormDialoge = true;
        this.openActionDialog = false;
      }else if(action === "schedule_session"){
        this.openActionDialog = false;
        this.openScheduleSessionDialog = true;
      }
    },
    closeCustomerDialog(){
      this.currentTrainerId = null;
      this.selectedTrainer = null;
      this.selectedTime = null;
      this.customerFormDialoge = false;
    },
    updateCustomerDetails(data){
      console.log("data",data)
      this.customerFormDialoge = false;
      this.currentTrainerId = null;
      this.selectedTrainer = null;
      this.selectedTime = null;
      if (data.status == "unpaid") {
        this.order_id_for_payment = data.id;
      }
    },
    scheduleComplete() {
      this.orderId = null;
      this.openScheduleSessionDialog = false;
      this.selectedTrainer = null;
      this.selectedTime = null;
      this.getTrainerSchedules();
    },
    closeScheduleSessionDialog(){
      this.orderId = null;
      this.openScheduleSessionDialog=false;
      this.selectedTrainer = null;
      this.selectedTime = null;
    }
  }
}
</script>
<style>
/** Trainer Schedule New Screen START **/
.ts-new{
  .calender-navi {
    width: 420px;
    text-align: center;
    margin: 0 auto;
  }
}
.trainer-slot span {
  text-align: center;
  font-weight: bold;
  color: #2e7d32;
}
.unavailable-class {
  background: #cbcbcb;
  cursor: not-allowed;
  z-index: +2;
}

.calendar-wrapper {
  overflow-x: auto;
}

.time-label {
  font-size: 12px;
  padding-left: 2px;
  border-bottom: 1px solid #eee;
  line-height: 60px;
}
.trainer-schedule {
  position: relative;
  background: #ffffff;
}
.booking {
  position: absolute;
  cursor: pointer;
}
.slot {
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #fefefe;
  z-index: 1;
}

.slot-plus {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  opacity: 0.3;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.slot:hover {
  background: rgba(79, 174, 175, 0.10)
}
slot:hover .slot-plus{
  opacity: 1;
}
.slot.unavailable {
  background-color: #f4f4f4;
  pointer-events: none;
  opacity: 0.5;
  border: 1px dashed #ddd;
}

.trainer-scroll-wrapper {
  overflow-x: auto;
}

.trainer-row-no-wrap {
  display: flex;
  flex-wrap: nowrap;
  min-width: max-content;
}

.time-column {
  min-width: 140px;
  border-right: 1px solid #ddd;
  background-color: #f9f9f9;
  flex-shrink: 0;
  border-right: 1px solid #ddd;
}

.trainer-column {
  flex: 1;
  min-width: 160px;
  max-width: 160px;
  flex-shrink: 0;
  border-right: 1px solid #ddd;
  position: relative;
}

.trainer-header {
  text-align: center;
  border-bottom: 1px solid #ccc;
  padding: 12px 4px;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 3;
  background-color: rgb(233, 241, 246);
  color: black;
  font-weight: 600;
}
</style>

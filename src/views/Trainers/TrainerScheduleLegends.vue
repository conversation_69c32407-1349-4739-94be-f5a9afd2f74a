<template>
  <div class="legend-container" title="schedule booking legend">
    <div class="legend-item">
      <div class="color-box" style="background-color: #f4f4f4;"></div>
      <span>Slot Not Available</span>
    </div>

<!--    <div class="legend-item">-->
<!--      <div class="color-box" style="background-color: red;"></div>-->
<!--      <span>Full Class</span>-->
<!--    </div>-->

<!--    <div class="legend-item">-->
<!--      <div class="color-box" style="background-color: yellow;"></div>-->
<!--      <span>Reschedule</span>-->
<!--    </div>-->
    <div class="legend-item">
      <div class="color-box" style="background-color: rgb(6, 180, 179);"></div>
      <span>Trainer Personal Booking</span>
    </div>
    <div class="legend-item">
      <div class="color-box" style="background-color: rgb(17, 42, 70);"></div>
      <span>Trainer Academy Program</span>
    </div>
  </div>
</template>

<script>
export default {}
</script>
<style scoped>
.legend-container {
  display: flex;
  align-items: center;
}
.legend-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 12px;
}

.color-box {
  width: 14px;
  height: 14px;
  margin-right: 8px;
  border-radius: 2px;
  border: 1px solid black;
}
</style>
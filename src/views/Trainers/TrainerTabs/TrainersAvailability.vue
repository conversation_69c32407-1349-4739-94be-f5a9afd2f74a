<template>
  <div v-if="1===3">
    <v-row>
      <div
          class="user-wrapper"
          v-if="checkReadPermission($modules.trainers.management.slug)"
      >
        <div
            class="
            md-layout-item md-medium-size-100 md-xsmall-size-100 md-size-50
          "
        >
          <div class="md-card md-theme-default">
            <div class="md-card-content">
              <div>
                <div class="header_title">
                  <v-row>
                    <v-col md="10">Availability Time slots</v-col>
                    <v-col md="2">
                      <div style="float: right">
                        <v-btn
                            v-if="
                            checkWritePermission(
                              $modules.trainers.availability.slug
                            ) && trainer.status_id == 1
                          "
                            @click="addAvailability"
                            class="teal-color"
                            dark
                        >
                          Configure Availability
                          <v-icon right dark>mdi-cog</v-icon>
                        </v-btn>
                      </div>
                    </v-col>
                  </v-row>
                </div>
                <div
                    class="md-content md-table md-theme-default table_borders"
                    table-header-color="orange"
                    value
                >
                  <div
                      class="
                      md-content md-table-content md-scrollbar md-theme-default
                    "
                  >
                    <table>
                      <thead class="md-card-header">
                      <tr>
                        <th class="md-table-head">
                          <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                          >
                            <div class="md-table-head-label">Days</div>
                          </div>
                        </th>
                        <th class="md-table-head">
                          <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                          >
                            <div class="md-table-head-label">Start Time</div>
                          </div>
                        </th>
                        <th class="md-table-head">
                          <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                          >
                            <div class="md-table-head-label">End Time</div>
                          </div>
                        </th>
                        <!-- <th class="md-table-head">
                          <div class="md-table-head-container md-ripple md-disabled">
                            <div class="md-table-head-label">Edit</div>
                          </div>
                        </th>
                        <th class="md-table-head">
                          <div class="md-table-head-container md-ripple md-disabled">
                            <div class="md-table-head-label">Delete</div>
                          </div>
                        </th> -->
                      </tr>
                      </thead>

                      <tbody>
                      <tr
                          class="md-table-row"
                          v-for="timeSlots in timeSlotList"
                          :key="timeSlots.id"
                      >
                        <td class="md-table-cell">
                          <div class="md-table-cell-container">
                              <span class="pr-2">{{
                                  getWeekdays(timeSlots.weekdays)
                                }}</span>
                          </div>
                        </td>
                        <td class="md-table-cell">
                          <div class="md-table-cell-container text-truncate">
                              <span>
                                {{ formatTime(timeSlots.start_time) }}</span
                              >
                          </div>
                        </td>
                        <td class="md-table-cell">
                          <div class="md-table-cell-container text-truncate">
                              <span class="pr-2">{{
                                  formatTime(timeSlots.end_time)
                                }}</span>
                          </div>
                        </td>

                        <!-- <td class="md-table-cell">
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-icon
                                v-bind="attrs"
                                v-on="on"
                                @click="editTimeSlot(timeSlots)"
                                color="#66c8c8"
                                >mdi-pencil</v-icon
                              >
                            </template>
                            <span>Edit Time Slot</span>
                          </v-tooltip>
                        </td> -->

                        <!-- <td class="md-table-cell">
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-icon
                                v-bind="attrs"
                                v-on="on"
                                @click="deleteAvailability(timeSlots)"
                                color="red"
                                >mdi-delete</v-icon
                              >
                            </template>
                            <span>Delete Time Slot</span>
                          </v-tooltip>
                        </td> -->
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <v-pagination
                    v-model="page"
                    :length="totalPages"
                ></v-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </v-row>

    <div class="md-card md-theme-default mt-8 shadow rounded-5">
      <div class="md-card-content md-card-table">
        <div class="table-responsive">
        </div>
        <!-- <availability-form
          :availabilityFormDialogue="availabilityFormDialogue"
          @close="availabilityFormDialogue = false"
          :trainerId="trainerId"
          :editId="editId"
          :aval="aval"
          @loadAvail="loadAvail"
        ></availability-form> -->
        <availability-form-multiple
            :availabilityFormDialogue="availabilityFormDialogue"
            @close="availabilityFormDialogue = false"
            :trainerId="trainerId"
            :editId="editId"
            :aval="aval"
            @loadAvail="loadAvail"
        ></availability-form-multiple>
        <confirm-model
            v-bind="confirmModel"
            @confirm="confirmActions"
            @close="confirmModel.id = null"
        ></confirm-model>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
// import AvailabilityForm from "../AvailabilityForm.vue";
import AvailabilityFormMultiple from "../AvailabilityFormMultiple.vue";

export default {
  props: {
    trainerId: { type: Number, default: null },
    trainer: {
      type: Object,
      default: () => {},
    },
    trainerLevels: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    // AvailabilityForm,
    AvailabilityFormMultiple,
  },
  created() {
    this.loadAvailability();
  },
  watch: {
    page() {
      this.loadAvailability();
    },
  },
  data() {
    return {
      confirmModel: {},
      availabilityFormDialogue: false,
      editAvailability: false,
      editId: null,
      isLoading: false,
      totalPages: 1,
      page: 1,
      aval: {},
      orderBy: "id",
      orderDir: "DESC",
      timeSlotList: {},
    };
  },

  mounted() {
    this.$store.dispatch("loadWeekdays");
  },

  methods: {
    formatTime(input) {
      return moment(input, "HH:mm:ss").format("hh:mm a");
    },
    getWeekdays(days) {
      let dayName = "";
      days.forEach((element) => {
        let findValue = this.$store.getters.getWeekdays.data.find(
            (y) => y.bit_value == element
        );
        if (findValue) {
          dayName = dayName.concat(findValue.name.substring(0, 3), ",");
        }
      });

      return dayName.slice(0, -1);
    },
    showTimeSlots(id) {
      console.log(id);
    },
    editTimeSlot(timeS) {
      this.editId = timeS.id;
      this.aval = timeS;
      this.availabilityFormDialogue = true;
    },
    addAvailability() {
      this.editId = null;
      this.aval = null;
      this.availabilityFormDialogue = true;
      console.log("add button clicked");
    },
    deleteTimeSlot(id) {
      console.log(id);
    },
    loadAvail() {
      this.loadAvailability();
      // this.$emit("loadAvaila");
    },
    getFilterUrl() {
      let url = `?page=${this.page}`;
      url += `&order_by=${this.orderBy}&sort_order=${this.orderDir}`;

      return url;
    },
    loadAvailability() {
      let url = this.getFilterUrl();
      this.$http
          .get(
              `venues/trainers/availability${
                  this.trainerId != null ? "/" + this.trainerId : ""
              }${url}`
          )
          .then((response) => {
            if (response.status == 200) {
              this.timeSlotList = response.data.data;
              this.totalPages = response.data.total_pages;
              console.log("total pages " + response.data.total_pages);
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    deleteAvailability(data) {
      this.confirmModel = {
        id: data.id,
        title: "Do you want to remove this Availability timing?",
        description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "delete_availability",
        data: { id: data.id, name: data.id },
      };
    },
    confirmActions(data) {
      if (data.type == "delete_availability") {
        this.showLoader("Loading");
        this.$http
            .post(
                `venues/trainers/availability/${this.trainerId}` +
                (data.id ? "/delete-availability/" + data.id : ""),
                {},
                {
                  headers: {
                    "Content-Type":
                        "multipart/form-data; boundary=${form._boundary}",
                  },
                }
            )
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                this.showSuccess("Removed successfully.");
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            });

        this.hideLoader();
        this.loadAvailability();
        // this.$emit("loadAvaila");
        // this.loadAvailability(this.trainerId);
      }
    },
  },
};
</script>

<style scoped>
tbody tr:hover {
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -webkit-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -moz-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  z-index: 1;
}
.button_navigation {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.button_navigation .v-item--active {
  background-color: #062b48 !important;
  color: #fff !important;
}
.v-select-list >>> .v-subheader {
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  color: black;
}
</style>

<template>
  <v-dialog v-model="selectedOpen" width="500px" scrollable persistent>
    <v-card color="grey lighten-4" min-width="350px" flat class="pt-0" v-if="selectedEvent && selectedEvent.type">
      <v-card-title >
        <div class="row pt-1 border-bottom">
          <div class="col-md-12">
            <div class="d-flex justify-space-between align-center mt-2">
              <SvgIcon
                  class="text-xl font-semibold"
                  text="Trainer"
                  style="color: black"
              >
              </SvgIcon>
              <v-btn fab x-small class="shadow-0" @click="close">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
      </v-card-title>
      <v-card-text class="pt-0">
        <v-row class="mt-0" v-if="selectedEvent.type !== 'Trainer Not Available'">
          <v-col md="5">
              <span class="font-semibold">
                {{ selectedEvent.type.toLowerCase() === "trainer" ? "Package " : selectedEvent.type.toLowerCase() === "academy" ? "Academy Name" : selectedEvent.type }}
              </span>
          </v-col>
          <v-col md="7">
              <span class="">
                {{ selectedEvent.type_name }}
              </span>
          </v-col>
        </v-row>
        <v-row class="mt-0" v-if="selectedEvent.type != 'Trainer Not Available'">
          <v-col md="5">
            <span class="font-semibold">
              {{ selectedEvent.type.toLowerCase() === "trainer" ? "Customer " : selectedEvent.type.toLowerCase() === "academy" ? "Program " : "" }}
            </span>
          </v-col>
          <v-col md="7">
            <span class="">
              {{ selectedEvent.program_name?selectedEvent.program_name:selectedEvent.name }}
            </span>
          </v-col>
        </v-row>
        <v-row class="mt-0" v-if="selectedEvent.type != 'Trainer Not Available'">
          <v-col md="5">
            <span class="font-semibold">Facility </span>
          </v-col>
          <v-col md="7">
            <span class="">{{ selectedEvent.facility_name || "NA" }}</span>
          </v-col>
        </v-row>
        <v-row class="mt-0">
          <v-col md="5">
            <span class="font-semibold">Time </span>
          </v-col>
          <v-col md="7">
              <span class="">
                {{ selectedEvent.start_time | timeFormat }} to
                {{ selectedEvent.end_time | timeFormat }}
              </span>
          </v-col>
        </v-row>
        <v-row class="mt-0">
          <v-col md="12" v-if="selectedEvent.type.toLowerCase() === 'academy'">
            <v-btn class="ma-2 white--text teal-color text-center" normal @click="viewStudents(selectedEvent)">
              <span>View Students</span>
            </v-btn>
          </v-col>
          <v-col md="12" v-else>
            <template v-if="selectedEvent.type.toLowerCase() === 'trainer'">
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                      :class="parseInt(selectedEvent.is_present) === 0 ? 'default-color' : parseInt(selectedEvent.is_present) === 1 ? 'green-color' : 'red-color'"
                      dark
                      class="text-decoration-none"
                      v-bind="attrs"
                      v-on="on"
                  >
                    {{ parseInt(selectedEvent.is_present) === 0 ? "Attendance" : parseInt(selectedEvent.is_present) === 1 ? "Attended" : "No show" }}
                  </v-btn>
                </template>
                <v-list v-if="parseInt(selectedEvent.is_present) === 0">
                  <v-list-item @click="confirmAttendance({ facility_booking_id: selectedEvent.facility_booking_id, training_sessions_id: selectedEvent.training_sessions_id, operation: 'present', })">
                    <v-list-item-title>Mark as attended</v-list-item-title>
                  </v-list-item>
                  <v-list-item @click="confirmAttendance({ facility_booking_id: selectedEvent.facility_booking_id, training_sessions_id: selectedEvent.training_sessions_id, operation: 'absent', })">
                    <v-list-item-title>Mark no show</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </template>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <confirm-model
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="confirmModel.id = null"
    ></confirm-model>
  </v-dialog>

</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  props: {
    selectedOpen: {
      type: Boolean,
      default: false,
    },
    selectedEvent: { type: Object, default: null },
  },
  components: {
    SvgIcon,
  },
  data(){
    return {
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
    }
  },
  methods: {
    close(){
      this.$emit('close');
    },
    confirmAttendance(data) {
      let msg = "This will mark the attendance of the customer. By clicking Yes you can confirm the operation.";
      this.confirmModel = {
        id: parseInt(data.facility_booking_id),
        title: `Do you want to mark the attendance?`,
        description: msg,
        type: "attendance",
        data: data,
      };
    },
    confirmActions(data) {
      if (data.type == "attendance") {
        this.markAttendance(data.data);
      }
      this.$forceUpdate();
      this.confirmModel.id = null;
    },
    markAttendance(data) {
      this.showLoader("Loading...");
      var formData = new FormData();
      formData.append("facility_booking_id", 9);
      formData.append("training_session_id", data.training_sessions_id);
      this.$http
          .post(`venues/trainers/customers/sessions/attendance/${data.operation}`, formData)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              // const data = response.data.data;
              this.$emit("getTrainerSchedules");
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
  },
}

</script>
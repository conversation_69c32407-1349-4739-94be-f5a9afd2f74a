<template>
  <v-dialog v-model="openActionDialog" width="350px" scrollable persistent>
    <v-card color="grey lighten-4" min-width="350px" flat class="pt-0">
      <!-- Header -->
      <v-card-title >
        <div class="row pt-1 border-bottom">
          <div class="col-md-12">
            <div class="d-flex justify-space-between align-center mt-2">
              <SvgIcon class="text-xl font-semibold" text="Choose an option" style="color: black"></SvgIcon>
              <v-btn fab x-small class="shadow-0" @click="close">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
      </v-card-title>

      <!-- Body -->
      <v-card-text class="pt-2">
        <v-row justify="center" align="center">
          <v-col cols="12" class="text-center">
            <v-btn color="primary" class="ma-2 d-block w-full" @click="selectOption('buy_package')">
              Buy Package
            </v-btn>
            <v-btn color="secondary" class="ma-2 d-block w-full" @click="selectOption('schedule_session')">
              Schedule Session
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  props: {
    openActionDialog: {
      type: Boolean,
      default: false,
    },
    selectedEvent: { type: Object, default: null },
  },
  components: {
    SvgIcon,
  },
  methods: {
    close(){
      this.$emit('close');
    },
    selectOption(action){
        this.$emit('openSelectedPopup', action);
    }
  },
}

</script>
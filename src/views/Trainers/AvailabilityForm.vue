<template>
  <v-dialog v-model="availabilityFormDialogue" scrollable persistent width="60%">
    <v-form ref="form">
      <v-card>
        <v-card-title class="headline"> Availability </v-card-title>
        <v-card-text class="mt-8 pt-5">
          <v-row>
            <v-col lg="4">
              <v-autocomplete
                :items="allWeekdays"
                item-text="name"
                item-value="bit_value"
                label="Days Applicable*"
                multiple
                outlined
                background-color="#fff"
                validate-on-blur
                :rules="[(v) => v.length > 0 || 'Days Applicable is required']"
                v-model="weekdays"
              >
                <template
                  v-if="allWeekdays.length == weekdays.length"
                  v-slot:selection="{ index }"
                >
                  <span v-if="index == 0">All Days</span>
                </template>
                <template v-else v-slot:selection="{ item, index }">
                  <span v-if="index == 0">{{ item.name }}</span>
                  <span v-if="index == 1">,{{ item.name }}</span>
                  <span v-if="index == 2">, ...</span>
                </template>
                <template v-slot:prepend-item>
                  <v-list-item ripple @click="toggle('trainerAvailability', null)">
                    <v-list-item-action>
                      <v-icon :color="weekdays.length > 0 ? 'indigo darken-4' : ''">{{
                        getIcon("trainerAvailability", null)
                      }}</v-icon>
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title>All</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col lg="4">
              <v-select
                :items="timings.slice(0, timings.length - 1)"
                :label="`From`"
                item-text="text"
                item-value="value"
                :rules="[(v) => !!v || 'From time is required']"
                v-model="start_time"
                required
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
              ></v-select>
            </v-col>
            <v-col lg="4">
              <v-select
                :items="timings.slice(1)"
                :label="`To`"
                item-text="text"
                item-value="value"
                v-model="end_time"
                :rules="[(v) => !!v || 'To time is required']"
                required
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2 white--text blue-color" @click="close">Close</v-btn>
          <v-btn class="ma-2 white--text teal-color" @click="addOrUpdateTrainerCustomer"
            >Save</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
import moment from "moment";
import trainer from "@/mixins/trainer";

export default {
  props: {
    availabilityFormDialogue: {
      type: Boolean,
      default: false,
    },
    trainerId: { type: Number, default: null },
    editId: { type: Number, default: null },
    aval: { type: Object, default: null },
  },
  created() {
    this.generateTiming();
  },
  data() {
    return {
      editFlag: false,
      weekdays: [],
      timings: [],
      start_time: null,
      end_time: null,
      timeIncrement: 30,
    };
  },
  watch: {
    availabilityFormDialogue(val) {
      if (val == true) {
        if (this.aval) {
          this.weekdays = this.aval.weekdays;
          this.start_time = this.aval.start_time;
          this.end_time = this.aval.end_time;
        }
      }
    },
  },
  computed: {
    allWeekdays() {
      return this.$store.getters.getWeekdays.data;
    },
  },
  mixins: [trainer],
  methods: {
    close() {
      this.weekdays = [];
      this.start_time = null;
      this.end_time = null;
      this.$emit("loadAvail");
      this.$emit("close");
    },
    addOrUpdateTrainerCustomer() {
      this.showLoader("Loading");

      let formData = new FormData();
      formData.append("id", this.editId ?? null);
      formData.append("weekdays", this.weekdays);
      formData.append("start_time", this.start_time);
      formData.append("end_time", this.end_time);

      this.$http
        .post(
          `venues/trainers/availability/${this.trainerId}` +
            (this.editId ? "/availability/" + this.editId : ""),
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data; boundary=${form._boundary}",
            },
          }
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Configuration saved successfully.");
            this.weekdays = [];
            this.start_time = null;
            this.end_time = null;

            this.$emit("loadAvail");
          }

          this.$emit("close");
        })
        .catch((error) => {
          this.errorChecker(error);
        });
      // } else {
      //   this.showError("URL is mandatory");
      // }

      this.hideLoader();
    },
    getIcon(type, field) {
      let icon = "mdi-checkbox-blank-outline";

      if (type == "gameFormations") {
        if (this.facility[field].length == this.gameFormations().length) {
          icon = "mdi-close-box";
        }
        if (
          this.facility[field].length > 0 &&
          this.facility[field].length != this.gameFormations().length
        )
          icon = "mdi-minus-box";
      } else if (type == "weekdays") {
        if (this.facility[field].length == this.weekdays.length) {
          icon = "mdi-close-box";
        }
        if (
          this.facility[field].length > 0 &&
          this.facility[field].length != this.weekdays.length
        )
          icon = "mdi-minus-box";
      } else if (type == "rentalweekdays") {
        if (
          this.facility.facility_rentals[field].weekdays.length == this.weekdays.length
        ) {
          icon = "mdi-close-box";
        }
        if (
          this.facility.facility_rentals[field].weekdays.length > 0 &&
          this.facility.facility_rentals[field].weekdays.length != this.weekdays.length
        )
          icon = "mdi-minus-box";
      } else if (type == "trainerAvailability") {
        if (this.weekdays.length == this.allWeekdays.length) {
          icon = "mdi-close-box";
        }
        if (this.weekdays.length > 0 && this.weekdays.lengthh != this.allWeekdays.length)
          icon = "mdi-minus-box";
      }
      return icon;
    },
    toggle(type, field) {
      this.$nextTick(() => {
        if (type == "gameFormations") {
          if (this.facility[field].length == this.gameFormations().length) {
            this.facility[field] = [];
          } else {
            this.facility[field] = this.gameFormations().map((item) => item.id);
          }
        } else if (type == "weekdays") {
          if (this.facility[field].length == this.weekdays.length) {
            this.facility[field] = [];
          } else {
            this.facility[field] = this.weekdays.map((item) => item.bit_value);
          }
        } else if (type == "rentalweekdays") {
          if (
            this.facility.facility_rentals[field].weekdays.length == this.weekdays.length
          ) {
            this.facility.facility_rentals[field].weekdays = [];
          } else {
            this.facility.facility_rentals[field].weekdays = this.weekdays.map(
              (item) => item.bit_value
            );
          }
        } else if (type == "trainerAvailability") {
          if (this.weekdays.length == this.allWeekdays.length) {
            this.weekdays = [];
          } else {
            this.weekdays = this.allWeekdays.map((item) => item.bit_value);
          }
        }
      });
      this.$forceUpdate();
    },
    generateTiming() {
      let currentTime = moment("00:00:00", "HH:mm:ss");
      this.timings = [];
      this.timings.push({
        value: currentTime.format("HH:mm:ss"),
        text: currentTime.format("hh:mm a"),
      });
      let hour = 0;
      while (hour < 24) {
        currentTime = currentTime.add(this.timeIncrement, "minutes");
        let data = {
          value: currentTime.format("HH:mm:ss"),
          text: currentTime.format("hh:mm a"),
        };
        if (data.value == "00:00:00") {
          data.value = "23:59:59";
          this.timings.push(data);
          hour = 24;
        } else {
          this.timings.push(data);
          hour = currentTime.get("hours");
        }
      }
    },
  },
};
</script>

<style></style>

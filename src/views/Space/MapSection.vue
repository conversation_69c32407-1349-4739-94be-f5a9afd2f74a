<template>
  <div
    :class="{ 'section': true, 'selected': isSelected }"
    @mousedown="handleSectionMouseDown"
    @mouseup="handleSectionMouseUp"
    @mousemove="handleSectionMouseMove"
  >
    {{ section.name }}
  </div>
</template>
<script>
export default {
    props: {
       section: { type: Object, default: function() { return {} } },
    },  
    data() {
        return {    
        isDragging: false,
        dragStartX: 0,
        dragStartY: 0,
        };
    },
    methods: {
        handleDragStart(event) {
            console.log("handleDragStart");
            console.log(event)
        // Implement handling drag start event
        },
        handleDragMove(event) {
        // Implement handling drag move event
        console.log("handleDragMove");
            console.log(event)
        },
        handleDragEnd(event) {
        // Implement handling drag end event
        console.log("handleDragEnd");
            console.log(event)
        },
    },
};

</script>

<style scoped>
</style>

<template>
  <div>
    <v-form>
      <v-dialog v-model="showInviteParticipantsForm" width="80%" scrollable persistent max-width="1000px">
        <v-form ref="form" v-model="valid">
          <v-card>
            <v-card-title>
              {{ "Add Invitees" }}
            </v-card-title>
            <div class="d-flex justify-space-around font-bold text-base pa-4 text-neon bg-neon opacity-10">
              <div class="pitch">Event: {{ event_name }}</div>
              <div class="pitch">Service: {{ service }}</div>
            </div>
            <v-card-text>
              <v-card
                class="pa-4"
                outlined
              >
                <div class="font-semibold text-base font-16">Event Details</div>
                <v-divider></v-divider>

                <v-card flat class="d-flex">
                  <v-img
                      class="ground_Img"
                      :src="
                          image_path
                            ? s3BucketURL + image_path
                            : require('@/assets/images/default_images/event_default.jpg')
                        "
                      height="180"
                  >
                    <v-row
                        class="fill-height ma-0 hoverImage"
                        align="center"
                        justify="center"
                    >
                      <div
                          align="center"
                          justify="center"
                          class="white--text eventbox pa-5"
                          style="width: 50%"
                      >
                        <div class="pa-0 title">
                          {{ location }}
                        </div>
                        <div class="pa-0 title">
                          {{ date | dateformat }}
                        </div>
                        <div class="pa-0 title">
                          {{ start_time | timeFormat }} to
                          {{ end_time | timeFormat }}
                        </div>
                      </div>
                    </v-row>
                  </v-img>
                </v-card>

              </v-card>

              <div
                class="bordered bg-white pa-4 rounded-lg mt-4"
              >
                <v-row>
                  <v-col md="4">
                    <label for="">
                      Select Ticket
                    </label>
                    <v-autocomplete
                        v-model="selectedTicket"
                        required
                        return-object
                        :items="this.event.tickets"
                        item-value="event_ticket_id"
                        item-text="name"
                        outlined
                        background-color="#fff"
                        @change="selectTicket"
                        :rules="[(v) => !!v || 'Select a ticket'] "
                        dense
                        class="q-autocomplete shadow-0"
                        hide-details="auto"
                    ></v-autocomplete>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col md="12">
                    <v-radio-group
                        class="d-flex"
                        row
                        v-model="uploadSheet"
                    >
<!--                      <v-radio-->
<!--                          label="Add Invitees"-->
<!--                          color="red"-->
<!--                          :value="0"-->
<!--                          class="custom-radio"-->
<!--                      ></v-radio>-->
                      <v-radio
                          label="Upload Sheet"
                          color="red"
                          :value="1"
                          class="custom-radio"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                </v-row>
                <template  v-if="uploadSheet">
                  <v-row>
                      <v-col>
                        <label for="">
                            Upload Invitees Sheet
                        </label>
                        <v-file-input
                            background-color="#fff"
                            class="q-text-field shadow-0"
                            dense
                            hide-details="auto"
                            outlined
                            prepend-icon
                            v-model="event.participantsSheet"
                            prepend-inner-icon="mdi-paperclip"
                            :rules="[(v) => !!v || 'Excel sheet is required'] "
                        >
                        </v-file-input>
                      </v-col>
                    <v-col>
                      <v-btn class="blue-color white--text" text style="margin-top: 23px" @click="downloadSampleExcelFile()"
                      >Download Sample
                      </v-btn
                      >
                    </v-col>
                  </v-row>
                </template>
                <template  v-else>
                  <v-row>
                    <v-col>
                      <label for="">
                        First Name*
                      </label>
                      <v-text-field
                          :rules="[(v) => !!v || 'Event Name is required']"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          outlined
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <label for="">
                        Last Name*
                      </label>
                      <v-text-field
                          :rules="[(v) => !!v || 'Event Name is required']"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          outlined
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <label for="">
                        Email*
                      </label>
                      <v-text-field
                          :rules="[(v) => !!v || 'Event Name is required']"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          outlined
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <label for="">
                        Mobile No*
                      </label>
                      <v-text-field
                          :rules="[(v) => !!v || 'Event Name is required']"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          outlined
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <label for="">
                        Company*
                      </label>
                      <v-text-field
                          :rules="[(v) => !!v || 'Event Name is required']"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          outlined
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col>
                      <v-btn class="teal-color white--text" text
                      >Add Invitee
                      </v-btn
                      >
                    </v-col>
                  </v-row>
                </template>
              </div>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn class="blue-color white--text" text
                     @click="close()"
              >Close
              </v-btn
              >
              <v-btn class="teal-color white--text" text @click="saveInvites"
              >Done
              </v-btn
              >
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>
    </v-form>
    <capture-image
      :open="webcamDialog"
      @close="webcamDialog = false"
      @confirm="confirmImageCapture"
    />
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
  </div>
</template>
<script>
import CaptureImage from "@/components/Image/CaptureImage";
import moment from "moment";
import bookingFields from "@/mixins/eventBookingFieldValidation";

export default {
  props: {
    showInviteParticipantsForm: { type: Boolean },
    start_time: { type: String },
    end_time: { type: String },
    date: { type: String },
    facility_id: { type: Number },
    order_id: { type: Number },
    id: { type: Number, default: 0 },
    event_name: { type: String },
    venue_service_id: { type: Number },
    service: { type: String },
    event_id: { type: Number },
    location: { type: String },
    event_type_id: { type: Number },
    start_date: { type: String },
    end_date: { type: String },
    image_path: { type: String },
    participants: { type: Number, default: 0 },
    sales: { type: Number, default: 0 },
  },
  components: {
    CaptureImage,
  },
  mixins: [bookingFields],
  data() {
    return {
      uploadSheet:1,
      ticketTypes: {},
      event: {},
      participant_count: 1,
      bookingForm: { price: 0, opt_marketing: false,total_price: 0 },
      event_customers: [],
      selectedTicket: {},
      productCategoryId: null,
      webcamDialog: false,
      endTimes: [],
      categories: [],
      companies: [],
      valid: false,
      repeatData: {
        available: [],
        excluded: [],
      },
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      isEmiratesIdCheck: false,
      displayed_participant: 1,
    };
  },
  watch: {
    showInviteParticipantsForm(val) {
      if (val === true) {
        this.bookingForm = { price: 0,total_price: 0 };
        this.selectedTicket = {};
        this.getEventDetails();
        this.$store.dispatch("loadPromotions", {
          date: this.date,
          product_type: "Event",
        });
        if (this.$refs.form) {
          this.$refs.form.resetValidation();
        }
        if (!this.$store.getters.getConfigurationStatus(this.venue_service_id)) {
          this.$store.dispatch("loadConfigurationsByVenueServiceId", this.venue_service_id);
        }
      }
    },
  },
  mounted() {
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$store.getters.getIdProofTypes.status == false) {
      this.$store.dispatch("loadIDProofTypes");
    }
    if (this.$store.getters.getPaymentMethods.status == false) {
      this.$store.dispatch("loadPaymentMethods", "normal");
    }
    if (this.$store.getters.getTags.status == false) {
      this.$store.dispatch("loadTags");
    }

    this.checkPermission = this.checkExportPermission(
      this.$modules.salesTeam.dashboard.slug
    );
    if (this.checkPermission) {
      this.$store.dispatch("loadSalesTeams", "Event");
      this.$forceUpdate();
    }

    if (this.$store.getters.getCustomerAgeRangeConfiguration.status == false) {
      this.$store.dispatch("LoadCustomerAgeRangeConfiguration");
    }
    if (this.$store.getters.getCustomerAgeRange.status == false) {
      this.$store.dispatch("LoadCustomerAgeRange");
    }
    this.setFieldConfigurations();

    console.log(this.event.tickets)
  },
  computed: {
    venueServiceConfiguration() {
      return this.$store.getters.getConfigurationByVenueServiceId(
          this.venue_service_id
      );
    },
    customerAgeRange() {
      return this.$store.getters.getCustomerAgeRangeConfiguration.data;
    },
    ageRanges() {
      return this.$store.getters.getCustomerAgeRange.data;
    },
    tags() {
      return this.$store.getters.getTags.data;
    },
    countries() {
      return this.$store.getters.getCountries.data;
    },
    promotions() {
      return this.$store.getters.getPromotions.data;
    },
    idProofTypes() {
      return this.$store.getters.getIdProofTypes.data.filter(i => !['Scanned document','Unified ID'].includes(i.name));
    },
    salesTeams() {
      return this.$store.getters.getSalesTeams.data;
    },
  },
  methods: {
    getTotalPrice(){
      let total = 0;
      if(this.bookingForm.products) {
        if(this.bookingForm.discount){
          total = this.bookingForm.total_price;
        }else{
          // this.bookingForm.products.forEach((p) => {
          //   total = p.price + (p.actual_tax * p.quantity);
          // });
          // total = this.bookingForm.total_price;
          if(this.bookingForm.price){
            let t = 0;
            this.bookingForm.products.forEach((p) => {
               t =  (p.actual_tax * p.quantity);
            });
            total = this.bookingForm.price + t;
          }
        }
      }
      return  total;
    },
    close() {
      this.event_customers = [];
      this.participant_count = 1;
      this.displayed_participant = 1;
      this.$refs.form.resetValidation();
      this.$emit("close");
    },

    getOrderDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/events/bookings/${this.order_id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.hideLoader();
            const data = response.data.data;
            // let customers = [];

            this.$set(
              this.bookingForm,
              "name",
              data.customer.first_name +
                `${
                  data.customer.last_name ? " " + data.customer.last_name : ""
                }`
            );
            data.customer.customer_id = data.customer.id;
            // this.setCustomerData(data.customer);

            if (data.customer) {
              this.displayed_participant = 1;
              this.bookingForm.name = `${data.customer.first_name}${
                data.customer.last_name ? " " + data.customer.last_name : ""
              }`;
              this.bookingForm.first_name = data.customer.first_name;
              this.bookingForm.last_name = data.customer.last_name;
              this.bookingForm.mobile = data.customer.customer_contact.mobile;
              this.bookingForm.email = data.customer.customer_contact.email;
              this.bookingForm.gender = data.customer.gender;
              this.bookingForm.dob = data.customer.dob;
              this.bookingForm.age_group = data.customer.age_group;
              this.bookingForm.country_id = data.customer.country_id;
              this.bookingForm.opt_marketing =
                data.customer.opt_marketing == 1 ? true : false;
              if (
                this.bookingForm.customer_type == "member" &&
                !this.bookingForm.card_number
              ) {
                this.searchMember(
                  data.customer.mobile,
                  data.customer.customer_id,
                  data.customer.first_name,
                  data.customer.last_name
                );
              }
            }

            if (data.event_booking) {
              this.bookingForm.id_proof_number =
                data.event_booking.id_proof_number;
              this.bookingForm.id_proof_type_id =
                data.event_booking.id_proof_type_id;
              this.bookingForm.id_proof_number =
                data.event_booking.id_proof_number;
              this.bookingForm.id_proof_file_name =
                data.event_booking.id_proof_file_name;
              this.bookingForm.customer_type = data.event_booking.customer_type;
            }

            if (data.discount != null) {
              if (data.discount.promotion != null) {
                this.bookingForm.promotion_code =
                  data.discount.promotion.promotion_code;
              }
              if (data.discount.member != null) {
                this.bookingForm.card_number = data.discount.member.card_number;
              }
              this.bookingForm.discount = data.discount;
            }
            this.bookingForm.price = data.price;
            this.event_customers.push(this.bookingForm);

            data.event_group_customers.forEach((item) => {
              this.event_customers.push({
                name: `${item.customer.first_name}${
                  item.customer.last_name ? " " + item.customer.last_name : ""
                }`,
                first_name: item.customer.first_name,
                last_name: item.customer.first_name,
                mobile: item.customer.customer_contact.mobile,
                email: item.customer.customer_contact.email,
                gender: item.customer.gender,
                dob: item.customer.dob,
                age_group: item.customer.age_group,
                country_id: item.customer.country_id,
              });
              this.displayed_participant++;
            });

            data.items.forEach((item) => {
              this.bookingForm.products.push({
                order_item_id: item.id,
                price: item.price,
                name: item.name,
                quantity: item.quantity,
                product_id: item.product_id,
                product_type_id: item.product_type_id,
                event_ticket_id: item.event_ticket_id,
                event_id: this.event_id,
              });
            });
            this.calculateMaxParticipants();

            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getEventDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/events/${this.id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.editMode = true;
            let eventData = response.data.data;
            this.event = eventData;
            if (eventData.lineups.length == 0) {
              this.event.lineups = [{}];
            }

            if (eventData.tickets.length == 0) {
              this.event.tickets = [{}];
            }

            this.bookingForm.products = [];
            if (this.order_id) {
              this.getOrderDetails();
            }
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    setCardData(data, index) {
      this.setCustomerData(data, index);
    },
    setCustomerData(data, index = 0) {
      //   console.log(data);
      if (data.mobile && data.first_name && data.customer_id) {
        this.isEmiratesIdCheck = false;
        if (!this.bookingForm.id) {
          this.bookingForm.promotion_code = null;
        }
        if(index == 0) {
          this.searchMember(
              data.mobile,
              data.customer_id,
              data.first_name,
              data.last_name,
              index
          );
        }
      }else{
        this.clearCardAndBenefits(index);
      }
      if (!data.customer_id) {
        this.$set(this.event_customers[index], "customer_id", null);
      }
      if (!data.name && data.first_name) {
        this.$set(this.event_customers[index], "name", data.first_name);
      }
      if (
        this.event_customers[index].customer_id &&
        !data.customer_id &&
        this.event_customers[index].name != data.name &&
        this.event_customers[index].mobile != data.mobile
      ) {
        this.$set(this.event_customers[index], "mobile", null);
        this.event_customers[index].search = null;
        this.event_customers[index].nameSearch = null;
        this.$set(this.event_customers[index], "email", null);
        this.$set(this.event_customers[index], "gender", null);
        this.$set(this.event_customers[index], "name", null);
        this.$set(this.event_customers[index], "customer_id", null);
        this.$set(this.event_customers[index], "first_name", null);
        this.$set(this.event_customers[index], "image_path", null);
        this.$set(this.event_customers[index], "dob", null);
        this.$set(this.event_customers[index], "age_group", null);
        this.$set(this.event_customers[index], "country_id", null);
        this.$set(this.event_customers[index], "last_name", null);
        this.$set(this.event_customers[index], "opt_marketing", false);
        this.$set(this.event_customers[index], "id_proof_type_id", null);
        this.$set(this.event_customers[index], "id_proof_number", null);
        this.$set(this.bookingForm, "customer_tag", null);
        this.$set(this.event_customers[index], "id_proof_path", null);
        this.$forceUpdate();
      }
      if (data.mobile)
        this.$set(this.event_customers[index], "mobile", data.mobile);
      if (data.email)
        this.$set(this.event_customers[index], "email", data.email);
      if (data.country_id) {
        this.$set(this.event_customers[index], "country_id", data.country_id);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.event_customers[index], "country_id", null);
        }
      }
      if (data.customer_tag) {
        // console.log(data.customer_tag);
        this.$set(
          this.event_customers[index],
          "customer_tag",
          data.customer_tag
        );
      } else {
        this.$set(this.event_customers[index], "customer_tag", null);
      }
      if (data.gender) {
        this.$set(this.event_customers[index], "gender", data.gender);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.event_customers[index], "gender", null);
        }
      }
      if (data.dob) {
        this.$set(this.event_customers[index], "dob", data.dob);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.event_customers[index], "dob", null);
        }
      }
      // console.log('data');
      // console.log(data);
      if (data.age_group) {
        this.$set(this.event_customers[index], "age_group", data.age_group);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.event_customers[index], "age_group", null);
        }
      }
      if (data.name) this.$set(this.event_customers[index], "name", data.name);
      if (data.last_name) {
        this.$set(this.event_customers[index], "last_name", data.last_name);
      } else {
        this.$set(this.event_customers[index], "last_name", null);
      }
      if (data.first_name)
        this.$set(this.event_customers[index], "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.event_customers[index], "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.event_customers[index], "image_path", data.image_path);
      } else {
        this.$set(this.event_customers[index], "image_path", null);
      }
      if (data.id_proof_type_id) {
        this.$set(
          this.event_customers[index],
          "id_proof_type_id",
          data.id_proof_type_id
        );
      }
      if (data.id_proof_path) {
        this.$set(
          this.event_customers[index],
          "id_proof_path",
          data.id_proof_path
        );
      } else {
        if (!this.isEmiratesIdCheck && data.customer_id) {
          this.$set(this.event_customers[index], "id_proof_path", null);
        }
      }
      if (data.id_proof) {
        this.$set(this.event_customers[index], "id_proof", data.id_proof);
      } else {
        if (!this.isEmiratesIdCheck && data.customer_id) {
          this.$set(this.event_customers[index], "id_proof", null);
        }
      }
      if (data.id_proof_number) {
        this.$set(
          this.event_customers[index],
          "id_proof_number",
          data.id_proof_number
        );
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.event_customers[index], "opt_marketing", true);
        } else {
          this.$set(this.event_customers[index], "opt_marketing", false);
        }
      }
      if (data.customer_documents) {
        this.event_customers[index].customer_documents =
          data.customer_documents;
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_type_id
        ) {
          this.$set(
            this.event_customers[index],
            "id_proof_type_id",
            data.customer_documents[0].id_proof_type_id
          );
        }
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_number
        ) {
          this.$set(
            this.event_customers[index],
            "id_proof_number",
            data.customer_documents[0].id_proof_number
          );
        }
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_path
        ) {
          this.$set(
            this.event_customers[index],
            "id_proof_path",
            data.customer_documents[0].id_proof_path
          );
        }
      } else {
        if (data.customer_id) {
          this.event_customers[index].customer_documents = [];
        }
      }
      this.$forceUpdate();
    },
    setMemberData(data,index = null) {
      this.bookingForm.customer_type = "member";
      this.setCustomerData(data,index);
      if (index === null) {
        this.$set(this.bookingForm, "card_number", data.card_number);
        this.$set(this.bookingForm, "membership_id", data.membership_id);
      } else {
        this.$set(
          this.event_customers[index],
          "card_number",
          data.card_number
        );
        this.$set(
          this.event_customers[index],
          "membership_id",
          data.membership_id
        );
      }
      this.verifyBenefit("membership");
    },
    setCustomerName(data) {
      this.$set(this.bookingForm, "last_name", data.last_name);
      this.$set(this.bookingForm, "first_name", data.first_name);
    },
    confirmImageCapture(image) {
      image.name = this.bookingForm.name
        ? this.bookingForm.name + "_" + moment().format("YYYYMMDDHHSS")
        : "user_image_" + moment().format("YYYYMMDDHHSS");
      this.bookingForm.image = image;
      this.webcamDialog = false;
    },

    selectTicket() {
      if (
        this.selectedTicket.quantity == 0 ||
        this.selectedTicket.quantity == this.selectedTicket.participants ||
        this.selectedTicket.quantity < this.selectedTicket.participants
      ) {
        this.showError("Tickets not available");
        this.selectedTicket = {};
        return;
      }
      this.selectedTicket.ticketQnt = 1;
      this.$forceUpdate();

      // let currentTicket = this.event.tickets.findIndex(
      //   (x) => x.product_id == this.selectedTicket.product_id
      // );
      // console.log(this.event.tickets[currentTicket]);
      // this.participant_count = this.event.tickets[currentTicket].participant_count;
    },

    quantityCheck() {
      if (
        this.selectedTicket.ticketQnt >
        this.selectedTicket.quantity - this.selectedTicket.participants
      ) {
        this.showError("Quantity larger than available tickets");
      }
    },

    downloadSampleExcelFile() {
      this.openFile("import/invite-customers.xlsx");
    },

    carosal(direction) {
      if (direction == "left")
        this.event.lineups.unshift(this.event.lineups.pop());
      else this.event.lineups.push(this.event.lineups.shift());
    },

    calculateMaxParticipants() {
      let old = this.participant_count;
      let max = this.bookingForm.products.reduce(
        (acc, num) =>
          acc +
          parseInt(num.quantity) *
            this.findTicketParticipantCount(num.product_id),
        0
      );
      // console.log("max");
      // console.log(max);
      if (old > max && max < this.displayed_participant) {
        this.displayed_participant = max;
      }
      // console.log("this.displayed_participant");
      // console.log(this.displayed_participant);
      this.participant_count = max;
      // console.log("this.participant_count");
      // console.log(this.participant_count);
    },
    findTicketParticipantCount(productId) {
      let currentTicket = this.event.tickets.findIndex(
        (x) => x.product_id == productId
      );
      // console.log("each");
      // console.log(this.event.tickets[currentTicket].participant_count);
      return parseInt(this.event.tickets[currentTicket].participant_count ?? 0);
    },
    refreshCustomers() {
      this.event_customers.splice(this.participant_count);
      //   this.isEmiratesIdCheck.splice(this.participant_count);
    },
    addProduct() {
      if (
        this.selectedTicket.ticketQnt >
        this.selectedTicket.quantity - this.selectedTicket.participants
      ) {
        this.showError("Quantity larger than available tickets");
        return;
      }
      // if (this.bookingForm.discount) {
      //   this.clearBenefit(0);
      // }
      let quantity = this.selectedTicket.ticketQnt
        ? parseInt(this.selectedTicket.ticketQnt)
        : 1;

      if (this.selectedTicket.event_ticket_id == null) {
        this.showError("Please add product");
        return;
      }
      let price = this.selectedTicket.price;
      let taxAmount = this.selectedTicket.tax_amount;
      if (
        this.bookingForm.products.length > 0 &&
        this.bookingForm.products.find(
          (x) => x.product_id == this.selectedTicket.product_id
        )
      ) {
        let index = this.bookingForm.products.findIndex(
          (x) => x.product_id == this.selectedTicket.product_id
        );
        this.bookingForm.products[index].quantity = parseInt(this.bookingForm.products[index].quantity) + parseInt(quantity);
        this.bookingForm.products[index].price = price;
        this.bookingForm.products[index].actual_tax = taxAmount;
      } else {
        // let currentTicket = this.event.tickets.findIndex(
        //   (x) => x.product_id == this.selectedTicket.product_id
        // );
        // this.participant_count =
        //   this.event.tickets[currentTicket].participant_count * parseInt(quantity);
        // console.log(this.event_customers.length);
        // if (this.event_customers.length > 0) {
        //   this.event_customers = this.event_customers.splice(1);
        // } else {
        //   this.event_customers = [];
        // }

        //check if participant count is less than current participants, then remove that many participants
        // console.log(this.event_customers.length);
        // // else {
        if (this.event_customers.length == 0) {
          this.event_customers.push({
            customer_type: null,
            member: null,
            company_id: null,
            company_sale_id: null,
            opt_marketing: null,
            mobile: null,
            name: null,
            email: null,
            nameSearch: null,
            customer_id: null,
            gender: null,
            country_id: null,
            id_proof_type_id: null,
            id_proof_number: null,
            id_proof: null,
            id_proof_path: null,
            image: null,
          });
        }
        // this.isEmiratesIdCheck = [];
        // this.isEmiratesIdCheck.push(false);
        // }

        this.bookingForm.products.push({
          price: price,
          event_id: this.event_id,
          name: this.selectedTicket.name,
          quantity: quantity,
          product_id: this.selectedTicket.product_id,
          event_ticket_id: this.selectedTicket.event_ticket_id,
          product_type_id: this.selectedTicket.product_type_id,
          tax: this.selectedTicket.tax_amount,
          actual_tax: taxAmount
        });
      }

      let ticketIndex = this.event.tickets.findIndex(
        (x) => x.product_id == this.selectedTicket.product_id
      );

      this.event.tickets[ticketIndex].participants += parseInt(quantity);
      this.bookingForm.price = 0;
      this.bookingForm.products.forEach( (p) => {
        this.bookingForm.price += p.price * p.quantity;
      })


      this.selectedTicket = {};
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      this.calculateMaxParticipants();
      //   this.refreshCustomers();
      this.$forceUpdate();
    },
    addCustomer() {
      if (this.displayed_participant < this.participant_count) {
        this.event_customers.push({
          customer_type: "normal",
          member: null,
          company_id: null,
          company_sale_id: null,
          opt_marketing: null,
          mobile: null,
          name: null,
          email: null,
          nameSearch: null,
          customer_id: null,
          gender: null,
          country_id: null,
          id_proof_type_id: null,
          id_proof_number: null,
          id_proof: null,
          id_proof_path: null,
          image: null,
        });
        this.displayed_participant++;
        this.$forceUpdate();
        if (this.venueServiceConfiguration.auto_fill_customers) {
          this.autoFillCustomer();
        }
      }

      //   this.isEmiratesIdCheck.push(false);
    },
    autoFillCustomer() {
      if (!this.event_customers) {
        return;
      }
      let index =
          this.event_customers.length > 0
              ? this.event_customers.length - 1
              : null;
      if (index >= 0) {
        let data = {
          customer_id: null,
          name: this.event_customers[0].name + " Guest #" + (index),
          first_name: this.event_customers[0].name,
          last_name:" Guest #" + (index),
          mobile: this.event_customers[0].mobile,
          email: this.event_customers[0].email,
        };
        this.setCustomerData(data, index);
      }
    },


    removeProduct(index) {
      let data = this.bookingForm.products[index];
      this.bookingForm.products.splice(index, 1);
      this.bookingForm.price -= data.price;
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      if (this.bookingForm.products.length == 0) {
        this.clearBenefit();
        this.bookingForm.promotion_code = null;
        this.bookingForm.price = 0;
        if(this.bookingForm.total_price){
          this.bookingForm.total_price = 0;
        }
      }
      //   this.event_customers = [];
      //   this.participant_count = 1;
      //   this.displayed_participant = 1;
      this.calculateMaxParticipants();
      this.refreshCustomers();
      this.$forceUpdate();
    },
    verifyBenefit(type,index = 0) {
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add at least one product");
        return;
      }
      if(index != 0){
        return;
      }
      this.clearBenefit(index);
      let data = {
        products: [],
      };
      if (type == "promotion" && index === 0) {
        data.promotion_code =  this.bookingForm.promotion_code;
        if (data.promotion_code === null) {
          this.hideLoader();
          this.clearBenefit(index);
          return;
        }
      } else if(type == "membership" && index === 0){
        data.card_number = this.event_customers[0].card_number;
      }
      if(this.event_customers[index] && this.event_customers[index].mobile){
        data.mobile = this.event_customers[index].mobile;
      }

      if (this.bookingForm.discount) {
        data.products = [];
        this.bookingForm.products.forEach((product) => {
          let pdata = product;
          if (product.discount) {
            pdata.price = product.discount.actual_price;
            delete pdata.discount;
          } else {
            if (product.product_price) {
              pdata.price = product.product_price;
            }
          }
          data.products.push(pdata);
        });
      }else{
        data.products = this.bookingForm.products;
        data.products.forEach((element) => {
          if (element.product_price) {
            element.price = element.product_price;
          }
        });
      }

      this.showLoader('verify benefits');
      let url = "venues/benefits/verify";
      this.$http
        .post(url, data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const beniData = response.data.data;
            if(beniData.discount){
              this.bookingForm.discount = beniData.discount;
              this.bookingForm.price = beniData.price;
              this.bookingForm.total_price = beniData.total;
            }

             // beniData.products.map((d) => {
             //    d.event_id = parseInt(d.event_id);
             //    d.event_ticket_id = parseInt(d.event_ticket_id);
             //    d.product_id = parseInt(d.product_id);
             //    d.product_type_id = parseInt(d.product_type_id);
             //    d.quantity = parseInt(d.quantity);
             //    d.price = parseFloat(d.price);
             //    d.tax = parseFloat(d.actual_tax);
             //    d.actual_tax = parseFloat(d.actual_tax);
             //  })
              this.bookingForm.products = beniData.products;
              if (data.promotion_code && type == "promotion") {
                this.bookingForm.promotion_code = data.promotion_code;
              }
              if(beniData.discount) {
                this.showSuccess("Discount Applied");
              }
              this.hideLoader();
              this.$forceUpdate();
          }else{
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    clearCardAndBenefits(attCustomerIndex = null) {
        if (this.event_customers[attCustomerIndex]) {
          this.event_customers[attCustomerIndex].customer_type = this.event_customers[attCustomerIndex].customer_type? this.event_customers[attCustomerIndex].customer_type: 'normal';
          this.$set(this.event_customers[attCustomerIndex],"card_number",null);
          if (this.event_customers[attCustomerIndex].promotion_code == null) {
            this.clearBenefit(attCustomerIndex);
          }
        }
        this.$forceUpdate();
    },
    clearBenefit(index = 0) {
      if (this.bookingForm && this.bookingForm.discount && index === 0) {
        if (this.event_customers[index].customer_type != "member") {
          this.event_customers[index].card_number = null;
        }
        if(this.event_customers[index].promotion_code){
          this.event_customers[index].promotion_code = null;
        }
        if(this.bookingForm.discount) {
          this.bookingForm.price = this.bookingForm.discount.actual_price;
          this.bookingForm.total_price = this.bookingForm.discount.actual_total;
        }


        //this.bookingForm.price = this.bookingForm.discount.actual_price;
        this.bookingForm.products.forEach((product, ind) => {
          if (product.discount) {
            this.bookingForm.products[ind].price =  (product.discount.actual_price/product.quantity);
            this.bookingForm.products[ind].tax =  (product.discount.actual_tax/product.quantity);
            this.bookingForm.products[ind].actual_tax =  (product.actual_tax);
            // Removing the properties
            delete this.bookingForm.products[ind].total;
            delete this.bookingForm.products[ind].total_price;
            // delete this.bookingForm.products[ind].actual_tax;
            this.bookingForm.products[ind].discount = null;
          }
        });
        setTimeout(() => {
          if(this.bookingForm.promotion_code){
            this.bookingForm.promotion_code = null;
          }
          this.bookingForm.discount = null;
          this.$forceUpdate();
        });
      }
    },
    customerTypeChange(index = 0) {
      if (index === 0) {
        if (this.bookingForm.promotion_code == null) {
          this.clearBenefit(index);
        }
        if (this.event_customers[index].customer_type == "normal" && this.event_customers[index].card_number != null) {
          this.bookingForm.promotion_code = null;
          this.$set(this.event_customers[index], "card_number", null);
          this.$set(this.bookingForm, "card_number", null);
          this.clearBenefit(index);
        }
        if (this.bookingForm.customer_type == "member") {
          this.bookingForm.promotion_code = null;
          this.clearBenefit(index);
        }

      }
    },
    getActiveCompanySales() {
      this.$http
        .get(`venues/companies/active/sales`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.companies = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getCompanySales() {
      return this.bookingForm.company_id != null && this.companies.length
        ? this.companies.find((item) => item.id == this.bookingForm.company_id)
            .company_sale
        : [];
    },

    confirmSinglePayment() {
      let booking = this.bookingForm.repeat_dates.find(
        (item) => item.date == this.date
      );
      this.confirmModel = {
        id: booking.id,
        title: `Pay only for ${moment(booking.date).format("Do MMM YYYY")}?`,
        description: `Do you want to seperately invoice for ${moment(
          booking.date
        ).format(
          "Do MMM YYYY"
        )} from other repeat dates? By clicking <b>Yes</b> you can confirm creating new invoice`,
        type: "singleInvoice",
      };
    },
    cancelSingleDate() {
      let booking = this.bookingForm.repeat_dates.find(
        (item) => item.date == this.date
      );
      this.confirmModel = {
        id: booking.id,
        title: `Cancel ${moment(booking.date).format("Do MMM YYYY")} booking?`,
        description: `Do you want to cancel booking for ${moment(
          booking.date
        ).format(
          "Do MMM YYYY"
        )} from repeated dates? By clicking <b>Yes</b> you can confirm cancelation`,
        type: "singleCancel",
      };
    },
    confirmActions(data) {
      if (data.type == "customer") {
        this.event_customers.splice(data.id, 1);
        this.displayed_participant--;
        // this.isEmiratesIdCheck.splice(data.id, 1);
      }
      this.confirmModel.id = null;
      this.$forceUpdate();
    },
    deleteCustomer(index) {
      this.confirmModel = {
        id: index,
        title: "Do you want to delete this customer?",
        description:
          "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "customer",
      };
      this.$forceUpdate();
    },


    saveInvites() {
      if (!this.$refs.form.validate()) {
        this.showError("Fill all required fields");
        return;
      }
      console.log('-----------------------------------')
      console.log(this.selectedTicket)
      var formData = new FormData();
      formData.append("ticket_id", this.selectedTicket?.event_ticket_id);
      formData.append("event_id", this.event.id);
      if(this.event.participantsSheet){
        formData.append("excel_file", this.event.participantsSheet);
      }
      this.showLoader("Loading");
      this.$http
          .post(
              `venues/events/send-invites`,
              formData,
              {
                headers: {
                  "Content-Type": "multipart/form-data; boundary=${form._boundary}",
                },
              }
          )
          .then((response) => {
            this.hideLoader();
            if (response.status == 200 && response.data.status == true) {
              this.showSuccess(
                  "Success"
              );
              let data = response.data.data;
              console.log(data);
              this.$emit("close");
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },


    timeFormat(time) {
      return moment(time, "HH:mm:ss").format("hh:mm a");
    },

    confirmCancel() {
      this.confirmModel = {
        id: this.order_id,
        title: `Do you want cancel this booking?`,
        description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel",
      };
    },

    searchMember(mobile, id, first_name, last_name, index = 0) {
      console.log(id, first_name, last_name, mobile);
      this.isSearchLoading = true;
      let query = "";
      if (typeof id != "undefined" && id != null) {
        query = `field=id&search=${id}`;
      } else {
        if (typeof mobile != "undefined") {
          query = `field=mobile_number&search=${mobile}`;
        }
      }
      if (query != "") {
        this.$http
          .get(`venues/memberships/members/filters?${query}`)
          .then((response) => {
            if (response.status == 200) {
              let data = response.data.data;
              if (data.length > 0) {
                if (data[0].card_number) {
                  this.bookingForm.customer_type = "member";
                  this.$set(this.bookingForm,"card_number",data[0].card_number);
                  this.$set(this.bookingForm,"membership_id",data[0].membership_id);

                  this.event_customers[index].customer_type = "member";
                  this.$set( this.event_customers[index],"card_number",data[0].card_number);
                  this.$set(this.event_customers[index], "membership_id",data[0].membership_id);
                  this.$forceUpdate();
                  if (this.bookingForm && this.bookingForm.products.length > 0) {
                    this.verifyBenefit("membership");
                  }
                }else{
                  this.$set(this.event_customers[index],"customer_type","member");
                  this.$set(this.event_customers[index],"card_number",data[0].card_number);
                  this.$set(this.event_customers[index], "membership_id",data[0].membership_id);
                  this.$forceUpdate();

                  this.bookingForm.customer_type = "normal";
                  this.$set(this.bookingForm, "customer_type", "normal");
                  this.$set(this.bookingForm,"card_number",null);
                  this.$set(this.bookingForm, "membership_id", null);
                  if (this.event_customers[index].customer_type == "member") {
                    this.event_customers[index].customer_type = "normal";
                    this.event_customers[index].membership_id = null;
                  }
                  this.clearCardAndBenefits(index);
                }
              } else {
                  if (this.event_customers[index].customer_type == "member") {
                    this.event_customers[index].customer_type = "normal";
                    this.event_customers[index].membership_id = null;
                  }
                  this.bookingForm.member = null;
                  this.$set(this.bookingForm, "card_number", null);
                  this.clearCardAndBenefits(index);
              }
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      }
    },
    changeIdProofTypeId() {
      if (
        this.bookingForm.customer_documents &&
        this.bookingForm.customer_documents.length
      ) {
        let objType = this.bookingForm.customer_documents.find((x) => {
          return x.id_proof_type_id === this.bookingForm.id_proof_type_id;
        });
        if (typeof objType !== "undefined" && objType.id_proof_type_id) {
          this.bookingForm.id_proof_number = objType.id_proof_number;
          this.bookingForm.id_proof_path = objType.id_proof_path;
        } else {
          this.bookingForm.id_proof_number = null;
          this.bookingForm.id_proof_path = null;
        }
      } else {
        // console.log("document length 0");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$card-outlined-border-width: 3px;
</style>

<template>
  <div>
    <v-form>
      <v-dialog v-model="showeventForm" width="80%" scrollable persistent>
        <v-form ref="form" v-model="valid">
          <v-card>
            <v-card-title>
              {{ "Add Booking" }}
              <v-spacer></v-spacer>
              <div
                md="3"
                v-if="promotions.length > 0"
                style="margin-bottom: -20px"
              >
                <v-autocomplete
                  v-if="bookingForm.card_number == null"
                  :items="[
                    { name: 'None', promotion_code: null },
                    ...promotions,
                  ]"
                  item-text="name"
                  height="50"
                  item-value="promotion_code"
                  v-model="bookingForm.promotion_code"
                  background-color="rgb(206, 168, 0)"
                  outlined
                  @change="verifyBenefit('promotion')"
                  label="Promotions"
                >
                </v-autocomplete>
              </div>
            </v-card-title>
            <div class="d-flex justify-space-around headline pa-4">
              <div class="pitch">Event: {{ event_name }}</div>
              <div class="pitch">Service: {{ service }}</div>
              <div class="pitch">
                Price: {{ bookingForm.price | toCurrency }}
                <span
                  v-if="bookingForm.discount != null"
                  class="text-decoration-line-through"
                >
                  {{ bookingForm.discount.actual_price | toCurrency }}
                </span>
              </div>
            </div>

            <v-card-text class="form_bg">
              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4"
                outlined
              >
                <div class="titles">Event Details</div>
                <v-divider></v-divider>
                <v-row class="mt-1">
                  <v-col sm="12" md="12">
                    <v-card flat class="d-flex">
                      <v-img
                        class="ground_Img"
                        :src="
                          image_path
                            ? s3BucketURL + image_path
                            : require('@/assets/images/default_images/event_default.jpg')
                        "
                        height="180"
                      >
                        <v-row
                          class="fill-height ma-0 hoverImage"
                          align="center"
                          justify="center"
                        >
                          <div
                            align="center"
                            justify="center"
                            class="white--text eventbox pa-5"
                            style="width: 50%"
                          >
                            <div class="pa-0 title">
                              {{ location }}
                            </div>
                            <div class="pa-0 title">
                              {{ date | dateformat }}
                            </div>
                            <div
                              class="block pa-0 title"
                              v-for="(eTiming, index) in event.event_schedules"
                              :key="index"
                            >
                              <!-- {{ event.date | dateformat }} -->
                              {{ eTiming.start_date | dateformat }} -
                              {{ eTiming.start_time | timeFormat }} to
                              {{ eTiming.end_date | dateformat }} -
                              {{ eTiming.end_time | timeFormat }}
                            </div>
                            <v-row no-gutters>
                              <v-spacer></v-spacer>
                              <v-col col="12" md="5" sm="5">
                                <v-btn small block color="#008483" dark>
                                  Attendence:
                                  {{ participants | numberFormatter }}
                                </v-btn>
                              </v-col>
                              <v-spacer></v-spacer>
                              <v-col col="12" md="5" sm="5">
                                <v-btn small block color="#008483" dark>
                                  Sales:{{ Number(sales) | toCurrency }}
                                </v-btn>
                              </v-col>
                              <v-spacer></v-spacer>
                            </v-row>
                          </div>
                        </v-row>
                      </v-img>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row>
                  <v-col
                    class
                    sm="6"
                    v-if="event && event.lineups && event.lineups.length > 0"
                  >
                    <h4 class="text-center text--lighten-1 mb-1">Lineup</h4>

                    <v-card
                      class="pa-2 overflow-y-auto grey--text text--lighten-2"
                      style="min-height: 190px; max-height: 190px"
                      elevation="0"
                      tile
                      pa-0
                    >
                      <!-- ----------------- -->

                      <v-card-text>
                        <v-row no-gutters>
                          <v-col md="1">
                            <v-btn
                              @click="carosal('left')"
                              icon
                              x-large
                              style="margin-top: 50px; margin-left: -18px"
                            >
                              <v-icon x-large>mdi-chevron-left</v-icon>
                            </v-btn>
                          </v-col>
                          <v-col>
                            <v-row style="min-height: 150px; max-height: 150px">
                              <v-col md="6" v-if="event.lineups[0] != null">
                                <v-card max-width="300" title flat>
                                  <div md="12" align="center">
                                    <v-avatar size="120" right>
                                      <img
                                        v-if="
                                          event.lineups[0].image_path != null &&
                                          event.lineups[0].image_path != '0'
                                        "
                                        :src="
                                          s3BucketURL +
                                          event.lineups[0].image_path
                                        "
                                        :alt="event.lineups[0].name"
                                      />
                                      <img
                                        v-else
                                        src="@/assets/images/default_images/image_default.jpg"
                                      />
                                    </v-avatar>
                                  </div>
                                  <div class="text-center title">
                                    {{ event.lineups[0].name }}
                                  </div>
                                </v-card>
                              </v-col>
                              <v-col md="6" v-if="event.lineups[0] != null">
                                <v-card-actions class="justify-center">
                                  {{ event.lineups[0].description }}
                                </v-card-actions>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col md="1">
                            <v-spacer></v-spacer>
                            <v-btn
                              @click="carosal('right')"
                              icon
                              x-large
                              style="margin-top: 50px; margin-left: -5px"
                            >
                              <v-icon x-large>mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>

                  <v-col
                    cols="6"
                    md="6"
                    class="detail_box"
                    v-if="event && event.description != 'null'"
                  >
                    <h4 class="text-center text--lighten-1 mb-1">
                      Description
                    </h4>
                    <v-card
                      class="pa-2 overflow-y-auto text--lighten-2"
                      style="min-height: 190px; max-height: 190px"
                      elevation="0"
                      tile
                      pa-0
                      >{{ event.description }}</v-card
                    >
                  </v-col>
                </v-row>
                <v-row no-gutters>
                  <v-col md="8">
                    <div class="titles d-flex justify-space-between mt-5">
                      <div>Customer Details</div>
                    </div>
                  </v-col>
                  <!-- Uncomment to enable emirates ID reader -->
                  <v-col
                    md="4"
                    class="text-right d-flex flex-row-reverse"
                    v-if="!order_id"
                  >
                    <card-data-button
                      class="mt-2"
                      label="HID Omnikey"
                      @data="
                        (data) => {
                          setCardData(data);
                        }
                      "
                    ></card-data-button>
                    <card-reader-button
                      class="mt-2"
                      label="Samsotech Reader"
                      @data="
                        (data) => {
                          setCardData(data);
                        }
                      "
                    ></card-reader-button>
                  </v-col>
                </v-row>
                <v-divider></v-divider>
                <v-row no-gutters>
                  <v-col md="3">
                    <v-radio-group
                      v-model="bookingForm.customer_type"
                      class="d-flex"
                      row
                      @change="customerTypeChange"
                      mandatory
                      :readonly="order_id > 0"
                    >
                      <v-radio
                        label="Normal"
                        color="cyan"
                        value="normal"
                      ></v-radio>
                      <!-- <v-radio
                        label="Corporate"
                        color="cyan"
                        value="corporate"
                      ></v-radio> -->
                      <v-radio
                        label="Member"
                        color="cyan"
                        value="member"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col md="3" v-if="bookingForm.customer_type == 'member'">
                    <v-member-search
                      v-model="bookingForm.member"
                      @clear="clearBenefit"
                      :selected="bookingForm.card_number"
                      @updateCustomer="setMemberData"
                      class="mt-4"
                    >
                    </v-member-search>
                  </v-col>
                  <!-- <v-col md="3" v-if="bookingForm.customer_type == 'corporate'">
                    <v-autocomplete
                      class="mt-4"
                      label="Company Name"
                      :items="companies"
                      v-model="bookingForm.company_id"
                      item-text="name"
                      item-value="id"
                      outlined
                      background-color="#fff"
                      dense
                    >
                    </v-autocomplete>
                  </v-col>
                  <v-col md="2" v-if="bookingForm.customer_type == 'corporate'">
                    <v-autocomplete
                      :disabled="bookingForm.company_id == null"
                      :items="getCompanySales()"
                      label="Sale Order ID"
                      item-text="sale_seq_no"
                      item-value="id"
                      class="mt-4 ml-2"
                      v-model="bookingForm.company_sale_id"
                      outlined
                      background-color="#fff"
                      dense
                    >
                    </v-autocomplete>
                  </v-col> -->
                  <v-spacer></v-spacer>
                  <!--

                    Uncomment to enable emirates ID reader

                    <v-col md="1">
                    <card-data-button
                      class="mt-4"
                      @data="
                        (data) => {
                          setCardData(data);
                        }
                      "
                    ></card-data-button>
                  </v-col> -->
                  <v-col md="3">
                    <v-switch
                      style="float: right"
                      v-model="bookingForm.opt_marketing"
                      label="Opt In Marketing"
                    ></v-switch>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col sm="4" md="4">
                    <v-mobile-search
                      label="Mobile No*"
                      :selected="bookingForm.mobile"
                      @updateCustomer="setCustomerData"
                      v-model="bookingForm.search"
                    ></v-mobile-search>
                  </v-col>

                  <v-col sm="4" md="4">
                    <v-name-search
                      :selected="bookingForm.name"
                      :mobile="bookingForm.mobile"
                      :email="bookingForm.email"
                      @updateCustomer="setCustomerData"
                      v-model="bookingForm.nameSearch"
                    ></v-name-search>
                  </v-col>

                  <v-col sm="4" md="4">
                    <v-text-field
                      :readonly="bookingForm.customer_id != null"
                      outlined
                      background-color="#fff"
                      v-model="bookingForm.email"
                      :label="`Email${field.email.is_required ? '*' : ''}`"
                      :placeholder="`Email${
                        field.email.is_required ? '*' : ''
                      }`"
                      :rules="emailRule"
                    ></v-text-field>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.gender.is_visible">
                    <v-select
                      :items="['Male', 'Female']"
                      :placeholder="`Gender${
                        field.gender.is_required ? '*' : ''
                      }`"
                      :label="`Gender${field.gender.is_required ? '*' : ''}`"
                      outlined
                      v-model="bookingForm.gender"
                      :rules="genderRule"
                      background-color="#fff"
                      :menu-props="{ bottom: true, offsetY: true }"
                    ></v-select>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.dob.is_visible">
                    <date-of-birth
                      :placeholder="`Date of Birth${
                        field.dob.is_required ? '*' : ''
                      }`"
                      :label="`Date of Birth${
                        field.dob.is_required ? '*' : ''
                      }`"
                      :rules="dobRule()"
                      v-model="bookingForm.dob"
                    >
                    </date-of-birth>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.nationality.is_visible">
                    <v-autocomplete
                      :items="countries"
                      :hint="`Nationality${
                        field.nationality.is_required ? '*' : ''
                      }`"
                      :label="`Nationality${
                        field.nationality.is_required ? '*' : ''
                      }`"
                      :rules="nationalityRule"
                      item-value="id"
                      item-text="name"
                      outlined
                      v-model="bookingForm.country_id"
                      background-color="#fff"
                    ></v-autocomplete>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.idProof.is_visible">
                    <v-select
                      :hint="`ID Type${field.idProof.is_required ? '*' : ''}`"
                      :label="`ID Type${field.idProof.is_required ? '*' : ''}`"
                      :rules="idTypeRule"
                      outlined
                      item-value="id"
                      item-text="name"
                      :items="idProofTypes"
                      v-model="bookingForm.id_proof_type_id"
                      @change="changeIdProofTypeId"
                      background-color="#fff"
                      :menu-props="{ bottom: true, offsetY: true }"
                    ></v-select>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.idProof.is_visible">
                    <v-row no-gutters>
                      <v-col md="7">
                        <v-text-field
                          :hint="`ID Number${
                            field.idProof.is_required ? '*' : ''
                          }`"
                          :label="`ID Number${
                            field.idProof.is_required ? '*' : ''
                          }`"
                          :rules="idTypeRule"
                          class="text_field1"
                          outlined
                          v-model="bookingForm.id_proof_number"
                          background-color="#fff"
                        ></v-text-field>
                      </v-col>
                      <v-col md="5">
                        <v-file-input
                          v-model="bookingForm.id_proof"
                          class="text_field2"
                          :placeholder="`Select image${
                            field.image.is_required ? '*' : ''
                          }`"
                          :label="`ID Proof${
                            field.image.is_required ? '*' : ''
                          }`"
                          :rules="idProofRule"
                          prepend-icon=""
                          prepend-inner-icon="mdi-card-account-details"
                          background-color="#fff"
                          outlined
                        >
                          <template v-slot:prepend-inner>
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on }">
                                <v-icon
                                  color="cyan"
                                  v-if="bookingForm.id_proof_path"
                                  @click="openFile(bookingForm.id_proof_path)"
                                  v-on="on"
                                >
                                  mdi-download-box
                                </v-icon>
                                <v-icon v-else v-on="on">
                                  mdi-card-account-details
                                </v-icon>
                              </template>
                              <span v-if="bookingForm.id_proof_path"
                                >Download uploaded file</span
                              >
                              <span v-else>Upload ID Proof</span>
                            </v-tooltip>
                          </template>
                          <template v-slot:selection="{ index, text }">
                            <v-chip
                              v-if="index == 0"
                              color="cyan accent-4"
                              dark
                              label
                              small
                            >
                              <span style="width: 40px" class="text-truncate">{{
                                text
                              }}</span>
                            </v-chip>
                          </template>
                        </v-file-input>
                      </v-col>
                    </v-row>
                    <div style="margin-top: -110px"></div>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.image.is_visible">
                    <v-row no-gutters>
                      <v-col md="8">
                        <v-file-input
                          v-model="bookingForm.image"
                          class="text_field1"
                          prepend-icon=""
                          :label="`Customer Image${
                            field.image.is_required ? '*' : ''
                          }`"
                          :placeholder="`Image${
                            field.image.is_required ? '*' : ''
                          }`"
                          :rules="imageRule"
                          prepend-inner-icon="mdi-image"
                          background-color="#fff"
                          outlined
                          show-size
                        >
                          <template v-slot:selection="{ index, text }">
                            <v-chip
                              v-if="index == 0"
                              color="cyan accent-4"
                              dark
                              label
                              small
                            >
                              <span
                                style="width: 120px"
                                class="text-truncate"
                                >{{ text }}</span
                              >
                            </v-chip>
                          </template>
                        </v-file-input>
                      </v-col>
                      <v-col md="4">
                        <v-btn
                          large
                          block
                          style="background-color: #fff"
                          outlined
                          height="56"
                          color="blue-grey"
                          class="white--text text_field2"
                          @click="webcamDialog = true"
                        >
                          <v-icon dark>mdi-camera</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>

              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4 mt-2"
                outlined
              >
                <div class="titles">Ticket Details</div>
                <v-row>
                  <v-col md="12">
                    <v-chip
                      label
                      color="cyan"
                      dark
                      class="ml-2"
                      v-for="(product, index) in bookingForm.products"
                      :key="index"
                      :close="!product.rental && !product.order_item_id"
                      @click:close="removeProduct(index)"
                    >
                      <v-avatar left>
                        <view-image
                          :image="product.image_path"
                          :contain="false"
                        ></view-image>
                      </v-avatar>
                      {{ product.name }} x {{ product.quantity }} -

                      {{ product.price | toCurrency }}
                      <span
                        v-if="product.discount != null"
                        class="text-decoration-line-through pl-1"
                      >
                        {{ product.discount.actual_price | toCurrency }}</span
                      >
                    </v-chip>
                  </v-col>

                  <v-col md="4">
                    <v-autocomplete
                      v-model="selectedTicket"
                      label="Select Ticket"
                      required
                      return-object
                      :items="this.event.tickets"
                      item-value="event_ticket_id"
                      item-text="name"
                      outlined
                      background-color="#fff"
                      @change="selectTicket"
                    ></v-autocomplete>
                  </v-col>
                  <v-col md="3">
                    <v-text-field
                      label="Quantity"
                      outlined
                      background-color="#fff"
                      type="number"
                      min="1"
                      v-model="selectedTicket.ticketQnt"
                      @keyup="quantityCheck"
                    ></v-text-field>
                  </v-col>
                  <v-col md="3">
                    <v-text-field
                      label="Price"
                      :readonly="productCategoryId != -1"
                      outlined
                      background-color="#fff"
                      v-model="selectedTicket.price"
                      :suffix="currencyCode"
                    ></v-text-field>
                  </v-col>
                  <v-col md="2">
                    <v-btn
                      class="white--text blue-color"
                      height="56"
                      block
                      @disabled="
                        this.selectedTicket.quantity >
                          this.selectedTicket.totalQnt
                      "
                      @click="addProduct"
                      >Add</v-btn
                    >
                  </v-col>
                </v-row>
              </v-card>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                v-if="bookingForm.status_id == 5"
                @click="$emit('pay', bookingForm.order_id)"
                class="ma-2 white--text teal-color"
                text
              >
                Pay
              </v-btn>
              <v-btn
                v-if="bookingForm.status_id == 5"
                @click="confirmCancel"
                class="ma-2 white--text red-color"
                text
                >Cancel Booking
              </v-btn>
              <v-btn @click="close()" class="ma-2 white--text blue-color" text
                >Close
              </v-btn>
              <v-btn
                class="ma-2 white--text teal-color"
                @click="confirmBooking"
                text
              >
                Confirm Booking
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>
    </v-form>
    <capture-image
      :open="webcamDialog"
      @close="webcamDialog = false"
      @confirm="confirmImageCapture"
    />
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
  </div>
</template>
<script>
import VMemberSearch from "@/components/Customer/VMemberSearch";
import CaptureImage from "@/components/Image/CaptureImage";
import moment from "moment";
import bookingFields from "@/mixins/bookingFieldValidation";

export default {
  props: {
    showeventForm: { type: Boolean },
    start_time: { type: String },
    end_time: { type: String },
    date: { type: String },
    facility_id: { type: Number },
    order_id: { type: Number },
    id: { type: Number, default: 0 },
    event_name: { type: String },
    venue_service_id: { type: Number },
    service: { type: String },
    event_id: { type: Number },
    location: { type: String },
    event_type_id: { type: Number },
    start_date: { type: String },
    end_date: { type: String },
    image_path: { type: String },
    participants: { type: Number, default: 0 },
    sales: { type: [Number,String], default: 0 },
  },
  components: {
    CaptureImage,
    VMemberSearch,
  },
  mixins: [bookingFields],
  data() {
    return {
      event: {},
      bookingForm: { price: 0, opt_marketing: false },
      selectedTicket: {},
      productCategoryId: null,
      webcamDialog: false,
      endTimes: [],
      categories: [],
      companies: [],
      valid: false,
      repeatData: {
        available: [],
        excluded: [],
      },
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      isEmiratesIdCheck: false,
    };
  },
  watch: {
    showeventForm(val) {
      if (val === true) {
        this.bookingForm = { price: 0 };
        this.selectedTicket = {};
        this.getEventDetails();
        this.$store.dispatch("loadPromotions", {
          date: this.date,
          product_type: "Event",
        });
        if (this.$refs.form) {
          this.$refs.form.resetValidation();
        }
      }
    },
  },
  mounted() {
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$store.getters.getIdProofTypes.status == false) {
      this.$store.dispatch("loadIDProofTypes");
    }
    if (this.$store.getters.getPaymentMethods.status == false) {
      this.$store.dispatch("loadPaymentMethods", "normal");
    }
  },
  computed: {
    countries() {
      return this.$store.getters.getCountries.data;
    },
    promotions() {
      return this.$store.getters.getPromotions.data;
    },
    idProofTypes() {
      return this.$store.getters.getIdProofTypes.data.filter(i => !['Scanned document','Unified ID'].includes(i.name));
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },

    getOrderDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/events/bookings/${this.order_id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.hideLoader();
            const data = response.data.data;
            this.$set(
              this.bookingForm,
              "name",
              data.customer.first_name +
                `${
                  data.customer.last_name ? " " + data.customer.last_name : ""
                }`
            );
            data.customer.customer_id = data.customer.id;
            // this.setCustomerData(data.customer);

            if (data.customer) {
              this.bookingForm.name = `${data.customer.first_name}${
                data.customer.last_name ? " " + data.customer.last_name : ""
              }`;
              this.bookingForm.first_name = data.customer.first_name;
              this.bookingForm.last_name = data.customer.last_name;
              this.bookingForm.mobile = data.customer.customer_contact.mobile;
              this.bookingForm.email = data.customer.customer_contact.email;
              this.bookingForm.gender = data.customer.gender;
              this.bookingForm.dob = data.customer.dob;
              this.bookingForm.country_id = data.customer.country_id;
              this.bookingForm.opt_marketing =
                data.customer.opt_marketing == 1 ? true : false;
              if (
                this.bookingForm.customer_type == "member" &&
                !this.bookingForm.card_number
              ) {
                this.searchMember(
                  data.customer.mobile,
                  data.customer.customer_id,
                  data.customer.first_name,
                  data.customer.last_name
                );
              }
            }

            if (data.event_booking) {
              this.bookingForm.id_proof_number =
                data.event_booking.id_proof_number;
              this.bookingForm.id_proof_type_id =
                data.event_booking.id_proof_type_id;
              this.bookingForm.id_proof_number =
                data.event_booking.id_proof_number;
              this.bookingForm.id_proof_file_name =
                data.event_booking.id_proof_file_name;
              this.bookingForm.customer_type = data.event_booking.customer_type;
            }

            if (data.discount != null) {
              if (data.discount.promotion != null) {
                this.bookingForm.promotion_code =
                  data.discount.promotion.promotion_code;
              }
              if (data.discount.member != null) {
                this.bookingForm.card_number = data.discount.member.card_number;
              }
              this.bookingForm.discount = data.discount;
            }
            this.bookingForm.price = data.price;
            data.items.forEach((item) => {
              this.bookingForm.products.push({
                order_item_id: item.id,
                price: item.price,
                name: item.name,
                quantity: item.quantity,
                product_id: item.product_id,
                product_type_id: item.product_type_id,
                event_ticket_id: item.event_ticket_id,
                event_id: this.event_id,
              });
            });

            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    getEventDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/events/${this.id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.editMode = true;
            let eventData = response.data.data;
            if (eventData.event_timings) {
              eventData.event_schedules = eventData.event_timings;
            } else {
              eventData.event_schedules = [];
            }

            this.event = eventData;
            if (eventData.lineups.length == 0) {
              this.event.lineups = [{}];
            }

            if (eventData.tickets.length == 0) {
              this.event.tickets = [{}];
            }

            this.bookingForm.products = [];
            if (this.order_id) {
              this.getOrderDetails();
            }
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    setCardData(data) {
      this.setCustomerData(data);
      // console.log(data);
      // if (!data.customer_id) {
      //   this.$set(this.bookingForm, "customer_id", null);
      // }

      // if (!data.name && data.first_name) {
      //   this.$set(this.bookingForm, "name", data.first_name);
      // }

      // if (!data.customer_id && this.bookingForm.name != data.name) {
      //   this.$set(this.bookingForm, "mobile", null);
      //   this.bookingForm.search = null;
      //   this.bookingForm.nameSearch = null;
      //   this.$set(this.bookingForm, "email", null);
      //   this.$set(this.bookingForm, "gender", null);
      //   this.$set(this.bookingForm, "name", null);
      //   this.$set(this.bookingForm, "customer_id", null);
      //   this.$set(this.bookingForm, "first_name", null);
      //   this.$set(this.bookingForm, "image_path", null);
      //   this.$set(this.bookingForm, "dob", null);
      //   this.$set(this.bookingForm, "country_id", null);
      //   this.$set(this.bookingForm, "last_name", null);
      //   this.$set(this.bookingForm, "opt_marketing", false);
      //   this.$set(this.bookingForm, "id_proof_type_id", null);
      //   this.$set(this.bookingForm, "id_proof_number", null);
      //   this.$set(this.bookingForm, "id_proof_path", null);
      //   this.$forceUpdate();
      // }

      // if (data.mobile) {
      //   this.$set(this.bookingForm, "mobile", data.mobile);
      //   this.isEmiratesIdCheck = true;
      // }
      // if (data.email) this.$set(this.bookingForm, "email", data.email);
      // if (data.country_id) {
      //   this.$set(this.bookingForm, "country_id", data.country_id);
      // } else {
      //   this.$set(this.bookingForm, "country_id", null);
      // }
      // if (data.id_proof_type_id) {
      //   this.$set(this.bookingForm, "id_proof_type_id", data.id_proof_type_id);
      // }

      // if (data.id_proof_path) {
      //   this.$set(this.bookingForm, "id_proof_path", data.id_proof_path);
      // } else {
      //   if (!this.isEmiratesIdCheck && data.customer_id) {
      //     this.$set(this.bookingForm, "id_proof_path", null);
      //   }
      // }

      // if (data.id_proof_number) {
      //   this.$set(this.bookingForm, "id_proof_number", data.id_proof_number);
      // }

      // if (data.gender) {
      //   this.$set(this.bookingForm, "gender", data.gender);
      // } else {
      //   this.$set(this.bookingForm, "gender", null);
      // }
      // if (data.dob) {
      //   this.$set(this.bookingForm, "dob", data.dob);
      // } else {
      //   this.$set(this.bookingForm, "dob", null);
      // }

      // if (data.image) {
      //   this.$set(this.bookingForm, "image", data.image);
      // }

      // if (data.name) this.$set(this.bookingForm, "name", data.name);
      // if (data.last_name) {
      //   this.$set(this.bookingForm, "last_name", data.last_name);
      // } else {
      //   this.$set(this.bookingForm, "last_name", null);
      // }
      // if (data.first_name)
      //   this.$set(this.bookingForm, "first_name", data.first_name);
      // if (data.customer_id)
      //   this.$set(this.bookingForm, "customer_id", data.customer_id);
      // if (data.image_path) {
      //   this.$set(this.bookingForm, "image_path", data.image_path);
      // } else {
      //   this.$set(this.bookingForm, "image_path", null);
      // }
      // if (data.opt_marketing) {
      //   if (data.opt_marketing == 1) {
      //     this.$set(this.bookingForm, "opt_marketing", true);
      //   } else {
      //     this.$set(this.bookingForm, "opt_marketing", false);
      //   }
      // }
      // this.$forceUpdate();
    },
    setCustomerData(data) {
      if (data.isEmiratesIdCheck) {
        this.isEmiratesIdCheck = true;
      }
      if (data.mobile && data.first_name && data.customer_id) {
        this.isEmiratesIdCheck = false;
        this.searchMember(
          data.mobile,
          data.customer_id,
          data.first_name,
          data.last_name
        );
      }
      if (!data.customer_id) {
        this.$set(this.bookingForm, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.bookingForm, "name", data.first_name);
      }

      if (
        this.bookingForm.customer_id &&
        !data.customer_id &&
        this.bookingForm.name != data.name &&
        this.bookingForm.mobile != data.mobile
      ) {
        this.$set(this.bookingForm, "mobile", null);
        this.bookingForm.search = null;
        this.bookingForm.nameSearch = null;
        this.$set(this.bookingForm, "email", null);
        this.$set(this.bookingForm, "gender", null);
        this.$set(this.bookingForm, "name", null);
        this.$set(this.bookingForm, "customer_id", null);
        this.$set(this.bookingForm, "first_name", null);
        this.$set(this.bookingForm, "image_path", null);
        this.$set(this.bookingForm, "dob", null);
        this.$set(this.bookingForm, "country_id", null);
        this.$set(this.bookingForm, "last_name", null);
        this.$set(this.bookingForm, "opt_marketing", false);
        this.$set(this.bookingForm, "id_proof_type_id", null);
        this.$set(this.bookingForm, "id_proof_number", null);
        this.$set(this.bookingForm, "id_proof_path", null);
        this.$forceUpdate();
      }

      if (data.mobile) this.$set(this.bookingForm, "mobile", data.mobile);
      if (data.email) this.$set(this.bookingForm, "email", data.email);
      if (data.country_id) {
        this.$set(this.bookingForm, "country_id", data.country_id);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.bookingForm, "country_id", null);
        }
      }
      if (data.gender) {
        this.$set(this.bookingForm, "gender", data.gender);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.bookingForm, "gender", null);
        }
      }
      if (data.dob) {
        this.$set(this.bookingForm, "dob", data.dob);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.bookingForm, "dob", null);
        }
      }
      if (data.name) this.$set(this.bookingForm, "name", data.name);
      if (data.last_name) {
        this.$set(this.bookingForm, "last_name", data.last_name);
      } else {
        this.$set(this.bookingForm, "last_name", null);
      }
      if (data.first_name)
        this.$set(this.bookingForm, "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.bookingForm, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.bookingForm, "image_path", data.image_path);
      } else {
        this.$set(this.bookingForm, "image_path", null);
      }
      if (data.id_proof_type_id) {
        this.$set(this.bookingForm, "id_proof_type_id", data.id_proof_type_id);
      }

      if (data.id_proof_path) {
        this.$set(this.bookingForm, "id_proof_path", data.id_proof_path);
      } else {
        if (!this.isEmiratesIdCheck && data.customer_id) {
          this.$set(this.bookingForm, "id_proof_path", null);
        }
      }
      if (data.id_proof) {
        this.$set(this.bookingForm, "id_proof", data.id_proof);
      } else {
        if (!this.isEmiratesIdCheck && data.customer_id) {
          this.$set(this.bookingForm, "id_proof", null);
        }
      }
      if (data.id_proof_number) {
        this.$set(this.bookingForm, "id_proof_number", data.id_proof_number);
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.bookingForm, "opt_marketing", true);
        } else {
          this.$set(this.bookingForm, "opt_marketing", false);
        }
      }
      if (data.customer_documents) {
        this.bookingForm.customer_documents = data.customer_documents;
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_type_id
        ) {
          this.$set(
            this.bookingForm,
            "id_proof_type_id",
            data.customer_documents[0].id_proof_type_id
          );
        }
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_number
        ) {
          this.$set(
            this.bookingForm,
            "id_proof_number",
            data.customer_documents[0].id_proof_number
          );
        }
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_path
        ) {
          this.$set(
            this.bookingForm,
            "id_proof_path",
            data.customer_documents[0].id_proof_path
          );
        }
      } else {
        if (data.customer_id) {
          this.bookingForm.customer_documents = [];
        }
      }
      this.$forceUpdate();
    },
    setMemberData(data) {
      this.$set(this.bookingForm, "card_number", data.card_number);
      this.bookingForm.customer_type = "member";
      this.setCustomerData(data);
      this.verifyBenefit("membership");
    },
    setCustomerName(data) {
      this.$set(this.bookingForm, "last_name", data.last_name);
      this.$set(this.bookingForm, "first_name", data.first_name);
    },
    confirmImageCapture(image) {
      image.name = this.bookingForm.name
        ? this.bookingForm.name + "_" + moment().format("YYYYMMDDHHSS")
        : "user_image_" + moment().format("YYYYMMDDHHSS");
      this.bookingForm.image = image;
      this.webcamDialog = false;
    },

    selectTicket() {
      if (
        this.selectedTicket.quantity == 0 ||
        this.selectedTicket.quantity == this.selectedTicket.participants ||
        this.selectedTicket.quantity < this.selectedTicket.participants
      ) {
        this.showError("Tickets not available");
        this.selectedTicket = {};
        return;
      }
      this.selectedTicket.ticketQnt = 1;
      this.$forceUpdate();
    },

    quantityCheck() {
      if (
        this.selectedTicket.ticketQnt >
        this.selectedTicket.quantity - this.selectedTicket.participants
      ) {
        this.showError("Quantity larger than available tickets");
      }
    },

    carosal(direction) {
      if (direction == "left")
        this.event.lineups.unshift(this.event.lineups.pop());
      else this.event.lineups.push(this.event.lineups.shift());
    },

    addProduct() {
      if (
        this.selectedTicket.ticketQnt >
        this.selectedTicket.quantity - this.selectedTicket.participants
      ) {
        this.showError("Quantity larger than available tickets");
        return;
      }
      let quantity = this.selectedTicket.ticketQnt
        ? parseInt(this.selectedTicket.ticketQnt)
        : 1;

      if (this.selectedTicket.event_ticket_id == null) {
        this.showError("Please add product");
        return;
      }
      let price = this.selectedTicket.price * quantity;
      if (
        this.bookingForm.products.length > 0 &&
        this.bookingForm.products.find(
          (x) => x.product_id == this.selectedTicket.product_id
        )
      ) {
        let index = this.bookingForm.products.findIndex(
          (x) => x.product_id == this.selectedTicket.product_id
        );

        this.bookingForm.products[index].quantity += parseInt(quantity);
        this.bookingForm.products[index].price += price;
      } else {
        this.bookingForm.products.push({
          price: price,
          event_id: this.event_id,
          name: this.selectedTicket.name,
          quantity: quantity,
          product_id: this.selectedTicket.product_id,
          event_ticket_id: this.selectedTicket.event_ticket_id,
          product_type_id: this.selectedTicket.product_type_id,
          tax: this.selectedTicket.tax_amount,
        });
      }

      let ticketIndex = this.event.tickets.findIndex(
        (x) => x.product_id == this.selectedTicket.product_id
      );

      this.event.tickets[ticketIndex].participants += parseInt(quantity);

      this.bookingForm.price += price;
      this.selectedTicket = {};
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      this.$forceUpdate();
    },

    removeProduct(index) {
      let data = this.bookingForm.products[index];
      this.bookingForm.products.splice(index, 1);
      this.bookingForm.price -= data.price;
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      if (this.bookingForm.products.length == 0) {
        this.clearBenefit();
        this.bookingForm.promotion_code = null;
        this.bookingForm.price = 0;
      }
      this.$forceUpdate();
    },
    verifyBenefit(type) {
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add atleast one product");
        return;
      }
      let data = {
        products: [],
      };
      if (type == "promotion") {
        data.promotion_code = this.bookingForm.promotion_code;
        if (data.promotion_code == null) {
          this.clearBenefit();
          return;
        }
      } else {
        data.card_number = this.bookingForm.card_number;
      }
      if (this.bookingForm.mobile) {
        data.mobile = this.bookingForm.mobile;
      }
      if (this.bookingForm.discount) {
        data.products = [];
        this.bookingForm.products.forEach((product) => {
          let pdata = product;
          if (product.discount) {
            pdata.price = product.discount.actual_price;
            delete pdata.discount;
          }
          pdata.price = pdata.product_price;
          data.products.push(pdata);
        });
      } else {
        data.products = this.bookingForm.products;
        data.products.forEach((element) => {
          if (element) {
            element.price = element.price / element.quantity;
          }
        });
      }
      let url = "venues/benefits/verify";
      this.$http
        .post(url, data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.bookingForm.discount = data.discount;
            this.bookingForm.price = data.price;
            this.bookingForm.products = data.products;
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    clearBenefit() {
      if (this.bookingForm.discount) {
        this.bookingForm.price = this.bookingForm.discount.actual_price;
        this.bookingForm.products.forEach((product, index) => {
          if (product.discount) {
            this.bookingForm.products[index].price =
              product.discount.actual_price;
          }
          this.bookingForm.products[index].discount = null;
        });
        setTimeout(() => {
          this.bookingForm.discount = null;
          this.$forceUpdate();
        });
      }
    },
    customerTypeChange() {
      if (this.bookingForm.customer_type == "corporate") {
        this.bookingForm.promotion_code = null;
        this.clearBenefit();
        this.getActiveCompanySales();
      }
      if (this.bookingForm.promotion_code == null) {
        this.clearBenefit();
      }
      if (this.bookingForm.customer_type == "normal") {
        this.bookingForm.promotion_code = null;
        this.$set(this.bookingForm, "card_number", null);
        this.clearBenefit();
      }
      if (this.bookingForm.customer_type == "member") {
        this.bookingForm.promotion_code = null;
        this.clearBenefit();
      }
    },
    getActiveCompanySales() {
      this.$http
        .get(`venues/companies/active/sales`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.companies = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getCompanySales() {
      return this.bookingForm.company_id != null && this.companies.length
        ? this.companies.find((item) => item.id == this.bookingForm.company_id)
            .company_sale
        : [];
    },

    confirmSinglePayment() {
      let booking = this.bookingForm.repeat_dates.find(
        (item) => item.date == this.date
      );
      this.confirmModel = {
        id: booking.id,
        title: `Pay only for ${moment(booking.date).format("Do MMM YYYY")}?`,
        description: `Do you want to seperately invoice for ${moment(
          booking.date
        ).format(
          "Do MMM YYYY"
        )} from other repeat dates? By clicking <b>Yes</b> you can confirm creating new invoice`,
        type: "singleInvoice",
      };
    },
    cancelSingleDate() {
      let booking = this.bookingForm.repeat_dates.find(
        (item) => item.date == this.date
      );
      this.confirmModel = {
        id: booking.id,
        title: `Cancel ${moment(booking.date).format("Do MMM YYYY")} booking?`,
        description: `Do you want to cancel booking for ${moment(
          booking.date
        ).format(
          "Do MMM YYYY"
        )} from repeated dates? By clicking <b>Yes</b> you can confirm cancelation`,
        type: "singleCancel",
      };
    },
    confirmActions() {},
    confirmBooking() {
      var formData = new FormData();
      for (let key in this.bookingForm) {
        if (
          this.bookingForm[key] != null &&
          !["id", "products", "search", "member", "discount"].includes(key)
        ) {
          if (key == "first_name" || key == "last_name") {
            if (!this.bookingForm["first_name"]) {
              formData.append(`first_name`, this.bookingForm["last_name"]);
            } else {
              formData.append(`first_name`, this.bookingForm["first_name"]);
              if (
                this.bookingForm["last_name"] &&
                this.bookingForm["last_name"] != null
              ) {
                formData.append(`last_name`, this.bookingForm["last_name"]);
              }
            }
          } else {
            formData.append(`${key}`, this.bookingForm[key]);
          }
        }
      }

      this.bookingForm.products.forEach((element, index) => {
        for (let key in element) {
          if (key != "discount") {
            formData.append(`products[${index}][${key}]`, element[key]);
          }
        }
      });

      formData.append("event_id", this.event.id);
      formData.append("date", this.date);

      this.$http
        .post(
          `venues/events/booking${this.order_id ? "/" + this.order_id : ""}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data; boundary=${form._boundary}",
            },
          }
        )
        .then((response) => {
          this.hideLoader();
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess(
              this.order_id
                ? "Booking successfully updated"
                : "Booking successfully added"
            );
            let data = response.data.data;
            this.$emit("booked", data.id);
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    timeFormat(time) {
      return moment(time, "HH:mm:ss").format("hh:mm a");
    },

    confirmCancel() {
      this.confirmModel = {
        id: this.order_id,
        title: `Do you want cancel this booking?`,
        description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel",
      };
    },

    searchMember(mobile, id, first_name, last_name) {
      console.log(id, first_name, last_name, mobile);
      this.isSearchLoading = true;
      let query = "";
      if (typeof id != "undefined" && id != null) {
        query = `field=id&search=${id}`;
      } else {
        if (typeof mobile != "undefined") {
          query = `field=mobile_number&search=${mobile}`;
        }
      }
      if (query != "") {
        this.$http
          .get(`venues/memberships/members/filters?${query}`)
          .then((response) => {
            if (response.status == 200) {
              let data = response.data.data;
              if (data.length > 0) {
                this.bookingForm.customer_type = "member";
                this.$set(this.bookingForm, "card_number", data[0].card_number);
                this.$forceUpdate();
                if (this.bookingForm && this.bookingForm.products.length > 0) {
                  this.verifyBenefit("membership");
                }
              } else {
                this.bookingForm.member = null;
                this.bookingForm.customer_type =
                  this.bookingForm.customer_type == "corporate"
                    ? "corporate"
                    : null;
                this.$set(this.bookingForm, "card_number", null);
                if (this.bookingForm.promotion_code == null) {
                  this.clearBenefit();
                }
              }
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      }
    },
    changeIdProofTypeId() {
      if (
        this.bookingForm.customer_documents &&
        this.bookingForm.customer_documents.length
      ) {
        let objType = this.bookingForm.customer_documents.find((x) => {
          return x.id_proof_type_id === this.bookingForm.id_proof_type_id;
        });
        if (typeof objType !== "undefined" && objType.id_proof_type_id) {
          this.bookingForm.id_proof_number = objType.id_proof_number;
          this.bookingForm.id_proof_path = objType.id_proof_path;
        } else {
          this.bookingForm.id_proof_number = null;
          this.bookingForm.id_proof_path = null;
        }
      } else {
        // console.log("document length 0");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$card-outlined-border-width: 3px;
</style>

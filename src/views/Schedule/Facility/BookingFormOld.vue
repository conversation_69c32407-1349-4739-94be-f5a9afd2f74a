<template>
  <div>
    <v-form>
      <v-dialog v-model="showBookingForm" width="80%" scrollable persistent>
        <v-form ref="form" v-model="valid">
          <v-card>
            <v-card-title>
              {{ "Add Booking" }}
              <v-spacer></v-spacer>
              <div
                md="3"
                v-if="promotions.length > 0"
                style="margin-bottom: -20px"
              >
                <v-autocomplete
                  v-if="this.bookingForm.card_number == null"
                  :items="[
                    { name: 'None', promotion_code: null },
                    ...promotions,
                  ]"
                  item-text="name"
                  height="50"
                  item-value="promotion_code"
                  v-model="bookingForm.promotion_code"
                  background-color="rgb(206, 168, 0)"
                  outlined
                  @change="verifyBenefit('promotion')"
                  label="Promotions"
                  :readonly="disablePromotion"
                >
                </v-autocomplete>
              </div>
            </v-card-title>
            <div class="d-flex justify-space-around headline pa-4">
              <div class="pitch">Facility: {{ facility_name }}</div>
              <div class="pitch">Service: {{ service }}</div>
              <!-- <div class="pitch">
                Price (Pre Tax): {{ bookingForm.price | toCurrency }}
                <span
                  v-if="
                    bookingForm.discount != null &&
                    bookingForm.price != bookingForm.discount.actual_price
                  "
                  class="text-decoration-line-through"
                >
                  {{ bookingForm.discount.actual_price | toCurrency }}</span
                >
              </div> -->

              <div class="pitch">
                Price
                {{ bookingForm.total_price | toCurrency }}
                <span
                  v-if="
                    bookingForm.discount != null &&
                    bookingForm.price != bookingForm.discount.actual_price
                  "
                  class="text-decoration-line-through"
                >
                  {{ bookingForm.discount.actual_total | toCurrency }}</span
                >
              </div>
            </div>

            <v-card-text class="form_bg">
              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4"
                outlined
              >
                <v-row>
                  <v-col>
                    <div class="titles">Booking Details</div>
                  </v-col>
                  <v-spacer></v-spacer>
                  <v-col md="3"
                    ><v-switch
                      v-if="!id && perCapacity == 0"
                      style="margin-top: -15px; float: right"
                      v-model="bookingForm.repeat"
                      label="Repeat bookings"
                      @change="changeRepeatBookingSwitch"
                    ></v-switch
                  ></v-col>
                </v-row>

                <v-divider></v-divider>
                <v-row class="mt-0" v-if="!bookingForm.repeat || id > 0">
                  <v-col sm="4" md="4">
                    <date-field
                      label="Booking date"
                      outlined
                      background-color="#fff"
                      readonly
                      :value="bookingForm.date"
                      :dayName="true"
                      disabled
                    >
                    </date-field>
                  </v-col>
                  <v-col sm="4" md="4">
                    <v-text-field
                      label="Start Time"
                      hint="Start Time"
                      outlined
                      background-color="#fff"
                      readonly
                      :value="timeFormat(bookingForm.start_time)"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col sm="4" md="4">
                    <v-select
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      label="End time"
                      background-color="#fff"
                      item-text="formatted"
                      item-value="time"
                      :items="endTimes"
                      @change="getRentalProducts"
                      :readonly="perCapacity == 1 || bookingForm.repeat"
                      v-model="bookingForm.end_time"
                      :rules="[(v) => !!v || 'End time is required']"
                    >
                    </v-select>
                  </v-col>
                </v-row>
                <v-row v-if="bookingForm.repeat && !id">
                  <v-col
                    md="12"
                    v-for="(repeat, index) in $store.getters.getRepeats"
                    :key="`repeat_${index}`"
                  >
                    <repeat-booking
                      @repeat="setRepeatData"
                      @remove="removeRepeatRow"
                      :date="repeat.date"
                      :facilityId="facility_id"
                      :venueServiceId="venue_service_id"
                      :index="index"
                      :minBookingTime="minBookingTime"
                    ></repeat-booking>
                  </v-col>
                </v-row>
                <div class="add_btn" v-if="bookingForm.repeat && !id">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        color="blue-color"
                        fab
                        small
                        dark
                        @click="addRepeatRow"
                      >
                        <v-icon>mdi-plus-circle</v-icon>
                      </v-btn>
                    </template>
                    Add Repeat
                  </v-tooltip>
                </div>
              </v-card>

              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4 mt-2"
                outlined
              >
                <v-row no-gutters>
                  <v-col md="10">
                    <div class="titles d-flex justify-space-between mt-5">
                      <div>Customer Details</div>
                    </div>
                  </v-col>
                  <!-- Uncomment to enable emirates ID reader -->
                  <v-col md="2" class="text-right" v-if="!order_id">
                    <card-data-button
                      class="mt-2"
                      label="Emirates ID"
                      @data="
                        (data) => {
                          setCardData(data);
                        }
                      "
                    ></card-data-button>
                  </v-col>
                </v-row>
                <v-divider></v-divider>
                <v-row no-gutters>
                  <v-col md="3">
                    <v-radio-group
                      v-model="bookingForm.customer_type"
                      class="d-flex"
                      row
                      @change="customerTypeChange"
                      mandatory
                      :readonly="id > 0"
                    >
                      <v-radio
                        label="Normal"
                        color="cyan"
                        value="normal"
                      ></v-radio>
                      <v-radio
                        label="Corporate"
                        color="cyan"
                        value="corporate"
                      ></v-radio>
                      <v-radio
                        label="Member"
                        color="cyan"
                        value="member"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col md="3" v-if="bookingForm.customer_type == 'member'">
                    <v-member-search
                      v-model="bookingForm.member"
                      @clear="clearBenefit"
                      :selected="bookingForm.card_number"
                      @updateCustomer="setMemberData"
                      class="mt-4"
                    >
                    </v-member-search>
                  </v-col>
                  <v-col md="3" v-if="bookingForm.customer_type == 'corporate'">
                    <v-autocomplete
                      class="mt-4"
                      label="Company Name"
                      :items="companies"
                      v-model="bookingForm.company_id"
                      item-text="name"
                      item-value="id"
                      outlined
                      background-color="#fff"
                      dense
                    >
                    </v-autocomplete>
                  </v-col>
                  <v-col md="2" v-if="bookingForm.customer_type == 'corporate'">
                    <v-autocomplete
                      :disabled="bookingForm.company_id == null"
                      :items="getCompanySales()"
                      label="Sale Order ID"
                      item-text="sale_seq_no"
                      item-value="id"
                      class="mt-4 ml-2"
                      v-model="bookingForm.company_sale_id"
                      outlined
                      background-color="#fff"
                      dense
                    >
                    </v-autocomplete>
                  </v-col>
                  <v-spacer></v-spacer>
                  <v-col md="3">
                    <v-switch
                      style="float: right"
                      v-model="bookingForm.opt_marketing"
                      label="Opt In Marketing"
                    ></v-switch>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col sm="4" md="4">
                    <div>
                      <v-mobile-search
                        label="Mobile No*"
                        outlined
                        :selected="bookingForm.mobile"
                        @updateCustomer="setCustomerData"
                        readonly
                        v-model="bookingForm.search"
                      ></v-mobile-search>
                    </div>
                  </v-col>

                  <v-col sm="4" md="4">
                    <div>
                      <v-name-search
                        label="Name*"
                        outlined
                        :selected="bookingForm.name"
                        :mobile="bookingForm.mobile"
                        :email="bookingForm.email"
                        @updateCustomer="setCustomerData"
                        readonly
                        v-model="bookingForm.nameSearch"
                      ></v-name-search>
                    </div>
                  </v-col>

                  <v-col sm="4" md="4">
                    <div>
                      <v-text-field
                        outlined
                        :readonly="bookingForm.customer_id != null"
                        background-color="#fff"
                        v-model="bookingForm.email"
                        :label="`Email${field.email.is_required ? '*' : ''}`"
                        :placeholder="`Email${
                          field.email.is_required ? '*' : ''
                        }`"
                        :rules="emailRule"
                      ></v-text-field>
                    </div>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.gender.is_visible">
                    <v-select
                      :items="['Male', 'Female']"
                      :placeholder="`Gender${
                        field.gender.is_required ? '*' : ''
                      }`"
                      :label="`Gender${field.gender.is_required ? '*' : ''}`"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      v-model="bookingForm.gender"
                      :rules="genderRule"
                      background-color="#fff"
                    ></v-select>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.dob.is_visible">
                    <date-of-birth
                      :placeholder="`Date of Birth${
                        field.dob.is_required ? '*' : ''
                      }`"
                      :label="`Date of Birth${
                        field.dob.is_required ? '*' : ''
                      }`"
                      :rules="dobRule()"
                      v-model="bookingForm.dob"
                    >
                    </date-of-birth>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.nationality.is_visible">
                    <v-autocomplete
                      :items="countries"
                      :hint="`Nationality${
                        field.nationality.is_required ? '*' : ''
                      }`"
                      :label="`Nationality${
                        field.nationality.is_required ? '*' : ''
                      }`"
                      :rules="nationalityRule"
                      item-value="id"
                      item-text="name"
                      outlined
                      v-model="bookingForm.country_id"
                      background-color="#fff"
                    ></v-autocomplete>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.idProof.is_visible">
                    <v-select
                      :hint="`ID Type${field.idProof.is_required ? '*' : ''}`"
                      :label="`ID Type${field.idProof.is_required ? '*' : ''}`"
                      :rules="idTypeRule"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      item-value="id"
                      item-text="name"
                      :items="idProofTypes"
                      v-model="bookingForm.id_proof_type_id"
                      background-color="#fff"
                    ></v-select>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.idProof.is_visible">
                    <v-row no-gutters>
                      <v-col md="7">
                        <v-text-field
                          :hint="`ID Number${
                            field.idProof.is_required ? '*' : ''
                          }`"
                          :label="`ID Number${
                            field.idProof.is_required ? '*' : ''
                          }`"
                          :rules="idTypeRule"
                          class="text_field1"
                          outlined
                          v-model="bookingForm.id_proof_number"
                          background-color="#fff"
                        ></v-text-field>
                      </v-col>
                      <v-col md="5">
                        <v-file-input
                          v-model="bookingForm.id_proof"
                          class="text_field2"
                          :placeholder="`${
                            bookingForm.id_proof_path ? 'Change' : 'Select'
                          }${field.idProof.is_required ? '*' : ''}`"
                          :label="`${
                            bookingForm.id_proof_path ? 'Download' : 'ID Proof'
                          } ${field.idProof.is_required ? '*' : ''}`"
                          :rules="idProofRule"
                          prepend-icon=""
                          background-color="#fff"
                          outlined
                        >
                          <template v-slot:prepend-inner>
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on }">
                                <v-icon
                                  color="cyan"
                                  v-if="bookingForm.id_proof_path"
                                  @click="openFile(bookingForm.id_proof_path)"
                                  v-on="on"
                                >
                                  mdi-download-box
                                </v-icon>
                                <v-icon v-else v-on="on">
                                  mdi-card-account-details
                                </v-icon>
                              </template>
                              <span v-if="bookingForm.id_proof_path"
                                >Download uploaded file</span
                              >
                              <span v-else>Upload ID Proof</span>
                            </v-tooltip>
                          </template>
                          <template v-slot:selection="{ index, text }">
                            <v-chip
                              v-if="index == 0"
                              color="cyan accent-4"
                              dark
                              label
                              small
                            >
                              <span style="width: 38px" class="text-truncate">{{
                                text
                              }}</span>
                            </v-chip>
                          </template>
                        </v-file-input>
                      </v-col>
                    </v-row>
                    <!-- <div style="margin-top: -110px"></div> -->
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.image.is_visible">
                    <v-row no-gutters>
                      <v-col md="8">
                        <v-file-input
                          v-model="bookingForm.profile_image"
                          class="text_field1"
                          prepend-icon=""
                          :placeholder="`${
                            bookingForm.id_proof_path ? 'Change' : 'Select'
                          }${field.image.is_required ? '*' : ''}`"
                          :label="`${
                            bookingForm.id_proof_path
                              ? 'Change image'
                              : 'Upload Image'
                          } ${field.image.is_required ? '*' : ''}`"
                          :rules="imageRule"
                          background-color="#fff"
                          outlined
                          show-size
                        >
                          <template v-slot:prepend-inner>
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on }">
                                <v-icon
                                  color="cyan"
                                  v-if="bookingForm.image_path"
                                  @click="openFile(bookingForm.image_path)"
                                  v-on="on"
                                >
                                  mdi-download-box
                                </v-icon>
                                <v-icon v-else v-on="on">mdi-image</v-icon>
                              </template>
                              <span v-if="bookingForm.image_path">
                                Download image</span
                              >
                              <span v-else>Upload Image</span>
                            </v-tooltip>
                          </template>
                          <template v-slot:selection="{ index, text }">
                            <v-chip
                              v-if="index == 0"
                              color="cyan accent-4"
                              dark
                              label
                              small
                            >
                              <span
                                style="width: 120px"
                                class="text-truncate"
                                >{{ text }}</span
                              >
                            </v-chip>
                          </template>
                        </v-file-input>
                      </v-col>
                      <v-col md="4">
                        <v-btn
                          large
                          block
                          style="background-color: #fff"
                          outlined
                          height="56"
                          color="blue-grey"
                          class="white--text text_field2"
                          @click="webcamDialog = true"
                        >
                          <v-icon dark>mdi-camera</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col md="4">
                    <v-switch
                      v-model="bookingForm.attendance"
                      label="Attendance"
                      v-if="
                        attendanceSwitch &&
                        perCapacity == 1 &&
                        attendies < capacity
                      "
                      @change="changeAttendanceSwitch"
                    ></v-switch>
                  </v-col>
                  <v-col md="6"></v-col>
                  <v-col md="2" v-if="perCapacity == 1">
                    <v-text-field
                      :disabled="bookingForm.attendance"
                      type="number"
                      label="Participants Count"
                      outlined
                      rows="2"
                      background-color="#fff"
                      required
                      v-model="bookingForm.attendance_count"
                      @keyup="checkSlotCapacity()"
                      @change="
                        checkSlotCapacity();
                        if (!bookingForm.attendance_count)
                          bookingForm.attendance_count = 1;
                      "
                    ></v-text-field>
                  </v-col>
                </v-row>

                <attendance
                  :totalAttendies="attendies"
                  :totalCapacity="capacity"
                  :totalBookedCapacity="bookedCapacity"
                  :attendanceCustomers="attendanceCustomers"
                  v-if="bookingForm.attendance"
                  @setAttendanceData="setAttendanceData"
                  :editFlag="editFlag"
                  :pastTime="pastTime"
                ></attendance>
              </v-card>

              <repeat-bookingPayment
                v-if="repeatId"
                v-bind="bookingForm"
                :repeatId="repeatId"
                :openProduct="bookingWithOpenProduct"
                @repeatDatesForPayment="repeatDatesForPaymentChange"
              ></repeat-bookingPayment>

              <v-card
                outlined
                color="#edf9ff"
                class="mt-2"
                style="border: 1px #ccc solid"
                v-if="venueServiceDocuments.length > 0"
              >
                <v-card-text>
                  <div class="titles">Documents</div>
                  <template v-for="doc in venueServiceDocuments">
                    <v-chip
                      :key="doc.id"
                      v-if="doc.file_path"
                      color="grey accent-4 mr-2 mb-2"
                      dark
                      label
                      @click="openFile(doc.file_path)"
                      small
                    >
                      {{ doc.name }}
                    </v-chip>
                  </template>
                </v-card-text>
              </v-card>
              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4 mt-2"
                outlined
              >
                <div class="titles d-flex justify-space-between">
                  <div class="titles">Product Details</div>
                  <v-btn
                    x-small
                    outlined
                    v-if="productCombinations.length > 1"
                    @click="chooseRentalCombination"
                    ><v-icon x-small>mdi-cart-arrow-right</v-icon> Rental
                    combination</v-btn
                  >
                </div>
                <v-row>
                  <v-col md="12">
                    <v-chip
                      label
                      color="cyan"
                      dark
                      class="ml-2 mb-2"
                      v-for="(product, index) in bookingForm.products"
                      :key="index"
                      :close="repeatId && product.rental ? false : true"
                      @click:close="removeProduct(index)"
                    >
                      <v-avatar left>
                        <view-image :image="product.image_path"></view-image>
                      </v-avatar>
                      {{ product.name }} x
                      {{ product.quantity | numberFormatter(3) }}
                      -
                      {{ product.total_price | toCurrency }}
                      <span
                        v-if="product.discount != null"
                        class="text-decoration-line-through pl-1"
                      >
                        {{ product.discount.actual_total | toCurrency }}</span
                      >
                    </v-chip>
                  </v-col>
                  <v-col :md="productCategoryId == -1 ? 2 : 3">
                    <v-autocomplete
                      :items="categoriesList"
                      v-model="productCategoryId"
                      label="Select Category"
                      required
                      item-value="id"
                      item-text="name"
                      @change="categoryChange"
                      outlined
                      background-color="#fff"
                    ></v-autocomplete>
                    <!-- { name: 'Open Item', id: -1 }, -->
                  </v-col>
                  <v-col md="3" v-if="productCategoryId == -1">
                    <v-text-field
                      v-model="selectedProduct.title"
                      label="Product name"
                      required
                      outlined
                      background-color="#fff"
                    >
                      <template v-slot:append>
                        <v-menu
                          top
                          nudge-bottom="105"
                          nudge-left="16"
                          :close-on-content-click="true"
                        >
                          <template v-slot:activator="{ on }">
                            <div
                              v-on="on"
                              class="d-flex align-center open-product"
                            >
                              {{
                                selectedProduct.rental == true
                                  ? "Base"
                                  : "Addon"
                              }}
                              <v-icon small color="#fff"
                                >mdi-chevron-down</v-icon
                              >
                            </div>
                          </template>
                          <v-card>
                            <v-card-text>
                              <v-radio-group
                                v-model="selectedProduct.rental"
                                column
                              >
                                <v-radio
                                  :disabled="repeatId"
                                  label="Base Product"
                                  :value="true"
                                ></v-radio>
                                <v-radio
                                  label="Addon Product"
                                  :value="false"
                                ></v-radio>
                              </v-radio-group>
                            </v-card-text>
                          </v-card>
                        </v-menu>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col md="4" v-else>
                    <v-autocomplete
                      v-model="selectedProduct"
                      label="Select Product"
                      required
                      return-object
                      :items="getProducts()"
                      item-value="id"
                      item-text="name"
                      outlined
                      background-color="#fff"
                    ></v-autocomplete>
                  </v-col>
                  <v-col :md="productCategoryId == -1 ? 1 : 2">
                    <v-text-field
                      label="Quantity"
                      outlined
                      background-color="#fff"
                      type="number"
                      min="1"
                      v-model="selectedProduct.quantity"
                    ></v-text-field>
                  </v-col>
                  <v-col md="1" v-if="productCategoryId == -1">
                    <v-select
                      label="Tax*"
                      v-model="selectedProduct.tax_type_id"
                      item-value="value"
                      item-text="text"
                      hint="Required tax"
                      :menu-props="{ bottom: true, offsetY: true }"
                      :items="taxTypes"
                      outlined
                      @change="taxChange()"
                      background-color="#fff"
                    ></v-select>
                  </v-col>
                  <v-col md="2" v-if="productCategoryId == -1">
                    <v-text-field
                      label="Price (Pre Tax)*"
                      outlined
                      rows="2"
                      :prefix="currencyCode"
                      background-color="#fff"
                      required
                      v-model="selectedProduct.pre_tax_price"
                      @change="calculateTaxVariation($event, 'pre')"
                    ></v-text-field>
                  </v-col>
                  <v-col md="2" v-if="productCategoryId == -1">
                    <v-text-field
                      label="Price (Post Tax)*"
                      outlined
                      rows="2"
                      :prefix="currencyCode"
                      background-color="#fff"
                      required
                      @change="calculateTaxVariation($event, 'post')"
                      v-model="selectedProduct.total_price"
                    ></v-text-field>
                  </v-col>
                  <v-col md="2" v-else>
                    <v-text-field
                      label="Price"
                      :readonly="productCategoryId != -1"
                      outlined
                      background-color="#fff"
                      v-model="selectedProduct.total_amount"
                      :suffix="currencyCode"
                    ></v-text-field>
                  </v-col>
                  <v-col md="1">
                    <v-btn
                      class="white--text blue-color"
                      height="56"
                      block
                      @click="addProduct"
                      >Add</v-btn
                    >
                  </v-col>
                </v-row>
              </v-card>
              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4 mt-2"
                outlined
              >
                <div class="titles d-flex justify-space-between">
                  <div class="titles">Order Note</div>
                </div>
                <v-row>
                  <v-col md="12">
                    <v-textarea
                      v-model="bookingForm.order_notes"
                      rows="3"
                      outlined
                      background-color="#fff"
                    ></v-textarea>
                  </v-col>
                </v-row>
              </v-card>
            </v-card-text>
            <v-card-actions>
              <v-btn
                v-if="perCapacity != 1 && bookingForm.status_id == 5"
                @click="reschedule()"
                class="ma-2 yellow-color"
                text
                :disabled="repeatId && !isEnableRepeateBookingReschedule"
                >Reschedule
              </v-btn>
              <v-btn
                v-if="id == 0"
                @click="addMaintenance()"
                class="ma-2 red-color"
                text
                >Maintenance
              </v-btn>
              <v-btn
                v-if="!repeatId && bookingForm.status_id == 5"
                @click="confirmCancel"
                class="ma-2 white--text red-color"
                text
                >Cancel Booking
              </v-btn>

              <v-btn
                v-if="repeatId && bookingForm.status_id == 5"
                @click="cancelRepeatBooking"
                class="ma-2 white--text red-color"
                text
                >Cancel Booking
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn @click="close()" class="ma-2 white--text blue-color" text
                >Close
              </v-btn>

              <v-btn
                v-if="bookingForm.status_id == 5 || bookingForm.status_id == 7"
                @click="showInvoice"
                class="ma-2 white--text teal-color"
                text
              >
                Invoice
              </v-btn>

              <v-btn
                v-if="repeatId && !bookingWithOpenProduct"
                @click="showRepeatPaymentsConfirmation"
                class="ma-2 white--text teal-color"
                text
                :disabled="repeatId && !isEnableRepeatBookingPayment"
              >
                Pay
              </v-btn>

              <v-btn
                class="ma-2 white--text teal-color"
                text
                @click="addOrEditBooking"
              >
                {{ order_id ? "Update Reservation" : "Confirm Reservation" }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>
    </v-form>
    <capture-image
      :open="webcamDialog"
      @close="webcamDialog = false"
      @confirm="confirmImageCapture"
    />
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    >
    </confirm-model>
    <product-combination
      :productCombinations="productCombinations"
      :showCombinations="showCombinationDialog"
      @close="showCombinationDialog = false"
      @changeCombination="changeRentalProducts"
      :selectedCombinationPrice="selectedCombinationPrice"
    >
    </product-combination>

    <repeat-order-payments
      v-if="enableRepeatOrderPay"
      v-bind="bookingForm"
      :show="enableRepeatOrderPay"
      :repeatBookingIds="repeatBookingIds"
      @close="enableRepeatOrderPay = false"
      :date="date"
      @payed="$emit('booked'), (enableRepeatOrderPay = false)"
    ></repeat-order-payments>
    <facility-maintenance
      :facilityId="maintenanceId"
      :venueServiceId="venue_service_id"
      @save="saveMaintenance"
      @close="closeMaintenance"
      :timeIncrement="increment"
      :startTime="start_time"
      :endTime="end_time"
      :startDate="date"
      :endDate="date"
    ></facility-maintenance>
  </div>
</template>
<script>
import VMemberSearch from "@/components/Customer/VMemberSearch";
import CaptureImage from "@/components/Image/CaptureImage";
import RepeatBooking from "@/components/Schedule/Facility/RepeatBooking";
import Attendance from "@/components/Schedule/Facility/Attandance";
import RepeatBookingPayment from "@/components/Schedule/Facility/RepeatBookingPayment";
import ProductCombination from "@/components/Schedule/Facility/ProductCombination";
import moment from "moment";
import bookingFields from "@/mixins/bookingFieldValidation";
import RepeatOrderPayments from "@/components/Order/RepeatOrderPayments.vue";
import FacilityMaintenance from "@/components/Facility/FacilityMaintenance";
export default {
  props: {
    showBookingForm: { type: Boolean },
    start_time: { type: String },
    end_time: { type: String },
    date: { type: String },
    facility_id: { type: Number },
    order_id: { type: Number },
    id: { type: Number, default: 0 },
    facility_name: { type: String },
    venue_service_id: { type: Number },
    service: { type: String },
    perCapacity: { type: Number },
    increment: { type: Number },
    minBookingTime: { type: Number },
  },
  components: {
    Attendance,
    CaptureImage,
    VMemberSearch,
    RepeatBooking,
    ProductCombination,
    RepeatBookingPayment,
    RepeatOrderPayments,
    FacilityMaintenance,
  },
  mixins: [bookingFields],
  data() {
    return {
      currentRepeatDates: null,
      bookingForm: {
        attendance: false,
        attendance_count: 1,
        opt_marketing: false,
      },
      selectedProduct: {},
      productCategoryId: null,
      webcamDialog: false,
      endTimes: [],
      categories: [],
      companies: [],
      valid: false,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      repeatId: null,
      productCombinations: [],
      showCombinationDialog: false,
      selectedCombinationPrice: 0,
      isEnableRepeatBookingPayment: false,
      isEnableRepeateBookingReschedule: false,
      repeatDatesForPayments: null,
      enableRepeatOrderPay: false,
      repeatBookingIds: [],
      currentOrderProducts: [],
      attendanceData: {},
      capacity: 0,
      attendies: 0,
      bookedCapacity: 2,
      attendanceSwitch: false,
      attendanceCustomers: [],
      editFlag: false,
      pastTime: false,
      maintenanceId: null,
      categoriesList: [
        { name: "All", id: null },
        { name: "Open Product", id: -1 },
      ],
      disablePromotion: false,
      bookingWithOpenProduct: false,
      isEmiratesIdCheck: false,
      deletedProducts: [],
    };
  },
  watch: {
    showBookingForm(val) {
      this.disablePromotion = false;
      this.repeatId = null;
      this.isEnableRepeateBookingReschedule = null;
      this.attendanceCustomers = [{}];
      this.attendanceData = [];
      this.currentOrderProducts = [];
      this.currentRepeatDates = null;
      this.attendanceSwitch = false;
      this.productCategoryId = null;
      this.selectedProduct = { rental: false };
      this.attendies = 0;
      this.deletedProducts = [];
      if (val === true) {
        this.bookingForm = {
          start_time: this.start_time,
          end_time: this.end_time,
          date: this.date,
          facility_id: this.facility_id,
          venue_service_id: this.venue_service_id,
          attendance_count: 1,
        };
        if (this.id > 0) {
          this.bookingForm.id = this.id;
          this.bookingForm.order_id = this.order_id;
        }
        this.$store.dispatch("loadPromotions", {
          date: this.date,
          venue_service_id: this.venue_service_id,
          product_type: "Facility",
        });
        this.setFieldConfigurations();
        this.getFacilityUtils();
      }
    },
  },
  mounted() {
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$store.getters.getIdProofTypes.status == false) {
      this.$store.dispatch("loadIDProofTypes");
    }
    if (this.$store.getters.getTaxTypes.status == false) {
      this.$store.dispatch("loadTaxTypes");
    }
    if (this.$store.getters.getPaymentMethods.status == false) {
      this.$store.dispatch("loadPaymentMethods", "normal");
    }
  },
  computed: {
    countries() {
      return this.$store.getters.getCountries.data;
    },
    promotions() {
      return this.$store.getters.getPromotions.data;
    },
    idProofTypes() {
      return this.$store.getters.getIdProofTypes.data;
    },
    taxTypes() {
      return this.$store.getters.getTaxTypes.data;
    },
  },
  methods: {
    setCardData(data) {
      if (!data.customer_id) {
        this.$set(this.bookingForm, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.bookingForm, "name", data.first_name);
      }

      if (!data.customer_id && this.bookingForm.name != data.name) {
        this.$set(this.bookingForm, "mobile", null);
        this.bookingForm.search = null;
        this.bookingForm.nameSearch = null;
        this.$set(this.bookingForm, "email", null);
        this.$set(this.bookingForm, "gender", null);
        this.$set(this.bookingForm, "name", null);
        this.$set(this.bookingForm, "customer_id", null);
        this.$set(this.bookingForm, "first_name", null);
        this.$set(this.bookingForm, "image_path", null);
        this.$set(this.bookingForm, "dob", null);
        this.$set(this.bookingForm, "country_id", null);
        this.$set(this.bookingForm, "last_name", null);
        this.$set(this.bookingForm, "opt_marketing", false);
        this.$forceUpdate();
      }

      if (data.mobile) {
        this.$set(this.bookingForm, "mobile", data.mobile);
        this.isEmiratesIdCheck = true;
      }
      if (data.email) this.$set(this.bookingForm, "email", data.email);
      if (data.country_id) {
        this.$set(this.bookingForm, "country_id", data.country_id);
      } else {
        this.$set(this.bookingForm, "country_id", null);
      }
      if (data.country_id) {
        this.$set(this.bookingForm, "id_proof_type_id", data.id_proof_type_id);
      }

      if (data.id_proof_number) {
        this.$set(this.bookingForm, "id_proof_number", data.id_proof_number);
      }

      if (data.gender) {
        this.$set(this.bookingForm, "gender", data.gender);
      } else {
        this.$set(this.bookingForm, "gender", null);
      }
      if (data.dob) {
        this.$set(this.bookingForm, "dob", data.dob);
      } else {
        this.$set(this.bookingForm, "dob", null);
      }

      if (data.image) {
        this.$set(this.bookingForm, "image", data.image);
      }

      if (data.name) this.$set(this.bookingForm, "name", data.name);
      if (data.last_name) {
        this.$set(this.bookingForm, "last_name", data.last_name);
      } else {
        this.$set(this.bookingForm, "last_name", null);
      }
      if (data.first_name)
        this.$set(this.bookingForm, "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.bookingForm, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.bookingForm, "image_path", data.image_path);
      } else {
        this.$set(this.bookingForm, "image_path", null);
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.bookingForm, "opt_marketing", true);
        } else {
          this.$set(this.bookingForm, "opt_marketing", false);
        }
      }
      this.$forceUpdate();
    },
    reschedule() {
      if (this.isEnableRepeateBookingReschedule) {
        let booking = this.repeatDatesForPayments.find((item) => item.isPaid);
        this.$emit("reschedule", booking.booking_id);
      } else {
        this.$emit("reschedule", this.bookingForm.id);
      }
    },
    addMaintenance() {
      this.maintenanceId = this.facility_id;
    },
    closeMaintenance() {
      this.maintenanceId = null;
    },
    saveMaintenance() {
      this.$emit("refresh");
      this.close();
    },
    rescheduleFromRepeatBooking(id) {
      this.$emit("reschedule", id);
    },
    close() {
      this.$emit("close");
    },
    setCustomerData(data) {
      if (data.mobile && data.first_name && data.customer_id) {
        this.isEmiratesIdCheck = false;
        this.searchMember(
          data.mobile,
          data.customer_id,
          data.first_name,
          data.last_name
        );
      } else {
        this.clearCardAndBenefits();
      }

      if (!data.customer_id) {
        this.$set(this.bookingForm, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.bookingForm, "name", data.first_name);
      }

      // if (data.last_name) {
      //   this.$set(
      //     this.bookingForm,
      //     "name",
      //     this.bookingForm.name + " " + data.last_name
      //   );
      // }

      if (
        this.bookingForm.customer_id &&
        !data.customer_id &&
        this.bookingForm.name != data.name &&
        this.bookingForm.mobile != data.mobile
      ) {
        this.$set(this.bookingForm, "mobile", null);
        this.bookingForm.search = null;
        this.bookingForm.nameSearch = null;
        this.$set(this.bookingForm, "email", null);
        this.$set(this.bookingForm, "gender", null);
        this.$set(this.bookingForm, "name", null);
        this.$set(this.bookingForm, "customer_id", null);
        this.$set(this.bookingForm, "first_name", null);
        this.$set(this.bookingForm, "image_path", null);
        this.$set(this.bookingForm, "dob", null);
        this.$set(this.bookingForm, "country_id", null);
        this.$set(this.bookingForm, "last_name", null);
        this.$set(this.bookingForm, "opt_marketing", false);
        this.$forceUpdate();
      }
      if (data.mobile) this.$set(this.bookingForm, "mobile", data.mobile);
      if (data.email) this.$set(this.bookingForm, "email", data.email);
      if (data.country_id) {
        this.$set(this.bookingForm, "country_id", data.country_id);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.bookingForm, "country_id", null);
        }
      }
      if (data.gender) {
        this.$set(this.bookingForm, "gender", data.gender);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.bookingForm, "gender", null);
        }
      }
      if (data.dob) {
        this.$set(this.bookingForm, "dob", data.dob);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.bookingForm, "dob", null);
        }
      }
      if (data.name) {
        data.name = data.name.replace(/\s\s+/g, " ");
        data.name = data.name.trim();
        this.$set(this.bookingForm, "name", data.name);
      }
      if (data.last_name) {
        data.last_name = data.last_name.replace(/\s\s+/g, " ");
        data.last_name = data.last_name.trim();
        this.$set(this.bookingForm, "last_name", data.last_name);
      } else {
        this.$set(this.bookingForm, "last_name", null);
      }
      if (data.first_name) {
        data.first_name = data.first_name.replace(/\s\s+/g, " ");
        data.first_name = data.first_name.trim();
        this.$set(this.bookingForm, "first_name", data.first_name);
      }
      if (data.customer_id)
        this.$set(this.bookingForm, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.bookingForm, "image_path", data.image_path);
      } else {
        this.$set(this.bookingForm, "image_path", null);
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.bookingForm, "opt_marketing", true);
        } else {
          this.$set(this.bookingForm, "opt_marketing", false);
        }
      }
      this.$refs.form.resetValidation();
      this.$forceUpdate();
    },
    setMemberData(data) {
      this.setCustomerData(data);
      this.$set(this.bookingForm, "card_number", data.card_number);
      this.verifyBenefit("membership");
    },
    confirmImageCapture(image) {
      image.name = this.bookingForm.name
        ? this.bookingForm.name + "_" + moment().format("YYYYMMDDHHSS")
        : "user_image_" + moment().format("YYYYMMDDHHSS");
      this.bookingForm.profile_image = image;
      this.webcamDialog = false;
    },
    addOrEditBooking() {
      if (this.attendanceData.length > 0) {
        let mobiles = this.attendanceData.map(function (item) {
          return item.mobile;
        });
        if (this.bookingForm.mobile) {
          mobiles.push(this.bookingForm.mobile);
        }
      }
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields to continue");
        return;
      }

      if (this.repeatId && this.bookingForm.id > 0) {
        this.bookingForm.products = this.currentOrderProducts;
      } else {
        var repeatBookingAvailableDates = [];
        if (this.bookingForm.repeat) {
          this.bookingForm.repeats.forEach((repeats) => {
            repeatBookingAvailableDates = repeatBookingAvailableDates.concat(
              repeats.available_dates
            );
          });
          repeatBookingAvailableDates = repeatBookingAvailableDates.sort(
            (a, b) => new Date(a) - new Date(b)
          );
        }
      }

      if (!this.bookingForm.repeats && !this.id) {
        this.bookingForm.repeat = false;
        delete this.bookingForm.repeats;
      }
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add atleast one product");
        return;
      }
      this.showLoader();
      var formData = new FormData();
      for (let key in this.bookingForm) {
        if (
          this.bookingForm[key] != null &&
          !Array.isArray(this.bookingForm[key]) &&
          typeof this.bookingForm[key] != "object"
        ) {
          formData.append(`${key}`, this.bookingForm[key]);
        } else if (Array.isArray(this.bookingForm[key])) {
          this.bookingForm[key].forEach((data, index) => {
            if (!Array.isArray(data)) {
              for (let innerKey in data) {
                if (Array.isArray(data[innerKey])) {
                  if (
                    innerKey != "start_times" &&
                    innerKey != "end_times" &&
                    innerKey != "times"
                  ) {
                    data[innerKey].forEach((deepData, deepIndex) => {
                      formData.append(
                        `${key}[${index}][${innerKey}][${deepIndex}]`,
                        typeof deepData == "object"
                          ? JSON.stringify(deepData)
                          : deepData
                      );
                    });
                  }
                } else {
                  formData.append(
                    `${key}[${index}][${innerKey}]`,
                    data[innerKey]
                  );
                }
              }
            } else if (Array.isArray(data)) {
              data.forEach((innerData, innerIndex) => {
                formData.append(`${key}[${index}][${innerIndex}]`, innerData);
              });
            } else {
              formData.append(
                `${key}[${index}]`,
                typeof data == "object" ? JSON.stringify(data) : data
              );
            }
          });
        }
      }

      if (this.attendanceData.length > 0) {
        for (let key in this.attendanceData) {
          if (typeof this.attendanceData[key] === "object") {
            if (this.attendanceData[key].name == undefined) {
              this.attendanceData[key].name =
                this.attendanceData[key].namesearch.first_name;
            }
            formData.append(
              `attendance_customer[${key}][mobile]`,
              this.attendanceData[key].mobile
            );
            if (this.attendanceData[key].name) {
              formData.append(
                `attendance_customer[${key}][name]`,
                this.attendanceData[key].name
              );
            } else {
              formData.append(
                `attendance_customer[${key}][name]`,
                this.attendanceData[key].namesearch.first_name
              );
            }
            formData.append(
              `attendance_customer[${key}][email]`,
              this.attendanceData[key].email
            );
          }
        }
      }

      if (this.bookingForm.id_proof) {
        formData.append("id_proof", this.bookingForm.id_proof);
      }

      if (this.bookingForm.profile_image) {
        formData.append("profile_image", this.bookingForm.profile_image);
      }

      if (
        this.bookingForm.repeat &&
        repeatBookingAvailableDates &&
        repeatBookingAvailableDates.length > 0
      ) {
        formData.append(
          "date",
          repeatBookingAvailableDates[repeatBookingAvailableDates.length - 1]
        );
      }

      formData.append("deleted_products", JSON.stringify(this.deletedProducts));
      this.$http
        .post(
          `venues/facilities/bookings${
            this.bookingForm.id > 0 ? "/" + this.bookingForm.id : ""
          }`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data; boundary=${form._boundary}",
            },
          }
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            let data = response.data.data;
            this.hideLoader();
            if (this.repeatId) {
              this.$emit("booked");
            } else {
              this.$emit("booked", data.order_id);
            }

            this.showSuccess("Booking updated successfully");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getFacilityUtils() {
      this.showLoader("Loading");
      this.endTimes = [];
      this.$http
        .get(
          `venues/facilities/bookings/utils?facility_id=${this.facility_id}&date=${this.date}&start_time=${this.start_time}&increment=${this.increment}&min_booking_time=${this.minBookingTime}&booking_id=${this.id}&per_capacity=${this.perCapacity}&venue_service_id=${this.venue_service_id}`
        )
        .then((response) => {
          this.hideLoader();
          if (response.status == 200 && response.data.status == true) {
            let data = response.data.data;
            this.bookingWithOpenProduct = false;
            if (data.capacity) {
              this.capacity = data.capacity;
            }
            this.productCombinations = [];
            this.endTimes = data.end_times;
            this.bookingForm.start_time = data.start_time;
            this.bookingForm.end_time = data.end_time;
            let start_time = moment(this.start_time, "HH:mm:ss").format(
              "HH:mm:ss"
            );
            let currentTime = moment(new Date()).format("HH:mm:ss");

            if (
              moment(start_time, "HH:mm:ss").isBefore(
                moment(currentTime, "HH:mm:ss")
              )
            ) {
              this.pastTime = true;
            } else {
              this.pastTime = false;
            }
            this.categories = data.categories;
            this.categoriesList = [
              { name: "All", id: null },
              { name: "Open Product", id: -1 },
              ...this.categories,
            ];
            if (
              data.facility_rentals.length > 0 &&
              this.id == 0 &&
              this.perCapacity == 0
            ) {
              let rentalProducts = data.default_products;
              this.bookingForm.price = 0;
              this.bookingForm.total_price = 0;
              this.bookingForm.products = [];
              rentalProducts.forEach((rentalProduct) => {
                this.bookingForm.price += rentalProduct.price;
                this.bookingForm.total_price +=
                  rentalProduct.price +
                  (rentalProduct.quantity >= 1
                    ? rentalProduct.tax_amount *
                      parseFloat(rentalProduct.quantity)
                    : rentalProduct.tax_amount);
                this.bookingForm.products.push({
                  product_id: rentalProduct.id,
                  product_type_id: rentalProduct.product_type_id,
                  price: rentalProduct.price,
                  name: rentalProduct.name,
                  tax: rentalProduct.tax_amount,
                  category_id: rentalProduct.category_id,
                  rental: true,
                  product_price: rentalProduct.product_price_when_overlapping
                    ? rentalProduct.product_price_when_overlapping
                    : rentalProduct.product_price,
                  quantity: rentalProduct.quantity,
                  total_price:
                    rentalProduct.price +
                    (rentalProduct.quantity >= 1
                      ? rentalProduct.tax_amount *
                        parseFloat(rentalProduct.quantity)
                      : rentalProduct.tax_amount),
                });
              });
            } else {
              this.$http
                .get(
                  `venues/facilities/bookings/participants?facility_id=${this.facility_id}&start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&date=${this.date}`
                )
                .then((participans_response) => {
                  this.hideLoader();
                  if (
                    participans_response.status == 200 &&
                    participans_response.data.status == true
                  ) {
                    const data_participans_response = Array.from(
                      new Set(participans_response.data.data.map((a) => a.id))
                    ).map((id) => {
                      return participans_response.data.data.find(
                        (a) => a.id === id
                      );
                    });

                    this.bookedCapacity = data_participans_response.reduce(
                      (a, b) => a + parseFloat(b.attendance),
                      0
                    );
                    if (this.bookedCapacity + 1 >= this.capacity) {
                      this.attendanceSwitch = false;
                    } else {
                      this.attendanceSwitch = true;
                    }
                    this.editFlag = false;
                  }
                });
              this.bookingForm.products = [];
              this.bookingForm.price = 0;
              this.bookingForm.total_price = 0;
            }
            if (this.perCapacity == 1) {
              if (data.facility_rentals[0]) {
                this.categoriesList.push({
                  name: "Tickets",
                  id: data.facility_rentals[0].category_id,
                });
              }
            }
            if (data.facility_rentals.length) {
              if (data.facility_rentals[0]) {
                let rentalProducts = {
                  id: data.facility_rentals[0].category_id,
                  name: this.perCapacity == 1 ? "Tickets" : "Rentals",
                  products: [
                    ...data.facility_rentals.map((item) => {
                      item.id = item.product_id;
                      item.rental = true;
                      return item;
                    }),
                  ],
                };
                this.categories.push(rentalProducts);
              }
            }
            this.bookingForm.opt_marketing = false;
            this.$refs.form.resetValidation();
            if (this.id > 0) {
              this.getBookingDetails();
            }
            this.hideLoader();
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.close();
          this.$emit("refresh");
          this.errorChecker(error);
        });
    },
    getProducts() {
      if (this.productCategoryId != null) {
        return this.categories.find((item) => item.id == this.productCategoryId)
          .products;
      }
      if (this.productCategoryId == null) {
        let products = [];
        this.categories.forEach((category) => {
          category.products.forEach((product) => {
            product.category_id = category.id;
            products.push(product);
          });
        });
        return products;
      }
      return [];
    },
    addProduct() {
      let pIndex = this.bookingForm.products.findIndex((item) =>
        item.product_id ? item.product_id == this.selectedProduct.id : ""
      );

      let quantity =
        this.selectedProduct.quantity && this.selectedProduct.quantity > 0
          ? this.selectedProduct.quantity
          : 1;

      if (this.selectedProduct.inventory_enable) {
        let sales = this.selectedProduct.sales ? this.selectedProduct.sales : 0;
        let totalQty = sales + parseFloat(quantity);
        parseFloat(this.selectedProduct.sales) + parseFloat(quantity) >=
          parseFloat(this.selectedProduct.actual_qty);
        if (parseFloat(this.selectedProduct.actual_qty) < totalQty) {
          if (
            this.selectedProduct.actual_qty - this.selectedProduct.sales ==
            0
          ) {
            this.showError("Product not available!");
          } else {
            this.showError(
              `Only ${
                this.selectedProduct.actual_qty - this.selectedProduct.sales
              } product available!`
            );
          }

          return;
        }
      }

      if (
        this.selectedProduct.id == null &&
        this.selectedProduct.title == null
      ) {
        this.showError("Please add product");
        return;
      }

      if (this.selectedProduct.inventory_enable) {
        this.selectedProduct.sales += parseFloat(quantity);
      }

      let price = parseFloat(this.selectedProduct.price) * parseFloat(quantity);

      if (this.selectedProduct.title != null) {
        if (this.selectedProduct.tax_type_id == null) {
          this.showError("Please select tax");
          return;
        }
        if (this.selectedProduct.price == null) {
          this.showError("Please add price");
          return;
        }

        this.selectedProduct.id = null;
        this.selectedProduct.name = this.selectedProduct.title;
        if (this.selectedProduct.tax_type_id == 1) {
          this.selectedProduct.tax_amount =
            this.selectedProduct.total_price - this.selectedProduct.price;
        }
      }

      if (pIndex == -1) {
        var obj = {
          product_id: this.selectedProduct.id ? this.selectedProduct.id : 0,
          price: price,
          name: this.selectedProduct.name,
          tax:
            (this.selectedProduct.tax_amount
              ? this.selectedProduct.tax_amount
              : 0) * quantity,
          quantity: parseFloat(quantity),
          product_type_id: this.selectedProduct.product_type_id,
          inventory_enable: this.selectedProduct.inventory_enable,
          venue_service_id: this.venue_service_id,
          category_id: this.selectedProduct.category_id,
          rental: this.selectedProduct.rental == true ? true : false,
          total_price:
            price +
            parseFloat(
              this.selectedProduct.tax_amount
                ? this.selectedProduct.tax_amount
                : 0
            ) *
              quantity,
        };
        this.bookingForm.products.push(obj);
        if (this.repeatId) {
          this.currentOrderProducts.push(obj);
        }
      } else {
        let newQuantity =
          parseFloat(this.bookingForm.products[pIndex].quantity) +
          parseFloat(quantity);
        this.bookingForm.products[pIndex].quantity = newQuantity;
        this.bookingForm.products[pIndex].price =
          this.selectedProduct.price * newQuantity;
        this.bookingForm.products[pIndex].total_price =
          this.bookingForm.products[pIndex].price +
          this.bookingForm.products[pIndex].tax * newQuantity;

        if (this.repeatId) {
          var findIndex = this.currentOrderProducts.findIndex(
            (x) => x.product_id == this.bookingForm.products[pIndex].product_id
          );
          this.currentOrderProducts[findIndex] =
            this.bookingForm.products[pIndex];
        }

        if (this.bookingForm.products[pIndex].inventory_enable) {
          if (this.bookingForm.products[pIndex].order_item_id) {
            this.deletedProducts.push(
              this.bookingForm.products[pIndex].order_item_id
            );
          }
        }
      }

      this.bookingForm.price += price;
      this.bookingForm.total_price = this.bookingForm.products.reduce(
        (a, b) => a + parseFloat(b.total_price),
        0
      );

      this.selectedProduct = {};
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      this.$forceUpdate();
    },
    getBookingDetails() {
      this.showLoader("Loading Facilities");
      this.facilities = [];
      this.$http
        .get(`venues/facilities/bookings/${this.bookingForm.order_id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.isEmiratesIdCheck = false;
            const data = response.data.data;
            this.bookingForm.order_notes = data.order_notes;
            if (data.facility_booking.length > 0) {
              let bookingData = data.facility_booking.find(
                (x) =>
                  x.date === this.date &&
                  x.start_time == this.start_time &&
                  x.facility_booking_duplicate_id == null
              );
              if (!bookingData) {
                bookingData = data.facility_booking.find(
                  (x) =>
                    x.date === this.date &&
                    x.facility_booking_duplicate_id == null
                );
              }
              this.bookingForm.id = bookingData.id;
              this.bookingForm.customer_type = bookingData.customer_type;
              this.bookingForm.id_proof_path = bookingData.id_proof;
              this.bookingForm.id_proof_number = bookingData.id_proof_number;
              this.bookingForm.id_proof_type_id = bookingData.id_proof_type_id;
              this.bookingForm.image_path = bookingData.image_path;
              this.bookingForm.start_time = bookingData.start_time;
              this.bookingForm.end_time = bookingData.end_time;
              this.bookingForm.attendance_count = bookingData.attendance;
              this.attendies = bookingData.attendance;
            }

            if (data.discount != null) {
              if (data.discount.promotion != null) {
                this.bookingForm.promotion_code =
                  data.discount.promotion.promotion_code;
              }
              if (data.discount.member != null) {
                this.bookingForm.card_number = data.discount.member.card_number;
              }
              this.bookingForm.discount = {
                actual_price: data.discount.actual_price,
                actual_tax: data.discount.actual_tax,
                actual_total: data.discount.actual_total,
              };
            }

            if (data.parent_order_id && data.parent_orders_id.length > 0) {
              this.bookingForm.parent_order_id = data.parent_order_id;
              this.bookingForm.parent_orders_id = data.parent_orders_id;
            } else {
              this.bookingForm.parent_orders_id = null;
            }

            if (data.customer) {
              this.bookingForm.name = `${data.customer.first_name}${
                data.customer.last_name ? " " + data.customer.last_name : ""
              }`;
              this.bookingForm.first_name = data.customer.first_name;
              this.bookingForm.last_name = data.customer.last_name;
              this.bookingForm.mobile = data.customer.mobile;
              this.bookingForm.email = data.customer.email;
              this.bookingForm.gender = data.customer.gender;
              this.bookingForm.dob = data.customer.dob;
              this.bookingForm.mobile = data.customer.mobile;
              this.bookingForm.country_id = data.customer.country_id;
              this.bookingForm.opt_marketing =
                data.customer.opt_marketing == 1 ? true : false;
              if (
                this.bookingForm.customer_type == "member" &&
                !this.bookingForm.card_number
              ) {
                this.searchMember(
                  data.customer.mobile,
                  data.customer.customer_id,
                  data.customer.first_name,
                  data.customer.last_name
                );
              }
            }

            if (data.group_customers.length) {
              this.$http
                .get(
                  `venues/facilities/bookings/participants?facility_id=${this.facility_id}&start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&date=${this.date}`
                )
                .then((participans_response) => {
                  if (
                    participans_response.status == 200 &&
                    participans_response.data.status == true
                  ) {
                    const data_participans_response = Array.from(
                      new Set(participans_response.data.data.map((a) => a.id))
                    ).map((id) => {
                      return participans_response.data.data.find(
                        (a) => a.id === id
                      );
                    });

                    this.bookedCapacity = data_participans_response.reduce(
                      (a, b) => a + parseFloat(b.attendance),
                      0
                    );
                  }
                });
              this.attendanceCustomers = data.group_customers;
              // this.attendies = this.attendanceCustomers.length;

              this.attendanceCustomers.shift();

              if (this.attendanceCustomers.length == 0) {
                this.bookingForm.attendance = false;
                if (this.bookedCapacity < this.capacity) {
                  this.attendanceSwitch = true;
                } else {
                  this.attendanceSwitch = false;
                }
              } else {
                this.bookingForm.attendance = true;
                this.attendanceSwitch = true;
              }
              this.editFlag = true;
            }
            this.bookingForm.price = data.price;
            this.bookingForm.status_id = data.status_id;
            this.bookingForm.order_id = data.id;
            this.bookingForm.customer_id = data.customer_id;

            if (data.company_sale != null) {
              this.bookingForm.company_id = data.company_sale.company_id;
              this.bookingForm.company_sale_id = data.company_sale.id;
              this.getActiveCompanySales();
            }

            this.bookingForm.products = [];
            this.currentOrderProducts = [];
            data.items.forEach((product) => {
              let pdata = {
                order_item_id: product.order_item_id,
                product_id: product.product_id,
                inventory_enable: product.inventory_enable,
                product_type_id: product.product_type_id,
                category_id: product.category_id,
                quantity: product.quantity,
                price: product.price,
                rental: product.rental != null ? true : false,
                venue_service_id: this.venue_service_id,
                discount: product.discount ? product.discount : false,
                name: product.name ? product.name : "Product Name",
                product_price: product.product_price,
                tax: product.tax,
                total_price: product.total,
              };
              if (product.category_name == "open_product" && product.rental) {
                this.bookingWithOpenProduct = true;
              }
              this.currentOrderProducts.push(pdata);
              this.bookingForm.products.push(pdata);
            });
            this.bookingForm.total_price = this.bookingForm.products.reduce(
              (a, b) => a + parseFloat(b.total_price),
              0
            );
            if (
              data.facility_booking.length > 0 &&
              data.facility_booking[0].repeat
            ) {
              let repeatData = data.facility_booking[0].repeat;
              this.bookingForm.repeat_dates_for_payment = [];
              this.bookingForm.repeat = true;
              this.bookingForm.repeats = repeatData.meta;
              this.bookingForm.repeat_id = repeatData.id;
              data.facility_booking.forEach((data) => {
                this.bookingForm.repeat_dates_for_payment.push({
                  date_id: data.id,
                  date: data.date,
                });
              });
              setTimeout(() => {
                this.repeatId = repeatData.id;
              });

              this.disablePromotion = true;
              this.$forceUpdate();
            }
            this.hideLoader();
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.hideLoader();
          this.errorChecker(error);
        });
    },
    removeProduct(index) {
      let data = this.bookingForm.products[index];

      if (this.bookingForm.products[index].inventory_enable) {
        if (data.order_item_id) {
          this.deletedProducts.push(data.order_item_id);
        }

        let products = this.categories.find(
          (item) => item.id == data.category_id
        ).products;

        if (products) {
          products.forEach((el) => {
            if (el.id == data.product_id) {
              el.sales -= data.quantity;
            }
          });
        }
      }

      this.bookingForm.products.splice(index, 1);
      if (this.bookingForm.repeats && this.bookingForm.repeats.length > 0) {
        this.bookingForm.repeats.forEach((repeat) => {
          findIndex = repeat.products.findIndex((x) => x.id == data.product_id);
          if (findIndex != -1) {
            repeat.products.splice(index, 1);
          }
        });
      }

      if (data.rental == false && this.repeatId) {
        var findIndex = this.currentOrderProducts.findIndex(
          (x) => x.product_id == data.product_id
        );
        if (findIndex != null) {
          this.currentOrderProducts.splice(findIndex, 1);
        }
      }

      this.bookingForm.total_price = this.bookingForm.products.reduce(
        (a, b) => a + parseFloat(b.total_price),
        0
      );

      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      this.$forceUpdate();
    },
    verifyBenefit(type) {
      this.clearBenefit();
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add atleast one product");
        return;
      }
      let data = {
        products: [],
      };
      if (type == "promotion") {
        data.promotion_code = this.bookingForm.promotion_code;
        if (data.promotion_code == null) {
          this.clearBenefit();
          return;
        }
      } else {
        data.card_number = this.bookingForm.card_number;
      }
      if (this.bookingForm.mobile) {
        data.mobile = this.bookingForm.mobile;
      }
      if (this.bookingForm.discount) {
        data.products = [];
        this.bookingForm.products.forEach((product) => {
          let pdata = product;
          if (product.discount) {
            pdata.price = product.discount.actual_price;
            delete pdata.discount;
          } else {
            if (product.product_price) {
              pdata.price = product.product_price;
            }
          }
          data.products.push(pdata);
        });
      } else {
        data.products = this.bookingForm.products;
        data.products.forEach((element) => {
          if (element.product_price) {
            element.price = element.product_price;
          } else {
            element.price = element.price / element.quantity;
          }
        });
      }

      let url = "venues/benefits/verify";
      this.$http
        .post(url, data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.bookingForm.discount = data.discount;
            this.bookingForm.price = data.price;
            this.bookingForm.products = data.products;
            this.bookingForm.total_price = data.total;
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    clearBenefit() {
      if (this.bookingForm.discount) {
        if (this.bookingForm.customer_type != "member") {
          this.bookingForm.card_number = null;
        }
        this.bookingForm.price = this.bookingForm.discount.actual_price;
        this.bookingForm.total_price = this.bookingForm.discount.actual_total;
        this.bookingForm.products.forEach((product, index) => {
          if (product.discount) {
            this.bookingForm.products[index].price =
              product.discount.actual_price;
            this.bookingForm.products[index].total_price =
              product.discount.actual_total;
          }
          this.bookingForm.products[index].discount = null;
        });
        setTimeout(() => {
          this.bookingForm.discount = null;
          this.$forceUpdate();
        });
      }
    },
    customerTypeChange() {
      if (this.bookingForm.customer_type == "corporate") {
        this.getActiveCompanySales();
      }
      if (this.bookingForm.promotion_code == null) {
        this.clearBenefit();
      }
      if (
        this.bookingForm.customer_type == "normal" &&
        this.bookingForm.card_number != null
      ) {
        this.$set(this.bookingForm, "card_number", null);
        this.clearBenefit();
      }
    },
    getRentalProducts() {
      if (this.currentRepeatDates) {
        this.validateRepeatBookings(this.currentRepeatDates);
      } else {
        this.$http
          .get(
            `venues/facilities/bookings/rentals?start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&facility_id=${this.facility_id}&date=${this.date}`
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              this.productCombinations = data;
              if (data.length) {
                this.changeRentalProducts(data[0]);
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      }
    },
    changeRentalProducts(rentalCombination) {
      let otherProducts = [];
      if (this.repeatId) {
        otherProducts = this.currentOrderProducts.filter(
          (item) => !item.rental
        );
      }
      if (!this.repeatId) {
        otherProducts = this.bookingForm.products.filter(
          (item) => !item.rental
        );
      }
      this.selectedCombinationPrice = rentalCombination.key;
      let rentals = [];
      rentalCombination.products.forEach((product) => {
        rentals.push({
          product_id: product.id,
          product_type_id: product.product_type_id,
          price: product.price,
          name: product.name,
          tax: product.tax_amount,
          category_id: product.category_id,
          rental: true,
          product_price: product.product_price_when_overlapping
            ? product.product_price_when_overlapping
            : product.product_price,
          quantity: product.quantity,
          total_price:
            product.price +
            (product.total_tax_amount
              ? product.total_tax_amount
              : product.tax_amount * product.quantity),
          total_tax_amount: product.total_tax_amount
            ? product.total_tax_amount
            : null,
        });
      });
      if (otherProducts) {
        this.bookingForm.products = [...rentals, ...otherProducts];
      } else {
        this.bookingForm.products = rentals;
      }

      this.bookingForm.total_price = this.bookingForm.products.reduce(
        (a, b) => a + parseFloat(b.total_price),
        0
      );

      this.showCombinationDialog = false;
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      this.$forceUpdate();
    },
    chooseRentalCombination() {
      this.showCombinationDialog = true;
    },
    getActiveCompanySales() {
      this.$http
        .get(`venues/companies/active/sales?date=${this.date}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.companies = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getCompanySales() {
      return this.bookingForm.company_id != null && this.companies.length
        ? this.companies.find((item) => item.id == this.bookingForm.company_id)
            .company_sale
        : [];
    },
    async removeRepeatRow(index) {
      await this.$store
        .dispatch("removeRepeatRow", index)
        .then(() => {
          this.setRepeatData();
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    setRepeatData() {
      this.productCombinations = [];
      this.bookingForm.repeats = this.$store.getters.getRepeats;
      this.combineRepeatProducts();
    },
    combineRepeatProducts() {
      let otherProducts = this.bookingForm.products.filter(
        (item) => !item.rental
      );
      let rentals = [];
      this.bookingForm.repeats.forEach((repeat) => {
        repeat.products.forEach((product) => {
          let index = rentals.findIndex(
            (item) => item.product_id == product.id
          );
          if (index == -1) {
            rentals.push({
              product_id: product.id,
              product_type_id: product.product_type_id,
              price: product.price,
              name: product.name,
              tax: product.tax_amount,
              category_id: product.category_id,
              rental: true,
              product_price: product.product_price_when_overlapping
                ? product.product_price_when_overlapping
                : product.product_price,
              quantity: product.quantity,
              total_price:
                product.price +
                product.tax_amount *
                  (product.quantity < 1 ? 1 : product.quantity),
            });
          } else {
            rentals[index].price += product.price;
            rentals[index].quantity += product.quantity;
            rentals[index].total_price +=
              product.price + product.tax_amount * product.quantity;
          }
        });
      });

      if (otherProducts) {
        this.bookingForm.products = [...rentals, ...otherProducts];
      } else {
        this.bookingForm.products = rentals;
      }
      this.bookingForm.price = this.bookingForm.products.reduce(
        (a, b) => a + parseFloat(b.price),
        0
      );

      let tax = this.bookingForm.products.reduce(
        (a, b) => a + parseFloat(b.tax * b.quantity),
        0
      );

      this.bookingForm.total_price = this.bookingForm.price + tax;
      this.$forceUpdate();
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
    },
    confirmActions(data) {
      if (data.type == "cancel") {
        this.deleteBookings(data.id);
      } else if (data.type == "repeatBookingCancel") {
        this.multipleCancelForRepeatBooking();
      }
      this.$forceUpdate();
      this.confirmModel.id = null;
    },

    timeFormat(time) {
      return moment(time, "HH:mm:ss").format("hh:mm a");
    },

    confirmCancel() {
      this.confirmModel = {
        id: this.order_id,
        title: `Do you want cancel this booking?`,
        description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel",
      };
    },

    cancelRepeatBooking() {
      if (this.bookingWithOpenProduct) {
        this.confirmCancel();
      } else if (
        this.repeatDatesForPayments.filter((element) => element.isPaid).length >
        0
      ) {
        this.confirmModel = {
          id: this.order_id,
          title: `Do you want cancel this booking?`,
          description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
          type: "repeatBookingCancel",
        };
      } else {
        this.showError("Please select date for cancel");
      }
    },

    deleteBookings(id) {
      this.$http
        .delete(`venues/orders/${id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Booking cancelled successfully");
            this.close();
            this.$emit("cancel");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    changeRepeatBookingSwitch() {
      if (!this.bookingForm.repeat) {
        this.currentRepeatDates = null;
        this.getRentalProducts();
        // this.categoriesList = [
        //   { name: "All", id: null },
        //   { name: "Open Product", id: -1 },
        //   ...this.categories,
        // ];
        this.bookingForm.repeats = [];
      } else {
        // this.categoriesList = [{ name: "All", id: null }, ...this.categories];
        this.$store.commit("resetRepeatState");
        this.$store
          .dispatch("addRepeatRow", {
            start_time: this.bookingForm.start_time,
            end_time: this.bookingForm.end_time,
            date: this.date,
            init: true,
          })
          .catch((error) => {
            this.errorChecker(error);
          });
        this.bookingForm.repeats = [];
      }
      this.$forceUpdate();
    },

    addRepeatRow() {
      this.$store
        .dispatch("addRepeatRow", {
          start_time: null,
          end_time: null,
          date: this.date,
          init: true,
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    showInvoice() {
      if (
        this.bookingForm.parent_order_id &&
        this.bookingForm.parent_orders_id.length > 0
      ) {
        this.$emit(
          "repeatBookingReceipt",
          this.bookingForm.parent_orders_id.sort((a, b) => b - a)
        );
      } else {
        this.$emit("pay", this.bookingForm.order_id);
      }
    },

    changeAttendanceSwitch() {
      if (this.bookingForm.attendance == false) {
        this.bookingForm.attendance_count = 1;
        this.attendanceCustomers = [{}];
        this.attendanceData = [];
      }
      this.$forceUpdate();
    },
    showRepeatPaymentsConfirmation() {
      if (
        this.repeatDatesForPayments.filter(
          (element) => element.isPaid && element.status_id == 5
        ).length ==
        this.repeatDatesForPayments.filter((element) => element.status_id != 2)
          .length
      ) {
        this.$emit("pay", this.bookingForm.order_id);
      } else if (
        this.repeatDatesForPayments.filter(
          (element) => element.isPaid && element.status_id == 1
        ).length > 0
      ) {
        this.showError("Please unselect paid date");
      } else if (
        this.repeatDatesForPayments.filter(
          (element) => element.isPaid && element.status_id == 5
        ).length > 0
      ) {
        var findUnpaidOrders = this.repeatDatesForPayments.filter(
          (x) => x.status_id == 5
        );

        if (
          findUnpaidOrders.length ==
          this.repeatDatesForPayments.filter(
            (element) => element.isPaid && element.status_id == 5
          ).length
        ) {
          this.$emit("pay", findUnpaidOrders[0].order_id);
        } else {
          this.multiplePayment();
        }
      } else {
        this.showError("Please select date for pay");
      }
    },

    multiplePayment() {
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add at least one product");
        return;
      }
      if (
        this.repeatDatesForPayments.filter(
          (element) => element.isPaid && element.status_id == 5
        ).length > 0
      ) {
        this.enableRepeatOrderPay = true;
        this.repeatBookingIds = [];
        this.repeatDatesForPayments.forEach((el) => {
          if (el.isPaid && el.status_id == 5) {
            this.repeatBookingIds.push(el);
          }
        });
      }
      this.$forceUpdate();
    },

    multipleCancelForRepeatBooking() {
      let bookingIds = [];

      this.repeatDatesForPayments.forEach((el) => {
        if (el.isPaid) {
          bookingIds.push({
            booking_id: el.booking_id,
            date: el.date,
            order_id: el.order_id,
            status_id: el.status_id,
            facility_id: el.facility_id,
            end_time: el.end_time,
            start_time: el.start_time,
          });
        }
      });
      this.showLoader();
      this.$http
        .post(`venues/facilities/bookings/repeat/multiple/cancel`, {
          booking_ids: bookingIds,
        })
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Booking cancelled successfully");
            this.close();
            this.$emit("cancel");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    repeatDatesForPaymentChange(dates, rentalCombination) {
      this.repeatDatesForPayments = dates;
      if (dates.filter((element) => element.isPaid).length == 1) {
        this.isEnableRepeatBookingPayment = true;
        this.isEnableRepeateBookingReschedule = true;
      } else if (dates.filter((element) => element.isPaid).length == 0) {
        this.isEnableRepeatBookingPayment = false;
        this.isEnableRepeateBookingReschedule = false;
      } else {
        this.isEnableRepeatBookingPayment = true;
        this.isEnableRepeateBookingReschedule = false;
      }

      if (rentalCombination) {
        this.changeRentalProducts(rentalCombination);
        if (this.bookingForm.promotion_code != null) {
          this.verifyBenefit("promotion");
        }
        if (this.bookingForm.card_number != null) {
          this.verifyBenefit("membership");
        }
      } else {
        this.changeRentalProducts({ products: [] });
      }
    },
    setAttendanceData(data) {
      this.attendanceData = data.customers;
      this.bookingForm.attendance_count =
        data.customers.filter((element) => element.mobile).length + 1;

      this.$forceUpdate();
    },
    searchMember(mobile, id, first_name, last_name) {
      console.log(id, first_name, last_name, mobile);
      this.isSearchLoading = true;
      let query = "";
      //if (typeof id != "undefined" && id != null) {
      query = `field=id&search=${id}`;
      //}
      // else {
      //   query = `field=mobile_number&search=${mobile}`;
      // }
      this.$http
        .get(`venues/memberships/members/filters?${query}`)
        .then((response) => {
          if (response.status == 200) {
            let data = response.data.data;
            if (data.length > 0) {
              this.bookingForm.customer_type = "member";
              this.$set(this.bookingForm, "card_number", data[0].card_number);
              this.$forceUpdate();
              if (this.bookingForm && this.bookingForm.products.length > 0) {
                this.verifyBenefit("membership");
              }
            } else {
              if (this.bookingForm.customer_type == "member") {
                this.bookingForm.customer_type = "normal";
              }
              this.clearCardAndBenefits();
            }
            this.$forceUpdate();
          } else {
            this.clearCardAndBenefits();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
          this.clearCardAndBenefits();
        });
    },
    clearCardAndBenefits() {
      this.bookingForm.member = null;
      this.bookingForm.customer_type = this.bookingForm.customer_type
        ? this.bookingForm.customer_type
        : null;
      this.$set(this.bookingForm, "card_number", null);
      if (this.bookingForm.promotion_code == null) {
        this.clearBenefit();
      }
      this.$forceUpdate();
    },
    checkSlotCapacity() {
      if (parseInt(this.bookingForm.attendance_count) <= 0) {
        this.bookingForm.attendance_count = 1;
        return;
      }

      let totalAttendance;
      if (this.id > 0) {
        totalAttendance =
          parseInt(this.bookingForm.attendance_count) +
          (this.bookedCapacity ? this.bookedCapacity - this.attendies : 0);
      } else {
        totalAttendance =
          parseInt(this.bookingForm.attendance_count) +
          (this.bookedCapacity ? this.bookedCapacity : 0);
      }

      if (totalAttendance > this.capacity) {
        this.bookingForm.attendance_count = 1;
        this.showError(
          `Only ${
            this.capacity -
            (this.attendies
              ? this.bookedCapacity - this.attendies
              : this.bookedCapacity)
          } slot available `
        );
      }
    },

    taxChange() {
      if (this.selectedProduct.price) {
        this.calculateTaxVariation(this.selectedProduct.price, "pre");
      } else if (this.selectedProduct.total_price) {
        this.calculateTaxVariation(this.selectedProduct.total_price, "post");
      }
    },

    calculateTaxVariation(amount, type) {
      let taxTypeId = this.selectedProduct.tax_type_id;
      let taxPercentage = 0;
      if (taxTypeId) {
        taxPercentage = this.taxTypes.find(
          (item) => item.value == taxTypeId
        ).percentage;
      }
      const priceData = this.getTaxVariation(type, taxPercentage, amount);
      this.selectedProduct.price = priceData.price;
      this.selectedProduct.pre_tax_price = priceData.price.toFixed(2);
      this.selectedProduct.total_price = priceData.total_price;
      this.$forceUpdate();
    },
    categoryChange(e) {
      if (e == -1) {
        this.selectedProduct.rental = false;
        this.selectedProduct.product_type_id = 6;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$card-outlined-border-width: 3px;
.open-product {
  background-color: teal;
  cursor: pointer;
  height: 30px;
  width: 60px;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  padding: 5px;
  transition: "border-radius 200ms ease-in-out";
  border-radius: 4px;
  text-align: center;
}
</style>

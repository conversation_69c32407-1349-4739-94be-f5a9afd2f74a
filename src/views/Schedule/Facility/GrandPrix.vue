<template>
  <v-container>
    <v-row class="mx-0 mb-2 pb-2">
      <v-col cols="12">
        <BackButton :handler="gotoSchedule"/>
      </v-col>
    </v-row>
    <div class="w-full row align-center justify-space-between pb-5">
        <div></div>
        <div><v-btn
            :disabled="!venueService"
            fab
            x-small
            color="white"
            @click="prevDate"
        >
          <v-icon dark>mdi-menu-left</v-icon>
        </v-btn>
          <date-field
              v-model="date"
              :block="false"
              :buttonAndText="true"
              :dayName="true"
              :disabled="!venueService"
              :back-fill="true"
              @change="loadGrandPrix"
          >
          </date-field>
          <v-btn
              fab
              color="white"
              x-small
              :disabled="!venueService"
              @click="nextDate"
          >
            <v-icon dark>mdi-menu-right</v-icon>
          </v-btn></div>
        <div>
          <v-btn
              class="export-button mt-2"
              elevation="0"
              height="40"
              @click="printLeaderBoard"
          >
            <SvgIcon text="Export" >
              <template v-slot:icon>
                <ExportIcon/>
              </template>
            </SvgIcon>
          </v-btn>
        </div>
    </div>
    <v-row>
      <v-col lg="7" ml="7" md="7" sm="12" xs="12" class="scrollable-div">
        <div class="d-flex justify-space-between align-center">
          <span class="card-heading">All sessions</span>
          <v-checkbox
              v-model="selectAll"
              label="Select All"
              class="mb-2"
              @change="toggleSelectAll"
          ></v-checkbox>
        </div>

        <v-row class="pt-0">
          <v-col md="4" lg="3" sm="6" v-for="(sessions,index) in raceSessions" :key="sessions.id">
              <div class="md-card shadow-2 rounded-lg pa-2 ma-0 relative">
                <v-checkbox
                    v-model="selectedSessions"
                    :value="sessions.id"
                    class="checkbox-top-right"
                    hide-details
                    @change="checkSelectAllStatus"
                ></v-checkbox>
              <p class="session-heading">Session {{index+1}}</p>
              <p class="session-item">
                <SvgIcon class="gap-x-2 m-b-4 font-medium">
                  <template v-slot:icon>
                    <ClockIcon/>
                  </template>
                  {{sessions.start_time}} - {{sessions.end_time}}
                </SvgIcon>
              </p>
              <p  class="session-item">
                <SvgIcon class="gap-x-2 m-b-4 font-medium">
                  <template v-slot:icon>
                    <ParticipantsIcon/>
                  </template>
                  {{ sessions.participants_count }}
                </SvgIcon>
              </p>
            </div>
          </v-col>
        </v-row>

      </v-col>




      <v-col lg="5" ml="5" md="5" sm="12" xs="12" class="scrollable-div" >
        <v-card class="shadow-2">
          <div class="d-flex align-center justify-space-between p-4">
            <span class="card-heading">Leaderboard 🏆</span>
          <v-text-field
              v-model="participantSearch"
              dense
              label="Search"
              prepend-inner-icon="mdi-magnify"
              hide-details
              @keyup="searchData"
              @click:append="searchData"
              clearable
              outlined
              @click:clear="clearSearch"
              class="q-text-field shadow-0 search-box"
          ></v-text-field>
          </div>
          <div class="mt-0 px-4 pb-4">
            <table class="table border-collapse">
              <thead>
              <tr class="opacity-70 tr-neon tr-rounded">
                <th>
                  <div class="text-center">Position</div>
                </th>
                <th>
                  <div class="">Name</div>
                </th>
                <th>
                  <div class="text-center">Best Lap</div>
                </th>
                <th>
                  <div class="text-center">Total time</div>
                </th>
              </tr>
              </thead>
              <tbody>
              <tr
                  v-for="(item, index) in filteredParticipants"
                  :key="index"
              >
                <td class="text-center">
                  {{item.position}}
                </td>
                <td class="">
                  <div class="d-flex align-center gap-x-1">
                    <goldMedal v-if="item.position == 1" />
                    <silverMedal v-if="item.position== 2" />
                    <bronzeMedal v-if="item.position== 3" />
                    {{item.participant_name}}
                  </div>
                </td>
                <td class="text-center">
                  {{item.best_lap_duration_ms |timeDurationMs}}
                </td>
                <td class="text-center">
                  {{item.total_race_duration_ms |timeDurationMs}}
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

import BackButton from "@/components/Common/BackButton.vue";
import moment from "moment/moment";
import bronzeMedal from "@/assets/images/race/bronzeMedal.svg";
import silverMedal from "@/assets/images/race/silverMedal.svg";
import goldMedal from "@/assets/images/race/goldMedal.svg";
import ClockIcon from "@/assets/images/race/ClockIcon.svg";
import ParticipantsIcon from "@/assets/images/race/ParticipantsIcon.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import ExportIcon from "@/assets/images/misc/export-icon.svg";

export default {
  name: "GrandPrix",
  components: {ExportIcon, SvgIcon, ClockIcon, BackButton,silverMedal,bronzeMedal,goldMedal,ParticipantsIcon},
  computed: {
    venueServices() {
      return this.$store.getters.getSportsService.filter((service) => service.name != "POS");
    },
    selectedSessionIds() {
      return this.selectedSessions;
    },
  },
  mounted() {
    if (typeof this.$route.params.data != "undefined") {
      this.venueService = parseInt(atob(this.$route.params.data));
      setTimeout(() => {
        this.loadGrandPrix();
      },500);
    }
  },
  data() {
    return {
      venueService: null,
      date: moment().format("YYYY-MM-DD"),
      participantSearch:null,
      raceSessions:[],
      participants: [],
      filteredParticipants: [],
      selectedSessions: [],
      selectAll: false, // Tracks "Select All" checkbox state
    }
  },
  methods: {
    toggleSelectAll() {
      if (this.selectAll) {
        // Select all session IDs
        this.selectedSessions = this.raceSessions.map(session => session.id);
      } else {
        // Unselect all
        this.selectedSessions = [];
      }
      if(this.selectedSessions.length > 0) {
        this.getLeaderboard()
      }
    },
    checkSelectAllStatus() {
      // Check if all sessions are selected
      this.selectAll = this.selectedSessions.length === this.raceSessions.length;
      if(this.selectedSessions.length > 0) {
        this.getLeaderboard()
      }
    },
    searchData(){
      this.filteredParticipants = this.participants.filter((item) => {
        return this.participantSearch ? item.participant_name.toLowerCase().includes(this.participantSearch.toLowerCase()) : true;
      });
    },
    clearSearch(){
      this.participantSearch = null;
      this.searchData();
    },
    gotoSchedule() {
      this.$router.push({name: "Schedule"}, () => {
      });
    },
    loadGrandPrix() {
      if (this.venueService) {
        this.showLoader("Loading sessions");
        let url = `venues/facilities/bookings/race/grandprix/get-sessions?venue_service_id=${this.venueService}&date=${this.date}`;
        this.$http.get(url).then((response) => {
          if (response.status == 200) {
            const data = response.data.sessions;
            if (data && data.length) {
              this.raceSessions = data;
            } else {
              this.raceSessions = [];
            }
          }
        }).catch((error) => {
          this.errorChecker(error);
        }).finally(() => {
          this.selectedSessions = [];
          this.selectAll = false;
          this.hideLoader();
        });
      }
    },
    getLeaderboard() {
      if(this.selectedSessions.length <= 0) {
        this.showError('Please select a session')
        return
      }
      this.showLoader("Loading leaderboard");
      let url = `venues/facilities/bookings/race/grandprix/get-leaderboard?`;
      url += this.selectedSessionIds.map(id => `session_ids[]=${encodeURIComponent(id)}`).join('&');

      // this.selectedSessionIds
      this.$http.get(url)
          .then((response) => {
            if (response.status == 200) {
              const data = response.data.data;
              if (data && data.length) {
                this.participants = data;
              } else {
                this.participants = [];
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          })
          .finally(()=> {
            this.searchData();
            this.hideLoader();
          })
    },
    printLeaderBoard() {
      if(this.selectedSessions.length <= 0) {
        this.showError('Please select a session')
        return
      }
      this.showLoader("Loading leaderboard");
      let url = `venues/facilities/bookings/race/grandprix/get-leaderboard-print?`;
      url += this.selectedSessionIds.map(id => `session_ids[]=${encodeURIComponent(id)}`).join('&');

      this.$http.get(url,{
        responseType: "blob",
      })
          .then((response) => {
            this.hideLoader();
            if (response.status == 200) {
              this.downloadReport(response, 'leader-board-report', 'pdf');
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(() => {
            this.hideLoader();
      });
    },
    nextDate() {
      let amount = 1;
      this.date = moment(this.date).add(amount, "days").format("YYYY-MM-DD");
      this.loadGrandPrix()
    },
    prevDate() {
      let amount = 1;
      this.date = moment(this.date)
          .subtract(amount, "days")
          .format("YYYY-MM-DD");
      this.loadGrandPrix()
    },
  }
}
</script>


<style scoped>

.table th{
  padding:8px !important;
}

.table tbody tr {
  transition: all 0.3s ease-in-out;
}

/* Apply shadow and light background on hover */
.table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1); /* Lighten the row */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15); /* Add shadow */
  transform: scale(1.02); /* Slightly enlarge the row */
}

.scrollable-div {
  max-height: 70vh;
  overflow-y: auto;
}

.card-heading{
  font-size: 1.3rem;
  font-weight: bold;
}
.search-box{
  max-width:250px;
  border-radius: 100px;
}

.session-heading{
  font-size: 0.9rem;
  font-weight: bold;
}

.session-item{
  font-size: 0.8rem;
}

.checkbox-top-right {
  position: absolute;
  top: -10px;
  right: 1px;
}
</style>
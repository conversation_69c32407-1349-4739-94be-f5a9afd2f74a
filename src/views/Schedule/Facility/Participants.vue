<template>
  <v-dialog scrollable :value="showParticipants" max-width="500" @input="close">
    <v-card>
      <v-toolbar color="teal" dark>
        <v-toolbar-title>Participants</v-toolbar-title>
        <v-spacer></v-spacer>
      </v-toolbar>
      <v-card-text>
        <v-list subheader v-if="participants.length > 0">
          <v-list-item
            v-for="(participant, index) in participants"
            :key="index"
          >
            <v-list-item-avatar>
              <view-image
                defaultImage="user"
                :contain="false"
                :image="participant.profile_image"
              ></view-image>
            </v-list-item-avatar>

            <v-list-item-content>
              <v-list-item-title
                >{{ participant.name }}
                <span class="cdisplayBlock" v-if="participant.credit_owed"
                  >Balance Due: {{ participant.credit_owed | toCurrency }}</span
                ></v-list-item-title
              >
            </v-list-item-content>
            <v-list-item-action>
              <v-btn
                class="ma-2 white--text teal-color"
                normal
                @click="getOrderDetails(participant.order_id)"
              >
                <span v-if="participant.status_id == 5">Invoice</span>

                <span v-else>Receipt</span>
              </v-btn>
            </v-list-item-action>
            <v-list-item-action>
              <v-btn
                class="ma-2 white--text teal-color"
                text
                @click="openBooking(participant)"
              >
                View
              </v-btn>
            </v-list-item-action>
          </v-list-item>
        </v-list>
        <div
          v-else
          style="height: 200px"
          class="d-flex justify-center align-center"
        >
          No Participants
        </div>
        <v-divider></v-divider>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          v-if="participants.length > 0"
          @click="downloadPdf()"
          class="ma-2 black--text yellow-color"
          text
          >Print
        </v-btn>

        <v-btn @click="close()" class="ma-2 white--text blue-color" text
          >Close
        </v-btn>
      </v-card-actions>
      <order-details
        :id="orderId"
        :log="false"
        @close="(orderId = null), openParticipants()"
      ></order-details>
    </v-card>
  </v-dialog>
</template>

<script>
import OrderDetails from "../../../components/Order/OrderDetails.vue";
export default {
  props: {
    start_time: { type: String },
    "show-participants": { type: Boolean, default: false },
    end_time: { type: String },
    date: { type: String },
    facility_id: { type: Number },
    refresh: { type: Boolean },
  },
  watch: {
    showParticipants(val) {
      if (val == true) {
        this.openParticipants();
      }
    },
    refresh() {
      this.openParticipants();
    },
  },
  components: {
    OrderDetails,
  },
  data() {
    return {
      participants: [],
      orderId: null,
    };
  },
  methods: {
    openParticipants() {
      this.showLoader("Loading");
      this.$http
        .get(
          `venues/facilities/bookings/participants?facility_id=${
            this.facility_id
          }&start_time=${this.start_time}&end_time=${
            this.end_time == "00:00:00" ? "23:59:00" : this.end_time
          }&date=${this.date}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.participants = data;
            this.hideLoader();
          }
        });
    },
    close() {
      this.$emit("close");
    },
    openBooking(data) {
      data.status = data.status_id == 5 ? "unpaid" : "paid";
      this.$emit("open-capacity-booking", data);
    },
    downloadPdf() {
      this.showLoader("Generating..");
      this.$http
        .get(
          `venues/facilities/bookings/booked-attendance-pdf?facility_id=${this.facility_id}&start_time=${this.start_time}&end_time=${this.end_time}&date=${this.date}`,
          {
            responseType: "blob",
          }
        )
        .then((response) => {
          if (response.status == 200) {
            this.downloadFile(response, `OrderCustomers`, "pdf");
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getOrderDetails(id) {
      this.orderId = id;
    },
  },
};
</script>

<style>
span.cdisplayBlock {
  display: block;
  font-size: 12px;
  padding-top: 5px;
}
</style>

<template>
  <v-dialog scrollable :value="showParticipants" max-width="700" @input="close">
    <v-card>
      <v-toolbar color="teal" dark height="80">
        <v-toolbar-title>Participants</v-toolbar-title>
        <v-spacer></v-spacer>
        <!-- <v-toolbar-title>Price {{ orderTotal | toCurrency }}</v-toolbar-title> -->
        <v-toolbar-title v-if="is_split_payment == 0"
        >Price {{ invoiceAmount | toCurrency }}</v-toolbar-title
        >
        <v-spacer></v-spacer>
        <v-text-field
            class="mt-6"
            style="width: 80px"
            v-model="search"
            label="Search"
            clearable
            outlined
            dense
        ></v-text-field>
        <v-spacer></v-spacer>
        <!-- <v-select
          v-if="payer"
          :items="payerCustomerList"
          v-model="payer"
          item-value="customer_id"
          item-text="customer_name"
          outlined
          :menu-props="{ bottom: true, offsetY: true }"
          return-object
          label="Payer"
          dense
          class="mt-6"
          style="width: 100px"
          :rules="[(v) => !!v || 'Payer is required']"
          required
          @change="changePayer()"
        ></v-select> -->
      </v-toolbar>
      <v-card-text>
        <v-list subheader v-if="participants.length > 0">
          <div
              v-for="(participant, index) in filterParticipants"
              :key="index"
              :class="`parent-list-items ${
              participants[index + 1] &&
              participants[index + 1].order_id != participants[index].order_id
                ? 'border-show'
                : ''
            }`"
          >
            <v-list-item class="d-block px-0">
              <div class="d-flex align-center">
                <v-list-item-action>
                  <!-- <v-checkbox
                    :disabled="participant.status_id === 1 || participant.status_id === 14"
                    :readonly="participant.order_discount_id !== null"
                    :indeterminate="
                      participant.status_id === 1 ||
                      participant.status_id === 14 ||
                      participant.order_discount_id !== null
                    "
                    @change="isPaidCheckBoxEnable(participant)"
                    v-model="participant.isPaid"
                  ></v-checkbox> -->
                  <v-checkbox
                      :disabled="
                    participant.invoice_generated === 1 ||
                    participant.o_status_id !== 5 ||
                    participant.order_status_id !== 11
                  "
                      :indeterminate="
                    participant.invoice_generated === 1 ||
                    participant.o_status_id !== 5 ||
                    participant.order_status_id !== 11
                  "
                      :value="participant.isSettle"
                      @change="checkedOrder(participant)"
                      v-model="participant.isSettle"
                  ></v-checkbox>
                </v-list-item-action>
                <v-list-item-avatar :width="60" :height="60">
                  <view-image
                      defaultImage="user"
                      :contain="false"
                      :image="participant.profile_image"
                      class=""
                  ></view-image>
                </v-list-item-avatar>

                <v-list-item-content>
                  <v-list-item-title class="ml-2">
                  <span
                      class="text-truncate"
                      style="cursor: pointer"
                      @click="showUserModel(participant.customer_id)"
                  >{{ participant.name }}</span
                  >
                    <span class="grey--text caption displayBlock">{{
                        participant.invoice_seq_no
                      }}</span>

                    <span
                        class="grey--text caption"
                        v-if="
                      participant.order_customer_id == participant.customer_id
                    "
                    >{{
                        (participant.order_total +
                            getParentOrderTotal(participant.parent_orders))
                            | toCurrency
                      }}</span
                    >
                    <span
                        v-if="
                      participant.actual_total != null &&
                      participant.order_discount_id !== null
                    "
                        class="text-decoration-line-through grey--text caption"
                    >
                    {{
                        (participant.actual_total +
                            getParentOrderTotal(participant.parent_orders))
                            | toCurrency
                      }}</span
                    >
                    <span
                        class="grey--text caption displayBlock"
                        v-if="participant.credit_owed"
                    >Credit Due:
                    {{ participant.credit_owed | toCurrency }}</span
                    ></v-list-item-title
                  >
                </v-list-item-content>

                <v-list-item-action class="d-flex flex-row justify-start align-center mt-0" >
                  <div style="min-width: 100px"></div>
                  <v-btn
                      v-if="
                    participant.order_customer_id == participant.customer_id
                  "
                      class="ma-2 white--text teal-color"
                      icon
                      @click="getOrderDetails(participant)"
                  >

                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-bind="attrs"
                                v-on="on"
                                v-if="participant.status_id == 5"
                        >
                          mdi-invoice-outline
                        </v-icon>
                        <v-icon v-else v-bind="attrs"
                                v-on="on"
                        >mdi-invoice-check-outline</v-icon>
                      </template>
                      <span
                      >View Transaction</span
                      >
                    </v-tooltip>
                  </v-btn>
                  <v-btn
                      v-else
                      class="ma-1 white--text teal-color"
                      :color="`${
                    participant.order_customer_id != participant.customer_id
                      ? 'purple'
                      : ''
                  }`"
                      small
                  >
                    <span>{{ participant.payer_name }}</span>
                  </v-btn>
                  <v-btn
                      v-if="
                    is_split_payment == 0 ||
                    (participant.so_parent_order_id != null &&
                      participant.order_id == participant.so_parent_order_id)
                  "
                      icon
                      class="ma-1 white--text blue-color"
                      @click="openBooking(participant)"
                  >
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-bind="attrs"
                                v-on="on"
                        >mdi-eye-outline
                        </v-icon>
                      </template>
                      <span
                      >View Booking</span
                      >
                    </v-tooltip>
                  </v-btn>
                  <v-btn
                      v-if="
                  participant.alert_notes != null &&
                  participant.alert_notes != ''
                "
                      class="ma-1 white--text red-color"
                      icon
                      @click="openAlert(participant.customer_id)"
                  >
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-bind="attrs"
                                v-on="on"
                        >mdi-alert
                        </v-icon>
                      </template>
                      <span
                      >Alert</span
                      >
                    </v-tooltip>
                  </v-btn>
                  <v-btn
                      v-show="
                    participant.check_in_time == null &&
                    participant.check_out_time == null
                  "
                      icon
                      @click="checkInAndOut(participant.group_customer_id)"
                      class="ma-1 white--text green-color"
                  >
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-bind="attrs"
                                v-on="on"
                        >mdi-map-marker-outline
                        </v-icon>
                      </template>
                      <span
                      >Check-in</span
                      >
                    </v-tooltip>
                  </v-btn
                  >
                  <v-btn
                      v-show="
                    participant.check_in_time !== null &&
                    participant.check_out_time === null
                  "
                      icon
                      @click="checkInAndOut(participant.group_customer_id)"
                      class="ma-1 white--text green-color"
                  >
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-bind="attrs"
                                v-on="on"
                        >mdi-map-marker-check-outline
                        </v-icon>
                      </template>
                      <span
                      >Check-out</span
                      >
                    </v-tooltip>
                  </v-btn>
                  <v-btn
                      v-if="participant.check_in_and_out === 'OUT'"
                      class="ma-2 white--text red-color"
                      icon
                      @click="voidCheckIn(participant.group_customer_id)"
                  >
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-bind="attrs"
                                v-on="on"
                        >mdi-map-marker-off-outline
                        </v-icon>
                      </template>
                      <span
                      >Void Check-in</span
                      >
                    </v-tooltip>


                  </v-btn>
                </v-list-item-action>
              </div>
            </v-list-item>
          </div>
        </v-list>
        <div
            v-else
            style="height: 200px"
            class="d-flex justify-center align-center"
        >
          No Participants
        </div>
        <!-- <v-divider></v-divider> -->
      </v-card-text>
      <v-card-actions class="modal-buttons">
        <v-btn
            v-if="participants.length > 0"
            @click="downloadPdf()"
            class="ma-2 black--text yellow-color"
            text
        >Print
        </v-btn>
        <v-btn @click="close()" class="ma-2 white--text blue-color" text
        >Close
        </v-btn>
        <v-spacer></v-spacer>
        <!-- <v-btn
          v-if="payerCustomerList.length > 0 && payer"
          @click="showMultiPaymentsConfirmation"
          class="ma-2 white--text teal-color"
          text
        >
          Pay
        </v-btn> -->
        <v-btn
            v-if="selectedOrders.length"
            @click="genrateInvoice"
            class="ma-2 white--text teal-color"
            text
        >
          Generate Invoice
        </v-btn>
      </v-card-actions>

      <order-details
          :id="orderId"
          :ids="orderIds"
          :invId="invoiceModel.invoiceId"
          :log="false"
          @close="(orderId = null), (orderIds = null), openParticipants()"
      ></order-details>
      <golf-order-payments
          :refreshBool="refreshBool"
          v-if="enableMultiOrderPay"
          v-bind="bookingForm"
          :show="enableMultiOrderPay"
          :multiBookingIds="multiBookingIds"
          @close="closeOrderPaymentDialog"
          :date="date"
          @payed="$emit('booked'), (enableMultiOrderPay = false)"
      ></golf-order-payments>
      <customer-alert-note
          v-bind="alertNote"
          :id="alertNote.id"
          :note="alertNote.note"
          @clearAlertNote="clearAlertNote"
          @closeAlert="closeAlert"
      >
      </customer-alert-note>
      <customer-model
          v-bind="userModel"
          @close="userModel.userID = null"
          :isPermissionCheck="
          checkWritePermission($modules.clients.customers.slug)
        "
      />
    </v-card>
  </v-dialog>
</template>

<script>
import OrderDetails from "../../../../components/Order/OrderDetails.vue";
import GolfOrderPayments from "@/components/Order/GolfOrderPayments.vue";
import CustomerModel from "@/views/Clients/Customer/CustomerModel";
import CustomerAlertNote from "@/views/Clients/Customer/CustomerAlertNote.vue";

export default {
  props: {
    start_time: {type: String},
    showParticipants: {type: Boolean, default: false},
    end_time: {type: String},
    date: {type: String},
    facility_id: {type: Number},
    refresh: {type: Boolean},
    venue_service_id: {type: Number},
    is_split_payment: {type: Number, default: 0},
  },
  watch: {
    showParticipants(val) {
      if (val == true) {
        this.openParticipants();
      }
    },
    refresh() {
      this.openParticipants();
    },
    gop() {
      this.enableMultiOrderPay = true;
    },
  },
  components: {
    OrderDetails,
    CustomerAlertNote,
    GolfOrderPayments,
    CustomerModel,
  },
  computed: {
    filterParticipants() {
      const search = this.search;
      return search === "" || search == null
          ? this.participants
          : this.participants.filter((participant) =>
              participant.name.toLowerCase().includes(search.toLowerCase())
          );
    },
  },
  data() {
    return {
      participants: [],
      multiplePayments: [],
      orderId: null,
      orderIds: null,
      payer: null,
      orderTotal: 0,
      payerCustomerList: [],
      bookingForm: {attendance: false, attendance_count: 1, products: []},
      enableMultiOrderPay: false,
      multiBookingIds: [],
      search: null,
      products: [],
      gop: 0,
      refreshBool: false,
      alertNote: {
        show: false,
        note: null,
        id: null,
      },
      userModel: {userID: null, type: "details"},
      selectedOrders: [],
      invoiceAmount: 0,
      invoiceModel: {
        invoiceId: null,
        invoiceIds: [],
        type: "details",
        orderIds: [],
      },
    };
  },
  methods: {
    showUserModel(userId) {
      this.userModel.userID = parseInt(userId);
      this.userModel.type = "details";
    },
    openAlert(id) {
      this.showLoader();
      this.$http
          .get("venues/customers/get-alert-note/" + id)
          .then((response) => {
            this.hideLoader();
            if (response.status == 200) {
              this.alertNote.id = response.data.data.customer_id;
              this.alertNote.note = response.data.data.alert_notes;
              this.alertNote.show = true;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    clearAlertNote(id) {
      this.showLoader();
      this.$http
          .post("venues/customers/clear-alert-note/" + id)
          .then((response) => {
            this.hideLoader();
            if (response.status == 200) {
              this.showSuccess("Customer alert note cleared!");
              this.closeAlert();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    closeAlert() {
      this.alertNote.show = false;
      this.openParticipants();
    },
    closeOrderPaymentDialog() {
      this.enableMultiOrderPay = false;
      this.openParticipants();
    },
    openParticipants() {
      this.showLoader("Loading");
      this.payer = null;
      this.payerCustomerList = [];
      let url = "participants";
      if (this.is_split_payment == 1) url = "split-participants";
      this.$http
          .get(
              `venues/facilities/bookings/golf/${url}?facility_id=${
                  this.facility_id
              }&start_time=${this.start_time}&end_time=${
                  this.end_time == "00:00:00" ? "23:59:00" : this.end_time
              }&date=${this.date}&venue_service_id=${
                  this.venue_service_id
              }&is_split_payment=${this.is_split_payment}`
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              // let parentParticipants = [];
              this.participants = data;
              // data.map((participant) => {
              //   if (participant.customer_id == participant.order_customer_id) {
              //     participant.dependent = [];
              //     parentParticipants.push(participant);
              //   }
              // });
              // if (parentParticipants.length) {
              //   console.log("data", data);
              //   parentParticipants.forEach((dParticipant, index) => {
              //     data.forEach((element) => {
              //       if (
              //         element.customer_id != dParticipant.customer_id &&
              //         element.order_id === dParticipant.order_id
              //       ) {
              //         parentParticipants[index].dependent.push(element);
              //       }
              //     });
              //   });
              //   this.participants = parentParticipants;
              //   this.orderTotal = 0;
              //   this.refreshBool = !this.refreshBool;

              //   console.log(this.participants);
              // }
              // this.participants = data;
              // this.orderTotal = 0;
              // this.refreshBool = !this.refreshBool;
              // // console.log(this.bookingForm.products);
              this.hideLoader();
            }
          });
    },
    close() {
      this.invoiceModel.invoiceId = null;
      this.$emit("close");
    },
    openBooking(data) {
      data.status = data.status_id == 5 ? "unpaid" : "paid";
      data.is_split_payment = this.is_split_payment;
      this.$emit("open-capacity-booking", data);
    },
    downloadPdf() {
      this.showLoader("Generating..");
      this.$http
          .get(
              `venues/facilities/bookings/booked-attendance-pdf?facility_id=${this.facility_id}&start_time=${this.start_time}&end_time=${this.end_time}&date=${this.date}&venue_service_id=${this.venue_service_id}`,
              {
                responseType: "blob",
              }
          )
          .then((response) => {
            if (response.status == 200) {
              this.downloadFile(response, `OrderCustomers`, "pdf");
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getOrderDetails(participant) {
      let parentOrderIds = [];
      if (participant.parent_orders.length) {
        parentOrderIds.push(participant.order_id);
        participant.parent_orders.map((item) => {
          parentOrderIds.push(item.id);
        });
        this.orderIds = parentOrderIds;
      } else {
        this.orderIds = null;
      }
      this.orderId = participant.order_id;
    },
    isPaidCheckBoxEnable(participant) {
      // console.log(participant);
      const dates = this.participants;
      if (participant.isPaid === false) {
        let nanoIndexes = this.participants
            .map((val, i) =>
                val.order_id === participant.order_id && val.isPaid ? i : -1
            )
            .filter((index) => index !== -1);
        if (nanoIndexes.length) {
          nanoIndexes.forEach((val) => {
            this.participants[val].isPaid = false;
          });
        }
      } else if (participant.isPaid === true) {
        let nanoIndexes = this.participants
            .map((val, i) =>
                val.order_id === participant.order_id && !val.isPaid ? i : -1
            )
            .filter((index) => index !== -1);
        // console.log(nanoIndexes);
        if (nanoIndexes.length) {
          nanoIndexes.forEach((val) => {
            this.participants[val].isPaid = true;
          });
        }
      }
      if (dates.filter((element) => element.isPaid).length > 0) {
        var formData = new FormData();
        var isPaidIndex = 0;
        var payerList = [];
        this.products = [];
        this.bookingForm.products = [];
        var uniqueOrderIds = [];
        dates.forEach((facilityBookings) => {
          if (facilityBookings.isPaid) {
            if (uniqueOrderIds.indexOf(facilityBookings.order_id) !== -1) {
              return;
            }
            uniqueOrderIds.push(facilityBookings.order_id);

            payerList.push({
              customer_id: facilityBookings.customer_id,
              customer_name: facilityBookings.name,
            });
            formData.append(
                `orderIds[${isPaidIndex}]`,
                facilityBookings.order_id
            );
            isPaidIndex++;
            this.bookingForm.id = facilityBookings.id;
            this.bookingForm.date = facilityBookings.date;
            this.bookingForm.end_time = facilityBookings.end_time;
            this.bookingForm.facility_id = facilityBookings.facility_id;
            this.bookingForm.facility_name = facilityBookings.facility_name;
            this.bookingForm.isPaid = facilityBookings.isPaid;
            this.bookingForm.min_booking_time =
                facilityBookings.min_booking_time;
            this.bookingForm.order_id = facilityBookings.order_id;
            this.bookingForm.per_capacity = facilityBookings.per_capacity;
            this.bookingForm.start_time = facilityBookings.start_time;
            this.bookingForm.status_id = facilityBookings.status_id;
          }
        });
        this.showLoader("Loading");
        this.$http
            .post(
                `venues/facilities/bookings/multiple-participant-products`,
                formData
            )
            .then((response) => {
              this.hideLoader();
              if (response.status == 200 && response.data.status == true) {
                if (response.data.data) {
                  let products = response.data.data.products;
                  let orderTotal = 0;
                  this.products = products;

                  this.payerCustomerList = payerList;
                  this.payer = this.payerCustomerList[0];
                  this.multiplePayments = this.participants;
                  this.products.forEach((item) => {
                    orderTotal += item.price + item.total_tax_amount;
                    let pata = {
                      order_item_id: item.id,
                      product_id: item.product_id,
                      product_type_id: item.product_type_id,
                      category_id: item.category_id,
                      quantity: item.quantity,
                      price: item.price,
                      rental: item.rental != null ? true : false,
                      discount: item.discount ? item.discount : false,
                      name: item.name ? item.name : "Product Name",
                      product_price: item.product_price,
                      tax: item.total_tax_amount,
                      total_tax_amount: item.total_tax_amount,
                      total_price: item.price + item.total_tax_amount,
                      venue_service_id: this.venue_service_id,
                    };
                    this.bookingForm.products.push(pata);
                  });
                  this.orderTotal = orderTotal;
                  // console.log(this.bookingForm.products);
                }
              }
            })
            .catch((error) => {
              this.hideLoader();
              this.errorChecker(error);
            });
      } else if (dates.filter((element) => element.isPaid).length == 0) {
        this.payerCustomerList = [];
        this.payer = null;
        this.bookingForm = [];
        this.orderTotal = 0;
      }
    },

    showMultiPaymentsConfirmation() {
      if (
          this.multiplePayments.filter(
              (element) => element.isPaid && element.status_id == 5
          ).length == 1
      ) {
        this.bookingForm.showBookingForm = false;
        this.payerCustomerList = [];
        // console.log(this.bookingForm);
        this.orderId = this.bookingForm.order_id;
      } else if (
          this.multiplePayments.filter(
              (element) => element.isPaid && element.status_id == 1
          ).length > 0
      ) {
        this.showError("Please unselect paid date");
      } else if (
          this.multiplePayments.filter(
              (element) => element.isPaid && element.status_id == 5
          ).length > 1
      ) {
        this.gop++;
        // console.log("pay");
        this.multiplePayment();
      } else {
        this.showError("Please select participant for pay");
      }
    },
    multiplePayment() {
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add at least one product");
        return;
      }
      if (
          this.multiplePayments.filter(
              (element) => element.isPaid && element.status_id == 5
          ).length > 0
      ) {
        this.multiBookingIds = [];

        this.multiplePayments.forEach((el) => {
          if (el.isPaid && el.status_id == 5) {
            this.multiBookingIds.push(el);
          }
        });
        this.bookingForm.customer_id = this.payer.customer_id;
        this.bookingForm.customer_name = this.payer.customer_name;
        // console.log("products", this.bookingForm.products);
        this.enableMultiOrderPay = true;
      }
      this.$forceUpdate();
    },
    changePayer() {
      this.bookingForm.customer_id = this.payer.customer_id;
      this.bookingForm.customer_name = this.payer.customer_name;
    },
    getPayerName(order_id) {
      let name = this.participants.find(
          (x) => x.order_id === order_id && x.order_customer_id === x.customer_id
      ).name;
      console.log(name);
      return name;
    },
    getColourDisabled(status_id) {
      if (status_id === 1) {
        return "success";
      } else {
        return "warning";
      }
    },
    getParentOrderTotal(parentOrders) {
      let total = 0;
      parentOrders.map((item) => {
        total += item.total;
      });
      return total;
    },
    voidCheckIn(id) {
      this.showLoader("Loading");
      this.$http
          .get(`venues/facilities/bookings/void-check-in/${id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.openParticipants();
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    checkInAndOut(id) {
      this.showLoader("Loading");
      this.$http
          .get(`venues/facilities/bookings/check-in-and-out/${id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.openParticipants();
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    checkedOrder(participant) {
      let invAmount = 0;
      let selectedOrd = [];
      this.selectedOrders = [];

      /** */

      console.log("participant.isSettle");
      console.log(participant.isSettle);
      if (!participant.isSettle) {
        /** unselect same order_id */
        this.participants.map((p) => {
          if (p.order_id === participant.order_id) {
            p.isSettle = false;
          }
        });
      } else if (participant.isSettle === true) {
        this.participants.map((item) => {
          if (item.order_id === participant.order_id) {
            item.isSettle = true;
          }
        });
      }
      /** calculate selected order */
      this.participants.map((item) => {
        if (item.isSettle === true) {
          const oIndex = selectedOrd.findIndex(
              (or) => or.order_id === item.order_id
          );
          if (oIndex === -1) {
            /** to fixed  */
            let arr = {
              order_id: item.order_id,
              total: parseFloat(item.order_total),
            };
            invAmount += item.order_total;
            selectedOrd.push(arr);
          }
        }
      });

      this.selectedOrders = selectedOrd;
      this.invoiceAmount = invAmount;
      console.log(this.selectedOrders);
    },
    /** invoice functions */
    genrateInvoice() {
      if (this.selectedOrders.length === 0) {
        this.showError("Select Order");
        return false;
      }
      this.showLoader("Wait ...");
      let formData = new FormData();
      this.selectedOrders.forEach((item, i) => {
        formData.append(`order_ids[${i}]`, item.order_id);
      });
      this.$http
          .post("venues/invoices", formData)
          .then((response) => {
            this.hideLoader();
            if (response.status == 200 && response.data.status) {
              this.selectedOrders = [];
              let data = response.data.data;
              this.invoiceData = data;
              this.invoiceModel.invoiceId = data.id;
              this.invoiceModel.orderIds = this.selectedOrders;
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
  },
};
</script>

<style>
.parent-list-items.border-show {
  border-bottom: 1px inset;
}

.modal-buttons {
  border-top: 1px inset;
}

span.cdisplayBlock {
  display: block;
  padding-top: 5px;
}

span.displayBlock {
  display: block;
}
</style>

<template>
  <v-container fluid>
    <v-row justify="center" no-gutters class="mt-3 pb-4 scheduleSelection">
      <v-hover v-slot:default="{ hover }">
        <v-btn
            icon
            :elevation="hover ? 2 : 0"
            :color="hover ? 'teal' : 'grey'"
            @click="getFacilitySchedule"
        >
          <v-icon>mdi-refresh-circle</v-icon>
        </v-btn>
      </v-hover>
      <v-spacer></v-spacer>
      <v-spacer></v-spacer>
      <v-col
          md="2"
          justify="center"
          v-if="
          checkReadPermission($modules.facility.schedule.slug) &&
            checkReadPermission($modules.events.schedule.slug)
        "
      >
        <router-link :to="`/schedule`">
          <v-btn block color="#062b48" dark tile>Facility</v-btn>
        </router-link>
      </v-col>
      <v-col
          md="2"
          justify="center"
          v-if="
          checkReadPermission($modules.facility.schedule.slug) &&
            checkReadPermission($modules.events.schedule.slug)
        "
      >
        <router-link :to="`/event-schedule`">
          <v-btn block light tile>Events</v-btn>
        </router-link>
      </v-col>
      <!-- <v-col
        md="2"
        justify="center"
        v-if="checkReadPermission($modules.workshops.schedule.slug)"
      >
        <router-link :to="`/workshop-schedule`">
          <v-btn block light tile>Workshop</v-btn>
        </router-link>
      </v-col> -->
      <v-spacer></v-spacer>
      <v-col md="2" class="text-lg-right mr-2">
        <v-select
            v-if="
            checkReadPermission($modules.facility.schedule.slug) &&
              checkReadPermission($modules.events.schedule.slug)
          "
            :items="venueServices"
            v-model="venueService"
            item-value="venue_service_id"
            item-text="name"
            outlined
            :menu-props="{ bottom: true, offsetY: true }"
            return-object
            background-color="#fff"
            dense
            @change="initializeScheduleForVenueService()"
        ></v-select>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col sm="2">
        <router-link :to="`/schedule`">
          <v-btn color="#062b48" dark tile>Day</v-btn>
        </router-link>
        <v-btn @click="gotoCalendar" light tile>Month</v-btn>
      </v-col>
      <v-col sm="2">
        <v-btn
            v-if="
            facilities.length > 0 &&
              checkExportPermission($modules.facility.schedule.slug)
          "
            @click="reportDownload"
            color="teal white--text"
            light
            tile
        >Report</v-btn
        >
      </v-col>
      <v-col sm="4">
        <v-row no-gutters>
          <v-col sm="1" class="text-lg-center mr-1">
            <v-btn
                :disabled="!venueService.venue_service_id"
                fab
                dark
                x-small
                color="teal"
                @click="prevDate"
            >
              <v-icon dark>mdi-menu-left</v-icon>
            </v-btn>
          </v-col>
          <v-col sm="8" class="text-lg-center">
            <date-field
                v-model="date"
                :button="true"
                :dayName="true"
                :disabled="!venueService.venue_service_id"
                @change="getFacilitySchedule"
            >
            </date-field>
          </v-col>
          <v-col sm="1" class="text-lg-center ml-1">
            <v-btn
                fab
                dark
                x-small
                color="teal"
                :disabled="!venueService.venue_service_id"
                @click="nextDate"
            >
              <v-icon dark>mdi-menu-right</v-icon>
            </v-btn>
          </v-col>
        </v-row>
      </v-col>

      <v-col sm="4" class="text-lg-right">
        <v-row no-gutters>
          <v-spacer></v-spacer>
          <v-col
              v-if="
              checkReadPermission($modules.facility.schedule.slug) &&
                !checkReadPermission($modules.events.schedule.slug)
            "
              sm="5"
              class="text-lg-right mr-2"
          >
            <v-select
                :items="venueServices"
                v-model="venueService"
                item-value="venue_service_id"
                item-text="name"
                outlined
                background-color="#fff"
                dense
                :menu-props="{ bottom: true, offsetY: true }"
                return-object
                @change="initializeScheduleForVenueService()"
            ></v-select>
          </v-col>

          <v-col
              v-if="isGameFormationEnabled"
              :sm="!checkReadPermission($modules.events.schedule.slug) ? 5 : 6"
              class="text-lg-right mr-2"
          >
            <v-select
                v-model="gameFormationFilter"
                :items="gameFormations()"
                label="Game Formation"
                item-value="id"
                item-text="name"
                multiple
                :menu-props="{ bottom: true, offsetY: true }"
                @change="getFacilitySchedule()"
                outlined
                dense
                background-color="#fff"
                class="sch-gameformation-field"
            >
              <template
                  v-if="gameFormations().length == gameFormationFilter.length"
                  v-slot:selection="{ index }"
              >
                <span v-if="index == 0">All Services</span>
              </template>
              <template v-else v-slot:selection="{ item, index }">
                <span v-if="index == 0">{{ item.name }}, </span>
                <span v-if="index == 1">{{ item.name }}, </span>
                <span v-if="index === 2" class="grey--text caption pl-1"
                >and {{ gameFormationFilter.length - 1 }} others</span
                >
              </template>
              <template v-slot:prepend-item>
                <v-list-item ripple @click="toggle">
                  <v-list-item-action>
                    <v-icon
                        :color="
                        gameFormationFilter.length > 0 ? 'teal darken-4' : ''
                      "
                    >{{ icon() }}</v-icon
                    >
                  </v-list-item-action>
                  <v-list-item-content>
                    <v-list-item-title>Select All</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
                <v-divider class="mt-2"></v-divider>
              </template>
            </v-select>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <div style="display: inline; display: flex" class="mt-6">
      <div
          style="width: 200px"
          class="d-flex flex-column overflow-x-hidden overflow-y-hidden"
      >
        <header-cell name="Time"></header-cell>
        <div
            class="overflow-auto no-scroll"
            ref="scheduleTimer"
            @scroll="onScroll"
            :style="
            `min-height: ${scheduleHeight}px; max-height: ${scheduleHeight}px;margin-right:-1px;`
          "
        >
          <time-column :increment="increment" :height="height"></time-column>
        </div>
      </div>
      <div style="width: calc(100% - 200px)">
        <div
            class="d-flex overflow-x-hidden overflow-y-hidden"
            ref="scheduleHeader"
        >
          <template v-for="facility in facilities">
            <header-cell
                :key="`h_${facility.id}`"
                :increment="increment"
                :height="height"
                :name="facility.name"
                :facility-id="facility.id"
                :is-public="facility.is_public"
                :is-enable-online="isEnableOnline"
                :isGameFormationEnabled="isGameFormationEnabled"
                :facility-online-disabled="facility.is_facility_online_disabled"
                :formation="facility.game_formations"
                :perCapacity="facility.per_capacity"
                :totalCapacity="facility.capacity"
                :totalAttendees="countTotalAttendees(facility)"
                @enableDisableOnlineFacility="enableDisableOnlineFacility"
                @showTotalAttendees="showTotalAttendees(facility.id)"
                :perDayCapacity="facility.capacity"
                :isEnablePerDayCapacity="facility.is_enable_per_day_capacity"
            ></header-cell>
          </template>
        </div>
        <div
            v-resize="onResize"
            ref="schedule"
            class="d-flex overflow-auto grey--text text--lighten-2 caption"
            :style="
            `min-height: ${scheduleHeight}px; max-height: ${scheduleHeight}px`
          "
            @scroll="onScroll"
        >
          <template v-for="(facility, index) in facilities">
            <facility-column
                :key="`t_${facility.id}`"
                :increment="increment"
                :height="height"
                @open-booking-form="openBooking"
                @open-participans-model="openParticipants"
                :name="facility.name"
                :capacity="facility.capacity"
                :perCapacity="facility.per_capacity"
                :perDayCapacity="facility.capacity"
                :isEnablePerDayCapacity="facility.is_enable_per_day_capacity"
                :minBookingTime="facility.min_booking_time"
                :id="facility.id"
                :bookings="getSlotData(index)"
                :totalAttendance="Number(facility.total_attendance)"
            >
            </facility-column>
          </template>
        </div>
      </div>
    </div>

    <booking-form
        v-bind="bookingForm"
        @repeatBookingReceipt="repeatBookingReceipt"
        @close="bookingForm.showBookingForm = false"
        @booked="completeOrder"
        @cancel="getFacilitySchedule"
        @pay="openOrderCloseBooking"
        @refresh="getFacilitySchedule"
        :perCapacity="bookingForm.per_capacity"
        :minBookingTime="bookingForm.min_booking_time"
        :increment="increment"
        @reschedule="showReschedule"
        @repeatRefundAndCancel="repeatRefundAndCancel"
    ></booking-form>
    <order-details
        :id="orderId"
        :ids="orderIds"
        :isSchedulePage="true"
        @close="(orderId = null), (orderIds = null)"
        @paymentDone="getFacilitySchedule"
    ></order-details>
    <!-- <participants
      :refresh="refresh"
      v-bind="participant"
      @open-booking="openBooking"
      @open-capacity-booking="openBookingCapacity"
      @close="(participant.showParticipants = false), getFacilitySchedule()"
    ></participants> -->
    <golf-participants
        :refresh="refresh"
        :venue_service_id="venueService.venue_service_id"
        v-bind="participant"
        @open-booking="openBooking"
        @open-capacity-booking="openBookingCapacity"
        @close="(participant.showParticipants = false), getFacilitySchedule()"
        @pay="openOrderCloseBooking"
        @booked="completeOrder"
    ></golf-participants>
    <attendees-popup-facility-booking
        :refresh="refresh"
        :venue_service_id="venueService.venue_service_id"
        v-bind="attendeesPopupParticipants"
        @open-capacity-booking="openBookingCapacity"
        @close="attendeesPopupParticipants.showParticipants = false"
    ></attendees-popup-facility-booking>
    <booking-details
        :venueServiceId="this.venueService.venue_service_id"
        :id="bookingOrderId"
        @close="(bookingOrderId = null), getFacilitySchedule()"
        @receipt="showReceipt"
        @repeatBookingReceipt="repeatBookingReceipt"
        @reschedule="showReschedule"
        @refund="showRefund"
        @cancel="deleteBookings"
        @cancelRepeatBooking="cancelRepeatBooking"
        @repeatRefundAndCancel="repeatRefundAndCancel"
        @refundSession="
        (participant.showParticipants = false), getFacilitySchedule()
      "
        @openCustomerProfile="openCustomerProfile"
    ></booking-details>

    <reschedule-booking
        :venueServiceId="this.venueService.venue_service_id"
        :id="rescheduleId"
        :currentDate="date"
        @close="rescheduleId = null"
        @refund="showRefund"
        @booked="completeOrder"
        @reload="getFacilitySchedule"
    ></reschedule-booking>
    <!-- <refund
      v-if="refund_dialog"
      :repeatRefundAmount="repeatRefundAmount"
      :repeatBookingdata="repeatBookingdata"
      :show="refund_dialog"
      @close="(refund_dialog = false), (repeatRefundAmount = null)"
      @reload="getFacilitySchedule"
      @refunded="
        (refund_dialog = false), (bookingOrderId = null), completeOrder()
      "
    ></refund> -->
    <RefundNew
        v-if="refundModel.invoiceId && refund_dialog"
        v-bind="refundModel"
        :refundInvoiceData="refundInvoiceData"
        :show="refund_dialog"
        :repeatRefundAmount="repeatRefundAmount"
        :repeatBookingdata="repeatBookingdata"
        @close="
        refund_dialog = false;
        repeatRefundAmount = null;
      "
        @refund="
        (refund_dialog = false), (bookingOrderId = null), completeOrder()
      "
        @reload="getFacilitySchedule"
    />
    <customer-model v-bind="userModel" @close="userModel.userID = null" />
    <confirm-model
        v-bind="confirmOEDModel"
        @confirm="confirmOnlineEnableDisable"
        @close="confirmOEDModel.id = null"
    ></confirm-model>
  </v-container>
</template>

<script>
import CustomerModel from "../../Clients/Customer/CustomerModel";
import RescheduleBooking from "./RescheduleBooking.vue";
import TimeColumn from "@/components/Schedule/TimeColumn";
import FacilityColumn from "@/components/Schedule/Facility/FacilityColumn";
import HeaderCell from "@/components/Schedule/HeaderCell";
import OrderDetails from "@/components/Order/OrderDetails";
// import Participants from "./Participants";
import GolfParticipants from "./Golf/GolfParticipants";
import AttendeesPopupFacilityBooking from "./AttendeesPopupFacilityBooking.vue";
import BookingForm from "./BookingForm";
// import refund from "@/components/Order/Refund.vue";
import RefundNew from "@/components/Invoice/RefundNew.vue";
import BookingDetails from "./BookingDetails";
import moment from "moment";
export default {
  components: {
    CustomerModel,
    FacilityColumn,
    TimeColumn,
    HeaderCell,
    BookingForm,
    OrderDetails,
    // Participants,
    GolfParticipants,
    AttendeesPopupFacilityBooking,
    BookingDetails,
    RescheduleBooking,
    RefundNew,
  },
  data() {
    return {
      userModel: { userID: null, type: "details" },
      repeatRefundAmount: null,
      repeatBookingdata: null,
      drag: false,
      facilities: [],
      bookingForm: {},
      date: moment().format("YYYY-MM-DD"),
      currentDate: null,
      venueService: {},
      gameFormationFilter: [],
      increment: 60,
      height: 1200,
      perCapacity: 0,
      orderId: null,
      orderIds: null,
      bookingOrderId: null,
      rescheduleId: null,
      participant: {},
      attendeesPopupParticipants: {},
      minBookingTime: 60,
      scheduleHeight: 500,
      refund_dialog: false,
      refresh: false,
      isEnableOnline: 0,
      isGameFormationEnabled: 0,
      confirmOEDModel: {
        id: null,
        title: null,
        description: null,
      },
      refundModel: { invoiceId: null, type: "full", amount: 0 },
      refundInvoiceData: {},
    };
  },
  mounted() {
    this.onResize();
    if (this.$store.getters.getVenueServices.status == false) {
      this.showLoader("Loading");
      this.$store.dispatch("loadVenueServices").then(() => {
        this.$nextTick(() => {
          if (this.$store.getters.getSportsService.length) {
            this.getRouteParams();
            this.initializeScheduleForVenueService();
          }
          this.hideLoader();
        });
      });
    } else {
      if (this.$store.getters.getSportsService.length) {
        this.getRouteParams();
        this.initializeScheduleForVenueService();
      }
    }
    if (this.$store.getters.getTags.status == false) {
      this.$store.dispatch("loadTags");
    }
  },
  computed: {
    venueServices() {
      return this.$store.getters.getSportsService.filter( (service) => service.name != "POS");
    },
    venueServiceConfiguration() {
      return this.$store.getters.getConfigurationByVenueServiceId(
          this.venueService.venue_service_id
      );
    },
  },
  methods: {
    initializeScheduleForVenueService() {
      if (this.venueService.venue_service_id) {
        if (
            !this.$store.getters.getConfigurationStatus(
                this.venueService.venue_service_id
            )
        ) {
          this.$store
              .dispatch(
                  "loadConfigurationsByVenueServiceId",
                  this.venueService.venue_service_id
              )
              .then((response) => {
                if (response.status == 200) {
                  if (this.venueServiceConfiguration.is_golf_enabled) {
                    this.$router.push({
                      name: "GolfSchedule",
                      params: {
                        data: btoa(
                            JSON.stringify({
                              venue_service: this.venueService,
                              date: this.date,
                            })
                        ),
                      },
                    });
                  }
                }
              });
        } else {
          if (this.venueServiceConfiguration.is_golf_enabled) {
            this.$router.push({
              name: "GolfSchedule",
              params: {
                data: btoa(
                    JSON.stringify({
                      venue_service: this.venueService,
                      date: this.date,
                    })
                ),
              },
            });
          }
        }
        if (this.gameFormations().length == 0) {
          this.showLoader("Loading");
          this.$store
              .dispatch(
                  "loadConfigurationsByVenueServiceId",
                  this.venueService.venue_service_id
              )
              .then(() => {
                this.hideLoader();
                this.getFacilitySchedule();
              });
        } else {
          this.getFacilitySchedule();
        }
      }
    },
    getRouteParams() {
      if (this.$route.params.data) {
        let data = JSON.parse(atob(this.$route.params.data));
        this.venueService = data.venue_service;
        this.date = data.date;
        if (data.order_id) {
          this.bookingOrderId = data.order_id;
        }
      } else {
        this.venueService = this.$store.getters.getSportsService[0];
      }
    },
    onResize() {
      this.scheduleHeight = window.innerHeight - 350;
    },
    gameFormations() {
      return this.$store.getters.getGameFormationsByVenueServiceId(
          this.venueService.venue_service_id
      );
    },
    openBooking(data) {
      if (
          data.per_capacity == 0 &&
          (data.status == "paid" || data.status == "trainer" || data.status == "unapproved")
      ) {
        this.bookingOrderId = data.order_id;
      } else {
        this.openBookingForm(data);
      }
    },
    openBookingCapacity(data) {
      if (data.status == "paid" || data.status == "trainer") {
        this.bookingOrderId = data.order_id;
      } else {
        console.log(data);
        this.openBookingForm(data);
      }
    },
    openBookingForm(data) {
      this.bookingForm = {
        showBookingForm: true,
        start_time: moment(data.start_time, "hh:mm a").format("HH:mm:ss"),
        end_time: moment(data.end_time, "hh:mm a").format("HH:mm:ss"),
        facility_name: data.facility_name,
        facility_id: data.facility_id,
        date: this.date,
        increment: this.increment,
        venue_service_id: this.venueService.venue_service_id,
        service: this.venueService.name,
        id: data.id != null ? data.id : 0,
        order_id: data.order_id,
        per_capacity: data.per_capacity,
        min_booking_time: data.min_booking_time,
      };
    },
    getFacilitySchedule() {
      if (this.participant && this.participant.showParticipants) {
        this.refresh = !this.refresh;
      }
      this.showLoader("Loading");
      this.$store
          .dispatch("loadFacilitySchedule", {
            venue_service_id: this.venueService.venue_service_id,
            date: this.date,
            game_formation_id: this.gameFormationFilter,
            backfill: this.checkBackfillPermission(
                this.$modules.facility.schedule.slug
            ),
          })
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              this.increment =
                  data.configuration.time_increment != null
                      ? data.configuration.time_increment
                      : 60;
              this.minBookingTime =
                  data.configuration.min_booking_time != null
                      ? data.configuration.min_booking_time
                      : 60;
              this.height = (60 / this.increment) * 1200;
              this.perCapacity = data.configuration.per_capacity
                  ? data.configuration.per_capacity
                  : 0;
              this.facilities = data.facilities;
              // console.log("fac", this.facilities);
              this.isEnableOnline = data.configuration.is_public;
              this.isGameFormationEnabled =
                  data.configuration.is_game_formation_enabled;
              this.hideLoader();
              if (this.date != this.currentDate) {
                this.currentDate = moment().format("YYYY-MM-DD");
                // this.scrollToAvailable();
              }
            }
          });
    },
    nextDate() {
      this.date = moment(this.date)
          .add(1, "days")
          .format("YYYY-MM-DD");
      this.getFacilitySchedule();
    },
    prevDate() {
      this.date = moment(this.date)
          .subtract(1, "days")
          .format("YYYY-MM-DD");
      this.getFacilitySchedule();
    },
    getSlotData(index) {
      return this.$store.getters.getFacilitySchedule[index];
    },
    openOrderCloseBooking(orderId) {
      this.bookingForm.showBookingForm = false;
      this.orderId = orderId;
      // console.log("order id: " + this.orderId);
    },
    completeOrder(orderId) {
      this.getFacilitySchedule();
      if (orderId) {
        if (this.bookingForm.per_capacity) {
          this.bookingForm.showBookingForm = false;
          this.openParticipants(this.bookingForm);
        } else {
          this.openOrderCloseBooking(orderId);
        }
      } else {
        this.bookingForm.showBookingForm = false;
      }
    },
    showReceipt(id) {
      this.bookingOrderId = null;
      this.orderId = id;
    },

    repeatBookingReceipt(orderIds) {
      this.bookingOrderId = null;
      this.bookingForm.showBookingForm = false;
      this.orderIds = orderIds;
    },

    showReschedule(id) {
      this.bookingOrderId = null;
      this.rescheduleId = id;
    },
    showRefund(id) {
      this.$store.dispatch("loadOrderDetails", id).then((response) => {
        if (response.status == 200) {
          this.refund_dialog = true;
        }
      });
      this.refund_dialog = true;
    },

    openCustomerProfile(userId) {
      this.userModel.userID = parseInt(userId);
      this.userModel.type = "details";
    },

    repeatRefundAndCancel(amount, formData) {
      if (amount) {
        this.payments = [
          {
            card_type_id: null,
            payment_code: null,
            payment_method_id: null,
            amount: null,
            payment_method: null,
          },
        ];

        this.$store.commit("setOrderPayments", this.payments);
        this.repeatRefundAmount = amount;
        this.repeatBookingdata = formData;
        this.refund_dialog = true;

        this.refundModel.invoiceId = 1;
        this.refundModel.amount = amount;
        this.refundModel.type = "partial";
      }
    },

    openParticipants(data) {
      this.participant = {
        showParticipants: true,
        start_time: moment(data.start_time, "hh:mm a").format("HH:mm:ss"),
        end_time: moment(data.end_time, "hh:mm a").format("HH:mm:ss"),
        facility_id: data.facility_id,
        date: this.date,
      };
    },
    gotoCalendar() {
      this.$router.push({
        name: "CalendarWithParams",
        params: {
          data: btoa(
              JSON.stringify({
                venue_service: this.venueService,
                date: this.date,
              })
          ),
        },
      });
    },
    toggle() {
      this.$nextTick(() => {
        if (this.gameFormationFilter.length == this.gameFormations().length) {
          this.gameFormationFilter = [];
        } else {
          this.gameFormationFilter = this.gameFormations().map(
              (item) => item.id
          );
        }
      });
      setTimeout(() => {
        this.getFacilitySchedule();
      });
    },
    icon() {
      if (this.gameFormationFilter.length == this.gameFormations())
        return "mdi-close-box";
      if (this.gameFormationFilter.length == 0)
        return "mdi-checkbox-blank-outline";
      return "mdi-minus-box";
    },
    scrollToAvailable() {
      setTimeout(() => {
        const el = this.$refs.schedule;
        let available = this.$el.getElementsByClassName("available")[0];
        if (typeof available !== "undefined") {
          if (available.offsetTop) el.scrollTop = available.offsetTop - 20;
        }
      });
    },
    confirmCancel() {
      this.confirmModel = {
        id: this.order_id,
        title: `Do you want cancel this booking?`,
        description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel",
      };
    },
    deleteBookings(id) {
      this.showLoader('Wait');
      this.$http
          .delete(`venues/orders/${id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.showSuccess("Booking cancelled successfully");
              this.getFacilitySchedule();
              this.bookingOrderId = null;
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },

    cancelRepeatBooking() {
      this.getFacilitySchedule();
      this.bookingOrderId = null;
    },

    reportDownload() {
      let date = moment(this.date).format("YYYY-MM-DD");
      let url =
          "?venue_service_id=" +
          this.venueService.venue_service_id +
          "&date=" +
          date;
      if (!url) return;
      this.showLoader("Downloading report");
      this.$http
          .get(`venues/facilities/bookings/bookings-schedule${url}`, {
            responseType: "blob",
          })
          .then((response) => {
            this.hideLoader();
            if (response.status == 200) {
              this.downloadFile(response, "Schedule Report");
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    onScroll() {
      const refElement = this.$refs.schedule;
      if (refElement) {
        const scrollLeft = refElement.scrollLeft;
        const scrollTop = refElement.scrollTop;
        this.$refs.scheduleTimer.scrollTop = scrollTop;
        this.$refs.scheduleHeader.scrollLeft = scrollLeft;
      }
    },
    enableDisableOnlineFacility(data) {
      let toggle = "Enabled";
      if (data.is_public) {
        toggle = "Disabled";
      }
      this.confirmOEDModel = {
        id: Math.floor(Math.random() * 100 + 1),
        title: toggle + " online booking?",
        description:
            "By clicking <b>Yes</b> online booking for date " +
            this.date +
            " <b>" +
            toggle +
            "</b>.  Do you need to continue your action ?",
        type: "update",
        data: data,
      };
    },
    /** only disable for particular date */
    confirmOnlineEnableDisable(data) {
      let formData = new FormData();
      let date = moment(this.date).format("YYYY-MM-DD");
      if (
          date != "" &&
          typeof data.data.facility_id != "undefined" &&
          data.data.facility_id != "" &&
          typeof data.data.is_public != "undefined"
      ) {
        formData.append("date", this.date);
        formData.append("facility_id", data.data.facility_id);
        formData.append("is_public", data.data.is_public);
        this.showLoader("LOADING ... ");
        this.$http
            .post(`venues/facilities/online-status`, formData, {
              headers: {
                "Content-Type": "multipart/form-data; boundary=${form._boundary}",
              },
            })
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                this.hideLoader();
                this.getFacilitySchedule();
                this.showSuccess("Online facility booking status updated");
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            });
      }
    },
    showTotalAttendees(facility_id) {
      this.attendeesPopupParticipants = {
        showParticipants: true,
        facility_id: facility_id,
        date: this.date,
      };
    },
    countTotalAttendees(facility) {
      if (facility.per_capacity == 1) {
        let attendees_count = 0;
        facility.bookings.forEach((booking) => {
          attendees_count += booking.attendance;
        });
        // console.log("-------------attendees_count-----------------------");
        // console.log(attendees_count);
        // console.log("------------------------------------");
        return attendees_count;
      } else {
        return 0;
      }
    },
  },
};
</script>

<style>
.no-scroll {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}
.no-scroll::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}
.sch-gameformation-field {
  height: 44px;
}
</style>

<template>
  <v-dialog v-model="dialog" max-width="800px" persistent>
    <v-card>
      <v-card-title class="justify-space-between">
        <span class="modal-heading">Passings</span>
        <v-spacer>
        </v-spacer>
        <div v-if="uniqueKarts.length > 1">
          <v-select
              v-model="selectedKart"
              :items="uniqueKarts"
              class="custom-select pt-0 q-autocomplete shadow-0"
              hide-details
              dense
              outlined
          />
        </div>
      </v-card-title>
      <v-divider></v-divider>

      <v-row class="participants-area">
        <v-col v-if="passings.length > 0" class="p-0" cols="12" md="12" subheader>
          <div class="table-responsive">
            <table class="table border-collapse ">
              <thead>
              <tr class="opacity-70 tr-neon tr-rounded ">
                <th>
                  <div class="text-center">
                    Kart
                  </div>
                </th>
                <th>
                  <div class="text-center">Transponder</div>
                </th>
                <th>
                  <div class="text-center">Loop</div>
                </th>
                <th>
                  <div class="text-center">Passed At</div>
                </th>
              </tr>
              </thead>
              <tbody>
              <tr
                  v-for="passing in filteredPassings"
                  :key="passing.id"
              >
                <td>
                  <div class="text-center">
                    <span>{{ passing.vehicle_name }}</span>
                  </div>
                </td>
                <td>
                  <div class="text-center">
                    <strong>{{ passing.transponder_name }}</strong>
                  </div>
                </td>
                <td>
                  <div class="text-center">
                    <strong>{{ passing.loop_name }}</strong>
                  </div>
                </td>
                <td>
                  <div class="text-center" v-if="passing.passet_at_raw">
                    <span>{{ passing.passet_at_raw | formatUnixTimestampMs }}</span>
                  </div>
                  <div class="text-center">
                    <span>{{ passing.passed_at | timeStampMs }}</span>
                  </div>
                </td>
              </tr>
              </tbody>

            </table>
          </div>
        </v-col>
        <v-col v-else class="p-0" cols="12" md="12">
          <div class="d-flex justify-center align-center" style="height: 200px;"> No Passings</div>
        </v-col>
      </v-row>


      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" @click="close">Close</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
export default {
  name: "PassingsModel",

  props: {
    raceId: {type: Number, default: null},
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    dialog(val) {
      if (val == true) {
        this.getPassings(this.raceId);
      }
    },
  },
  data() {
    return {
      passings: [],
      selectedKart: "All", // Dropdown selection value
    }
  },
  computed: {
    // Extract unique vehicle names for the dropdown
    uniqueKarts() {
      const vehicles = this.passings.map((passing) => passing.vehicle_name);
      return ["All", ...new Set(vehicles)]; // Add "All" option
    },
    // Filter passings based on selectedKart
    filteredPassings() {
      if (!this.selectedKart || this.selectedKart === "All") {
        return this.passings;
      }
      return this.passings.filter(
          (passing) => passing.vehicle_name === this.selectedKart
      );
    },
  },
  methods: {
    close() {
      this.selectedKart = 'All';
      this.$emit("close");
    },
    getPassings(race_id) {
      this.showLoader("Loading Passings");
      this.$http.get(`venues/facilities/bookings/race/show-passings/${race_id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.passings = response.data.data;
              this.hideLoader();
            }
          }).catch((error) => {
        this.errorChecker(error);
      }).finally(() => {
        this.hideLoader();
      })
    },
  }
}
</script>


<style scoped>
.participants-area {
  padding: 10px 0;
  margin: 0 20px;
}
.v-select {
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.875rem;
  text-align: center;
}


.v-select .vs__dropdown-menu {
  max-height: 200px;
  overflow-y: auto;
}

.custom-select{
  background: white !important;
  max-width: 150px !important;
}
</style>
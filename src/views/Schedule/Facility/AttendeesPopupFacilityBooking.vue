<template>
  <v-dialog scrollable :value="showParticipants" max-width="700" @input="close">
    <v-card>
      <v-toolbar color="teal" dark height="80">
        <v-toolbar-title>
          <v-list-item-content>
            <v-list-item-title class="text-h5">
              Participants
            </v-list-item-title>
            <v-list-item-subtitle style="padding: 5px">
              <span style="font-size: 12px; font-weight: 600">
                <span>Check In : {{ totalCheckIn }}</span>
                <br />
                Check Out : {{ totalCheckOut }}
              </span>
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-toolbar-title>

        <v-text-field
          class="mt-6"
          style="width: 50px"
          v-model="search"
          label="Search"
          clearable
          outlined
          dense
        ></v-text-field>
      </v-toolbar>
      <v-card-text>
        <v-list subheader v-if="filterParticipants.length > 0">
          <div
            v-for="(participant, index) in filterParticipants"
            :key="index"
            :class="
              `parent-list-items ${
                participants[index + 1] &&
                participants[index + 1].order_id != participants[index].order_id
                  ? 'border-show'
                  : ''
              }`
            "
          >
            <v-list-item style="padding: 0px">
              <v-list-item-avatar :width="60" :height="60">
                <view-image
                  defaultImage="user"
                  :contain="false"
                  :image="participant.profile_image"
                  class=""
                ></view-image>
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title class="ml-2">
                  {{ participant.name }}
                  <span
                    class="grey--text caption displayBlock"
                    v-if="participant.status_id == 1"
                    >{{ participant.order_seq_no }}</span
                  >
                  <span class="grey--text caption displayBlock" v-else>{{
                    participant.invoice_seq_no
                  }}</span>
                  <span
                    class="grey--text caption"
                    v-if="
                      participant.order_customer_id == participant.customer_id
                    "
                    >{{
                      (participant.order_total +
                        getParentOrderTotal(participant.parent_orders))
                        | toCurrency
                    }}</span
                  >
                  <span
                    v-if="
                      participant.actual_total != null &&
                        participant.order_discount_id !== null
                    "
                    class="text-decoration-line-through grey--text caption"
                  >
                    {{
                      (participant.actual_total +
                        getParentOrderTotal(participant.parent_orders))
                        | toCurrency
                    }}</span
                  >
                  <span
                    class="grey--text caption displayBlock"
                    v-if="participant.credit_owed"
                    >Credit Due:
                    {{ participant.credit_owed | toCurrency }}</span
                  ></v-list-item-title
                >
              </v-list-item-content>
              <v-list-item-action>
                <v-btn
                  v-if="
                    participant.order_customer_id == participant.customer_id
                  "
                  class="ma-2 white--text teal-color"
                  normal
                  @click="getOrderDetails(participant)"
                >
                  <span v-if="participant.status_id == 5">Invoice</span>
                  <span v-else>Receipt</span>
                </v-btn>
                <v-btn
                  v-else
                  class="ma-1 white--text teal-color"
                  :color="
                    `${
                      participant.order_customer_id != participant.customer_id
                        ? 'purple'
                        : ''
                    }`
                  "
                  normal
                >
                  <span>{{ participant.payer_name }}</span>
                </v-btn>
              </v-list-item-action>
              <v-list-item-action>
                <v-btn
                  class="ma-1 white--text teal-color"
                  @click="openBooking(participant)"
                >
                  View
                </v-btn>
              </v-list-item-action>
              <v-list-item-action
                v-if="
                  participant.alert_notes != null &&
                    participant.alert_notes != ''
                "
              >
                <v-btn
                  class="ma-1 white--text red-color"
                  @click="openAlert(participant.customer_id)"
                >
                  Alert
                </v-btn>
              </v-list-item-action>
              <v-list-item-action>
                <v-btn
                  :color="
                    participant.check_in_and_out == 'OUT'
                      ? 'error'
                      : participant.check_in_and_out == 'GONE'
                      ? 'secondary'
                      : 'success'
                  "
                  @click="
                    participant.check_in_and_out == 'IN' ||
                    participant.check_in_and_out == 'OUT'
                      ? checkInAndOut(participant.group_customer_id)
                      : ''
                  "
                  class="ma-2 white--text teal-color"
                >
                  {{
                    participant.check_in_time != null &&
                    participant.check_out_time != null
                      ? "Checked out"
                      : participant.check_in_and_out
                      ? "Check " +
                        (participant.check_in_and_out == "OUT" ? "Out" : "In")
                      : "Check In/Out"
                  }}
                </v-btn>
              </v-list-item-action>
              <!-- <v-list-item-action> -->

              <!-- <v-btn class="ma-2 white--text teal-color">Check Out</v-btn> -->
              <!-- </v-list-item-action> -->
            </v-list-item>
          </div>
        </v-list>
        <div
          v-else
          style="height: 200px"
          class="d-flex justify-center align-center"
        >
          No Participants
        </div>
        <!-- <v-divider></v-divider> -->
      </v-card-text>
      <v-card-actions>
        <v-btn @click="close()" class="ma-2 white--text blue-color" text
          >Close
        </v-btn>
      </v-card-actions>
      <customer-alert-note
        v-bind="alertNote"
        :id="alertNote.id"
        :note="alertNote.note"
        @clearAlertNote="clearAlertNote"
        @closeAlert="closeAlert"
      >
      </customer-alert-note>
      <order-details
        :id="orderId"
        :ids="orderIds"
        :log="false"
        @close="(orderId = null), (orderIds = null), openParticipants()"
      ></order-details>
    </v-card>
  </v-dialog>
</template>

<script>
import CustomerAlertNote from "@/views/Clients/Customer/CustomerAlertNote.vue";
import OrderDetails from "../../../components/Order/OrderDetails.vue";

export default {
  props: {
    start_time: { type: String },
    showParticipants: { type: Boolean, default: false },
    end_time: { type: String },
    date: { type: String },
    facility_id: { type: Number },
    refresh: { type: Boolean },
    venue_service_id: { type: Number },
  },
  components: {
    CustomerAlertNote,
    OrderDetails,
  },
  watch: {
    showParticipants(val) {
      if (val == true) {
        this.openParticipants();
      }
    },
    refresh() {
      this.openParticipants();
    },
    customer_name(val) {
      console.log(val);
    },
  },
  data() {
    return {
      participants: [],
      multiplePayments: [],
      orderId: null,
      orderIds: null,
      payer: null,
      orderTotal: 0,
      payerCustomerList: [],
      bookingForm: { attendance: false, attendance_count: 1, products: [] },
      enableMultiOrderPay: false,
      multiBookingIds: [],
      products: [],
      gop: 0,
      refreshBool: false,
      search: null,
      totalCheckIn: 0,
      totalCheckOut: 0,
      alertNote: {
        show: false,
        note: null,
        id: null,
      },
    };
  },
  computed: {
    filterParticipants() {
      const search = this.search;
      return search === "" || search == null
        ? this.participants
        : this.participants.filter((participant) =>
            participant.name.toLowerCase().includes(search.toLowerCase())
          );
    },
  },
  methods: {
    openBooking(data) {
      data.status = data.status_id == 5 ? "unpaid" : "paid";
      this.$emit("open-capacity-booking", data);
    },
    getOrderDetails(participant) {
      let parentOrderIds = [];
      if (participant.parent_orders.length) {
        parentOrderIds.push(participant.order_id);
        participant.parent_orders.map((item) => {
          parentOrderIds.push(item.id);
        });
        this.orderIds = parentOrderIds;
      } else {
        this.orderIds = null;
      }
      this.orderId = participant.order_id;
    },
    openAlert(id) {
      this.showLoader();
      this.$http
        .get("venues/customers/get-alert-note/" + id)
        .then((response) => {
          this.hideLoader();
          if (response.status == 200) {
            this.alertNote.id = response.data.data.customer_id;
            this.alertNote.note = response.data.data.alert_notes;
            this.alertNote.show = true;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });

      console.log(id);
    },
    clearAlertNote(id) {
      this.showLoader();
      this.$http
        .post("venues/customers/clear-alert-note/" + id)
        .then((response) => {
          this.hideLoader();
          if (response.status == 200) {
            this.showSuccess("Customer alert note cleared!");
            this.closeAlert();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    closeAlert() {
      this.alertNote.show = false;
      this.openParticipants();
    },
    closeOrderPaymentDialog() {
      this.enableMultiOrderPay = false;
      this.openParticipants();
    },
    openParticipants() {
      if (!this.facility_id) {
        return;
      }
      this.showLoader("Loading");
      this.payer = null;
      this.payerCustomerList = [];
      this.$http
        .get(
          `venues/facilities/bookings/golf/participants?facility_id=${this.facility_id}&date=${this.date}&venue_service_id=${this.venue_service_id}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.participants = data;
            this.totalCheckIn = 0;
            this.totalCheckOut = 0;
            this.participants.forEach((el) => {
              if (el.check_in_and_out == "OUT" || el.check_in_and_out == "GONE") {
                this.totalCheckIn++;
              }
              if (el.check_in_and_out == "GONE") {
                this.totalCheckOut++;
              }
            });
            this.hideLoader();
          }
        });
    },

    checkInAndOut(id) {
      this.showLoader("Loading");
      this.$http
        .get(`venues/facilities/bookings/check-in-and-out/${id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.hideLoader();
            this.openParticipants();
          }
        });
    },

    close() {
      this.$emit("close");
    },
    getColourDisabled(status_id) {
      if (status_id === 1) {
        return "success";
      } else {
        return "warning";
      }
    },
    getParentOrderTotal(parentOrders) {
      let total = 0;
      parentOrders.map((item) => {
        total += item.total;
      });
      return total;
    },
  },
};
</script>

<style>
.parent-list-items.border-show {
  /* border-bottom: 1px inset; */
}
span.cdisplayBlock {
  display: block;
  padding-top: 5px;
}
span.displayBlock {
  display: block;
}
</style>

<template>
  <v-container fluid>
    <div class="md-card md-theme-default mt-8 shadow rounded-5">
      <div class="md-card-content md-card-table">
        <div>
          <div class="d-flex justify-space-between align-center">
            <SvgIcon
              class="text-2xl font-semibold"
              text="Website Support Report"
            >
            </SvgIcon>
            <v-spacer />

            <div class="d-flex align-center gap-x-4 mt-2">
              <v-menu
                v-model="menu1"
                :close-on-content-click="false"
                :nudge-right="40"
                filled
                min-width="290px"
                offset-y
                transition="scale-transition"
              >
                <template v-slot:activator="{ on }">
                  <v-text-field
                    outlined
                    dense
                    class="q-text-field shadow-0"
                    v-model="date1Formatted"
                    readonly
                    v-on="on"
                    flat
                    hide-details
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="searchParams.from_date"
                  @input="menu1 = false"
                ></v-date-picker>
              </v-menu>
              <v-menu
                v-model="menu2"
                :close-on-content-click="false"
                :nudge-right="40"
                filled
                min-width="290px"
                offset-y
                transition="scale-transition"
              >
                <template v-slot:activator="{ on }">
                  <v-text-field
                    class="q-text-field shadow-0"
                    dense
                    v-model="date2Formatted"
                    outlined
                    readonly
                    v-on="on"
                    flat
                    hide-details
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="searchParams.to_date"
                  @input="menu2 = false"
                ></v-date-picker>
              </v-menu>
              <v-btn
                class="mr-2 bg-blue text-white"
                height="40"
                elevation="0"
                @click="getSupportReport"
              >
                Go
              </v-btn>
            </div>
            <v-col md="4" class="text-right">
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    v-if="checkExportPermission($modules.reports.website_support.slug)"
                    class="export-button mt-2"
                    elevation="0"
                    height="40"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <SvgIcon text="Export Report">
                      <template v-slot:icon>
                        <ExportIcon />
                      </template>
                    </SvgIcon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item @click="reportDownload('pdf')">
                    <v-list-item-icon>
                      <v-icon>mdi-file-pdf-box</v-icon>
                    </v-list-item-icon>
                    <v-list-item-title>Export as PDF</v-list-item-title>
                  </v-list-item>
                  <v-list-item @click="reportDownload('excel')">
                    <v-list-item-icon>
                      <v-icon>mdi-file-excel-box</v-icon>
                    </v-list-item-icon>
                    <v-list-item-title>Export as Excel</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>
          </div>
          <br /><br />
          <div class="table-responsive">
            <table class="table border-collapse text-center">
              <thead>
                <tr class="opacity-70 tr-neon tr-rounded text-center">
                  <th>
                    <div class="ml-10">Subject</div>
                  </th>
                  <th>
                    <div class="ml-10">Requester</div>
                  </th>
                  <th>
                    <div class="ml-10">Ticket Date & Time</div>
                  </th>
                  <th>
                    <div class="ml-10">First Respond</div>
                  </th>
                  <th>
                    <div class="ml-10">Resolved/Forwarded At</div>
                  </th>
                  <th>
                    <div class="ml-10">Attachments</div>
                  </th>
                </tr>
              </thead>

              <tbody>
                <tr v-for="ticket in ticketsList" :key="ticket.id">
                  <td class="text-left">
                    <template v-if="ticket.subject.length>50">
                      {{ ticket.subject.toString().slice(0,50) }}...
                    </template>
                    <template v-else>
                      {{ ticket.subject }}
                    </template>
                  </td>
                  <td>
                    {{ ticket.first_name }} {{ ticket.last_name }}
                  </td>
                  <td>
                    {{ ticket.created_at | timeStampOrignal }}
                  </td>
                  <td>
                    <template v-if="ticket.first_respond">
                      {{ ticket.first_respond | timeStampOrignal }}
                    </template>
                    <template v-else>
                      N/A
                    </template>
                  </td>
                  <td>
                    <template v-if="ticket.resolved_at">
                      {{ ticket.resolved_at | timeStampOrignal }}
                    </template>
                    <template v-else>
                      N/A
                    </template>
                  </td>
                  <td>
                    {{ ticket.has_attachment ? "Yes" : "No" }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <v-row>
      <v-col cols="4"></v-col>
      <v-col cols="4">
        <v-pagination
          v-model="page"
          :length="totalPages"
          class="new-pagination"
          total-visible="7"
        ></v-pagination>
      </v-col>
      <v-col class="d-flex align-center justify-end" cols="4">
        <div class="d-flex align-center justify-end text-sm gap-x-2">
          <span class=""> Result </span>
          <div style="width: 80px">
            <v-select
              v-model="perPage"
              :items="[10, 15, 25, 50]"
              :menu-props="{ bottom: true, offsetY: true }"
              class="q-autocomplete text-sm"
              flat
              hide-details
              solo
              @change="getSupportReport"
            ></v-select>
          </div>
          <span>Per Page</span>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import moment from "moment";
import { mapGetters } from "vuex";
import ExportIcon from "@/assets/images/misc/export-icon.svg";

import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  components: {
    ExportIcon,
    SvgIcon,
  },
  data() {
    return {
      ticketsList: [],
      btnShow: false,
      fileName: null,
      isLoading: false,
      page: 1,
      perPage: null,
      totalPages: 1,
      menu1: null,
      menu2: null,
      searchParams: {
        from_date: moment().subtract(30, "days").format("YYYY-MM-DD"),
        to_date: moment().format("YYYY-MM-DD"),
      },
    };
  },

  created() {
    setTimeout(() => {
      this.btnShow = true;
    }, 10);
  },
  mounted() {
    this.getSupportReport();
  },

  computed: {
    ...mapGetters({
      checkReadPermission: "checkReadPermission",
    }),
    date1Formatted() {
      return moment(this.searchParams.from_date, "YYYY-MM-DD").format(
        "Do MMM YYYY"
      );
    },
    date2Formatted() {
      return moment(this.searchParams.to_date, "YYYY-MM-DD").format(
        "Do MMM YYYY"
      );
    },
  },
  watch: {
    page() {
      this.getSupportReport();
    },

  },
  methods: {
    gotoPage(route) {
      this.$router.push(route);
    },
    showUserModel(userId) {
      this.userModel.userID = parseInt(userId);
      this.userModel.type = "details";
    },
    getFilter() {
      let url =
        "&from_date=" +
        this.searchParams.from_date +
        "&to_date=" +
        this.searchParams.to_date;

      this.fileName =
        this.searchParams.from_date + "-to-" + this.searchParams.to_date;
      if (this.fileName != null) {
        this.fileName = "Website-Support-Report-" + this.fileName.replace(/\s/g, "");
      }
      else {
        this.fileName = "Website-Support-Report";
      }
      return url;
    },
    getSupportReport() {
      let url = "";
      url = this.getFilter();
      this.showLoader("Loading");
      this.$http
        .get(
          "venues/reports/website-support-report?page=" +
            this.page +
            "&per_page=" +
            (this.perPage != null ? this.perPage : 10) +
            url
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const { data } = response.data;
            this.ticketsList = data.data;
            this.totalPages = data.last_page;
          }
          this.hideLoader();
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    reportDownload(type) {
      let link = "";
      if (type == "pdf") {
        link = "website-support-report-pdf";
      } else if (type == "excel") {
        link = "website-support-report-excel";
      }
      let url = "";
      url = this.getFilter();
      if (!url) return;
      this.showLoader("Downloading report");
      console.log(link);
      this.$http
        .get(`venues/reports/` + link + `/download?${url}`, {
          responseType: "blob",
        })
        .then((response) => {
          this.hideLoader();
          if (response.status == 200) {
            this.downloadReport(response, this.fileName, type);
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
  },
};
</script>

<style scoped>
.salesBtn {
  padding: 5px;
  font-size: 14px;
  font-weight: bold;
  color: #066a8c;
}
.btn_bg {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
}

.light-blue-text {
  color: rgba(17, 42, 70, 0.8) !important;
}
</style>

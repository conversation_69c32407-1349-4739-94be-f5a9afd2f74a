<template>
  <v-container fluid>
  <ParentDashboard
      v-if="hasChildVenues && checkReadPermission($modules.general.parent_dashboard_view.slug)"
  />
  <Dashboard v-else-if="!hasChildVenues || !checkReadPermission($modules.general.parent_dashboard_view.slug)" />
  </v-container>
</template>
<script>
import ParentDashboard from "@/views/Dashboard/ParentDashboard.vue";
import Dashboard from "@/views/Dashboard/Dashboard.vue";

export default {
  name: "MainDashboard",
  components: { ParentDashboard,Dashboard},
  computed: {
    hasChildVenues() {
      return this.$store.getters.venueInfo.sub_venues;
    },
  }
}
</script>

<style scoped>

</style>
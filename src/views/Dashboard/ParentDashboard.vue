<template>
  <div class="dashboard">
    <div class="d-flex justify-space-between">
      <div>
        <h4 class="m-b-5 text-2xl text-blue font-semibold">Dashboard</h4>
      </div>
      <div>
        <div class="d-flex align-center gap-x-4">
          <v-autocomplete
            v-model="searchParams.time_intervel"
            :items="timeDuration"
            class="q-autocomplete shadow-0 add-on-prepend"
            hide-details
            dense
            item-text="title"
            item-value="name"
            outlined
            placeholder="Time Period"
            style="max-width: 180px"
            @change="changeFxn"
            height="20"
          >
            <template v-slot:prepend-inner>
              <SvgIcon class="add-on-svg">
                <template v-slot:icon>
                  <EventIcon />
                </template>
              </SvgIcon>
            </template>
          </v-autocomplete>
          <div v-if="flag" class="d-flex align-center gap-x-4">
            <v-menu
              v-model="menu1"
              :close-on-content-click="false"
              :nudge-right="40"
              filled
              min-width="290px"
              offset-y
              transition="scale-transition"
            >
              <template v-slot:activator="{ on }">
                <v-text-field
                  v-model="date1Formatted"
                  class="shadow-0 q-text-field"
                  dense
                  flat
                  hide-details
                  background-color="#fff"
                  outlined
                  readonly
                  style="max-width: 180px !important"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="searchParams.start_date"
                @input="menu1 = false"
              ></v-date-picker>
            </v-menu>
            <v-menu
              v-model="menu2"
              :close-on-content-click="false"
              :nudge-right="40"
              min-width="290px"
              offset-y
              transition="scale-transition"
            >
              <template v-slot:activator="{ on }">
                <v-text-field
                  v-model="date2Formatted"
                  class="shadow-0 q-text-field"
                  dense
                  flat
                  hide-details
                  outlined
                  background-color="#fff"
                  readonly
                  v-on="on"
                  style="max-width: 180px !important"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="searchParams.end_date"
                @input="menu2 = false"
              ></v-date-picker>
            </v-menu>
            <v-btn
              class="mr-2 bg-blue text-white"
              height="40"
              elevation="0"
              @click="changeFxn"
            >
              Go
            </v-btn>
          </div>
        </div>
      </div>
    </div>
    <v-row class="m-b-4">
      <v-col lg="3" sm="6" md="6">
        <v-card class="rounded-lg shadow">
          <div>
            <div
              class="p-3 bg-blue text-white rounded-t d-flex justify-space-between align-center"
            >
              <SvgIcon class="font-semibold" text="Bookings">
                <template v-slot:icon>
                  <BookingIcon />
                </template>
              </SvgIcon>
              <p class="text-sm">Year to Date</p>
            </div>
            <div v-if="isLoading" class="text-center py-4">
              <v-progress-circular
                indeterminate
                color="primary"
              ></v-progress-circular>
            </div>

            <table v-else class="dash_tbl table-striped">
              <tbody>
                <tr
                  v-if="
                    checkWritePermission($modules.facility.management.slug) ||
                    checkWritePermission($modules.facility.schedule.slug)
                  "
                >
                  <td class="text-left">Facility Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_G != "undefined" ? ytd_G.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.events.management.slug) ||
                    checkWritePermission($modules.events.schedule.slug)
                  "
                >
                  <td class="text-left">Events Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_E != "undefined" ? ytd_E.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.workshops.management.slug) ||
                    checkWritePermission($modules.workshops.schedule.slug)
                  "
                >
                  <td class="text-left">Academy Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_C != "undefined" ? ytd_C.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission(
                      $modules.memberships.management.slug
                    ) ||
                    checkWritePermission($modules.memberships.dashboard.slug)
                  "
                >
                  <td class="text-left">Membership Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_M != "undefined" ? ytd_M.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.trainers.management.slug) ||
                    checkWritePermission($modules.trainers.dashboard.slug)
                  "
                >
                  <td class="text-left">Trainer Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_T != "undefined" ? ytd_T.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.vouchers.management.slug) ||
                    checkWritePermission($modules.vouchers.issues.slug)
                  "
                >
                  <td class="text-left">Voucher Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_V != "undefined" ? ytd_V.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.pos.management.slug) ||
                    checkWritePermission($modules.pos.dashboard.slug)
                  "
                >
                  <td class="text-left">Retail Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_R != "undefined" ? ytd_R.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr>
                  <td class="text-left font-bold">Total:</td>
                  <td class="text-right font-bold">
                    {{ getTotalByKey("bookings") | numberFormatter }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </v-card>
      </v-col>
      <v-col lg="3" sm="6" md="6">
        <v-card class="rounded-lg shadow">
          <div>
            <div
              class="p-3 bg-neon text-white rounded-t d-flex justify-space-between align-center"
            >
              <SvgIcon class="font-semibold" text="Sales">
                <template v-slot:icon>
                  <WalletIcon />
                </template>
              </SvgIcon>
              <p class="text-sm">Year to Date</p>
            </div>
            <div v-if="isLoading" class="text-center py-4">
              <v-progress-circular
                indeterminate
                color="primary"
              ></v-progress-circular>
            </div>

            <table v-else class="dash_tbl table-striped">
              <tbody>
                <tr
                  v-if="
                    checkWritePermission($modules.facility.management.slug) ||
                    checkWritePermission($modules.facility.schedule.slug)
                  "
                >
                  <td class="text-left">Facility Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_G != "undefined" ? ytd_G.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.events.management.slug) ||
                    checkWritePermission($modules.events.schedule.slug)
                  "
                >
                  <td class="text-left">Events Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_E != "undefined" ? ytd_E.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.workshops.management.slug) ||
                    checkWritePermission($modules.workshops.schedule.slug)
                  "
                >
                  <td class="text-left">Academy Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_C != "undefined" ? ytd_C.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission(
                      $modules.memberships.management.slug
                    ) ||
                    checkWritePermission($modules.memberships.dashboard.slug)
                  "
                >
                  <td class="text-left">Membership Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_M != "undefined" ? ytd_M.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.trainers.management.slug) ||
                    checkWritePermission($modules.trainers.dashboard.slug)
                  "
                >
                  <td class="text-left">Trainer Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_T != "undefined" ? ytd_T.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.vouchers.management.slug) ||
                    checkWritePermission($modules.vouchers.issues.slug)
                  "
                >
                  <td class="text-left">Voucher Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_V != "undefined" ? ytd_V.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.pos.management.slug) ||
                    checkWritePermission($modules.pos.dashboard.slug)
                  "
                >
                  <td class="text-left">Retail Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof ytd_R != "undefined" ? ytd_R.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr>
                  <td class="text-left font-bold">Total:</td>
                  <td class="text-right font-bold">
                    {{ getTotalByKey("sales") | toCurrency }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </v-card>
      </v-col>
      <v-col lg="3" sm="6" md="6">
        <v-card class="rounded-lg shadow">
          <div>
            <div class="p-3 bg-blue text-white rounded-t">
              <SvgIcon class="font-semibold" text="Bookings | Advance">
                <template v-slot:icon>
                  <BookingIcon />
                </template>
              </SvgIcon>
            </div>
            <div v-if="isLoading" class="text-center py-4">
              <v-progress-circular
                indeterminate
                color="primary"
              ></v-progress-circular>
            </div>

            <table v-else class="dash_tbl table-striped">
              <tbody>
                <tr
                  v-if="
                    checkWritePermission($modules.facility.management.slug) ||
                    checkWritePermission($modules.facility.schedule.slug)
                  "
                >
                  <td class="text-left">Facility Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_G != "undefined" ? adv_G.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.events.management.slug) ||
                    checkWritePermission($modules.events.schedule.slug)
                  "
                >
                  <td class="text-left">Events Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_E != "undefined" ? adv_E.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.workshops.management.slug) ||
                    checkWritePermission($modules.workshops.schedule.slug)
                  "
                >
                  <td class="text-left">Academy Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_C != "undefined" ? adv_C.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission(
                      $modules.memberships.management.slug
                    ) ||
                    checkWritePermission($modules.memberships.dashboard.slug)
                  "
                >
                  <td class="text-left">Membership Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_M != "undefined" ? adv_M.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.trainers.management.slug) ||
                    checkWritePermission($modules.trainers.dashboard.slug)
                  "
                >
                  <td class="text-left">Trainer Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_T != "undefined" ? adv_T.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.vouchers.management.slug) ||
                    checkWritePermission($modules.vouchers.issues.slug)
                  "
                >
                  <td class="text-left">Voucher Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_V != "undefined" ? adv_V.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.pos.management.slug) ||
                    checkWritePermission($modules.pos.dashboard.slug)
                  "
                >
                  <td class="text-left">Retail Bookings</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_R != "undefined" ? adv_R.bookings : 0)
                        | numberFormatter
                    }}
                  </td>
                </tr>
                <tr>
                  <td class="text-left font-bold">Total:</td>
                  <td class="text-right font-bold">
                    {{ getAdvanceTotalByKey("bookings") | numberFormatter }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </v-card>
      </v-col>
      <v-col lg="3" sm="6" md="6">
        <v-card class="rounded-lg shadow">
          <div>
            <div class="p-3 bg-neon text-white rounded-t">
              <SvgIcon class="font-semibold" text="Sales | Advance">
                <template v-slot:icon>
                  <WalletIcon />
                </template>
              </SvgIcon>
            </div>
            <div v-if="isLoading" class="text-center py-4">
              <v-progress-circular
                indeterminate
                color="primary"
              ></v-progress-circular>
            </div>

            <table v-else class="dash_tbl table-striped">
              <tbody>
                <tr
                  v-if="
                    checkWritePermission($modules.facility.management.slug) ||
                    checkWritePermission($modules.facility.schedule.slug)
                  "
                >
                  <td class="text-left">Facility Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_G != "undefined" ? adv_G.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.events.management.slug) ||
                    checkWritePermission($modules.events.schedule.slug)
                  "
                >
                  <td class="text-left">Events Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_E != "undefined" ? adv_E.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.workshops.management.slug) ||
                    checkWritePermission($modules.workshops.schedule.slug)
                  "
                >
                  <td class="text-left">Academy Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_C != "undefined" ? adv_C.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission(
                      $modules.memberships.management.slug
                    ) ||
                    checkWritePermission($modules.memberships.dashboard.slug)
                  "
                >
                  <td class="text-left">Membership Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_M != "undefined" ? adv_M.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.trainers.management.slug) ||
                    checkWritePermission($modules.trainers.dashboard.slug)
                  "
                >
                  <td class="text-left">Trainer Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_T != "undefined" ? adv_T.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.vouchers.management.slug) ||
                    checkWritePermission($modules.vouchers.issues.slug)
                  "
                >
                  <td class="text-left">Voucher Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_V != "undefined" ? adv_V.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr
                  v-if="
                    checkWritePermission($modules.pos.management.slug) ||
                    checkWritePermission($modules.pos.dashboard.slug)
                  "
                >
                  <td class="text-left">Retail Sales</td>
                  <td class="text-right">
                    {{
                      Number(typeof adv_R != "undefined" ? adv_R.sales : 0)
                        | toCurrency
                    }}
                  </td>
                </tr>
                <tr>
                  <td class="text-left font-bold">Total:</td>
                  <td class="text-right font-bold">
                    {{ getAdvanceTotalByKey("sales") | toCurrency }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </v-card>
      </v-col>
    </v-row>

    <v-row class="mb-4">
      <DashboardSalesGraph />
    </v-row>
  </div>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
import EventIcon from "@/assets/images/misc/event-icon.svg";
import moment from "moment/moment";
import WalletIcon from "@/assets/images/misc/wallet-money.svg";
import BookingIcon from "@/assets/images/misc/booking.svg";
import { EventBus } from "@/main";
import DashboardSalesGraph from "@/components/Chart/DashboardSalesGraph.vue";

export default {
  name: "ParentDashboard",
  components: {
    DashboardSalesGraph,
    BookingIcon,
    WalletIcon,
    EventIcon,
    SvgIcon,
  },
  mounted: function () {
    this.init();
    EventBus.$on("reloadDashboard", () => {
      this.init();
    });
  },
  computed: {
    date1Formatted() {
      return moment(this.searchParams.start_date, "YYYY-MM-DD").format(
        "Do MMM YYYY"
      );
    },
    date2Formatted() {
      return moment(this.searchParams.end_date, "YYYY-MM-DD").format(
        "Do MMM YYYY"
      );
    },
  },
  methods: {
    init() {
      this.getDashboardCount();
    },
    getDashboardCount() {
      this.isLoading = true;
      if (
        this.searchParams.time_intervel != "All" &&
        this.searchParams.time_intervel != null
      ) {
        if (this.searchParams.time_intervel == "custom") {
          this.flag = true;
        } else if (this.searchParams.time_intervel == "day") {
          this.searchParams.start_date = moment().format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "week") {
          this.searchParams.start_date = moment()
            .startOf("week")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "month") {
          this.searchParams.start_date = moment()
            .startOf("month")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "year") {
          this.searchParams.start_date = moment()
            .startOf("year")
            .format("YYYY-MM-DD");
        }
      }
      let url = "";
      url += "?start_date=" + this.searchParams.start_date;
      url += "&end_date=" + this.searchParams.end_date;

      this.showLoader("Loading");
      this.$http
        .get(`venues/dashboard/parent/ytd-advance` + url)
        .then((res) => {
          if (res.status == 200) {
            let ytd = res.data.data.ytd;
            let advance = res.data.data.advance;
            this.ytd_G = ytd.find((y) => y.name == "Facility");
            this.ytd_E = ytd.find((y) => y.name == "Event");
            this.ytd_C = ytd.find((y) => y.name == "Academy");
            this.ytd_M = ytd.find((y) => y.name == "Membership");
            this.ytd_T = ytd.find((y) => y.name == "Trainer");
            this.ytd_V = ytd.find((y) => y.name == "Voucher");
            this.ytd_R = ytd.find((y) => y.name == "POS");

            this.adv_C = advance.find((y) => y.name == "Academy");
            this.adv_E = advance.find((y) => y.name == "Event");
            this.adv_G = advance.find((y) => y.name == "Facility");
            this.adv_M = advance.find((y) => y.name == "Membership");
            this.adv_T = advance.find((y) => y.name == "Trainer");
            this.adv_V = advance.find((y) => y.name == "Voucher");
            this.adv_R = advance.find((y) => y.name == "POS");
            this.hideLoader();
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    changeFxn() {
      if (this.searchParams.time_intervel == "custom") {
        this.flag = true;
      } else {
        this.flag = false;
      }
      this.refreshFlag = !this.refreshFlag;
    },
    getTotalByKey(key) {
      let total = 0;
      if (
        this.checkWritePermission(this.$modules.facility.management.slug) ||
        this.checkWritePermission(this.$modules.facility.schedule.slug)
      ) {
        total += Number(typeof this.ytd_G != "undefined" ? this.ytd_G[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.events.management.slug) ||
        this.checkWritePermission(this.$modules.events.schedule.slug)
      ) {
        total += Number(typeof this.ytd_E != "undefined" ? this.ytd_E[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.workshops.management.slug) ||
        this.checkWritePermission(this.$modules.workshops.schedule.slug)
      ) {
        total += Number(typeof this.ytd_C != "undefined" ? this.ytd_C[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.memberships.management.slug) ||
        this.checkWritePermission(this.$modules.memberships.dashboard.slug)
      ) {
        total += Number(typeof this.ytd_M != "undefined" ? this.ytd_M[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.trainers.management.slug) ||
        this.checkWritePermission(this.$modules.trainers.dashboard.slug)
      ) {
        total += Number(typeof this.ytd_T != "undefined" ? this.ytd_T[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.vouchers.management.slug) ||
        this.checkWritePermission(this.$modules.vouchers.issues.slug)
      ) {
        total += Number(typeof this.ytd_V != "undefined" ? this.ytd_V[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.pos.management.slug) ||
        this.checkWritePermission(this.$modules.pos.dashboard.slug)
      ) {
        total += Number(typeof this.ytd_R != "undefined" ? this.ytd_R[key] : 0);
      }
      return total;
    },
    getAdvanceTotalByKey(key) {
      let total = 0;
      if (
        this.checkWritePermission(this.$modules.facility.management.slug) ||
        this.checkWritePermission(this.$modules.facility.schedule.slug)
      ) {
        total += Number(typeof this.adv_G != "undefined" ? this.adv_G[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.events.management.slug) ||
        this.checkWritePermission(this.$modules.events.schedule.slug)
      ) {
        total += Number(typeof this.adv_E != "undefined" ? this.adv_E[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.workshops.management.slug) ||
        this.checkWritePermission(this.$modules.workshops.schedule.slug)
      ) {
        total += Number(typeof this.adv_C != "undefined" ? this.adv_C[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.memberships.management.slug) ||
        this.checkWritePermission(this.$modules.memberships.dashboard.slug)
      ) {
        total += Number(typeof this.adv_M != "undefined" ? this.adv_M[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.trainers.management.slug) ||
        this.checkWritePermission(this.$modules.trainers.dashboard.slug)
      ) {
        total += Number(typeof this.adv_T != "undefined" ? this.adv_T[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.vouchers.management.slug) ||
        this.checkWritePermission(this.$modules.vouchers.issues.slug)
      ) {
        total += Number(typeof this.adv_V != "undefined" ? this.adv_V[key] : 0);
      }
      if (
        this.checkWritePermission(this.$modules.pos.management.slug) ||
        this.checkWritePermission(this.$modules.pos.dashboard.slug)
      ) {
        total += Number(typeof this.adv_V != "undefined" ? this.adv_V[key] : 0);
      }
      return total;
    },
  },
  data: () => ({
    params: { venue_service_ids: [], product_ids: [], product_type_ids: [] },
    timeDuration: [
      { name: "All", title: "All" },
      { name: "day", title: "This Day" },
      { name: "week", title: "This Week" },
      { name: "month", title: "This Month" },
      { name: "year", title: "This Year" },
      { name: "custom", title: "Custom" },
    ],
    searchParams: {
      start_date: moment().startOf("year").format("YYYY-MM-DD"),
      end_date: moment().format("YYYY-MM-DD"),
    },
    menu1: false,
    menu2: false,
    flag: false,
    refreshFlag: false,
    ytd_G: undefined,
    ytd_E: undefined,
    ytd_C: undefined,
    ytd_M: undefined,
    ytd_T: undefined,
    ytd_V: undefined,
    ytd_R: undefined,
    adv_G: undefined,
    adv_E: undefined,
    adv_C: undefined,
    adv_M: undefined,
    adv_T: undefined,
    adv_V: undefined,
    adv_R: undefined,
    isLoading: false,
  }),
};
</script>

<style scoped>
/* .headline {
  background: none;
} */
.table th {
  font-size: 1rem !important;
  height: 48px;
  border-bottom: 0 !important;
  font-weight: 500 !important;
  opacity: 1 !important;
}
.table td {
  font-size: 1rem !important;
  height: 48px;
  padding: 0 16px;
  font-weight: 400;
  border-bottom: thin solid rgba(0, 0, 0, 0.05) !important;
  opacity: 1 !important;
}
.dash_tbl {
  border-collapse: collapse;
  width: 90%;
  margin: 0 auto;
  font-weight: 400;
  font-size: 14px;
  td {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.membership-da-table tr {
  border-bottom: 1px solid #f5f5f5;
}
.membership-da-table tr:last-child {
  border-bottom: none;
}

.add-on-prepend .v-input__prepend-inner {
  margin-top: 5px !important;
}
</style>
<script>
export default {
  name: "ForwardTicket",
  props: {
    ticketId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      thresholdModel: false,
      email: '',
      subject: '',
      description: '',
      valid: true
    }
  },
  computed: {
    emailRule() {
      const rules = [];
      const rule1 = (v) => !!v || "Email is required";
      rules.push(rule1);

      const rule2 = (v) => !v || /.+@.+\..+/.test(v) || "E-mail must be valid";
      rules.push(rule2);
      return rules;
    },
  },
  methods: {
    closeModal() {
      this.thresholdModel = false;

    },

    forwardTicket() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return false;
      }
      this.showLoader("Submitting")
      this.$http.post('/venues/support/forward-ticket', {
        email: this.email,
        subject: this.subject,
        description: this.description,
        ticket_id: atob(this.ticketId)
      })
          .then(res => {
            if (res.status === 200) {
              this.showSuccess("Ticket forwarded successfully")
              this.closeModal()
              this.$emit("refetch")
            }
          })
          .catch(error => {
            this.errorChecker(error)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
  }
}
</script>

<template>
  <div>
    <v-btn class="forward-ticket" elevation="0" type="button" @click="thresholdModel =true">
      Forward Ticket
    </v-btn>
    <v-dialog v-model="thresholdModel" max-width="800px" persistent scrollable width="100%">
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-title class="border-bottom">
            <div class="w-full">
              <div class="d-flex justify-space-between align-center">
                <p class="mb-0 font-medium">
                  Forward Ticket
                </p>
                <v-btn class="shadow-0" fab x-small @click="closeModal">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </v-card-title>
          <v-card-text>
            <v-row class="align-end">
              <v-col cols="12" sm="6">
                <label for="">
                  Email
                </label>
                <v-text-field
                    v-model="email"
                    :rules="emailRule"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                    type="email"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <label for="">
                  Subject
                </label>
                <v-text-field
                    v-model="subject"
                    :rules="[
                      (v) => !!v || 'Title is required',
                    ]"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12">
                <label for="">
                  Message
                </label>
                <v-textarea
                    v-model="description"
                    :rules="[
                      (v) => !!v || 'message is required',
                    ]"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="d-flex just">
                <v-btn
                    v-if="checkWritePermission($modules.customerSupport.b2cSupport.slug)"
                    class="white--text teal-color ml-auto"
                    height="40"
                    @click="forwardTicket"
                >
                  Forward Ticket
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>
  </div>
</template>

<style scoped>

</style>
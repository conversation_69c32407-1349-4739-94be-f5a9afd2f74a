<template>
    <v-container fluid>
      <ShopTabs/>
      <div class="d-flex justify-space-between align-center gap-x-2 mb-3">
        <h3 class="font-semibold text-blue">
          Select Service
        </h3>
      </div>
      <div class="d-flex align-center gap-x-4 overflow-x-auto pb-3 mb-2">
        <v-btn
            v-for="service in venueServices" :key="service.name"
            :class="{'active':selectedService && selectedService.name.toLowerCase()===service.name.toLowerCase()}"
            class="service"
            elevation="0"
            @click="changeService(service.id)"
        >
          {{ service.name }}
        </v-btn>
      </div>
      <v-row v-if="selectedService">
        <v-form ref="form" class="w-full" v-model="valid">
        <v-col sm="12" xl="9">
          <v-card v-for="(bookingTime,index) in bookingForm.bookings" :key="index"
                  class="shadow-0 bordered pa-0 rounded-3 mb-5">
            <div class="d-flex justify-space-between align-center gap-x-2 pb-1 mb-3 border-bottom rounded-0 px-5 pt-5">
              <h3 class="font-semibold text-blue text-base">
                Booking {{bookingForm.repeatBooking ? index+1 : '' }} Details
              </h3>
              <div v-if="bookingTime.facility && bookingTime.facility.per_capacity === 0">
                <div v-if="index === 0" class="d-flex align-center gap-x-2">
                  <p class="text-sm mb-0">
                    Repeat Booking
                  </p>
                  <v-switch
                      v-model="bookingForm.repeatBooking"
                      :false-value="0"
                      :ripple="false"
                      :true-value="1"
                      class="ma-0 v-input--reverse"
                      dense
                      hide-details
                  />
                </div>
              </div>
              <v-btn v-else-if="index > 0" icon @click="removeBookingTime(index)">
                <DeleteIcon/>
              </v-btn>
              </div>
            <v-row class="px-5 my-3">
              <v-col lg="4" sm="12">
                <label for="">
                  Booking Date
                </label>
                <date-field
                    v-model="bookingTime.date"
                    :backFill="checkBackfillPermission($modules.facility.schedule.slug)"
                    :hide-details="true"
                    @change="loadFacilities"
                    :rules="[(v) => !!v || 'Date is required']"
                    :dense="true"
                    label=""
                >
                  <template #prepend-inner>
                    <CalendarIcon/>
                  </template>
                </date-field>
              </v-col>
              <v-col lg="2" sm="12">
                <label for="">
                  {{ endTimeDisable ? 'Arrival' : 'Start' }} Time
                </label>
                <v-select
                    v-model="bookingTime.start_time"
                    :items="bookingTime.start_timings"
                    :menu-props="{ bottom: true, offsetY: true }"
                    :rules="[(v) => !!v || 'Start Time Required']"
                    :value="timeFormat(bookingTime.start_time)"
                    background-color="#fff"
                    @change="changeEndTime(index),loadFacilities(index)"
                    dense
                    hide-details="auto"
                    item-text="text"
                    item-value="value"
                    outlined
                >
                  <template #prepend-inner>
                    <TimeIcon/>
                  </template>
                </v-select>
              </v-col>
              <v-col lg="2" sm="12">
                <label for="">
                  {{ endTimeDisable ? 'Departure' : 'End' }} Time
                </label>
                <v-select
                    :readonly="endTimeDisable"
                    v-model="bookingTime.end_time"
                    :items="bookingTime.end_timings"
                    :menu-props="{ bottom: true, offsetY: true }"
                    :rules="[(v) => !!v || 'End Time Required']"
                    :value="timeFormat(bookingTime.end_time)"
                    background-color="#fff"
                    @change="loadFacilities(index)"
                    dense
                    hide-details="auto"
                    item-text="text"
                    item-value="value"
                    outlined
                >
                  <template #prepend-inner>
                    <TimeIcon/>
                  </template>
                </v-select>
              </v-col>
              <v-col lg="4" sm="12">
                <label for="">
                  Select Facility
                </label>
                <v-select
                    v-model="bookingTime.facility"
                    :items="bookingTime.facilities"
                    :menu-props="{ bottom: true, offsetY: true }"
                    :rules="[(v) => !!v || 'Facility is Required']"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    item-text="name"
                    outlined
                    @change="getRentals(index)"
                    return-object
                />
              </v-col>
              <template v-if="bookingForm.repeatBooking">
                <v-col lg="3" sm="12">
                  <label for="">
                    Select Type
                  </label>
                  <v-select
                      v-model="bookingTime.repeatType"
                      :items="['Daily', 'Weekly', 'Monthly', 'Advanced']"
                      :menu-props="{ bottom: true, offsetY: true }"
                      :rules="[(v) => !!v || 'Type is Required']"
                      background-color="#fff"
                      dense
                      outlined
                  />
                </v-col>
                <v-col v-if="bookingTime.repeatType === 'Weekly'" md="3">
                  <label for="">
                    Weekdays
                  </label>
                  <v-select
                      :items="weekdays"
                      :value="bookingTime.weekdays"
                      background-color="#fff"
                      dense
                      item-text="name"
                      item-value="value"
                      multiple
                      outlined
                  >
                    <template
                        v-if="weekdays.length === bookingTime.weekdays.length"
                        v-slot:selection="{ index }"
                    >
                      <span v-if="index === 0">All Days</span>
                    </template>
                    <template v-else v-slot:selection="{ item, index }">
                      <span v-if="index === 0">{{ item.name }}</span>
                      <span v-if="index === 1">, {{ item.name }}</span>
                      <span v-if="index === 2">, ...</span>
                    </template>
                  </v-select>
                </v-col>

                <v-col v-if="bookingTime.repeatType !== 'Advanced'" md="2">
                  <label for="">
                    Repeat Every
                  </label>
                  <v-text-field
                      :disabled="bookingTime.repeatType == null || bookingTime.repeatType === 'Never'"
                      :suffix="getRepeatSuffix(bookingTime.repeatType)"
                      :value="bookingTime.repeatNumber"
                      background-color="#fff"
                      dense
                      hide-details="auto"
                      min="1"
                      outlined
                      type="number"
                  >/
                  </v-text-field>
                </v-col>
                <v-col v-if="bookingTime.repeatType !== 'Advanced'" md="2">
                  <label for="">
                    Repeat End
                  </label>
                  <v-select
                      v-model="bookingTime.repeatEndType"
                      :disabled="bookingTime.repeatType == null"
                      :items="['Count', 'Date']"
                      :value="bookingTime.repeatEndType"
                      background-color="#fff"
                      dense
                      outlined
                  ></v-select>
                </v-col>
                <v-col
                    v-if="bookingTime.repeatType !== 'Advanced' && (bookingTime.repeatEndType == null || bookingTime.repeatEndType === 'Count')"
                    md="2"
                >
                  <label for="">
                    End After
                  </label>
                  <v-text-field
                      :disabled="bookingTime.repeatType == null"
                      :value="bookingTime.repeatOccurrence"
                      background-color="#fff"
                      dense
                      min="0"
                      outlined
                      suffix="Occurrence"
                      type="number"
                  ></v-text-field>
                </v-col>
                <v-col v-else-if="bookingTime.repeatType !== 'Advanced'" md="2">
                  <label for="">
                    End Date
                  </label>
                  <date-field
                      :backFill="checkBackfillPermission($modules.facility.management.slug)"
                      :value="bookingTime.repeatEndDate"
                      dense
                  >
                  </date-field>
                </v-col>
              </template>
            </v-row>
            <div class="product-details px-5 py-3 font-medium">
              Product Details
            </div>
            <v-row class="pa-5">
              <v-col :md="bookingTime.productCategoryId === -1 ? 2 : 3">
                <label for="">
                  Select Category
                </label>
                <v-autocomplete
                    v-model="bookingTime.productCategoryId"
                    :items="categoriesList"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    item-text="name"
                    item-value="id"
                    outlined
                    required
                >
                </v-autocomplete>
              </v-col>
              <v-col v-if="bookingTime.productCategoryId === -1" md="2">
                <label for="">
                  Product name
                </label>
                <v-text-field
                    v-model="bookingTime.selectedProduct.title"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    outlined
                    required
                >
                  <template v-slot:append>
                    <v-menu
                        :close-on-content-click="true"
                        nudge-bottom="105"
                        nudge-left="16"
                        top
                    >
                      <template v-slot:activator="{ on }">
                        <div class="d-flex align-center open-product" v-on="on">
                          {{ bookingTime.selectedProduct.rental ? "Base" : "Addon" }}
                        </div>
                      </template>
                      <v-card>
                        <v-card-text>
                          <v-radio-group v-model="bookingTime.selectedProduct.rental" column>
                            <v-radio
                                :value="true"
                                label="Base Product"
                            ></v-radio>
                            <v-radio :value="false" label="Addon Product"></v-radio>
                          </v-radio-group>
                        </v-card-text>
                      </v-card>
                    </v-menu>
                  </template>
                </v-text-field>
              </v-col>
              <v-col v-else md="4">
                <label for="">
                  Select Product
                </label>
                <v-autocomplete
                    v-model="bookingTime.selectedProduct"
                    :items="bookingTime.products"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    item-text="name"
                    item-value="id"
                    outlined
                    required
                    return-object
                ></v-autocomplete>
              </v-col>
              <v-col :md="bookingTime.productCategoryId === -1 ? 1 : 2">
                <label for="">
                  Quantity
                </label>
                <v-text-field
                    v-model="bookingTime.selectedProduct.quantity"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    min="1"
                    outlined
                    type="number"
                ></v-text-field>
              </v-col>
              <v-col v-if="bookingTime.productCategoryId === -1" md="2">
                <label>
                  Tax
                </label>
                <v-select
                    v-model="bookingTime.selectedProduct.tax_type_id"
                    :items="taxTypes"
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    item-text="text"
                    item-value="value"
                    outlined
                ></v-select>
              </v-col>
              <v-col v-if="bookingTime.productCategoryId === -1" md="2">
                <label for="">
                  Price (Pre Tax)
                </label>
                <v-text-field
                    v-model="bookingTime.selectedProduct.pre_tax_price"
                    :prefix="currencyCode"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    outlined
                    required
                    rows="2"
                ></v-text-field>
              </v-col>
              <v-col v-if="bookingTime.productCategoryId === -1" md="2">
                <label for="">
                  Price (Post Tax)
                </label>
                <v-text-field
                    v-model="bookingTime.selectedProduct.total_price"
                    :prefix="currencyCode"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    outlined
                    required
                    rows="2"
                ></v-text-field>
              </v-col>
              <v-col v-else md="2">
                <label for="">
                  Price
                </label>
                <v-text-field
                    v-model="bookingTime.selectedProduct.total_price"
                    :readonly="bookingTime.productCategoryId !== -1"
                    :suffix="currencyCode"
                    background-color="#fff"
                    dense
                    hide-details="auto"
                    outlined
                />
              </v-col>
              <v-col class="d-flex align-end" md="1">
                <v-btn
                    block
                    class="white--text teal-color"
                    elevation="0"
                    height="40"
                    @click="addSelectedProduct(index)"
                >
                  Add
                </v-btn>
              </v-col>
            </v-row>
            <template v-if="bookingTime.facility">
              <div class="text-sm px-5 py-3 font-medium">
                Added Products
              </div>

              <v-col md="12">
                <v-chip
                    v-for="(product, productIndex) in bookingTime.facility.products"
                    :key="productIndex"
                    :close="true"
                    class="ml-2 mb-2 added-product-chip"
                    close-icon="mdi-close"
                    @click:close="removeProduct(productIndex,index)"
                    label
                >
                  {{ product.name }} x {{ product.quantity | numberFormatter(3) }} - {{
                    Number(product.total_price) | toCurrency
                  }}
                  <span
                      v-if="product.discount != null"
                      class="text-decoration-line-through pl-1"
                  >
                      {{ product.discount.actual_total | toCurrency }}
                </span>
                </v-chip>
              </v-col>
            </template>
            <ShopCustomerPopup
                v-if="showCustomerPopup"
                :is-facility="true"
                :index="index"
                :show-customer-popup="showCustomerPopup"
                @close="closeCustomerPopup"
                @setCustomers="setCustomers"
                :max-customers="getMaxCustomers(index)"
            />
          </v-card>

          <div v-if="bookingForm.repeatBooking" class="d-flex justify-center align-center mt-3">
            <v-btn class="white--text" icon text @click="addBookingTiming">
              <PlusIcon/>
            </v-btn>
          </div>
          <div class="d-flex justify-end align-center mt-8 gap-x-4">
            <p class="mb-0 font-semibold">
              Total: {{ totalPrice | toCurrency }}
            </p>
            <v-btn class="white--text blue-color" text @click="validateBooking">
              Proceed
            </v-btn>
          </div>
        </v-col>
        </v-form>
      </v-row>
    </v-container>
  </template>

  <script>
  import ShopTabs from "@/views/Shop/ShopTabs.vue";
  import moment from "moment/moment";
  import TimeIcon from "@/assets/images/retail/time.svg";
  import CalendarIcon from "@/assets/images/retail/calendar.svg";
  import PlusIcon from "@/assets/images/misc/plus.svg";
  import ShopCustomerPopup from "@/components/Shop/ShopCustomerPopup.vue";
  import DeleteIcon from "@/assets/images/misc/delete-bg-icon.svg";
  // import facilities from "@/views/Facility/Facilities.vue";
  // import DeleteIcon from "@/assets/images/misc/delete-bg-icon.svg";

  export default {
    components: {
      DeleteIcon,
      ShopCustomerPopup,
      // DeleteIcon,
      ShopTabs, TimeIcon, CalendarIcon, PlusIcon
    },
    data() {
      return {
        valid: false,
        showCustomerPopup: false,
        time_increment: 60,
        min_booking_time: 60,
        facilities: [],
        selectedService: null,
        bookingForm: {
          bookings: [
            {
              facilities: [],
              date: moment().format("YYYY-MM-DD"),
              start_time: null,
              end_time: null,
              facility: null,
              repeatType: 'Weekly',
              weekdays: [],
              products: [],
              repeat: 1,
              repeatEndType: 'Count',
              endAfter: 1,
              productCategoryId: null,
              selectedProduct: {
                title: '',
                quantity: 1,
                tax_type_id: null,
                pre_tax_price: 0,
                total_price: 0,
                rental: true,
              },
              timings: [],
            },
          ],
          repeatBooking: 0,
        },
        categoriesList: [
          {name: "All", id: null},
          {name: "Open Product", id: -1},
        ],
      }
    },
    watch: {
      'bookingForm.repeatBooking': {
        handler: function (val) {
          if (!val) {
            this.bookingForm.bookings = this.bookingForm.bookings.slice(0, 1)
          }
        },
        deep: true
      },
    },
    mounted() {
      if (this.$store.getters.getVenueServices.status == false) {
        this.$store.dispatch("loadVenueServices")
        // .then((res) => {
        //   if (res.length) {
        //     this.selectedService = this.venueServices[0];
        // }
        // });
      }
      if (!this.$store.getters.getTaxTypes.status) {
        this.$store.dispatch("loadTaxTypes");
      }
      this.getStartTimes()
    },
    computed: {
      totalPrice(){
        let totalSum = 0;
        this.bookingForm.bookings.forEach(booking => {
          if (booking.facility && booking.facility.products) {
            booking.facility.products.forEach(product => {
              totalSum += product.total_price;
            });
          }
        });
        return totalSum;
      },
      endTimeDisable() {
        return !!(this.selectedService && this.selectedService.per_capacity === 1);

      },
      venueServices() {
        return this.$store.getters.getSportsService.filter((service) => service.name != "POS");
      },
      weekdays() {
        return [
          {name: 'Monday', value: 1},
          {name: 'Tuesday', value: 2},
          {name: 'Wednesday', value: 3},
          {name: 'Thursday', value: 4},
          {name: 'Friday', value: 5},
          {name: 'Saturday', value: 6},
          {name: 'Sunday', value: 7},
        ]
      },
      taxTypes() {
        return this.$store.getters.getTaxTypes.data
      },
    },
    methods: {
      async setCustomers(data) {

        let index = data.index;

        let cartData = {products: [], date_ranges: []};
        cartData.type = "Facility";
        cartData.facility_id = this.bookingForm.bookings[index].facility.id;
        cartData.facility_name = this.bookingForm.bookings[index].facility.name;
        cartData.venue_service_id = this.selectedService.id;
        cartData.start_date = this.bookingForm.bookings[index].date;
        cartData.start_time = this.bookingForm.bookings[index].start_time;
        cartData.end_time = this.bookingForm.bookings[index].end_time;

        data.customers.forEach((item) => {
          delete item.search;
          delete item.nameSearch;
        });
        cartData.customers = data.customers;

        let tickets = this.bookingForm.bookings[index].facility.products;

        tickets = tickets.map((ele) => {
          if (ele.quantity > 0) {
            ele.total_price = ((ele.product_price + ele.tax_amount) * ele.quantity);
          } else {
            ele.total_price = 0;
          }
          return ele;
        });

        let pushObject = tickets.filter(ele => ele.quantity > 0)

        cartData.products = [...cartData.products, ...pushObject];

        await this.$store.dispatch("addToCart", JSON.parse(JSON.stringify(cartData)));

        setTimeout(() => {
          this.$emit('close');
          this.resetBookingForm();
        }, 250)

      },
      getMaxCustomers(index) {
        let max = 1;
        if(this.bookingForm.bookings[index].facility && this.bookingForm.bookings[index].facility.per_capacity == 1){
          max = this.bookingForm.bookings[index].facility.products.reduce((sum, product) => {
            return sum + product.participant_count;
          }, 1);
        }
        return max;
        // return this.bookingForm.bookings.reduce((total, booking) => {
        //   if (booking.facility && booking.facility.products) {
        //     return total + booking.facility.products.reduce((sum, product) => {
        //       return sum + (product.participant_count || 0);
        //     }, 0);
        //   }
        //   return total;
        // }, 1);
      },
      closeCustomerPopup() {
        this.showCustomerPopup = false;
      },
      resetBookingForm() {
        this.bookingForm = {
          bookings: [
            {
              date: moment().format("YYYY-MM-DD"),
              start_time: null,
              end_time: null,
              facility: null,
              repeatType: 'Weekly',
              weekdays: [],
              repeat: 1,
              repeatEndType: 'Count',
              endAfter: 1,
              productCategoryId: null,
              selectedProduct: {
                title: '',
                quantity: 1,
                tax_type_id: null,
                pre_tax_price: 0,
                total_price: 0,
                rental: true,
              },
              facilities: [],
              start_timings: [],
              end_timings: [],
            },
          ],
          repeatBooking: 0,
        }
        if (this.$refs.form) {
          this.$refs.form.resetValidation();
        }
      },
      changeService(serviceId) {
        this.selectedService = this.venueServices.find(service => service.id === serviceId)
        this.bookingForm.bookings.forEach((ele, ind) => {
          this.loadFacilities(ind);
        });
        this.resetBookingForm();
        this.loadTimings(0);
        // this.loadFacilities();
      },
      getTimings(facilities) {

        let minStartTime = moment("23:59:59", "HH:mm:ss");
        let maxEndTime = moment("00:00:00", "HH:mm:ss");

        facilities.forEach(facility => {
          facility.facility_rentals.forEach(rental => {
            const startTime = moment(rental.start_time, "HH:mm:ss");
            const endTime = moment(rental.end_time, "HH:mm:ss");

            if (startTime.isBefore(minStartTime)) {
              minStartTime = startTime;
            }

            if (endTime.isAfter(maxEndTime)) {
              maxEndTime = endTime;
            }
          });
        });

        return {
          start_time: minStartTime.format("HH:mm:ss"),
          end_time: maxEndTime.format("HH:mm:ss")
        };

      },
      generateTimeSlots(startTime, endTime, increment) {
        const timeSlots = [];
        let currentTime = moment(startTime, 'HH:mm:ss');
        const endMoment = moment(endTime, 'HH:mm:ss');

        if (endTime === '23:59:59') {
          endMoment.add(1, 'second');
        }

        while (currentTime.isBefore(endMoment) || currentTime.isSame(endMoment)) {
          if (currentTime.isSame(endMoment) && currentTime.format('HH:mm:ss') === "00:00:00") {
            timeSlots.push('23:59:59');
          } else {
            timeSlots.push(currentTime.format('HH:mm:ss'));
          }
          currentTime.add(increment, 'minutes');
        }

        return timeSlots;
      },
      convertToTimeSlotObjects(timeSlots) {
        return timeSlots.map(time => {
          return {
            text: moment(time, 'HH:mm:ss').format('hh:mm a'),
            value: time
          };
        });
      },
      validateBooking(){
        this.showLoader('Validating Booking');
        let data = JSON.parse(JSON.stringify(this.bookingForm));

        data.bookings = data.bookings.map(ele => {
          delete ele.facilities;
          delete ele.end_timings;
          delete ele.start_timings;
          delete ele.products;
          delete ele.selectedProduct;
          return ele;
        })

        this.$http.post(`venues/shop/facilities/validate-booking/${this.selectedService.id}`, data)
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                // let data = response.data.data;
                // console.log(data)
                this.showCustomerPopup = true;
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            }).finally(() => {
              this.hideLoader();
            });
      },
      removeProduct(pIndex,index){
        this.bookingForm.bookings[index].facility.products.splice(pIndex,1);
      },
      addSelectedProduct(index) {
        let product = this.bookingForm.bookings[index].selectedProduct;
        product.total_price = product.total_price * product.quantity;

        let existingProduct = this.bookingForm.bookings[index].facility.products.find(p =>
            p.product_id === product.product_id || p.id === product.id
        );

        if (existingProduct) {
          existingProduct.quantity += product.quantity;
          existingProduct.price += product.price * product.quantity;
          existingProduct.total_price += product.total_price;
        } else {
          this.bookingForm.bookings[index].facility.products.push(JSON.parse(JSON.stringify(product)));
        }

        this.bookingForm.bookings[index].selectedProduct = {
          title: '',
          quantity: 1,
          tax_type_id: null,
          pre_tax_price: 0,
          total_price: 0,
          rental: true,
        };
        this.$forceUpdate();
      },
      loadTimings(index) {
        let date = this.bookingForm.bookings[index].date;
        this.showLoader('Loading Timings');
        this.$http
            .get(
                `venues/facilities/bookings/schedule?type=ground&venue_service_id=${this.selectedService.id}&date=${date}`
            )
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                let data = response.data.data;
                this.time_increment = data.configuration.time_increment;
                this.min_booking_time = data.configuration.min_booking_time;
                let {start_time, end_time} = this.getTimings(data.facilities);
                let timings = this.generateTimeSlots(start_time, end_time, this.time_increment);
                let formattedTimings = this.convertToTimeSlotObjects(timings);
                if (!formattedTimings.length) {
                  this.showError('No Facilities available');
                  return;
                }
                this.bookingForm.bookings[index].start_timings = formattedTimings.slice(0, formattedTimings.length - 1);
                this.bookingForm.bookings[index].end_timings = formattedTimings.slice(1);
                this.bookingForm.bookings[index].start_time = formattedTimings[0].value;
                this.bookingForm.bookings[index].end_time = moment(formattedTimings[0].value, 'HH:mm:ss').add(this.min_booking_time, 'minutes').format('HH:mm:ss');
                this.loadFacilities(index)
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            }).finally(() => {
          this.hideLoader();
        });
      },
      loadFacilities(index) {
        if (!this.bookingForm.bookings[index].start_time || !this.bookingForm.bookings[index].end_time || !this.bookingForm.bookings[index].date || !this.selectedService) {
          return;
        }
        const startTime = moment(this.bookingForm.bookings[index].start_time, "HH:mm:ss");
        let endTime = moment(this.bookingForm.bookings[index].end_time, "HH:mm:ss");
        if (this.bookingForm.bookings[index].end_time === '00:00:00') {
          endTime = moment("23:59:59", "HH:mm:ss");
        }
        if (endTime.isBefore(startTime)) {
          this.showError('End time cannot be before start time');
          return;
        }
        let duration = endTime.diff(startTime, 'minutes');

        this.showLoader('Loading Facilities');
        this.$http
            .get(
                `venues/shop/facilities?date=${this.bookingForm.bookings[index].date}&start_time=${this.bookingForm.bookings[index].start_time}&end_time=${this.bookingForm.bookings[index].end_time}&venue_service_id=${this.selectedService.id}`
            )
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                let loadedFacilities = response.data.data;
                this.bookingForm.bookings[index].facilities = loadedFacilities
                    .map((ele) => {
                      ele.products = [];
                      return ele;
                    })
                    .filter(f => f.min_booking_time <= duration)
                if (this.bookingForm.bookings[index].facility) {
                  this.getRentals(index)
                }
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            }).finally(() => {
          this.hideLoader();
        });
      },
      changeEndTime(index) {
        this.bookingForm.bookings[index].end_time = moment(this.bookingForm.bookings[index].start_time, 'HH:mm:ss').add(this.min_booking_time, 'minutes').format('HH:mm:ss');
      },
      getDefaultProducts(index) {
        if (
            this.bookingForm.bookings[index].date &&
            this.bookingForm.bookings[index].start_time &&
            this.bookingForm.bookings[index].facility
        ) {
          let url = `venues/facilities/bookings/rentals?start_time=${this.bookingForm.bookings[index].start_time}&end_time=${this.bookingForm.bookings[index].end_time}&facility_id=${this.bookingForm.bookings[index].facility.id}&date=${this.bookingForm.bookings[index].date}&venue_service_id=${this.selectedService.id}`;
          this.$http
              .get(url)
              .then((response) => {
                this.hideLoader();
                if (response.status == 200 && response.data.status == true) {
                  this.bookingForm.bookings[index].facility.products = [];
                  let r = response.data;
                  delete r.status;
                  delete r.message;
                  if(this.bookingForm.bookings[index].facility.per_capacity == 0){
                    if(r.data.length > 0){
                      let prod = r.data[0].products;
                      prod = prod.map((p) => {
                          p.total_price = (p.product_price + p.tax_amount) * p.quantity;
                          p.tax = p.tax_amount;
                          p.product_id = p.id;
                          p.total_amount = (p.product_price + p.tax_amount) * p.quantity;
                        return p;
                      })
                      prod.forEach(ele => {
                        let existingProduct = this.bookingForm.bookings[index].facility.products.find(p =>
                            p.product_id === ele.product_id || p.id === ele.id
                        );
                        if (existingProduct) {
                          existingProduct.quantity += ele.quantity;
                          existingProduct.price += ele.price * ele.quantity;
                          existingProduct.total_price += ele.total_price;
                        }
                        else{
                          this.bookingForm.bookings[index].facility.products.push(JSON.parse(JSON.stringify(ele)));
                        }
                      })
                      this.$forceUpdate();
                    }
                  }

                } else {
                  this.showError("Facility not available for this time");
                  this.resetBookingForm();
                }
              })
              .catch((err) => {
                console.error(err);
                this.hideLoader();
                this.showError("Slot not available");
                this.resetBookingForm();
              });
        } else {
          this.showError("Please select facility,and choose date and times");
        }
      },
      getRentals(index) {
        if (
            this.bookingForm.bookings[index].date &&
            this.bookingForm.bookings[index].start_time &&
            this.bookingForm.bookings[index].facility
        ) {
          this.bookingForm.bookings[index].products = [];
          let url = `venues/facilities/bookings/utils?facility_id=${this.bookingForm.bookings[index].facility.id}&booking_id=0&date=${this.bookingForm.bookings[index].date}&start_time=${this.bookingForm.bookings[index].start_time}&increment=${this.time_increment}&min_booking_time=${this.min_booking_time}&per_capacity=${this.bookingForm.bookings[index].facility.per_capacity}&venue_service_id=${this.selectedService.id}`;
          // if(this.bookingForm.bookings[index].facility.per_capacity == 0){
          //   url =   `venues/facilities/bookings/rentals?start_time=${this.bookingForm.bookings[index].start_time}&end_time=${this.bookingForm.bookings[index].end_time}&facility_id=${this.bookingForm.bookings[index].facility.id}&date=${this.bookingForm.bookings[index].date}&venue_service_id=${this.selectedService.id}`;
          // }
          this.showLoader("Checking Availability");
          this.$http
              .get(url)
              .then((response) => {
                this.hideLoader();
                if (response.status == 200 && response.data.status == true) {
                  if (response.data.data.facility_rentals && response.data.data.facility_rentals.length > 0) {

                    let products = [];
                    response.data.data.facility_rentals.forEach(rental => {
                      rental.rental = true;
                      rental.isAddon = false;
                      rental.tax = rental.tax_amount
                      rental.total_price = (rental.product_price + rental.tax_amount) * rental.quantity
                      products.push(rental);
                    });

                    response.data.data.categories.forEach(category => {
                      category.products.forEach(product => {
                        product.rental = false;
                        product.isAddon = true;
                        product.product_id = product.id;
                        product.product_price = product.price;
                        product.tax = product.tax_amount;
                        products.push(product);
                      });
                    });
                    this.bookingForm.bookings[index].products = products
                    this.$forceUpdate();
                    if (this.bookingForm.bookings[index].facility.per_capacity === 0) {
                      this.getDefaultProducts(index);
                      //   this.bookingForm.bookings[index].products = response.data.data.products;
                    }

                  } else {
                    this.showError("Facility not available for this time");
                  }
                } else {
                  this.showError("Facility not available for this time");
                  this.resetBookingForm();
                }
              })
              .catch((err) => {
                console.error(err);
                this.hideLoader();
                this.showError("Slot not available");
                this.resetBookingForm();
              });
        } else {
          this.showError("Please select facility,and choose date and times");
        }
      },
      getRepeatSuffix(type) {
        if (type.toString().toLowerCase() === 'daily') {
          return 'Day(s)'
        } else if (type.toString().toLowerCase() === 'weekly') {
          return 'Week(s)'
        } else if (type.toString().toLowerCase() === 'monthly') {
          return 'Month(s)'
        }
      },
      timeFormat(time) {
        if (!time) {
          return time;
        }
        return moment(time, "HH:mm:ss").format("hh:mm a");
      },
      getStartTimes() {
        let currentTime = moment("00:00:00", "HH:mm:ss");
        this.startTimes = [];
        this.startTimes.push({
          value: currentTime.format("HH:mm:ss"),
          text: currentTime.format("hh:mm a"),
        });
        let hour = 0;
        while (hour < 24) {
          currentTime = currentTime.add(60, "minutes");
          currentTime = currentTime.add(60, "minutes");
          let data = {
            value: currentTime.format("HH:mm:ss"),
            text: currentTime.format("hh:mm a"),
          };
          if (data.value == "00:00:00") {
            data.value = "23:59:59";
            this.startTimes.push(data);
            hour = 24;
          } else {
            this.startTimes.push(data);
            hour = currentTime.get("hours");
          }
        }
      },
      addBookingTiming() {
        this.bookingForm.bookings.push({
          date: null,
          start_time: null,
          end_time: null,
          facility: null,
          repeatType: 'Weekly',
          weekdays: [],
          repeat: 1,
          repeatEndType: 'Count',
          endAfter: 1,
          productCategoryId: null,
          selectedProduct: {
            title: '',
            quantity: 1,
            tax_type_id: null,
            pre_tax_price: 0,
            total_price: 0,
            rental: true,
          }
        })
      },
      removeBookingTime(index) {
        this.bookingForm.bookings.splice(index, 1)
      },

    },
  }
  </script>

  <style lang="scss" scoped>
  ::v-deep {
    .search {
      border-radius: 4px;
      border: 1px solid #EFEDED;
      background: #FFF;
      max-width: 250px;

      .v-label {
        opacity: 0.3;
      }
    }

    .v-input__prepend-inner {
      margin-top: 12px !important;
    }

    .v-input {
      font-size: 0.875rem !important;
    }
  }

  .service {
    border-radius: 4px;
    border: 1px solid rgba(17, 42, 70, 0.10);
    background: #FFF !important;
    padding: 0.875rem 1.25rem;
    cursor: pointer;
    text-transform: capitalize;
    opacity: 0.7 !important;
    font-weight: 400 !important;

    &.active {
      font-weight: 500 !important;
      border-radius: 4px;
      border: 1px solid #4FAEAF;
      color: #4FAEAF;
      opacity: 1 !important;
      background: rgba(79, 174, 175, 0.10) !important;
    }
  }

  .product-details {
    background: #F7F9FB;
  }

  .added-product-chip {
    padding: 1rem 0.75rem !important;
    line-height: 1rem !important;

    &, &:hover {
      background: #F5F8FA !important;
    }

    &.v-size--default {
      height: fit-content !important;
    }
  }

  .open-product {
    cursor: pointer;
    background: #F5F8FA !important;
    padding: 0.25rem;
    text-align: center;
  }
  </style>

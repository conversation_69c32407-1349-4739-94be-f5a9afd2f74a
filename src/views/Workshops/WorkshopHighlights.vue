<template>
  <v-container>
    <BackButton :handler="goToWorkshops"/>
    <v-container fluid>
      <v-form ref="form" v-model="valid" lazy-validation>
        <div class="bordered bg-white rounded-lg p-8 mb-7">
          <div class="row">
            <div class="col-12 col-md-7 d-flex justify-center flex-column">
              <h3 class="text-2xl font-semibold" style="font-weight: 700 ; font-size: 20px; line-height: 20px">
                {{ workshop.name }} </h3>
              <p class="mt-5" style="font-weight: 500 ; font-size: 14px; ">
                {{ workshop.description && truncateWithEllipsis(workshop.description, 250) || "Academy Description" }}
              </p>
              <p class="mt-2" style="font-weight: 500 ; font-size: 14px; ">
                <template v-if="workshop.start_date">
                  {{ workshop.start_date | dateformat }}
                </template>
                <template v-else>
                  Start Date
                </template>
                -
                <template v-if="workshop.end_date">
                  {{ workshop.end_date | dateformat }}
                </template>
                <template v-else>
                  End Date
                </template>
              </p>
            </div>
            <div>
              <view-image
                  v-if="workshop.image_path"
                  :contain="false"
                  :image="workshop.image_path"
                  defaultImage="workshop"
                  style="border-radius: 4px; height: 93px"
              ></view-image>
            </div>
          </div>
        </div>
        <div class="d-flex justify-end">
          <v-switch
              v-model="isEnableArabic"
              :false-value="false"
              :true-value="true"
              class="mx-4 my-0"
              dense
              hide-details="auto"
              label="Arabic"
          ></v-switch>
        </div>
        <div class="bordered bg-white rounded-lg p-8 mb-7">
          <v-row>
            <v-col cols="12" >
              <v-row dense>
                <v-col md="6">
                  <label for="">
                    Title*
                  </label>
                  <v-text-field
                      v-model="highlights.title"
                      :rules="[(v) => !!v || 'Highlight title is required']"
                      background-color="#fff"
                      class="q-text-field shadow-0"
                      dense
                      hide-details="auto"
                      outlined
                  ></v-text-field>
                </v-col>
                <v-col v-if="isEnableArabic" md="6" >
                  <label for="">
                    Title(AR)*
                  </label>
                  <v-text-field
                      v-model="highlights.ar_title"
                      :rules="[(v) => !!v || 'Title(AR) is required']"
                      background-color="#fff"
                      class="q-text-field shadow-0"
                      dense
                      hide-details="auto"
                      outlined
                      reverse
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12">
              <div>
                <label for="">Description</label>
                <RichEditor
                    key="en_editior"
                    v-model="highlights.description"
                    contents-lang-direction="ltr"
                    language="en"
                />
              </div>
              <div v-if="isEnableArabic" class="mt-2">
                <label for="">
                  Description (AR)
                </label>
                <RichEditor
                    key="ar_editior"
                    v-model="highlights.ar_description"
                    contents-lang-direction="rtl"
                    language="ar"
                />
              </div>
            </v-col>
          </v-row>
        </div>
        <div class="bordered bg-white rounded-lg p-8 mb-7">
          <v-row>
            <v-col md="4" cols="12" v-for="(document) in highlights.documents" :key="document.index">
              <image-uploader
                  @upload="
                      (data) => {
                        document.image = data;
                      }
                    "
                  :image_path="document.file_path"
                  :height="200"
                  defaultImage="user"
                  @remove="
                      () => {
                        highlights.documents.splice(highlights.documents.indexOf(document), 1);
                      }
                    "
                  text="Picture/Video"
                  :show-guide="false"
                  :allow-video="true"
                  message-text=""
                  ref="image_upload"
                  :is-remove-icon="true"
              ></image-uploader>
            </v-col>
            <v-col md="4" cols="12" class="d-flex rounded-3 flex-column justify-center align-center min-h-300">
              <v-btn
                  color="#ffffff"
                  elevation="0"
                  fab
                  style="color:#4FAEAF;"
                  @click.stop="addDocument"
              >
                <v-icon>mdi-plus</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
        <div class="d-flex justify-end align-center mt-8">
          <v-btn
              class="ma-2 white--text blue-color text-capitalize"
              color="darken-1"
              text
              @click="store"
          >
            Save
          </v-btn>
        </div>
      </v-form>
    </v-container>
  </v-container>
</template>
<script>

import BackButton from '../../components/Common/BackButton.vue'
import RichEditor from '../../components/Common/RichEditor.vue'
import ImageUploader from '../../components/Image/ImageUploader.vue'

export default {
  components: {
    ImageUploader,
    RichEditor,
    BackButton
  },
  data() {
    return {
      valid:true,
      isEnableArabic:false,
      defaultDocument:{
        file_path:null,
        image:null
      },
      workshop: {
        name: null,
        description: null,
        start_date: null,
        end_date: null,
      },
      highlights:{
        title: null,
        ar_title: null,
        description: "",
        ar_description: "",
        documents:[
          {
            ...this.defaultDocument,
            index:Math.random()
          }
        ]
      }
    };
  },
  mounted() {
    if (typeof this.$route.params.data != "undefined") {
      this.workshop.id = parseInt(atob(this.$route.params.data));
      this.getWorkshopDetails();
    }else{
      this.goToWorkshops();
    }
  },
  methods: {
    getWorkshopDetails () {
      this.showLoader('Loading')
      this.$http
          .get(`venues/workshops/details/${this.workshop.id}/highlights`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let workshopData = response.data.data
              this.workshop.status_id = 1
              this.workshop = workshopData
              if (!this.workshop.highlights){
                this.highlights.title = this.workshop.name;
                this.highlights.description = this.workshop.description;
                this.highlights.ar_title = this.workshop.ar_name;
                this.highlights.ar_description = this.workshop.ar_description;
                if (this.workshop.ar_name || this.workshop.ar_description){
                  this.isEnableArabic = true;
                }
              }else{
                this.highlights = this.workshop.highlights
              }
              if (!this.highlights.documents || this.highlights.documents.length === 0){
                this.highlights.documents = [
                  {
                    ...this.defaultDocument
                  }
                ]
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    store () {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      this.showLoader('Loading')
      const formData = new FormData();
      formData.append('title', this.highlights.title);
      formData.append('description',this.utf8ToBase64(this.highlights.description));
      if (this.highlights.ar_title){
        formData.append('ar_title', this.highlights.ar_title);
      }
      if (this.highlights.ar_description){
        formData.append('ar_description',this.utf8ToBase64(this.highlights.ar_description));
      }
      this.highlights.documents.forEach((document,index) => {
        if (document.id){
          formData.append(`documents[${index}][id]`, document.id);
        }
        if (document.image) {
          formData.append(`documents[${index}][image]`, document.image);
        }
      });
      this.$http
          .post(`venues/workshops/${this.workshop.id}/highlights`,formData)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.showSuccess("Highlights added successfully");
              this.getWorkshopDetails()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    addDocument () {
      this.highlights.documents.push({
        ...this.defaultDocument,
        index: Math.random()
      })
    },
    goToWorkshops() {
      this.$router.push({name: "Workshops"});
    },
    truncateWithEllipsis(str, maxLength) {
      if (str.length > maxLength) {
        return str.substring(0, maxLength - 3) + '...';
      } else {
        return str;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.add_ground_containers {
  width: -webkit-fill-available;
}

.imageInput {
  padding-top: 0;
}

.add_btn {
  margin-top: -20px;
}

.numbering-icon {
  width: 26px;
  height: 26px;
  line-height: 26px;
  font-weight: 500;
  color: white;
  font-size: 12px;
  text-align: center;
}
</style>

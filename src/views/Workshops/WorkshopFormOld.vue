<template>
  <v-container>
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="titles">Academy Details</div>
      <v-card rounded flat :style="cardStyle">
        <v-btn
          small
          @click="goToWorkshops"
          absolute
          top
          right
          style="top: -50px; margin-right: -1%"
        >
          <v-icon small>mdi-backburger</v-icon>Back
        </v-btn>
        <v-card-text class="pb-0">
          <v-row>
            <v-col cols="12" sm="4" md="4">
              <image-upload
                @upload="
                  (data) => {
                    workshop.image = data;
                  }
                "
                @remove="
                  () => {
                    workshop.image = null;
                  }
                "
                :image_path="workshop.image_path"
                :height="250"
                defaultImage="workshop"
              ></image-upload>
            </v-col>
            <v-col cols="12" sm="8" md="8">
              <v-row dense>
                <v-col cols="6" sm="4" md="4">
                  <v-text-field
                    v-model="workshop.name"
                    label="Academy Name*"
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Academy Name is required']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <v-select
                    v-model="workshop.workshop_type_id"
                    label="Type*"
                    :items="workshopTypes"
                    item-text="name"
                    item-value="id"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Type is required']"
                    required
                    :readonly="editMode"
                  ></v-select>
                </v-col>
                <v-col cols="4" md="4">
                  <v-select
                    v-model="workshop.type"
                    :items="[
                      { slug: 'F', name: 'Free' },
                      { slug: 'P', name: 'Paid' },
                    ]"
                    label="Fee Type*"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    :rules="[(v) => !!v || 'Academy Type is required']"
                    item-text="name"
                    item-value="slug"
                    background-color="#fff"
                    :readonly="editMode"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <v-select
                    v-model="workshop.venue_service_id"
                    label="Service*"
                    :items="venueServices"
                    item-text="name"
                    item-value="venue_service_id"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Service is required']"
                    required
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <date-field
                    :backFill="
                      checkBackfillPermission($modules.workshops.schedule.slug)
                    "
                    v-model="workshop.start_date"
                    :rules="[(v) => !!v || 'Start date is required']"
                    label="Start date*"
                    @change="workshopDateChange('start')"
                  >
                  </date-field>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <date-field
                    :backFill="
                      checkBackfillPermission($modules.workshops.schedule.slug)
                    "
                    v-model="workshop.end_date"
                    :rules="[(v) => !!v || 'End date is required']"
                    label="End date*"
                    @change="workshopDateChange('end')"
                  >
                  </date-field>
                </v-col>

                <v-col cols="12">
                  <v-textarea
                    v-model="workshop.description"
                    name="description"
                    label="Description"
                    rows="2"
                    outlined
                    background-color="#fff"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <div class="titles pt-6">
        <span>Academy Programs</span>
      </div>
      <v-row>
        <v-col>
          <v-expansion-panels
            flat
            hover
            tile
            light
            multiple
            accordion
            v-model="openProgramTabs"
          >
            <template
              v-for="(program, wp) in workshop.programs"
              class="pa-4 mb-4"
            >
              <v-expansion-panel
                :key="`a
        s_${wp}`"
                style="margin-top: 8px"
              >
                <v-expansion-panel-header>
                  <span class="titles">{{
                    program.name ? program.name : `Program ${wp + 1}`
                  }}</span>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-tooltip bottom v-if="workshop.programs.length > 1">
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        fab
                        x-small
                        absolute
                        top
                        right
                        style="margin-top: -8px"
                        @click="deleteWorkshopProgram(wp)"
                      >
                        <v-icon color="red">mdi-delete</v-icon>
                      </v-btn>
                    </template>
                    Delete
                  </v-tooltip>
                  <v-row>
                    <v-col cols="6" sm="4" md="4">
                      <v-text-field
                        v-model="program.name"
                        label="Program Name*"
                        outlined
                        background-color="#fff"
                        :rules="[(v) => !!v || 'Program Name is required']"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="4">
                      <v-text-field
                        label="Capacity*"
                        required
                        outlined
                        background-color="#fff"
                        v-model="program.capacity"
                        :rules="[
                          (v) => !!v || 'Capacity is required',
                          (v) => !isNaN(v) || 'Quantity must be Number',
                        ]"
                      ></v-text-field>
                    </v-col>
                    <v-col md="4">
                      <v-select
                        label="Duration*"
                        required
                        v-model="program.duration"
                        hint="Session duration (default 1 hr)"
                        :items="[
                          { text: '15 min', value: 15 },
                          { text: '30 min', value: 30 },
                          { text: '45 min', value: 45 },
                          { text: '1 hr', value: 60 },
                          { text: '1 hr 30 min', value: 90 },
                          { text: '2 hr', value: 120 },
                        ]"
                        outlined
                        :menu-props="{ bottom: true, offsetY: true }"
                        background-color="#fff"
                        @change="programDurationChange(wp)"
                        :rules="[(v) => !!v || 'Duration is required']"
                      ></v-select>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col>
                      <v-expansion-panels
                        flat
                        hover
                        tile
                        light
                        multiple
                        accordion
                        v-model="openTimeTabs"
                      >
                        <template
                          outlined
                          v-for="(schedule, ps) in program.schedules"
                        >
                          <v-expansion-panel
                            :key="'schedule_' + ps"
                            style="border: thin solid rgba(0, 0, 0, 0.12)"
                          >
                            <v-expansion-panel-header>
                              <span class="titles">Timing {{ ps + 1 }}</span>
                            </v-expansion-panel-header>
                            <v-expansion-panel-content>
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <v-btn
                                    v-bind="attrs"
                                    v-on="on"
                                    color="red"
                                    v-if="program.schedules.length > 0"
                                    @click="deletedSchedules(wp, ps)"
                                    fab
                                    x-small
                                    dark
                                    absolute
                                    top
                                    right
                                  >
                                    <v-icon>mdi-delete</v-icon>
                                  </v-btn>
                                </template>
                                Delete
                              </v-tooltip>
                              <v-container
                                v-bind:class="{
                                  isBookingExistsBorder:
                                    schedule.checkBookingsExist,
                                }"
                              >
                                <v-row>
                                  <v-col lg="4">
                                    <v-autocomplete
                                      :items="weekdays"
                                      item-text="name"
                                      item-value="bit_value"
                                      label="Days Applicable*"
                                      multiple
                                      outlined
                                      background-color="#fff"
                                      validate-on-blur
                                      :rules="[
                                        (v) =>
                                          v.length > 0 ||
                                          'Days Applicable is required',
                                      ]"
                                      v-model="schedule.weekdays"
                                      @change="
                                        checkBookingsExistsInFacility(wp, ps)
                                      "
                                    >
                                      <template
                                        v-if="
                                          weekdays.length ==
                                          schedule.weekdays.length
                                        "
                                        v-slot:selection="{ index }"
                                      >
                                        <span v-if="index == 0">All Days</span>
                                      </template>
                                      <template
                                        v-else
                                        v-slot:selection="{ item, index }"
                                      >
                                        <span v-if="index == 0">{{
                                          item.name
                                        }}</span>
                                        <span v-if="index == 1"
                                          >,{{ item.name }}</span
                                        >
                                        <span v-if="index == 2">, ...</span>
                                      </template>
                                      <template v-slot:prepend-item>
                                        <v-list-item
                                          ripple
                                          @click="toggle('schedules', wp, ps)"
                                        >
                                          <v-list-item-action>
                                            <v-icon
                                              :color="
                                                schedule.weekdays.length > 0
                                                  ? 'indigo darken-4'
                                                  : ''
                                              "
                                              >{{
                                                getIcon("schedules", wp, ps)
                                              }}</v-icon
                                            >
                                          </v-list-item-action>
                                          <v-list-item-content>
                                            <v-list-item-title
                                              >All</v-list-item-title
                                            >
                                          </v-list-item-content>
                                        </v-list-item>
                                      </template>
                                    </v-autocomplete>
                                  </v-col>

                                  <v-col lg="4" md="4">
                                    <v-select
                                      v-model="schedule.venue_service_id"
                                      label="Ground Assignment – Service*"
                                      :items="venueServices"
                                      item-text="name"
                                      item-value="venue_service_id"
                                      outlined
                                      :menu-props="{
                                        bottom: true,
                                        offsetY: true,
                                      }"
                                      @change="
                                        getFacilities($event),
                                          (schedule.is_external = false)
                                      "
                                      background-color="#fff"
                                      :rules="[
                                        (v) => !!v || 'Service is required',
                                      ]"
                                      required
                                    ></v-select>
                                  </v-col>
                                  <v-col lg="4" md="4">
                                    <v-select
                                      v-model="schedule.facility_id"
                                      item-value="id"
                                      item-text="name"
                                      :items="getServiceFacilities(wp, ps)"
                                      label="Ground Assignment – Facility*"
                                      outlined
                                      :menu-props="{
                                        bottom: true,
                                        offsetY: true,
                                      }"
                                      @change="
                                        facilityChange(wp, ps, 'internal')
                                      "
                                      background-color="#fff"
                                      :rules="
                                        facilityRule(schedule.is_external)
                                      "
                                    >
                                      <template v-slot:append-item>
                                        <v-divider></v-divider>
                                        <v-list-item
                                          ripple
                                          @click="
                                            facilityChange(wp, ps, 'external')
                                          "
                                        >
                                          <v-list-item-action>
                                            <v-icon color="teal"
                                              >mdi-open-in-new</v-icon
                                            >
                                          </v-list-item-action>
                                          <v-list-item-content>
                                            <v-list-item-title
                                              >External</v-list-item-title
                                            >
                                          </v-list-item-content>
                                        </v-list-item>
                                      </template>
                                      <template v-slot:label>
                                        <span v-if="schedule.is_external"
                                          >External Location</span
                                        >
                                        <span v-else
                                          >Ground Assignment – Facility*</span
                                        >
                                      </template>
                                    </v-select>
                                  </v-col>
                                  <v-col md="2">
                                    <date-field
                                      :backFill="
                                        checkBackfillPermission(
                                          $modules.workshops.schedule.slug
                                        )
                                      "
                                      v-model="schedule.start_date"
                                      label="Start date*"
                                      :rules="[
                                        (v) => !!v || 'Start date is required',
                                      ]"
                                      @change="
                                        checkBookingsExistsInFacility(wp, ps)
                                      "
                                    >
                                    </date-field>
                                  </v-col>
                                  <v-col md="2">
                                    <date-field
                                      :backFill="
                                        checkBackfillPermission(
                                          $modules.workshops.schedule.slug
                                        )
                                      "
                                      v-model="schedule.end_date"
                                      label="End date*"
                                      :maxDate="workshop.end_date"
                                      :rules="[
                                        (v) => {
                                          if (v) {
                                            if (
                                              schedule.end_date <
                                              schedule.start_date
                                            ) {
                                              return 'End date should be greater than Start date';
                                            }
                                          }
                                          if (!v) {
                                            return 'End date is required';
                                          }
                                          return true;
                                        },
                                      ]"
                                      @change="
                                        checkBookingsExistsInFacility(wp, ps)
                                      "
                                    >
                                    </date-field>
                                  </v-col>
                                  <v-col lg="2" md="2">
                                    <v-select
                                      :items="getServiceTimeSlot(wp, ps)"
                                      label="Start Time*"
                                      item-text="formatted"
                                      item-value="time"
                                      v-model="schedule.start_time"
                                      :rules="[
                                        (v) => !!v || 'Start time is required',
                                      ]"
                                      validate-on-blur
                                      outlined
                                      :menu-props="{
                                        bottom: true,
                                        offsetY: true,
                                      }"
                                      background-color="#fff"
                                      @change="EndTimeValidator(wp, ps)"
                                    ></v-select>
                                  </v-col>
                                  <v-col lg="2" md="2">
                                    <v-select
                                      :readonly="true"
                                      :items="getServiceTimeSlot(wp, ps)"
                                      label="End Time*"
                                      item-text="formatted"
                                      item-value="time"
                                      v-model="schedule.end_time"
                                      :rules="[
                                        (v) =>
                                          !!v ||
                                          'Facility is not available for this time! Change start time ',
                                      ]"
                                      outlined
                                      :menu-props="{
                                        bottom: true,
                                        offsetY: true,
                                      }"
                                      validate-on-blur
                                      background-color="#fff"
                                    ></v-select>
                                  </v-col>
                                  <v-col lg="4" md="4">
                                    <v-autocomplete
                                      :items="trainers"
                                      label="Trainers*"
                                      item-text="name"
                                      item-value="id"
                                      multiple
                                      v-model="schedule.trainer_ids"
                                      :rules="[
                                        (v) =>
                                          !!v.length ||
                                          'Trainer(s) is required',
                                      ]"
                                      validate-on-blur
                                      outlined
                                      background-color="#fff"
                                    >
                                    </v-autocomplete>
                                  </v-col>
                                  <v-col v-if="schedule.checkBookingsExist">
                                    <p class="isBookingExists">
                                      {{ schedule.checkBookingsExist }}
                                    </p>
                                  </v-col>
                                  <v-col lg="12" md="12">
                                    <v-card
                                      flat
                                      outlined
                                      v-if="schedule.is_external"
                                    >
                                      <v-btn
                                        fab
                                        absolute
                                        top
                                        right
                                        x-small
                                        elevation="1"
                                        @click="
                                          showFullScreenMap(schedule, wp, ps)
                                        "
                                      >
                                        <v-icon>mdi-fullscreen</v-icon>
                                      </v-btn>
                                      <v-card-text>
                                        <v-row>
                                          <v-col
                                            cols="12"
                                            sm="12"
                                            md="12"
                                            class="pr-4"
                                          >
                                            <v-row
                                              no-gutters
                                              v-if="!mapFullScreenDialoge"
                                            >
                                              <v-col md="12">
                                                <v-autocomplete
                                                  label="Location*"
                                                  v-model="schedule.location"
                                                  :items="
                                                    schedule.locationEntries
                                                  "
                                                  @keyup="
                                                    changelocationText(
                                                      $event.target.value,
                                                      schedule
                                                    )
                                                  "
                                                  item-text="value"
                                                  item-value="value"
                                                  hide-no-data
                                                  :loading="schedule.isLoading"
                                                  outlined
                                                  background-color="#fff"
                                                  :rules="[
                                                    (v) =>
                                                      !!v ||
                                                      'Location is required',
                                                  ]"
                                                  @change="
                                                    changeLocation(schedule)
                                                  "
                                                ></v-autocomplete>
                                              </v-col>
                                            </v-row>
                                            <GmapMap
                                              v-bind:center="{
                                                lat: parseFloat(
                                                  schedule.latitude
                                                ),
                                                lng: parseFloat(
                                                  schedule.longitude
                                                ),
                                              }"
                                              :zoom="12"
                                              map-type-id="terrain"
                                              @click="
                                                updateCoordinates(
                                                  $event,
                                                  wp,
                                                  ps
                                                )
                                              "
                                              style="width: 100%; height: 200px"
                                            >
                                              <GmapMarker
                                                ref="mapRef"
                                                :position="{
                                                  lat: parseFloat(
                                                    schedule.latitude
                                                  ),
                                                  lng: parseFloat(
                                                    schedule.longitude
                                                  ),
                                                }"
                                                :clickable="true"
                                                :draggable="true"
                                                @dragend="
                                                  updateCoordinates(
                                                    $event,
                                                    wp,
                                                    ps
                                                  )
                                                "
                                              />
                                            </GmapMap>
                                          </v-col>
                                        </v-row>
                                      </v-card-text>
                                    </v-card>
                                  </v-col>
                                </v-row>
                              </v-container>
                            </v-expansion-panel-content>
                          </v-expansion-panel>
                        </template>
                      </v-expansion-panels>
                    </v-col>
                  </v-row>

                  <div class="add_btn">
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-bind="attrs"
                          v-on="on"
                          color="blue-color"
                          fab
                          x-small
                          dark
                          @click="addTiming(wp)"
                        >
                          <v-icon>mdi-plus-circle</v-icon>
                        </v-btn>
                      </template>
                      Add timing
                    </v-tooltip>
                  </div>
                  <div class="titles pt-6">
                    <span>Pricing</span>
                  </div>

                  <v-row>
                    <v-col>
                      <v-expansion-panels
                        flat
                        hover
                        tile
                        light
                        multiple
                        accordion
                        v-model="openPriceTabs"
                      >
                        <template
                          outlined
                          class="mt-4"
                          v-for="(product, pp) in program.pricing"
                          ><v-expansion-panel
                            :key="'product' + pp"
                            style="border: thin solid rgba(0, 0, 0, 0.12)"
                          >
                            <v-expansion-panel-header>
                              <span class="titles">{{
                                product.name
                                  ? product.name
                                  : `Product ${pp + 1}`
                              }}</span>
                            </v-expansion-panel-header>
                            <v-expansion-panel-content>
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <v-btn
                                    v-bind="attrs"
                                    v-on="on"
                                    color="red"
                                    v-if="program.pricing.length > 0"
                                    @click="deletedProductPricing(wp, pp)"
                                    fab
                                    x-small
                                    dark
                                    absolute
                                    top
                                    right
                                  >
                                    <v-icon>mdi-delete</v-icon>
                                  </v-btn>
                                </template>
                                Delete
                              </v-tooltip>
                              <v-container>
                                <v-row>
                                  <v-col
                                    cols="12"
                                    :sm="workshop.workshop_type_id == 1 ? 6 : 4"
                                  >
                                    <v-text-field
                                      label="Product*"
                                      required
                                      outlined
                                      background-color="#fff"
                                      v-model="product.name"
                                      :rules="[
                                        (v) =>
                                          !!v || 'Product name is required',
                                      ]"
                                    ></v-text-field>
                                  </v-col>
                                  <v-col
                                    lg="3"
                                    md="3"
                                    v-if="workshop.workshop_type_id == 1"
                                  >
                                    <v-select
                                      :items="durationType"
                                      label="Duration type*"
                                      item-text="name"
                                      item-value="slug"
                                      v-model="product.duration"
                                      :rules="[
                                        (v) => !!v || 'Duration is required',
                                      ]"
                                      outlined
                                      :menu-props="{
                                        bottom: true,
                                        offsetY: true,
                                      }"
                                      validate-on-blur
                                      background-color="#fff"
                                      @change="
                                        durationChange(wp, pp),
                                          validateDate(product.count, wp, pp)
                                      "
                                    ></v-select>
                                  </v-col>
                                  <v-col
                                    cols="12"
                                    sm="3"
                                    v-if="workshop.workshop_type_id == 1"
                                  >
                                    <v-text-field
                                      :disabled="product.duration == 'FT'"
                                      label="Duration Count*"
                                      required
                                      outlined
                                      background-color="#fff"
                                      @change="validateDate($event, wp, pp)"
                                      v-model="product.count"
                                      :rules="[
                                        (v) => {
                                          if (product.duration == 'FT')
                                            return true;
                                          if (!v) return 'Count is required';
                                          return true;
                                        },
                                      ]"
                                    ></v-text-field>
                                  </v-col>
                                  <v-col
                                    lg="2"
                                    md="2"
                                    v-if="workshop.workshop_type_id == 1"
                                  >
                                    <v-select
                                      :items="occurrence"
                                      label="Occurrence*"
                                      item-text="name"
                                      item-value="slug"
                                      v-model="product.occurrence"
                                      :rules="[
                                        (v) => !!v || 'Occurrence is required',
                                      ]"
                                      outlined
                                      :menu-props="{
                                        bottom: true,
                                        offsetY: true,
                                      }"
                                      validate-on-blur
                                      background-color="#fff"
                                    ></v-select>
                                  </v-col>
                                  <v-col
                                    cols="12"
                                    sm="2"
                                    v-if="workshop.workshop_type_id == 1"
                                  >
                                    <v-text-field
                                      label="Frequency*"
                                      required
                                      outlined
                                      background-color="#fff"
                                      v-model="product.frequency"
                                      :rules="[
                                        (v) => {
                                          if (!v)
                                            return 'Frequency is required';
                                          if (v <= 0)
                                            return 'Frequency should greater than zero';
                                          return true;
                                        },
                                      ]"
                                    ></v-text-field>
                                  </v-col>
                                  <v-col md="2">
                                    <v-select
                                      @change="taxChange(product)"
                                      label="Tax*"
                                      :disabled="workshop.type == 'F'"
                                      v-model="product.tax_type_id"
                                      item-value="value"
                                      item-text="text"
                                      hint="Required tax"
                                      :rules="[
                                        (v) => {
                                          if (workshop.type == 'F') return true;
                                          if (!v) return 'Tax is required';
                                          return true;
                                        },
                                      ]"
                                      :items="taxTypes"
                                      outlined
                                      :menu-props="{
                                        bottom: true,
                                        offsetY: true,
                                      }"
                                      background-color="#fff"
                                    ></v-select>
                                  </v-col>

                                  <v-col cols="12" sm="3" md="3">
                                    <v-text-field
                                      label="Price (Pre Tax)*"
                                      required
                                      outlined
                                      :disabled="workshop.type == 'F'"
                                      background-color="#fff"
                                      :suffix="currencyCode"
                                      v-model="product.pre_tax_price"
                                      @change="
                                        calculateTaxVariation(
                                          $event,
                                          product,
                                          'pre'
                                        )
                                      "
                                      :rules="[
                                        (v) => {
                                          if (workshop.type == 'F') return true;
                                          if (v <= 0)
                                            return 'Price should greater than zero';
                                          if (v && isNaN(v))
                                            return 'Price must be Number';
                                          return true;
                                        },
                                      ]"
                                    ></v-text-field>
                                  </v-col>

                                  <v-col cols="12" sm="3" md="3">
                                    <v-text-field
                                      label="Price (Post Tax)*"
                                      required
                                      outlined
                                      :disabled="workshop.type == 'F'"
                                      background-color="#fff"
                                      :suffix="currencyCode"
                                      v-model="product.total_price"
                                      @change="
                                        calculateTaxVariation(
                                          $event,
                                          product,
                                          'post'
                                        )
                                      "
                                      :rules="[
                                        (v) => {
                                          if (workshop.type == 'F') return true;
                                          if (v <= 0)
                                            return 'Price should greater than zero';
                                          if (v && isNaN(v))
                                            return 'Price must be Number';
                                          return true;
                                        },
                                      ]"
                                    ></v-text-field>
                                  </v-col>
                                </v-row>
                              </v-container>
                            </v-expansion-panel-content>
                          </v-expansion-panel>
                        </template>
                      </v-expansion-panels>
                    </v-col>
                  </v-row>

                  <div class="add_btn">
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-bind="attrs"
                          v-on="on"
                          color="blue-color"
                          fab
                          x-small
                          dark
                          @click="addPricing(wp)"
                        >
                          <v-icon>mdi-plus-circle</v-icon>
                        </v-btn>
                      </template>
                      Add pricing
                    </v-tooltip>
                  </div>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </template>
          </v-expansion-panels>
        </v-col>
      </v-row>
      <div class="add_btn">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              color="teal-color"
              fab
              x-small
              dark
              @click="addProgram"
            >
              <v-icon>mdi-plus-circle</v-icon>
            </v-btn>
          </template>
          Add
        </v-tooltip>
      </div>

      <div class="titles mt-6">Document</div>
      <v-card
        flat
        rounded
        :style="cardStyle"
        class="mb-4"
        v-for="(document, k) in workshop.documents"
        :key="'d_' + k"
      >
        <v-card-text>
          <v-row>
            <v-col cols="12" sm="4" md="4">
              <v-text-field
                label="Name"
                required
                outlined
                background-color="#fff"
                v-model="document.name"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="4" md="4">
              <v-select
                v-model="document.document_type_id"
                :items="documentTypes"
                label="Type"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                item-text="name"
                item-value="id"
                background-color="#fff"
              ></v-select>
            </v-col>
            <v-col cols="12" sm="4" md="4">
              <v-file-input
                :label="
                  editMode && document.original_file_name ? '' : 'Select File'
                "
                v-model="document.file"
                prepend-inner-icon="mdi-paperclip"
                prepend-icon
                outlined
                background-color="#fff"
              >
                <template v-slot:label>
                  <span v-if="!editMode || !document.original_file_name">
                    Select file
                  </span>
                  <span
                    v-if="
                      editMode == true &&
                      document.file == null &&
                      document.original_file_name
                    "
                    class="font-weight-bold"
                  >
                    <span
                      style="width: 70%; display: inline-block"
                      class="text-truncate"
                      >{{ document.original_file_name }}</span
                    >
                    <span
                      style="width: 20%; display: inline-block"
                      class="text-truncate"
                      >.{{
                        document.original_file_name.split(".")[
                          document.original_file_name.split(".").length - 1
                        ]
                      }}</span
                    >
                  </span>
                </template>
              </v-file-input>
            </v-col>

            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="red"
                  @click="deleteDocuments(k)"
                  fab
                  x-small
                  dark
                  absolute
                  top
                  right
                >
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </template>
              Delete
            </v-tooltip>
          </v-row>
        </v-card-text>
      </v-card>
      <div class="add_btn">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              color="teal-color"
              fab
              x-small
              dark
              @click="addDocuments"
            >
              <v-icon>mdi-plus-circle</v-icon>
            </v-btn>
          </template>
          Add
        </v-tooltip>
      </div>
      <v-row>
        <v-spacer></v-spacer>
        <v-btn
          color=" darken-1"
          class="ma-2 white--text teal-color"
          text
          @click="goToWorkshops"
          >Back</v-btn
        >
        <v-btn
          color="darken-1"
          class="ma-2 white--text blue-color"
          @click="updateWorkshop"
          text
          >Save</v-btn
        >
        <v-btn
          color="darken-1"
          class="ma-2 white--text blue-color"
          @click="publishWorkshop"
          text
          >Publish</v-btn
        >
      </v-row>

      <v-dialog v-model="locationDialoge" scrollable persistent width="40%">
        <v-card>
          <v-card-title class="headline">Select Location</v-card-title>
          <v-card-text class="pa-5">
            <v-row>
              Please select a location from the list. If you don't see correct
              location name in the list below try changing location on map by
              dragging the marker or clicking on the preffered location.
            </v-row>
            <v-row>
              <v-select
                :items="mapLocations"
                item-text="formatted_address"
                item-value="formatted_address"
                v-model="workshop.location"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
              ></v-select>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="blue-color white--text" text @click="closeLocation"
              >Close</v-btn
            >
            <v-btn class="teal-color white--text" text @click="dragMapChange"
              >Done</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="mapFullScreenDialoge" scrollable width="80%">
        <v-card>
          <v-card-text class="pa-4">
            <v-row no-gutters v-if="mapFullScreenDialoge">
              <v-col md="12">
                <v-autocomplete
                  label="Location"
                  v-model="tempSchedule.location"
                  :items="tempSchedule.locationEntries"
                  @keyup="changelocationText($event.target.value, tempSchedule)"
                  item-text="value"
                  item-value="value"
                  hide-no-data
                  :loading="tempSchedule.isLoading"
                  outlined
                  background-color="#fff"
                  @change="changeLocation(tempSchedule)"
                ></v-autocomplete>
              </v-col>
            </v-row>
            <GmapMap
              v-bind:center="{
                lat: parseFloat(tempSchedule.latitude),
                lng: parseFloat(tempSchedule.longitude),
              }"
              :zoom="12"
              map-type-id="terrain"
              @click="updateCoordinates($event, tmpPindex, tmpSindex)"
              style="width: 100%; height: 600px"
            >
              <GmapMarker
                ref="mapRef"
                :position="{
                  lat: parseFloat(tempSchedule.latitude),
                  lng: parseFloat(tempSchedule.longitude),
                }"
                :clickable="true"
                :draggable="true"
                @dragend="updateCoordinates($event, tmpPindex, tmpSindex)"
              />
            </GmapMap>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-form>
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
  </v-container>
</template>
<script>
import { GetSuggestions, placeDetails } from "@/utils/placesUtils";
import constants from "../../utils/constants";
import moment from "moment";

export default {
  data() {
    return {
      openTimeTabs: [0],
      openPriceTabs: [0],
      openProgramTabs: [0],
      addStageDialog: false,
      locationDialoge: false,
      mapFullScreenDialoge: false,
      mapLocations: [],
      locationHistory: {},
      valid: true,
      loc: {
        latitude: 24.**************,
        longitude: 54.37532545660189,
      },
      workshop: {
        workshop_type_id: 1,
        name: null,
        type: null,
        description: null,
        venue_service_id: null,
        status_id: 11,

        start_date: null,
        end_date: null,
        facility_id: null,
        documents: [
          {
            name: null,
            document_type_id: null,
            file: null,
          },
        ],
        programs: [
          {
            name: null,
            duration: null,
            capacity: null,

            schedules: [
              {
                venue_service_id: null,
                facility_id: null,
                weekdays: [],
                start_date: null,
                end_date: null,
                start_time: null,
                end_time: null,
                latitude: 24.45342255,
                longitude: 54.35851069,
                heatmap: null,
                is_external: false,
                autocompleteLocationModel: null,
                location: null,
                locationEntries: [],
                isLoading: false,
              },
            ],
            pricing: [
              {
                name: null,
                duration: null,
                duration_count: null,
                occurrence: null,
                frequency: null,
                price: null,
                tax_type: null,
              },
            ],
          },
        ],
        deleted_schedules: [],
        deleted_documents: [],
        deleted_programs: [],
        deleted_pricing: [],
      },
      facilities: [],
      timeSlot: [],
      levels: [],
      editMode: false,
      timings: constants.TIMINGS,
      isLoading: false,
      autocompleteLocationModel: null,
      locationSearchText: null,
      locationEntries: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      workshopTypes: [],
      trainers: [],
      tempWorkshopForLocation: null,
      programIndex: null,
      scheduleIndex: null,
      occurrence: [
        //Don;'t remove: TODO:
        // { slug: "Y", name: "Yearly" },
        // { slug: "M", name: "Monthly" },
        // Commented becouse can't handle yearly & montly without date selection
        { slug: "W", name: "Weekly" },
        { slug: "D", name: "Daily" },
      ],
      durationType: [
        { slug: "FT", name: "Full term" },
        { slug: "M", name: "Month" },
        { slug: "W", name: "Week" },
        { slug: "D", name: "Day" },
      ],
      tempSchedule: {},
      tmpPindex: null,
      tmpSindex: null,
    };
  },
  watch: {
    // locationSearchText(newVal) {
    //   var _vue = this;
    //   if (
    //     newVal == null ||
    //     newVal.length == 0 ||
    //     this.autocompleteLocationModel == newVal
    //   )
    //     return;
    //   this.isLoading = true;
    //   GetSuggestions(newVal)
    //     .then(function (res) {
    //       _vue.isLoading = false;
    //       _vue.locationEntries = res;
    //     })
    //     .catch((error) => {
    //       this.errorChecker(error);
    //     });
    // },
  },
  computed: {
    venueServices() {
      return this.$store.getters.getVenueServices.data;
    },
    documentTypes() {
      return this.$store.getters.getDocumentTypes.data;
    },
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
    taxTypes() {
      return this.$store.getters.getTaxTypes.data;
    },
  },
  created() {
    if (typeof this.$route.params.data != "undefined") {
      this.workshop.id = parseInt(atob(this.$route.params.data));
    }
  },
  mounted() {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
    if (this.$store.getters.getDocumentTypes.status == false) {
      this.$store.dispatch("loadDocumentTypes");
    }
    if (!this.$store.getters.getWeekdays.data.length) {
      this.$store.dispatch("loadWeekdays");
    }
    if (this.$store.getters.getTaxTypes.status == false) {
      this.$store.dispatch("loadTaxTypes").then((res) => {
        if (res.status == 200 && this.workshop.id) {
          this.getWorkshopDetails();
        }
      });
    } else if (this.workshop.id) {
      this.getWorkshopDetails();
    }
    this.getWorkshopTypes();
    this.getTrainers();
  },
  methods: {
    changelocationText(newVal, schedule) {
      var _vue = schedule;
      var _vue2 = this;
      if (newVal == null || newVal.length == 0 || schedule.location == newVal) {
        return;
      }
      schedule.locationEntries = [];
      GetSuggestions(newVal)
        .then(function (res) {
          _vue.isLoading = false;
          _vue.locationEntries = res;
          _vue2.$forceUpdate();
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    showFullScreenMap(schedule, wp, sp) {
      // console.log(schedule);
      this.mapFullScreenDialoge = true;
      this.tempSchedule.location = schedule.location;
      this.tempSchedule.locationEntries = [
        { value: schedule.location, id: "0" },
      ];
      this.tempSchedule = schedule;
      this.tmpPindex = wp;
      this.tmpSindex = sp;
    },
    programDurationChange(programIndex) {
      if (this.workshop.programs[programIndex].length != 0) {
        this.workshop.programs[programIndex].schedules.forEach((schedule) => {
          schedule.is_external = false;
          schedule.facility_id = null;
          schedule.start_time = null;
          schedule.end_time = null;
        });
        this.openTimeTabs = this.workshop.programs[programIndex].schedules.map(
          (k, i) => i
        );
      }
    },
    durationChange(programIndex, pricingIndex) {
      if (
        this.workshop.programs[programIndex].pricing[pricingIndex].duration ==
        "FT"
      ) {
        this.workshop.programs[programIndex].pricing[pricingIndex].count = 1;
      }
      if (
        this.workshop.programs[programIndex].pricing[pricingIndex].count &&
        this.workshop.programs[programIndex].pricing[pricingIndex].duration ==
          "M"
      ) {
        this.workshop.programs[programIndex].pricing[pricingIndex].count = null;
      }
    },

    validateDate(event, programIndex, pricingIndex) {
      if (
        this.workshop.programs[programIndex].pricing[pricingIndex].duration ==
        "M"
      ) {
        const start_date = moment(this.workshop.start_date);
        const end_date = moment(this.workshop.end_date);
        const month = end_date.diff(start_date, "month");
        if (month < event) {
          this.workshop.programs[programIndex].pricing[pricingIndex].count =
            null;
          this.showError("Product duration is greater than workshop duration");
        }
      }
      if (
        this.workshop.programs[programIndex].pricing[pricingIndex].duration ==
        "W"
      ) {
        const start_date = moment(this.workshop.start_date);
        const end_date = moment(this.workshop.end_date);
        const week = end_date.diff(start_date, "week");
        if (week < event) {
          this.workshop.programs[programIndex].pricing[pricingIndex].count =
            null;
          this.showError("Product duration is greater than workshop duration");
        }
      }
      if (
        this.workshop.programs[programIndex].pricing[pricingIndex].duration ==
        "D"
      ) {
        const start_date = moment(this.workshop.start_date);
        const end_date = moment(this.workshop.end_date);
        const days = end_date.diff(start_date, "days");

        if (days < event) {
          this.workshop.programs[programIndex].pricing[pricingIndex].count =
            null;
          this.showError("Product duration is greater than workshop duration");
        }
      }
    },

    changeLocation(schedule) {
      // this.workshop.location = schedule.location;
      if (schedule.location) {
        let placeId = schedule.locationEntries.find(
          (val) => val.value == schedule.location
        ).id;
        placeDetails(placeId)
          .then((data) => {
            var lat = data[0].geometry.location.lat();
            var lng = data[0].geometry.location.lng();
            this.workshop.latitude = lat;
            this.workshop.longitude = lng;
            schedule.latitude = lat;
            schedule.longitude = lng;
            //schedule.autocompleteLocationModel = data[0].formatted_address;
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      }
    },

    dragMapChange() {
      this.tempWorkshopForLocation.locationEntries = [
        { value: this.workshop.location, id: "0" },
      ];
      this.tempWorkshopForLocation.autocompleteLocationModel =
        this.workshop.location;
      this.tempWorkshopForLocation.locationSearchText = this.workshop.location;
      this.locationDialoge = false;

      this.tempWorkshopForLocation.location = this.workshop.location;
    },

    addDocuments() {
      this.workshop.documents.push({
        name: "",
        document_type_id: null,
        file: null,
      });
      this.$forceUpdate();
    },

    deleteDocuments(index) {
      if (
        this.workshop.documents[index].name == "" &&
        this.workshop.documents[index].type == ""
      ) {
        this.workshop.documents.splice(index, 1);
        if (this.workshop.documents.length == 0) {
          this.workshop.documents = [{}];
        }
        this.$forceUpdate();
      } else {
        this.confirmModel = {
          id: index,
          title: "Do you wpnt to delete this document?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "document",
        };
      }
    },

    deleteWorkshopProgram(index) {
      let data = this.workshop.programs[index];

      if (data.id == null) {
        this.workshop.programs.splice(index, 1);
        this.$forceUpdate();
        return true;
      } else {
        this.confirmModel = {
          id: index,
          title: "Do you wpnt to delete this workshop slot?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "program",
        };
      }
    },

    deletedSchedules(programIndex, scheduleIndex) {
      let data = this.workshop.programs[programIndex].schedules[scheduleIndex];
      this.programIndex = programIndex;
      this.scheduleIndex = scheduleIndex;
      if (data.id == null) {
        this.workshop.programs[programIndex].schedules.splice(scheduleIndex, 1);
        this.$forceUpdate();
        return true;
      } else {
        this.confirmModel = {
          id: data.id,
          title: "Do you want to delete this time slot?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "schedule",
        };
      }
    },

    deletedProductPricing(programIndex, pricingIndex) {
      let data = this.workshop.programs[programIndex].pricing[pricingIndex];
      this.programIndex = programIndex;
      this.scheduleIndex = pricingIndex;
      if (data.id == null) {
        this.workshop.programs[programIndex].pricing.splice(pricingIndex, 1);
        this.$forceUpdate();
        return true;
      } else {
        this.confirmModel = {
          id: data.id,
          title: "Do you want to delete this product?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "product",
        };
      }
    },

    confirmActions(data) {
      if (data.type == "document") {
        if (this.workshop.documents[data.id].id != null)
          this.workshop.deleted_documents.push(
            this.workshop.documents[data.id].id
          );
        this.workshop.documents.splice(data.id, 1);
        if (this.workshop.documents.length == 0) {
          this.workshop.documents = [{}];
        }
      } else if (data.type == "program") {
        let adata = this.workshop.programs[data.id];
        if (adata.id != null) {
          this.workshop.deleted_programs.push(adata.id);
        }
        this.workshop.programs.splice(data.id, 1);
      } else if (data.type == "schedule") {
        if (data.id != null) {
          this.workshop.deleted_schedules.push(data.id);
        }
        this.workshop.programs[this.programIndex].schedules.splice(
          this.scheduleIndex,
          1
        );
      } else if (data.type == "product") {
        if (data.id != null) {
          this.workshop.deleted_pricing.push(data.id);
        }
        this.workshop.programs[this.programIndex].pricing.splice(
          this.scheduleIndex,
          1
        );
      }

      this.programIndex = null;
      this.scheduleIndex = null;

      this.$forceUpdate();
      this.confirmModel.id = null;
    },
    updateWorkshop(e) {
      e.preventDefault();
      this.workshop.status_id = 11;
      this.addOrEditWorkshop();
    },
    publishWorkshop(e) {
      e.preventDefault();
      this.workshop.status_id = 1;
      this.addOrEditWorkshop();
    },
    /* eslint-disable */
    addOrEditWorkshop() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (this.venueServices.length == 0) {
        this.showError(
          "No Facilities found. Please add facilities to add workshop."
        );
        return;
      }

      this.showLoader("Saving");
      let formData = new FormData();
      for (let key in this.workshop) {
        if (key == "programs") {
          this.workshop[key].forEach((program, index) => {
            for (let key_pro in program) {
              if (key_pro == "pricing") {
                program[key_pro].forEach((pricing, pr_ind) => {
                  for (let key_pro_price in pricing) {
                    if (
                      key_pro_price == "price" &&
                      pricing[key_pro_price] == null
                    ) {
                      pricing[key_pro_price] = 0;
                    }
                    if (pricing[key_pro_price] != null) {
                      formData.append(
                        `programs[${index}][${key_pro}][${pr_ind}][${key_pro_price}]`,
                        pricing[key_pro_price]
                      );
                    }
                  }
                });
              } else if (key_pro == "schedules") {
                program[key_pro].forEach((schedule, pr_sche) => {
                  for (let key_pro_sche in schedule) {
                    if (schedule[key_pro_sche] != null) {
                      if (key_pro_sche == "weekdays") {
                        formData.append(
                          `programs[${index}][timing][${pr_sche}][${key_pro_sche}]`,
                          JSON.stringify(schedule[key_pro_sche])
                        );
                      } else if (key_pro_sche == "trainer_ids") {
                        schedule[key_pro_sche].forEach(
                          (trainer_ids, train_ind) => {
                            formData.append(
                              `programs[${index}][timing][${pr_sche}][${key_pro_sche}][${train_ind}]`,
                              trainer_ids
                            );
                          }
                        );
                      } else {
                        formData.append(
                          `programs[${index}][timing][${pr_sche}][${key_pro_sche}]`,
                          schedule[key_pro_sche]
                        );
                      }
                    }
                  }
                });
              } else {
                if (program[key_pro] != null) {
                  formData.append(
                    `programs[${index}][${key_pro}]`,
                    program[key_pro]
                  );
                }
              }
            }
          });
        } else if (key == "documents") {
          this.workshop[key].forEach((document, doc_ind) => {
            if (document.name && document.document_type_id) {
              if (document.id) {
                formData.append(`documents[${doc_ind}][id]`, document.id);
              }
              if (document.file) {
                formData.append(`documents[${doc_ind}][file]`, document.file);
              }
              formData.append(`documents[${doc_ind}][name]`, document.name);
              formData.append(
                `documents[${doc_ind}][document_type_id]`,
                document.document_type_id
              );
            }
          });
        } else if (
          key == "deleted_programs" ||
          key == "deleted_schedules" ||
          key == "deleted_documents" ||
          key == "deleted_pricing"
        ) {
          formData.append(key, JSON.stringify(this.workshop[key]));
        } else {
          if (this.workshop[key]) formData.append(key, this.workshop[key]);
        }
      }

      // for (var pair of formData.entries()) {
      // }

      this.$http
        .post(`venues/workshops`, formData, {
          headers: {
            "Content-Type": "multipart/form-data; boundary=${form._boundary}",
          },
        })
        .then((response) => {
          if (response.status == 200 && response.data.status) {
            this.goToWorkshops();
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    updateCoordinates(e, programIndex, scheduleIndex) {
      this.tempWorkshopForLocation =
        this.workshop.programs[programIndex].schedules[scheduleIndex];
      this.locationHistory.lat = this.workshop.latitude;
      this.locationHistory.lng = this.workshop.longitude;

      let lat = e.latLng.lat();
      let lng = e.latLng.lng();

      this.tempWorkshopForLocation.latitude = lat;
      this.tempWorkshopForLocation.longitude = lng;

      let geocoder = new google.maps.Geocoder();
      var locationss = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
      };
      let that = this;
      geocoder.geocode({ location: locationss }, function (results, status) {
        if (status === "OK") {
          if (results.length > 0) {
            that.mapLocations = results;
            that.workshop.location = results[0].formatted_address;
            that.locationDialoge = true;
          }
        }
      });
      this.workshop.latitude = lat;
      this.workshop.longitude = lng;
    },

    closeLocation() {
      this.locationDialoge = false;
      this.workshop.latitude = this.locationHistory.lat;
      this.workshop.longitude = this.locationHistory.lng;
      this.workshop.location = null;
    },

    async getFacilities(venueServiceId) {
      if (this.facilities[venueServiceId] && venueServiceId > 0) return;
      this.facilities[venueServiceId] = [];
      await this.$http
        .get(`venues/facilities/short?venue_service_id=${venueServiceId}`)
        .then((response) => {
          if (
            response.status == 200 &&
            response.data.status == true &&
            response.data.data.length != 0
          ) {
            const data = response.data.data;
            let select = [];
            let type = data[0].type;
            select.push({ header: type.toUpperCase() });
            select.push({ divider: true });
            data.forEach((facility) => {
              if (facility.type != type) {
                select.push({ divider: true });
                select.push({ header: facility.type.toUpperCase() });
                select.push({ divider: true });
                type = facility.type;
              }
              select.push(facility);
            });
            this.facilities[venueServiceId] = select;
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    getServiceFacilities(main_ind, index) {
      let venue_service_id =
        this.workshop.programs[main_ind].schedules[index].venue_service_id;
      if (this.facilities[venue_service_id] != null) {
        return this.facilities[venue_service_id];
      }
      return [];
    },

    getLevels(venueServiceId) {
      this.$http
        .get(
          `venues/general/color-codes/workshop?venue_service_id=${venueServiceId}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.levels = response.data.data;
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    getTimesByDuration(programIndex, scheduleIndex) {
      let venueServiceId =
        this.workshop.programs[programIndex].schedules[scheduleIndex]
          .venue_service_id;
      let facilityId =
        this.workshop.programs[programIndex].schedules[scheduleIndex]
          .facility_id;

      let duration = 0;
      if (
        this.workshop.programs[programIndex] &&
        this.workshop.programs[programIndex].duration
      ) {
        duration = this.workshop.programs[programIndex].duration;
      }

      if (duration) {
        if (
          this.timeSlot[venueServiceId + "." + duration + "." + facilityId] &&
          venueServiceId > 0
        )
          return;
        this.timeSlot[venueServiceId + "." + duration + "." + facilityId] = [];
        let url = "";
        if (facilityId) {
          url = `&facility_id=${facilityId}`;
        }
        this.$http
          .get(
            `venues/workshops/time-by-duration?duration=${duration}&venue_service_id=${venueServiceId}` +
              url
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.timeSlot[
                venueServiceId + "." + duration + "." + facilityId
              ] = response.data.data;
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      } else {
        this.showError("Please select duration!");
      }
    },

    getServiceTimeSlot(main_ind, index) {
      let duration = this.workshop.programs[main_ind].duration;
      let venueServiceId =
        this.workshop.programs[main_ind].schedules[index].venue_service_id;
      let facilityId =
        this.workshop.programs[main_ind].schedules[index].facility_id;
      if (
        this.timeSlot[venueServiceId + "." + duration + "." + facilityId] !=
        null
      ) {
        return this.timeSlot[
          venueServiceId + "." + duration + "." + facilityId
        ];
      }
      return [];
    },

    EndTimeValidator(main_ind, index) {
      const start_time = moment(
        this.workshop.programs[main_ind].schedules[index].start_time,
        "HH:mm:ss"
      );
      this.workshop.programs[main_ind].schedules[index].end_time = moment(
        start_time
      )
        .add(this.workshop.programs[main_ind].duration, "minutes")
        .format("HH:mm:ss");

      const timeSlot = this.getServiceTimeSlot(main_ind, index);
      const solotIndex = timeSlot.findIndex(
        (x) =>
          x.time == this.workshop.programs[main_ind].schedules[index].end_time
      );

      if (solotIndex == -1) {
        this.showError("Facility not available this time duration");
        this.workshop.programs[main_ind].schedules[index].end_time = null;
      }

      this.checkBookingsExistsInFacility(main_ind, index);
    },

    getWorkshopTypes() {
      this.$http
        .get(`venues/workshops/types`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.workshopTypes = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    getTrainers() {
      this.$http
        .get(`venues/trainers/short`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.trainers = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    getWorkshopDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/workshops/${this.workshop.id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.editMode = true;
            let data = response.data.data;

            this.workshop = {
              id: data.id,
              name: data.name,
              workshop_type_id: data.workshop_type_id,
              type: data.type,
              venue_service_id: data.venue_service_id,
              start_date: data.start_date,
              end_date: data.end_date,
              description: data.description,
              image_path: data.image_path,
              programs: data.programs,
            };

            this.workshop.programs.forEach((program, programIndex) => {
              program.schedules.forEach((schedules, scheduleIndex) => {
                setTimeout(() => {
                  this.getFacilities(schedules.venue_service_id);
                });
                setTimeout(() => {
                  this.getTimesByDuration(programIndex, scheduleIndex);
                });
                if (schedules.facility_id == null) {
                  schedules.is_external = true;
                  schedules.location = schedules.location;
                  this.locationEntries = [
                    { value: schedules.location, id: "0" },
                  ];
                  schedules.locationEntries = [
                    { value: schedules.location, id: "0" },
                  ];
                  this.$forceUpdate();
                }

                schedules.trainer_ids = schedules.trainer.map(
                  (item) => item.trainer_id
                );
              });

              program.pricing.forEach((pricing) => {
                if (pricing.price) {
                  pricing.pre_tax_price = pricing.price;
                  pricing.total_price = pricing.total_price;
                }
              });
            });

            if (data.documents.length == 0) {
              this.workshop.documents = [{}];
            } else {
              this.workshop.documents = data.documents.map((item) => {
                return {
                  id: item.id,
                  document_type_id: item.document_type_id,
                  file_path: item.file_path,
                  original_file_name: item.original_file_name,
                  name: item.name,
                };
              });
            }

            this.workshop.deleted_documents = [];
            this.workshop.deleted_schedules = [];
            this.workshop.deleted_programs = [];
            this.workshop.deleted_pricing = [];

            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    goToWorkshops() {
      this.$router.push({ name: "Workshops" }, () => {});
    },

    facilityChange(programIndex, scheduleIndex, type) {
      if (type == "external") {
        this.workshop.programs[programIndex].schedules[
          scheduleIndex
        ].is_external = true;
        this.workshop.programs[programIndex].schedules[
          scheduleIndex
        ].facility_id = null;

        this.workshop.programs[programIndex].schedules[
          scheduleIndex
        ].latitude = 24.45342255;
        this.workshop.programs[programIndex].schedules[
          scheduleIndex
        ].longitude = 54.35851069;
      } else {
        this.workshop.programs[programIndex].schedules[
          scheduleIndex
        ].is_external = false;
      }
      this.$forceUpdate();
      this.getTimesByDuration(programIndex, scheduleIndex);
    },

    addAvailability() {
      this.workshop.programs.push({
        start_time: null,
        end_time: null,
        weekdays: [],
      });
      this.$forceUpdate();
    },

    addFacility() {
      this.workshop.schedules.push({
        venue_service_id: null,
        facility_id: null,
        weekdays: [],
      });
      this.$forceUpdate();
    },

    duplicateFacility(index) {
      let data = this.workshop.schedules[index];
      if (data.id != null) delete data.id;
      this.workshop.schedules.splice(
        index,
        0,
        JSON.parse(JSON.stringify(data))
      );
    },

    getIcon(field, pIndex, sIndex) {
      let icon = "mdi-checkbox-blank-outline";
      if (
        this.workshop.programs[pIndex].schedules[sIndex].weekdays.length ==
        this.weekdays.length
      ) {
        icon = "mdi-close-box";
      }
      if (
        this.workshop.programs[pIndex].schedules[sIndex].weekdays.length > 0 &&
        this.workshop.programs[pIndex].schedules[sIndex].weekdays.length !=
          this.weekdays.length
      )
        icon = "mdi-minus-box";
      return icon;
    },

    toggle(field, pIndex, sIndex) {
      this.$nextTick(() => {
        if (
          this.workshop.programs[pIndex][field][sIndex].weekdays.length ==
          this.weekdays.length
        ) {
          this.workshop.programs[pIndex][field][sIndex].weekdays = [];
        } else {
          this.workshop.programs[pIndex][field][sIndex].weekdays =
            this.weekdays.map((item) => {
              return item.bit_value;
            });
        }
      });
      this.$forceUpdate();
    },

    calculateTaxVariation(amount, product, type) {
      let taxTypeId = product.tax_type_id;
      let taxPercentage = 0;
      if (taxTypeId) {
        taxPercentage = this.taxTypes.find(
          (item) => item.value == taxTypeId
        ).percentage;
      }
      const priceData = this.getTaxVariation(type, taxPercentage, amount);
      if (priceData) {
        product.price = priceData.price.toFixed(4);
        product.pre_tax_price = priceData.price.toFixed(4);
        product.total_price = priceData.total_price.toFixed(4);
      }
      this.$forceUpdate();
    },

    taxChange(product) {
      if (product.price) {
        this.calculateTaxVariation(product.price, product, "pre");
      } else if (product.total_price) {
        this.calculateTaxVariation(product.total_price, product, "post");
      }
    },

    facilityRule(type) {
      const rules = [];
      let rule = null;
      if (!type) {
        rule = (v) => !!v || "Facility is required";
        rules.push(rule);
      }
      return rules;
    },

    workshopDateChange(type) {
      if (type == "start") {
        this.workshop.programs.forEach((programs) => {
          programs.schedules.forEach((schedule) => {
            schedule.start_date = this.workshop.start_date;
          });
        });
      }
      if (type == "end") {
        this.workshop.programs.forEach((programs) => {
          programs.schedules.forEach((schedule) => {
            schedule.end_date = this.workshop.end_date;
          });
        });
      }
    },

    checkBookingsExistsInFacility(main_ind, index) {
      var schedule = this.workshop.programs[main_ind].schedules[index];

      if (
        schedule.end_time &&
        schedule.start_date &&
        schedule.start_time &&
        schedule.facility_id
      ) {
        var schedule = {
          start_date: schedule.start_date,
          end_date: schedule.end_date,
          start_time: schedule.start_time,
          end_time: schedule.end_time,
          facility_id: schedule.facility_id,
          weekdays: schedule.weekdays,
        };

        this.$http
          .post(`venues/workshops/check-booking-exists`, schedule)
          .then((response) => {
            if (response.status == 200 && response.data.status) {
              if (response.data.data) {
                this.workshop.programs[main_ind].schedules[
                  index
                ].checkBookingsExist = response.data.data;
              } else {
                this.workshop.programs[main_ind].schedules[
                  index
                ].checkBookingsExist = null;
              }
              this.$forceUpdate();
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      }
    },

    addProgram() {
      this.openProgramTabs.push(this.workshop.programs.length);
      this.workshop.programs.push({
        name: null,
        duration: null,
        capacity: null,

        schedules: [
          {
            venue_service_id: null,
            facility_id: null,
            weekdays: [],
            start_time: null,
            end_time: null,
            latitude: 24.45342255,
            longitude: 54.35851069,
            heatmap: null,
            is_external: false,
          },
        ],
        pricing: [
          {
            name: null,
            duration: null,
            duration_count: null,
            occurrence: null,
            frequency: null,
            price: null,
            tax_type: null,
          },
        ],
      });
    },

    addPricing(index) {
      this.openPriceTabs.push(this.workshop.programs[index].pricing.length);
      this.workshop.programs[index].pricing.push({
        name: null,
        duration_type: null,
        duration_count: null,
        occurrence: null,
        frequency: null,
        price: null,
        tax_type: null,
      });
    },

    addTiming(index) {
      this.openTimeTabs.push(this.workshop.programs[index].schedules.length);
      this.workshop.programs[index].schedules.push({
        venue_service_id: null,
        facility_id: null,
        weekdays: [],
        start_time: null,
        end_time: null,
        start_date: this.workshop.start_date ? this.workshop.start_date : null,
        end_date: this.workshop.end_date ? this.workshop.end_date : null,
        latitude: 24.**************,
        longitude: 54.37532545660189,
        heatmap: null,
        location: null,
        autocompleteLocationModel: null,
        locationEntries: [],
        isLoading: false,
      });
    },
  },
};
</script>

<style scoped>
.add_ground_containers {
  width: -webkit-fill-available;
}
.imageInput {
  padding-top: 0;
}
.add_btn {
  margin-top: -20px;
}
.isBookingExists {
  color: #a52a2a;
  font-size: 0.875rem !important;
  font-weight: 500;
  letter-spacing: 0.0071428571em !important;
  line-height: 1.375rem;
  font-family: "Inter", sans-serif !important;
  text-align: center;
}

.isBookingExistsBorder {
  border: 2px solid #a52a2a;
  border-radius: 4px;
}
</style>

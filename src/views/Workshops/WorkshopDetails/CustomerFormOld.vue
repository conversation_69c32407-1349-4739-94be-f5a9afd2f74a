<template>
  <v-dialog v-model="customerFormDialoge" scrollable persistent width="50%">
    <v-form ref="form">
      <v-card>
        <v-card-title class="headline">
          <div>Customer</div>
          <v-spacer></v-spacer>
          <div
            md="3"
            v-if="
              (promotions.length > 0 && prorateDetails.price) ||
              (promotions.length > 0 && editFlag)
            "
            style="margin-bottom: -20px"
          >
            <v-autocomplete
              :items="[{ name: 'None', promotion_code: null }, ...promotions]"
              item-text="name"
              height="50"
              item-value="promotion_code"
              v-model="registerForm.promotion_code"
              background-color="rgb(206, 168, 0)"
              outlined
              :readonly="discountApplied && editFlag"
              @change="verifyBenefit('promotion')"
              label="Promotions"
            >
            </v-autocomplete></div
        ></v-card-title>
        <v-card-text class="form_bg pt-2">
          <div class="titles pt-2">Program details</div>
          <v-row>
            <v-col cols="6">
              <v-select
                label="Program*"
                :items="workshopPrograms"
                item-text="name"
                item-value="id"
                v-model="programSelected"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                :readonly="editFlag"
                background-color="#fff"
                @change="programChange(), rorate()"
                :rules="[(v) => !!v || 'Program is required']"
                required
              >
              </v-select>
            </v-col>

            <v-col sm="6">
              <v-select
                label="Product*"
                v-model="workshopProduct"
                :rules="[(v) => !!v.product_id || 'Product is required']"
                :items="productAndSchedules.pricing"
                item-text="name"
                item-value="product_id"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                required
                :readonly="editFlag"
                return-object
                background-color="#fff"
                @change="
                  (selectedDays = [{ day: { time: [] } }]), checkProrate()
                "
              ></v-select>
            </v-col>
            <v-col cols="6">
              <v-text-field
                label="Price"
                readonly
                v-model="workshopProduct.price"
                outlined
                :disabled="!workshopProduct.price"
                background-color="#fff"
                append="Discount"
                :prefix="currencyCode"
              >
                <template v-slot:append v-if="workshopProduct.discount">
                  <div class="text-decoration-line-through">
                    {{ currencyCode }}
                    {{ workshopProduct.discount.actual_price }}
                  </div>
                </template>
              </v-text-field>
            </v-col>
            <v-col sm="6">
              <v-select
                label="Level"
                v-model="level_id"
                :items="levels"
                item-text="name"
                item-value="id"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
              ></v-select>
            </v-col>
            <v-col md="6" class="pb-0 pt-0">
              <date-field
                :backFill="
                  checkBackfillPermission($modules.workshops.schedule.slug)
                "
                v-model="startDate"
                label="Select class start date*"
                :rules="[(v) => !!v || 'Start date is required']"
                @change="checkProrate"
              >
              </date-field>
            </v-col>
            <v-col sm="12" class="pb-0 pt-0">
              <div class="titles">Timing details</div>
              <v-card
                outlined
                :style="cardStyle"
                class="mt-2"
                v-for="(days, index) in selectedDays"
                :key="index"
              >
                <v-card-text>
                  <v-row sm="12">
                    <v-col sm="6">
                      <v-select
                        label="Day*"
                        v-model="days.day"
                        :items="weekdays"
                        item-text="name"
                        item-value="id"
                        outlined
                        :menu-props="{ bottom: true, offsetY: true }"
                        return-object
                        @change="daysChange(index)"
                        background-color="#fff"
                      ></v-select>
                    </v-col>

                    <v-col>
                      <v-select
                        label="Select Time slot"
                        v-model="days.workshop_schedule_id"
                        :items="days.day.time"
                        item-text="name"
                        item-value="workshop_schedule_id"
                        outlined
                        :menu-props="{ bottom: true, offsetY: true }"
                        background-color="#fff"
                        @change="checkProrate"
                        :rules="[(v) => !!v || 'Time Slot is required']"
                      >
                        <template slot="item" slot-scope="{ item, on, attrs }">
                          <v-list-item
                            ripple
                            v-bind="attrs"
                            v-on="on"
                            :disabled="
                              checkStartDatePassFromEndDateOfClass(item)
                            "
                          >
                            <v-list-item-content>
                              <v-list-item-title>
                                {{ item.name }}</v-list-item-title
                              >
                              <v-list-item-subtitle>
                                <div
                                  class="text-trancate"
                                  style="max-width: 100px"
                                >
                                  {{ item.facility_name || "NA" }}
                                </div>
                                <div
                                  class="text-trancate mt-1"
                                  style="max-width: 100px"
                                >
                                  {{ item.start_date | dateformat }} -to-
                                  {{ item.end_date | dateformat }}
                                </div>
                              </v-list-item-subtitle>
                            </v-list-item-content>
                            <v-list-item-action>
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <v-btn
                                    v-bind="attrs"
                                    v-on="on"
                                    fab
                                    x-small
                                    icon
                                  >
                                    <v-icon>mdi-information</v-icon>
                                  </v-btn>
                                </template>
                                Trainers :-
                                {{ item.trainer || "NA" }}
                              </v-tooltip>
                            </v-list-item-action>
                          </v-list-item>
                        </template>
                      </v-select>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>

              <div class="add_btn" v-if="checkAddButtonStatus()">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      v-on="on"
                      color="blue-color"
                      fab
                      x-small
                      dark
                      @click="addDay()"
                    >
                      <v-icon>mdi-plus-circle</v-icon>
                    </v-btn>
                  </template>
                  Add Day
                </v-tooltip>
              </div>
            </v-col>
          </v-row>

          <v-row
            class="ma-1 mt-2"
            v-if="prorateDetails.dates && prorateDetails.dates.length"
          >
            <v-alert type="info" outlined text>
              Customer will be able to attend
              {{ prorateDetails.available_slots }}/{{
                prorateDetails.total_slots
              }}
              classes which will start from
              {{ prorateDetails.dates[0].selected_date | dateformat }} and end
              by
              {{
                prorateDetails.dates[prorateDetails.dates.length - 1]
                  .selected_date | dateformat
              }}. <br />
              <b>Fee: {{ prorateDetails.price | toCurrency }}</b>
              <span
                class="text-decoration-line-through"
                v-if="prorateDetails.product.price > prorateDetails.price"
              >
                {{ prorateDetails.product.price | toCurrency }}
              </span>
              <div class="mt-2">
                <b>Occurrences: </b>
                <span
                  v-for="(date, index) in prorateDetails.dates"
                  :key="index"
                >
                  <span v-if="index == 0">{{
                    date.selected_date | dateformat
                  }}</span>
                  <span v-else-if="index == prorateDetails.dates.length - 1"
                    >and {{ date.selected_date | dateformat }}</span
                  >
                  <span v-else>, {{ date.selected_date | dateformat }}</span>
                </span>
              </div>
            </v-alert>
          </v-row>
          <v-row>
            <v-col md="4">
              <div class="titles pt-5">Customer details</div>
            </v-col>
            <v-col :md="orderId ? 6 : 3">
              <v-switch
                style="float: right"
                v-model="registerForm.opt_marketing"
                label="Opt In Marketing"
              ></v-switch>
            </v-col>
            <!-- Uncomment to enable emirates ID reader -->
            <!-- <v-col md="3" class="text-right" v-if="!orderId">
              <card-data-button
                class="mt-4"
                label="Emirates ID"
                @data="
                  (data) => {
                    setCardData(data);
                  }
                "
              ></card-data-button>
            </v-col> -->
            <v-col
              md="5"
              class="text-right d-flex flex-row-reverse"
              v-if="!orderId"
            >
              <card-data-button
                class="mt-5"
                label="HID Omnikey"
                @data="
                  (data) => {
                    setCardData(data);
                  }
                "
              ></card-data-button>
              <card-reader-button
                class="mt-5"
                label="Samsotech Reader"
                @data="
                  (data) => {
                    setCardData(data);
                  }
                "
              ></card-reader-button>
            </v-col>
          </v-row>
          <v-card outlined class="mb-1" :style="cardStyle">
            <v-container>
              <v-row no-gutters justify="center">
                <v-col md="6">
                  <image-upload
                    @upload="
                      (data) => {
                        registerForm.image = data;
                      }
                    "
                    :height="150"
                    :image_path="registerForm.image_path"
                    defaultImage="user"
                    @remove="
                      () => {
                        registerForm.image = null;
                      }
                    "
                    ref="image_upload"
                  ></image-upload>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" sm="6" md="6">
                  <v-mobile-search
                    :selected="registerForm.mobile"
                    v-model="registerForm.search"
                    @updateCustomer="setCustomerData($event)"
                  ></v-mobile-search>
                </v-col>

                <v-col cols="12" sm="6" md="6">
                  <v-name-search
                    :selected="registerForm.name"
                    :mobile="registerForm.mobile"
                    :email="registerForm.email"
                    v-model="registerForm.nameSearch"
                    @updateCustomer="setCustomerData($event)"
                  ></v-name-search>
                </v-col>

                <v-col cols="12" sm="6" md="6">
                  <v-text-field
                    v-model="registerForm.email"
                    label="Email*"
                    outlined
                    background-color="#fff"
                    required
                    :rules="[
                      (v) => !!v || 'E-mail is required',
                      (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                    ]"
                  ></v-text-field>
                </v-col>
                <v-col md="6">
                  <date-of-birth v-model="registerForm.dob"> </date-of-birth>
                </v-col>
                <v-col cols="12" sm="6" md="6">
                  <v-autocomplete
                    v-model="registerForm.country_id"
                    :items="countries"
                    label="Nationality"
                    item-value="id"
                    item-text="name"
                    outlined
                    background-color="#fff"
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12" sm="6" md="6">
                  <v-select
                    v-model="registerForm.gender"
                    :items="['Male', 'Female']"
                    label="Gender"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                  ></v-select>
                </v-col>

                <v-col cols="12" sm="6" md="6">
                  <v-file-input
                    :label="registerForm.document.id != null ? '' : 'Document'"
                    v-model="registerForm.document.file"
                    prepend-inner-icon="mdi-paperclip"
                    prepend-icon
                    outlined
                    background-color="#fff"
                  >
                    <template v-slot:label>
                      <span v-if="!registerForm.document.id">
                        Select file
                      </span>
                      <span
                        v-if="
                          registerForm.document.id &&
                          !registerForm.document.file
                        "
                        class="font-weight-bold"
                      >
                        <span
                          style="width: 70%; display: inline-block"
                          class="text-truncate"
                          >{{ registerForm.document.original_file_name }}</span
                        >
                        <span
                          style="width: 20%; display: inline-block"
                          class="text-truncate"
                          >.{{
                            registerForm.document.original_file_name.split(".")[
                              registerForm.document.original_file_name.split(
                                "."
                              ).length - 1
                            ]
                          }}</span
                        >
                      </span>
                    </template>
                    <template v-slot:prepend-inner>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on }">
                          <v-icon
                            color="cyan"
                            v-if="
                              registerForm.document.id &&
                              !registerForm.document.file
                            "
                            @click="openFile(registerForm.document.file_path)"
                            v-on="on"
                          >
                            mdi-download-box
                          </v-icon>
                          <v-icon v-else v-on="on">mdi-image</v-icon>
                        </template>
                        <span v-if="registerForm.document.file_path">
                          Download image</span
                        >
                        <span v-else>Upload Image</span>
                      </v-tooltip>
                    </template>
                  </v-file-input>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
          <div v-if="workshop.documents.length > 0" class="titles pt-2">
            Documents
          </div>
          <v-card
            outlined
            class="mb-4"
            :style="cardStyle"
            v-if="workshop.documents.length > 0"
          >
            <v-card-text>
              <v-row>
                <v-col
                  md="2"
                  sm="2"
                  v-for="document in workshop.documents"
                  :key="document.id"
                >
                  <div
                    align="center"
                    @click="openFile(document.file_path)"
                    style="cursor: pointer"
                  >
                    <v-btn large text>
                      <v-icon large>mdi-file-document</v-icon>
                    </v-btn>
                    <div class="title">{{ document.name }}</div>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2 white--text blue-color" @click="close"
            >Close</v-btn
          >
          <v-btn @click="save" class="ma-2 white--text teal-color">{{
            editFlag ? "Update" : "Save"
          }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
import moment from "moment";

export default {
  props: {
    customerFormDialoge: {
      type: Boolean,
      default: false,
    },
    "order-id": { type: Number, default: null },
    workshop: Object,
  },
  data() {
    return {
      workshopPrograms: [],
      productAndSchedules: {
        products: [],
      },
      workshopBookingId: null,
      registerForm: { document: { id: null }, opt_marketing: false },
      editFlag: false,
      programSelected: {},
      workshopProduct: {
        price: null,
      },
      selectedDays: [{ day: { time: [] } }],
      levels: [],
      schedules: [],
      weekdays: [],
      level_id: null,
      prorateDetails: {},
      discountApplied: false,
      startDate: moment().format("YYYY-MM-DD"),
    };
  },
  watch: {
    customerFormDialoge: {
      immediate: true,
      handler(val) {
        if (val) {
          this.reset();
          if (this.orderId) {
            this.getOrderDetails();
          }
          this.prorateDetails = {};
          let date = moment(this.workshop.start_date, "YYYY-MM-DD").isBefore(
            moment()
          )
            ? moment().format("YYYY-MM-DD")
            : this.workshop.start_date;
          this.$store.dispatch("loadPromotions", {
            date: date,
            venue_service_id: this.workshop.venue_service_id,
            product_type: "Academy",
          });
          this.workshopProduct = { price: null };
          this.getLevels();
        }
      },
    },
    workshop(val) {
      if (val) {
        this.workshopPrograms = val.programs;
      }
    },
  },
  mounted() {
    if (this.$store.getters.getDocumentTypes.status == false) {
      this.$store.dispatch("loadDocumentTypes");
    }
  },
  computed: {
    documentTypes() {
      return this.$store.getters.getDocumentTypes.data;
    },
    countries() {
      return this.$store.getters.getCountries.data;
    },
    promotions() {
      return this.$store.getters.getPromotions.data;
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },

    reset() {
      this.editFlag = false;
      this.registerForm = { document: { id: null }, opt_marketing: false };
      this.workshopProduct = {
        price: null,
      };
      this.level_id = null;
      this.productAndSchedules = {
        products: [],
      };
      this.weekdays = [];
      this.selectedDays = [{ day: { time: [] } }];
      this.programSelected = {};
      setTimeout(() => {
        this.$refs["image_upload"].cancel();
        this.$refs.form.resetValidation();
      });
    },
    checkAddButtonStatus() {
      if (this.workshopProduct.occurrence == "D") {
        return this.selectedDays.length < 7 * this.workshopProduct.frequency;
      }
      return this.selectedDays.length < this.workshopProduct.frequency;
    },
    addDay() {
      if (this.checkAddButtonStatus()) {
        this.selectedDays.push({ day: { time: [] } });
      }
    },
    async getOrderDetails() {
      this.showLoader("Loading");
      await this.$http
        .get(`venues/workshops/schedules/booking/${this.orderId}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.hideLoader();
            this.editFlag = true;
            const data = response.data.data;
            this.workshopBookingId = data.workshop_order.id;
            this.programSelected = data.workshop_order.workshop_program_id;
            this.programChange();
            this.selectedDays = [];
            this.workshopProduct = this.productAndSchedules.pricing.find(
              (x) => x.product_id == data.product_id
            );
            this.level_id = data.workshop_order.level_id;

            data.workshop_order.booking.forEach((booking) => {
              let findValue = this.$store.getters.getWeekdays.data.find(
                (y) => y.bit_value == booking.weekdays[0]
              );
              this.selectedDays.push({
                day: findValue,
                workshop_schedule_id: booking.workshop_schedule_id,
                workshop_booking_id: booking.workshop_booking_id,
                id: booking.id,
              });
            });

            if (data.workshop_order.document_id) {
              this.$set(
                this.registerForm,
                "document",
                data.workshop_order.document
              );
            }
            let customer = data.workshop_order.customer;
            customer.customer_id = customer.id;
            customer.image_path = customer.profile_image;

            this.discountApplied = false;
            if (data.discount != null) {
              this.workshopProduct.price = data.price;
              this.workshopProduct.discount = data.discount;
              this.discountApplied = true;
              if (data.discount.promotion != null) {
                this.registerForm.promotion_code =
                  data.discount.promotion.promotion_code;
              }
            }
            this.setCustomerData(customer);
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    programChange() {
      this.productAndSchedules = [];
      let program = this.workshop.programs.find(
        (x) => x.id == this.programSelected
      );
      this.productAndSchedules.schedules = program.schedules;
      this.productAndSchedules.pricing = program.pricing;
      this.weekdays = [];
      this.productAndSchedules.schedules.forEach((schedule) => {
        schedule.weekdays.forEach((element) => {
          let findValue = this.$store.getters.getWeekdays.data.find(
            (y) => y.bit_value == element
          );
          var obj = {
            trainer: schedule.trainer
              .map((trainer) => trainer.first_name + trainer.last_name)
              .join(","),
            facility_name: schedule.facility_name
              ? schedule.facility_name
              : schedule.location,
            workshop_schedule_id: schedule.id,
            start_time: schedule.start_time,
            end_time: schedule.end_time,
            start_date: schedule.start_date,
            end_date: schedule.end_date,
            name:
              moment(schedule.start_time, "HH:mm").format("h:mm a") +
              " To " +
              moment(schedule.end_time, "HH:mm").format("h:mm a"),
          };
          let weekDaysIndex = this.weekdays.findIndex(
            (x) => x.bit_value == element
          );
          if (weekDaysIndex != -1) {
            this.weekdays[weekDaysIndex].time.push(obj);
          } else {
            findValue.time = [];
            findValue.time.push(obj);
            this.weekdays.push(findValue);
          }
        });
      });
    },
    getLevels() {
      this.$http
        .get(
          `venues/general/color-codes/academy?venue_service_id=${this.workshop.venue_service_id}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.levels = response.data.data;
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    setCustomerData(data) {
      if (!data.customer_id) {
        this.$set(this.registerForm, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.registerForm, "name", data.first_name);
      }

      if (
        this.registerForm.customer_id &&
        !data.customer_id &&
        this.registerForm.name != data.name &&
        this.registerForm.mobile != data.mobile
      ) {
        this.$set(this.registerForm, "mobile", null);
        this.registerForm.search = null;
        this.registerForm.nameSearch = null;
        this.$set(this.registerForm, "email", null);
        this.$set(this.registerForm, "gender", null);
        this.$set(this.registerForm, "name", null);
        this.$set(this.registerForm, "customer_id", null);
        this.$set(this.registerForm, "first_name", null);
        this.$set(this.registerForm, "image_path", null);
        this.$set(this.registerForm, "dob", null);
        this.$set(this.registerForm, "country_id", null);
        this.$set(this.registerForm, "last_name", null);
        this.$set(this.registerForm, "opt_marketing", false);
        this.$forceUpdate();
      }

      if (data.mobile) this.$set(this.registerForm, "mobile", data.mobile);
      if (data.email) this.$set(this.registerForm, "email", data.email);
      if (data.country_id) {
        this.$set(this.registerForm, "country_id", data.country_id);
      } else {
        this.$set(this.registerForm, "country_id", null);
      }
      if (data.gender) {
        this.$set(this.registerForm, "gender", data.gender);
      } else {
        this.$set(this.registerForm, "gender", null);
      }
      if (data.dob) {
        this.$set(this.registerForm, "dob", data.dob);
      } else {
        this.$set(this.registerForm, "dob", null);
      }
      if (data.name) this.$set(this.registerForm, "name", data.name);
      if (data.last_name) {
        this.$set(this.registerForm, "last_name", data.last_name);
      } else {
        this.$set(this.registerForm, "last_name", null);
      }
      if (data.first_name)
        this.$set(this.registerForm, "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.registerForm, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.registerForm, "image_path", data.image_path);
      } else {
        this.$set(this.registerForm, "image_path", null);
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.registerForm, "opt_marketing", true);
        } else {
          this.$set(this.registerForm, "opt_marketing", false);
        }
      }
      this.$forceUpdate();
    },
    save() {
      if (this.prorateDetails.available_slots == 0) {
        this.showError(
          "No class available for this day. Please choose another day! "
        );
        return;
      }
      let last = this.selectedDays[this.selectedDays.length - 1];
      if (
        this.checkAddButtonStatus() ||
        !last.day ||
        !last.day.bit_value ||
        !last.workshop_schedule_id
      ) {
        this.showError("Please select all schedules");
        return;
      }
      // let program = this.workshop.programs.find(
      //   (x) => x.id == this.programSelected
      // );
      // if (
      //   (!this.editFlag && program.members == program.capacity) ||
      //   program.members > program.capacity
      // ) {
      //   this.showError("Booking Full");
      //   return;
      // }
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      let formData = new FormData();
      formData.append("workshop_id", this.workshop.id);
      formData.append("workshop_type_id", this.workshop.workshop_type_id);
      formData.append("workshop_program_id", this.programSelected);
      formData.append("product_id", this.workshopProduct.product_id);
      if (this.level_id) {
        formData.append("level_id", this.level_id);
      }
      formData.append("venue_service_id", this.workshop.venue_service_id);
      if (this.editFlag) {
        formData.append("order_id", this.orderId);
        formData.append("workshop_booking_id", this.workshopBookingId);
      }
      this.selectedDays.forEach((el, index) => {
        formData.append(`days[${index}][bit_value]`, el.day.bit_value);
        formData.append(
          `days[${index}][workshop_schedule_id]`,
          el.workshop_schedule_id
        );

        formData.append(
          `days[${index}][start_date]`,
          el.day.time.find(
            (x) => x.workshop_schedule_id == el.workshop_schedule_id
          ).start_date
        );

        formData.append(
          `days[${index}][end_date]`,
          el.day.time.find(
            (x) => x.workshop_schedule_id == el.workshop_schedule_id
          ).end_date
        );

        if (this.editFlag && el.id) {
          formData.append(`days[${index}][id]`, el.id);
        }
      });
      for (let key in this.registerForm) {
        if (
          this.registerForm[key] &&
          typeof this.registerForm[key] != "object"
        ) {
          formData.append(key, this.registerForm[key]);
        } else if (key == "document" && this.registerForm[key].file) {
          formData.append(key, this.registerForm[key].file);
          if (this.registerForm[key].id) {
            formData.append("document_id", this.registerForm[key].id);
          }
        }
      }
      formData.append("opt_marketing", this.registerForm.opt_marketing);
      formData.append("start_date", this.startDate);
      this.showLoader("Saving customer..");
      this.$http
        .post(`venues/workshops/schedules/booking`, formData, {
          headers: {
            "Content-Type": "multipart/form-data; boundary=${form._boundary}",
          },
        })
        .then((response) => {
          if (response.status == 200 && response.data.status) {
            this.hideLoader();
            this.$emit("complete", response.data.data.order_id);
            this.close();
            if (this.editFlag) this.showSuccess("Update successfully");
            else this.showSuccess("Created schedule successfully");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    checkProrate() {
      this.prorateDetails = {};
      let last = this.selectedDays[this.selectedDays.length - 1];
      if (
        this.checkAddButtonStatus() ||
        !last.day ||
        !last.day.bit_value ||
        !last.workshop_schedule_id
      )
        return;
      let formData = {};
      formData.workshop_program_id = this.programSelected;
      formData.product_id = this.workshopProduct.product_id;
      formData.start_date = this.startDate;
      formData.days = [];
      // console.log(this.selectedDays);
      this.selectedDays.forEach((el) => {
        if (el.day.bit_value && el.workshop_schedule_id) {
          let day = {};
          day.bit_value = el.day.bit_value;
          day.workshop_schedule_id = el.workshop_schedule_id;
          day.start_date = el.day.time.find(
            (x) => x.workshop_schedule_id == el.workshop_schedule_id
          ).start_date;
          day.end_date = el.day.time.find(
            (x) => x.workshop_schedule_id == el.workshop_schedule_id
          ).end_date;
          formData.days.push(day);
        }
      });
      if (
        !formData.workshop_program_id ||
        !formData.product_id ||
        formData.days.length == 0
      )
        return;
      this.$http
        .post(`venues/workshops/schedules/booking/check`, formData)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.prorateDetails = data;
            this.workshopProduct.price = data.price;
            this.$forceUpdate();
            if (this.registerForm.promotion_code) {
              this.verifyBenefit("promotion");
            }
          }
        })
        .catch((error) => {
          this.prorateDetails = {};
          this.errorChecker(error);
        });
    },
    verifyBenefit(type) {
      if (!this.workshopProduct.product_id) {
        this.showError("Please select the product");
        return;
      }
      let data = {
        products: [],
      };
      if (this.workshopProduct.discount) {
        this.workshopProduct.price = this.workshopProduct.discount.actual_price;
        delete this.workshopProduct.discount;
      }
      this.workshopProduct.workshop_id = this.workshop.id;
      this.workshopProduct.quantity = 1;
      if (type == "promotion") {
        data.promotion_code = this.registerForm.promotion_code;
        if (data.promotion_code == null) {
          this.discountApplied = false;
          return;
        }
      } else {
        data.card_number = this.registerForm.card_number;
      }
      if (this.registerForm.customer_id) {
        data.customer_id = this.registerForm.customer_id;
      }

      data.products = [this.workshopProduct];

      let url = "venues/benefits/verify";
      this.$http
        .post(url, data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            if (data.discount) {
              this.workshopProduct = data.products[0];
              this.workshopProduct.discount = data.discount;
              this.workshopProduct.price = data.price;
              this.$forceUpdate();
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    daysChange(index) {
      if (this.selectedDays[index].day.time.length == 1) {
        this.selectedDays[index].workshop_schedule_id =
          this.selectedDays[index].day.time[0].workshop_schedule_id;
        this.checkProrate();
      } else {
        // if (this.selectedDays[index].day.time.length > 1) {
        //   this.selectedDays[index].workshop_schedule_id =
        //     this.selectedDays[index].day.time[0].workshop_schedule_id;
        // }
        // this.checkProrate();
      }
    },

    checkStartDatePassFromEndDateOfClass(item) {
      // let result = !moment(this.startDate, "YYYY-MM-DD").isBefore(
      //   moment(item.end_date, "YYYY-MM-DD")
      // );
      if (
        moment(this.startDate, "YYYY-MM-DD").isBefore(
          moment(item.end_date, "YYYY-MM-DD")
        )
      ) {
        // console.log("start date is before slot end date");
        return false; // return false means do not disable this slot
      } else if (
        moment(this.startDate, "YYYY-MM-DD").isSame(
          moment(item.end_date, "YYYY-MM-DD")
        )
      ) {
        // console.log("start date is equal for slot end date");
        let now = moment();
        let currentDate = now.format("YYYY-MM-DD");
        if (
          moment(currentDate, "YYYY-MM-DD").isSame(
            moment(item.end_date, "YYYY-MM-DD")
          )
        ) {
          // console.log("end date is equal for current date");
          let startDateTime = moment(
            this.startDate + " " + now.format("HH:mm"),
            "YYYY-MM-DD HH:mm"
          ).format("YYYY-MM-DD HH:mm");
          let endDateTime = moment(
            item.end_date + " " + item.end_time,
            "YYYY-MM-DD HH:mm"
          ).format("YYYY-MM-DD HH:mm");
          let result = !moment(startDateTime, "YYYY-MM-DD HH:mm").isBefore(
            moment(endDateTime, "YYYY-MM-DD HH:mm")
          );
          return result;
        } else {
          // console.log("current date is before for slot end date");
          return false;
        }
      } else {
        // console.log("start date is grater than end date slot");
        return true;
      }
      // let result = !moment(startDateTime, "YYYY-MM-DD HH:mm").isBefore(
      //   moment(endDateTime, "YYYY-MM-DD HH:mm")
      // );
      // return result;
    },
    setCardData(data) {
      if (!data.customer_id) {
        this.$set(this.registerForm, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.registerForm, "name", data.first_name);
      }

      if (!data.customer_id && this.registerForm.name != data.name) {
        this.$set(this.registerForm, "mobile", null);
        this.registerForm.search = null;
        this.registerForm.nameSearch = null;
        this.$set(this.registerForm, "email", null);
        this.$set(this.registerForm, "gender", null);
        this.$set(this.registerForm, "name", null);
        this.$set(this.registerForm, "customer_id", null);
        this.$set(this.registerForm, "first_name", null);
        this.$set(this.registerForm, "image_path", null);
        this.$set(this.registerForm, "dob", null);
        this.$set(this.registerForm, "country_id", null);
        this.$set(this.registerForm, "last_name", null);
        this.$set(this.registerForm, "opt_marketing", false);
        this.$forceUpdate();
      }

      if (data.mobile) {
        this.$set(this.registerForm, "mobile", data.mobile);
      }
      if (data.email) this.$set(this.registerForm, "email", data.email);
      if (data.country_id) {
        this.$set(this.registerForm, "country_id", data.country_id);
      } else {
        this.$set(this.registerForm, "country_id", null);
      }
      if (data.country_id) {
        this.$set(this.registerForm, "id_proof_type_id", data.id_proof_type_id);
      }

      if (data.id_proof_number) {
        this.$set(this.registerForm, "id_proof_number", data.id_proof_number);
      }

      if (data.gender) {
        this.$set(this.registerForm, "gender", data.gender);
      } else {
        this.$set(this.registerForm, "gender", null);
      }
      if (data.dob) {
        this.$set(this.registerForm, "dob", data.dob);
      } else {
        this.$set(this.registerForm, "dob", null);
      }

      if (data.image) {
        this.$set(this.registerForm, "image", data.image);
      }

      if (data.name) this.$set(this.registerForm, "name", data.name);
      if (data.last_name) {
        this.$set(this.registerForm, "last_name", data.last_name);
      } else {
        this.$set(this.registerForm, "last_name", null);
      }
      if (data.first_name)
        this.$set(this.registerForm, "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.registerForm, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.registerForm, "image_path", data.image_path);
      } else {
        this.$set(this.registerForm, "image_path", null);
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.registerForm, "opt_marketing", true);
        } else {
          this.$set(this.registerForm, "opt_marketing", false);
        }
      }
      this.$forceUpdate();
    },
  },
};
</script>

<style></style>

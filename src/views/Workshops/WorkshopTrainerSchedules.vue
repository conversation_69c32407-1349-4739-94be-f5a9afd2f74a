<template>
  <v-container fluid>
    <div class="row row--dense">
      <v-col cols="12" xl="12" lg="12">
        <div class="d-flex justify-space-between flex-wrap" >
          <div class="d-flex bordered qp-tab-nav">
            <div class="nv_item">
              <v-autocomplete
                  :items="venueServices"
                  v-model="venueServiceIds"
                  item-value="venue_service_id"
                  item-text="name"
                  outlined
                  multiple
                  @change="changeVenueService"
                  placeholder="Services"
                  background-color="rgba(79, 174, 175, 0.1)"
                  hide-details
                  style="max-width: 150px;"
                  class="q-autocomplete shadow-0 nv_item_select q-tab-autocomplete"
                  :height="46"
                  dense
                  color="#4FAEAF"
                  item-color="#4FAEAF"
              >
                <template
                    v-if="venueServiceIds.length == venueServices.length"
                    v-slot:selection="{ index }"
                >
                  <span v-if="index == 0">All Services</span>
                </template>
                <template v-else v-slot:selection="{ item, index }">
                  <span v-if="index == 0">{{ item.name }}</span>
                  <span v-if="index == 1">, {{ item.name }}</span>
                  <span v-if="index == 2">, ...</span>
                </template>
                <template v-slot:prepend-item>
                  <v-list-item ripple @click="toggle()">
                    <v-list-item-action>
                      <v-icon :color="venueServiceIds && venueServiceIds.length > 0 ? 'indigo darken-4' : ''">{{ icon() }}</v-icon>
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title>Select All</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <v-divider class="mt-2"></v-divider>
                </template>
              </v-autocomplete>
            </div>
            <div class="d-flex p-2">
              <template >
                <div class="nv_item d-flex p-4 pointer" @click="gotoPage('/workshops')">
                  <SvgIcon class="text-xl text-thin-black" text="Academy" >
                    <template v-slot:icon>
                      <DashboardIcon />
                    </template>
                  </SvgIcon>
                </div>
                <v-divider
                    vertical style="background-color: rgba(220, 220, 220, 1)"
                ></v-divider>
              </template>
              <template>
                <div class="nv_item d-flex p-4 pointer" @click="gotoPage('/workshop-trainer-schedules')" v-if="checkReadPermission($modules.workshops.trainerSchedule.slug)">
                  <SvgIcon class="text-xl  qp-tab-nav-is-active text-thin-black " text="Schedules" >
                    <template v-slot:icon>
                      <SchedulesIcon />
                    </template>
                  </SvgIcon>
                </div>
              </template>
              <template>
                <div class="nv_item d-flex p-4 pointer" @click="gotoPage('/workshop-sales')" v-if="checkReadPermission($modules.workshops.sales.slug)">
                  <SvgIcon class="text-xl  text-thin-black " text="Sales" >
                    <template v-slot:icon>
                      <AnalyticsIcon />
                    </template>
                  </SvgIcon>
                </div>
              </template>
            </div>
          </div>
        </div>
      </v-col>
    </div>
    <v-divider class="mt-4" style="border-color: rgba(17, 42, 70, 0.14) !important;"/>
    <div class="md-card md-theme-default mt-8 shadow rounded-5 ts-new">
      <div class="md-card-content">
        <v-row>
          <v-col sm="11" class="align-center">
            <div class="calender-navi">
              <v-row no-gutters>
                <v-col sm="1" class="text-lg-center mr-1">
                  <v-btn
                      fab
                      x-small
                      color="white"
                      @click="prevDate"
                  >
                    <v-icon dark>mdi-menu-left</v-icon>
                  </v-btn>
                </v-col>
                <v-col sm="8" class="text-lg-center">
                  <date-field
                      v-model="date"
                      :buttonAndText="true"
                      :dayName="true"
                      :back-fill="true"
                      @change="changeDate"
                  >
                  </date-field>
                </v-col>
                <v-col sm="1" class="text-lg-center ml-1">
                  <v-btn
                      fab
                      color="white"
                      x-small
                      @click="nextDate"
                  >
                    <v-icon dark>mdi-menu-right</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </v-col>
          <v-spacer></v-spacer>
          <v-col sm="1">
            <div class="mx-2">
              <v-hover v-slot:default="{ hover }">
                <v-btn
                    style="height: 40px; border: 1px solid #dcdcdc !important"
                    :elevation="hover ? 2 : 0"
                    :color="hover ? 'teal' : 'white'"
                    @click="getTrainerSchedules"
                >
                  <v-icon>mdi-refresh</v-icon>
                </v-btn>
              </v-hover>
            </div>
          </v-col>
        </v-row>
        <div style="display: inline; display: flex" class="mt-6">
          <div style="width: 160px" class="d-flex flex-column">
            <HeaderCell  name="Booking Time" />
            <div
                v-if="trainersData"
                class="overflow-auto no-scroll"
                ref="scheduleTimer"
                @scroll="onScroll"
                :style="`min-height: ${dynamicHeight}px; max-height: ${dynamicHeight}px;margin-right:-1px;`"
            >
              <TimeColumn :height="dynamicHeight" :timeSlots="timeSlots" />
            </div>
          </div>
          <div style="width: calc(100% - 200px)">
            <div class="d-flex overflow-x-hidden overflow-y-hidden" ref="scheduleHeader" v-if="trainersData">
              <template v-for="(trainer, index) in trainersData">
                <HeaderCell
                    :key="`h_${index}`"
                    :name="trainer.name"
                    :perDayCapacity="1000"
                />
              </template>
            </div>
            <div
                ref="schedule"
                class="d-flex overflow-auto grey--text text--lighten-2 caption"
                :style="`min-height: ${dynamicHeight}px; max-height: ${dynamicHeight}px`"
                @scroll="onScroll"
            >
              <template v-for="(trainer, dIndex) in trainersData">
                <DataColumn
                    v-if="trainer"
                    :key="`t_${dIndex}`"
                    :increment="increment"
                    :height="dynamicHeight"
                    :date="date"
                    :timeSlots="timeSlots"
                    :schedules="trainer.schedules"
                    :availabilities="trainer.availabilities"
                    @addParticipant="(childData)=>addParticipantModalShow(trainer,childData)"
                    @showParticipant="(childData)=>showParticipantModal(trainer,childData)"
                    @addProgram="(childData) => addProgramModalShow(trainer, childData)"
                    @showTrainerParticipant="(childData)=>showTrainerParticipantModal(trainer,childData)"
                />
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-4">
      <div class="legend-container" title="schedule booking legend">
        <div class="legend-item">
          <div class="color-box" style="background-color: green;"></div>
          <span>Slot Available</span>
        </div>

        <div class="legend-item">
          <div class="color-box" style="background-color: red;"></div>
          <span>Full Class</span>
        </div>

        <div class="legend-item">
          <div class="color-box" style="background-color: yellow;"></div>
          <span>Reschedule</span>
        </div>

      </div>
    </div>
    <AddProgram :addProgramDialog="addProgramDialog" :trainer="selectedTrainer" v-if="selectedTrainer" @close="closeProgramDialoag" @programAdded="programAdded"/>
    <BookWorkshopTrainerProgram :date="date" :addParticipantDialog="addParticipantDialog" :trainer="selectedTrainer" :workshopData="workshopData" @complete="customerschedulecompleted" @close="closeAddParticipantDialog"/>
    <CustomerAttendanceModal
      v-if="selectedSlotData"
      :showParticipantDialog="showParticipantDialog"
      :trainer="selectedTrainer"
      :slotData="selectedSlotData"
      @addStudent="addStudentModal"
      @close="closeShowParticipantDialog"
      @reloadSchedule="reloadSchedule"
    />
      <order-details
        :id="order_id_for_payment"
        :ids="orderIds"
        @close="order_id_for_payment = null; orderIds = null;"
        @paymentDone="paymentDone"
    ></order-details>
    <TrainerCustomerTooltipDialog
        :selectedEvent="selectedEvent"
        :selectedOpen="selectedOpen"
        @close="closeTrainerCustomerTooltipDialog"
        @getTrainerSchedules="getTrainerSchedules"
    />
  </v-container>
</template>

<script>
import moment from "moment";
import HeaderCell from "@/components/Workshop/Schedules/HeaderCell.vue";
import TimeColumn from "@/components/Workshop/Schedules/TimeColumn.vue";
import DataColumn from "@/components/Workshop/Schedules/DataColumn.vue";
import DashboardIcon from "@/assets/images/memberships/dashboard-icon.svg";
import AnalyticsIcon from "@/assets/images/memberships/analytics-up.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import SchedulesIcon from "@/assets/images/trainers/calendar-icon.svg";
import AddProgram from "@/components/Workshop/Schedules/AddProgram.vue";
import BookWorkshopTrainerProgram from "@/components/Workshop/Schedules/BookWorkshopTrainerProgram.vue";
import CustomerAttendanceModal from "@/components/Workshop/Schedules/CustomerAttendanceModal.vue";
import TrainerCustomerTooltipDialog from "@/views/Trainers/TrainerCustomerTooltipDialog.vue";
import OrderDetails from "../../components/Order/OrderDetails.vue";
export default {
  components: {
    OrderDetails,
    CustomerAttendanceModal,
    BookWorkshopTrainerProgram,
    AddProgram,
    SvgIcon, SchedulesIcon, DashboardIcon, AnalyticsIcon, DataColumn, HeaderCell, TimeColumn,
    TrainerCustomerTooltipDialog,
  },
  data() {
    return {
      date: moment().format("YYYY-MM-DD"),
      weekDays: [],
      increment: 60,
      height: 4800,
      tpopup: false,
      selectedDate: moment().format("YYYY-MM-DD"),
      selectedTime: "12:00 am - 01-00 am",
      trainersData: [],
      timeSlots: [],
      timeSlots2: [],
      selectedData: {},
      userModel: {userID: null, type: "details"},
      venueServiceIds: [],
      trainerStatus: 'active',
      addProgramDialog: false,
      addParticipantDialog: false,
      showParticipantDialog:false,
      workshopData: null,
      selectedTrainer: null,
      selectedSlotData: null,
      trainers: [],
      startTime: "00:00:00",
      endTime: "23:59:00",
      dynamicHeight: 0,


      orderId: null,
      order_id_for_payment: null,
      orderIds: null,
      selectedEvent: {},
      selectedOpen: false,
    }
  },
  mounted() {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices").then(() => {
        this.venueServiceIds = this.venueServices.map(
            (item) => item.venue_service_id
        );
        this.getTrainerSchedules()
      });
    } else {
      this.venueServiceIds = this.venueServices.map(
          (item) => item.venue_service_id
      );
      this.getTrainerSchedules()
    }
    this.generateWeekDays();
    this.calculateContainerHeight();
    this.generateSlotTiming();
  },

  computed: {
    venueServices() {
      return this.$store.getters.getVenueServices.data.filter(
          (service) => service.name != "POS"
      );
    },
  },
  watch: {
    venueServices(venuS, old) {
      if (!old.length && venuS.length) {
        this.venueServiceIds = this.venueServices.map(
            (item) => item.venue_service_id
        );
      }
    }
  },
  methods: {
    calculateContainerHeight(){
      const start = this.convertToMinutes("00:00:00");
      const end = this.convertToMinutes("23:59:00");
      this.dynamicHeight  = ((end - start));
    },
    convertToMinutes(time) {
      const [hours, minutes] = time.split(":").map(Number);
      return hours * 60 + minutes;
    },
    gotoPage(route) {
      this.$router.push(route);
    },
    nextDate() {
      let amount = 1;
      this.date = moment(this.date).add(amount, "days").format("YYYY-MM-DD");
      this.generateWeekDays();
      this.getTrainerSchedules();
    },
    prevDate() {
      let amount = 1;
      this.date = moment(this.date).subtract(amount, "days").format("YYYY-MM-DD");
      this.generateWeekDays();
      this.getTrainerSchedules();
    },
    changeDate() {
      this.generateWeekDays();
      this.getTrainerSchedules();
    },
    getFormattedDate(date) {
      return moment(date, "YYYY-MM-DD").format("ddd Do");
    },
    generateWeek(startingDate) {
      const daysOfWeek = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      const result = [];

      // Convert the starting date string to a Date object
      const startDate = new Date(startingDate);

      // Generate the list of weekdays starting from the given date
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);
        const day = daysOfWeek[currentDate.getDay()];
        result.push({
          day: day,
          date: currentDate.toISOString().split("T")[0],
        });
      }
      return result;
    },
    generateWeekDays() {
      this.weekDays = [];
      this.weekDays = this.generateWeek(this.date);
    },
    getTrainerSchedules() {
      let url = `from_date=${this.date}&status=${this.trainerStatus}`;
      this.showLoader("Loading Trainers");

      if (this.venueServiceIds.length > 0) {
        this.venueServiceIds.map((service, index) => {
          url += `&venue_service_id[${index}]=${service}`;
        });
      }
      this.showLoader("Loading");
      this.$http
          .get(`venues/workshops/trainers/schedules?${url}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.trainersData = response.data.data;
              // console.log(this.trainersData);
            }
            this.hideLoader();
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getTotalCount(date) {
      let checkIn = 0;
      let checkOut = 0;
      if (date && Object.keys(this.trainersData).length) {
        this.logs[date] && Object.keys(this.logs[date]).forEach((key) => {
          const item = this.logs[date][key];
          checkIn += item.total_check_in;
          checkOut += item.total_check_out;
        })
      }
      return {check_in: checkIn, check_out: checkOut};
    },
    onScroll() {
      const refElement = this.$refs.schedule;
      if (refElement) {
        const scrollLeft = refElement.scrollLeft;
        const scrollTop = refElement.scrollTop;
        this.$refs.scheduleTimer.scrollTop = scrollTop;
        this.$refs.scheduleHeader.scrollLeft = scrollLeft;
      }
    },
    openParticipantPopup(data) {
      this.tpopup = true;
      this.selectedData.date = data.date;
      this.selectedData.hour = data.hour;
      this.selectedData.slots = data.slots;
      if (this.timeSlots && this.timeSlots.length > 0) {
        this.selectedData.time = this.timeSlots.find(slot => slot.hour === data.hour).time;
      }
    },
    closeModal() {
      this.userModel.userID = null;
      this.tpopup = false;
    },
    showUserModal(userId) {
      this.userModel.userID = userId;
      this.userModel.type = "details";
    },
    changeVenueService() {
      this.getTrainerSchedules();
    },
    toggle() {
      this.$nextTick(() => {
        if (this.venueServices.length == this.venueServiceIds.length) {
          this.venueServiceIds = [];
        } else {
          this.venueServiceIds = this.venueServices.map(
              (item) => item.venue_service_id
          );
          this.$forceUpdate;
        }
      });
    },
    icon() {
      if (this.venueServices.length == this.venueServiceIds.length)
        return "mdi-close-box";
      if (this.venueServiceIds.length > 0) return "mdi-minus-box";
      return "mdi-checkbox-blank-outline";
    },
    addProgramModalShow(trainer){
      if(moment(this.date).isBefore(moment(), 'day')){
        this.showError("Program cannot be added for past dates.");
      }else{
        this.selectedTrainer = {id: trainer.id, name: trainer.name, user_id: trainer.user_id};
        this.addProgramDialog = true;
      }
    },
    showParticipantModal(trainer,childData){
      this.selectedTrainer = {id: trainer.id, name: trainer.name, user_id: trainer.user_id};
      this.selectedSlotData = childData.program;
      this.showParticipantDialog = true;
    },
    addParticipantModalShow(trainer,childData){
      this.selectedTrainer = {id: trainer.id, name: trainer.name, user_id: trainer.user_id};
      this.selectedSlotData = childData.program;
      this.workshopData = {...this.selectedSlotData};
      this.addParticipantDialog = true;
    },
    addStudentModal(){
      this.showParticipantDialog = false;
      this.workshopData = {...this.selectedSlotData};
      this.addParticipantDialog = true;
    },
    closeProgramDialoag(){
      this.addProgramDialog = false;
      this.selectedTrainer = null;
    },
    programAdded(){
      this.addProgramDialog = false;
      this.selectedTrainer = false;
      this.reloadSchedule();

    },
    closeAddParticipantDialog(){
      this.addParticipantDialog = false;
      this.workshopData = null;
      this.selectedTrainer = null;
    },
    closeShowParticipantDialog(){
      this.workshopData = null;
      this.selectedTrainer = null;
      this.showParticipantDialog = false;
    },
    reloadSchedule(){
      this.workshopData = null;
      this.selectedTrainer = null;
      this.showParticipantDialog = false;
      this.getTrainerSchedules();
    },
    getProgramsForSlot(trainer, slot) {
      // Map time slots to the trainer's programs.
      const [start, end] = slot.split(" - ");
      return trainer.programs.filter((program) =>
          program.schedules.some(
              (schedule) => this.isOverlapping(schedule.start, schedule.end, start, end)
          )
      );
    },
    isOverlapping(scheduleStart, scheduleEnd, slotStart, slotEnd) {
      const toMinutes = (time) => {
        const [hours, minutes] = time.match(/(\d+):(\d+)/).slice(1).map(Number);
        const isPM = time.includes("PM") && hours !== 12;
        return (isPM ? hours + 12 : hours) * 60 + minutes;
      };
      const s1 = toMinutes(scheduleStart);
      const e1 = toMinutes(scheduleEnd);
      const s2 = toMinutes(slotStart);
      const e2 = toMinutes(slotEnd);
      return s1 < e2 && s2 < e1;
    },
    isProgramInSlot(program, slot) {
      const programStart = new Date(program.start_time);
      const programEnd = new Date(program.end_time);
      const slotStart = slot.start;
      const slotEnd = slot.end;
 // Check if the program overlaps with the slot
      return programStart < slotEnd && programEnd > slotStart;
    },
    formatTime(date) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },
    generateSlotTiming() {
      this.timeSlots = [];
      const totalDayMinutes = 1440;
      //const divisions = totalDayMinutes / this.increment;
      const startTimeMoment = moment(this.startTime, "HH:mm:ss");
      const endTimeMoment = moment(this.endTime, "HH:mm:ss");
      let data = [];
      for (let index = 0; index < totalDayMinutes; index += this.increment) {
        let start = moment().startOf("day").add(index, "minutes");
        let end = start.clone().add(this.increment, "minutes");
        if(end.format('HH:mm:ss') == "00:00:00"){
          end = start.clone().add(this.increment - 1, "minutes");
        }


        if (start.isSameOrAfter(startTimeMoment) && end.isSameOrBefore(endTimeMoment)) {
          const element = {
            id: index,
            start_time: start.format("HH:mm:ss"),
            end_time: end.format("HH:mm:ss"),
            formated_start_time: start.format("hh:mm a"),
            formated_end_time: end.format("hh:mm a"),
            height: 60,
            fixed: true,
          };
          data.push(element);
        }
      }
      this.timeSlots = [...data];
    },
    paymentDone() {
      console.log('payment done')
    },


    customerschedulecompleted(data) {
      this.addParticipantDialog = false;
      this.selectedTrainer = null;
      this.workshopData = null;
      if (data) {
        this.order_id_for_payment = data.order_id;
      }
    },
    showTrainerParticipantModal(trainer,childData){
      console.log("trainer",trainer);
      console.log("childData",childData);
      this.selectedEvent = childData.program;
      this.selectedOpen = true;
    },
    closeTrainerCustomerTooltipDialog(){
      this.selectedOpen = false;
      this.selectedEvent = null;
    },



  }
}
</script>
<style>
/** Trainer Schedule New Screen START **/
.ts-new{
  .calender-navi {
    width: 420px;
    text-align: center;
    margin: 0 auto;
  }
}
.schedule-container {
  display: grid;
  grid-template-columns: 150px repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.time-slot {
  background-color: #4CAF50;
  color: white;
  padding: 10px;
  font-weight: bold;
  text-align: center;
}

.trainer-slot {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  padding: 10px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.trainer-slot span {
  text-align: center;
  font-weight: bold;
  color: #2e7d32;
}

.trainer-slot.free {
  background-color: #f0f0f0;
  color: #9e9e9e;
}

.trainer-slot.program {
  grid-row: span var(--row-span, 1);
}


.schedule-container {
  display: flex;
  flex-direction: row;
  font-family: Arial, sans-serif;
}
.schedule-grid {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.time-column {
  width: 100px;
  border-right: 1px solid #ddd;
}
.time-slot {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ddd;
}
.trainer-columns {
  display: flex;
  flex: 1;
}
.trainer-column {
  flex: 1;
  border-right: 1px solid #ddd;
}
.trainer-header {
  height: 50px;
  background-color: #f4f4f4;
  text-align: center;
  line-height: 50px;
  font-weight: bold;
}
.program-slot {
  height: 50px;
  position: relative;
  border-bottom: 1px solid #ddd;
}
.program-block {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #4caf50;
  color: #fff;
  text-align: center;
  line-height: 50px;
  font-size: 14px;
  border-radius: 5px;
}

.legend-container {
  display: flex;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 12px;
}

.color-box {
  width: 14px;
  height: 14px;
  margin-right: 8px;
  border-radius: 2px;
  border: 1px solid black;
}
.unavailable-class {
  background: #cbcbcb;
  cursor: not-allowed;
  z-index: +2;
}
</style>

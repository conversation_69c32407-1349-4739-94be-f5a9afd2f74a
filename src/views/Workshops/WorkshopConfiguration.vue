<template>
  <v-dialog :value="viewConfig" width="1000" :retain-focus="false" @input="close" scrollable>
    <v-card>
      <div class="px-4 pt-4 pb-0 d-flex justify-space-between align-center border-bottom">
        <v-row class="px-0 py-0 m-0" >
          <v-col cols="8"><h3 class="font-semibold text-blue pb-2">Configurations</h3></v-col>
        </v-row>
      </div>
      <v-card-text class="">
        <v-row dense>
          <GeneralMembership
              :note=true
              note-text="Note: This will apply on all new memberships by default"
              :existingMembershipData="benefit_workshop"
              @updateForm="updateFormWithMembership"
          />
        </v-row>
        <v-row dense>
          <v-col lg="12" md="12">
            <v-card rounded outlined class="mb-4 rounded shadow-0 bordered">
              <v-col lg="4" md="4">
                <v-checkbox
                  v-model="configuration.tnc_enable"
                  false-value="0"
                  true-value="1"
                  label="Enable Website T&C's"
                ></v-checkbox>
              </v-col>
              <v-col lg="12" md="12" class="mb-5" v-if="configuration.tnc_enable == 1">
                  <h2 class="pb-4">Terms and Conditions</h2>
                  <div class=" pb-4 d-flex flex-row flex-wrap"
                style="width: 100%"
              >
<!--                <text-editor-->
<!--                  @complete="setTncContent"-->
<!--                  :message="configuration.tnc"-->
<!--                  style="width: 100%"-->
<!--                />-->
                <RichEditor v-model="configuration.tnc" />
                  </div>
              </v-col>
            </v-card>
          </v-col>
          <v-col lg="12" md="12">
            <v-form ref="pform">
              <v-card rounded outlined class="mb-4 rounded shadow-0 bordered">
                <v-row dense class="ml-2">
                  <v-col lg="12" md="12"><div class="text-base font-semibold black-text mt-6 ml-1">Documents</div></v-col>
                </v-row>
                <v-row v-for="(document, k) in configuration.field_document_configurations" :key="'d_' + k" class="ml-1 mr-1">
                  <v-col cols="12" md="3">
                    <v-text-field
                      class="q-text-field shadow-0"
                      hide-details="auto"
                      dense
                      background-color="#fff"
                      outlined
                      label="Name"
                      v-model="document.name"
                      :rules="[(v) => !!v || 'Document field name is required']"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" sm="4" md="4">
                    <v-file-input
                      class="q-text-field shadow-0"
                      :label="document.document_id != null ? '' : 'Document'"
                      v-model="document.file"
                      prepend-inner-icon="mdi-paperclip"
                      prepend-icon
                      outlined
                      dense
                      hide-details
                      background-color="#fff"
                    >
                      <template v-slot:label>
                        <span v-if="!document.document_id"> Select file </span>
                        <span v-if="document.document_id && !document.file" class="font-weight-bold">
                          <span style="width: 70%; display: inline-block" class="text-truncate">{{ document.uploaded_original_file_name }}</span>
                          <span style="width: 20%; display: inline-block" class="text-truncate">.{{ document.uploaded_original_file_name.split(".")[document.uploaded_original_file_name.split(".").length - 1] }}</span>
                        </span>
                      </template>
                    </v-file-input>
                  </v-col>
                  <v-col cols="12" sm="2" md="2">
                    <v-checkbox
                      class="mt-0 mb-3"
                      v-model="document.is_visible"
                      label="View"
                      color="success"
                      value="document.is_visible"
                      v-bind:false-value="0"
                      v-bind:true-value="1"
                      hide-details
                      @change=" !document.is_visible ? (document.is_required = 0) : ''"
                    ></v-checkbox>
                  </v-col>
                  <v-col cols="12" sm="2" md="2">
                    <v-checkbox
                      class="mt-0 mb-3"
                      v-model="document.is_required"
                      label="Mandatory"
                      color="success"
                      value="document.is_required"
                      hide-details
                      v-bind:false-value="0"
                      v-bind:true-value="1"
                      @change=" document.is_required ? (document.is_visible = 1) : ''"
                    ></v-checkbox>
                  </v-col>
                  <v-col cols="12" sm="1" md="1">
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          style="position: relative; top: calc(50% - 33px)"
                          v-if="
                            checkDeletePermission(
                              $modules.facility.management.slug
                            )
                          "
                          v-bind="attrs"
                          v-on="on"
                          color="red"
                          class="mt-2"
                          @click="deleteDocuments(k)"
                          fab
                          x-small
                          dark
                        >
                          <v-icon>mdi-delete</v-icon>
                        </v-btn>
                      </template>
                      Delete
                    </v-tooltip>
                  </v-col>
                </v-row>
                <div class="add_btn">
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-bind="attrs"
                          v-on="on"
                          color="blue-color"
                          fab
                          x-small
                          dark
                          @click="addDocuments"
                        >
                          <v-icon>mdi-plus-circle</v-icon>
                        </v-btn>
                      </template>
                      Add Document
                    </v-tooltip>
                  </div>
              </v-card>
              <v-card rounded outlined class="mb-4 rounded shadow-0 bordered">
                <div class="text-base font-semibold black-text ma-5 mt-6">Booking Configurations</div>
                <v-row dense class="ma-3">
                    <v-col cols="12">
                      <table class="table text-center table-bordered">
                        <thead>
                        <tr class="opacity-70 tr-neon tr-rounded text-center font-bold black-text">
                          <th class="text-center">Field Name</th>
                          <th class="text-center">View</th>
                          <th class="text-center">Mandatory</th>
                          <th class="text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(config, index) in sortedFieldConfigurations"
                            :key="config.slug">
                          <td class="black-text font-bold text-sm  " style="font-weight: 500 !important;width: 55%">{{ config.name }}</td>
                          <td>
                            <v-btn :style=" viewRestrictedFields.includes(config.slug) && 'cursor:not-allowed;'" icon @click="checkAsVisible(index)">
                              <TickIcon v-if="config.is_visible == 1 "/>
                              <CloseIcon v-else/>
                            </v-btn>
                          </td>
                          <td>
                            <v-btn :style=" mandatoryRestrictedFields.includes(config.slug) && 'cursor:not-allowed;'" icon @click="checkAsMandatory(index)">
                              <TickIcon v-if="config.is_required == 1 "/>
                              <CloseIcon v-else/>
                            </v-btn>
                          </td>
                          <td>
                            <template v-if="config.is_additional">


                              <v-btn icon @click="sortField(config.slug, -1)">
                                <v-icon dark color="green">mdi-arrow-up</v-icon>
                              </v-btn>
                              <v-btn icon @click="sortField(config.slug, 1)">
                                <v-icon dark color="red">mdi-arrow-down</v-icon>
                              </v-btn>


                              <v-btn :disabled="!config.is_additional"
                                     icon
                                     @click="editAdditionalField(config)"
                              >
                                <v-icon dark>mdi-pencil-outline</v-icon>
                              </v-btn>
                              <v-btn
                                  icon
                                  :disabled="!config.is_additional"
                                  @click="deleteAdditionalField(index)"
                              >
                                <v-icon dark >mdi-trash-can-outline</v-icon>
                              </v-btn>


                            </template>
                            <template v-else>
                              <span>-</span>
                            </template>
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </v-col>
                  <v-col cols="12">
                    <v-btn
                        class="ma-2 white--text blue-color float-right"
                        height="45"
                        text
                        @click="additional_field_dialogue = true"
                    >Add Field
                    </v-btn>
                  </v-col>
                  </v-row>
              </v-card>
              <v-card rounded outlined class="mb-4 rounded shadow-0 bordered">
                <v-row class="mt-0">
                  <v-col cols="12" lg="12" md="12">
                    <v-form ref="cfform">
                      <div class="text-base font-semibold black-text ma-5 mt-6">Academy Additional Fields</div>
                      <v-row dense class="ma-3">
                        <v-col cols="12" md="6" sm="12" v-for="(cfield, cindex) in custom_fields" :key="'d_' + cindex">
                          <v-card class="rounded-2 bordered shadow-0 mt-2" :style="cardStyle" >
                            <v-card-text>
                              <v-row class="mt-1">
                                <v-col cols="12" sm="6" md="6" class="pr-1 pl-1">
                                  <v-text-field
                                      v-model="cfield.field_name"
                                      class="q-text-field shadow-0"
                                      hide-details="auto"
                                      dense
                                      background-color="#fff"
                                      outlined
                                      :rules="[(v) => !!v || 'Field name is required']"
                                      label="Field Name"
                                  ></v-text-field>
                                </v-col>
                                <v-col cols="12" sm="6" md="6" class="pr-1 pl-1">
                                  <v-select
                                      v-model="cfield.field_type"
                                      :items="['Text','File','Single-select dropdown','Multi-select dropdown']"
                                      :menu-props="{ bottom: true, offsetY: true }"
                                      :rules="[(v) => !!v || 'Field Type is required']"
                                      class="q-autocomplete shadow-0"
                                      hide-details="auto"
                                      outlined
                                      placeholder="Field Type"
                                      dense
                                  ></v-select>
                                </v-col>
                              </v-row>
                              <div v-if="['Single-select dropdown','Multi-select dropdown'].includes(cfield.field_type)" class="mt-4">
                                <v-row v-if="cfield.options.length > 0">
                                  <v-col
                                      cols="12"
                                      md="6"
                                      class="py-0 px-0"
                                      v-for="(option, key) in cfield.options"
                                      :key="`ci_key_${key}`"
                                  >
                                    <div class="d-flex items-center">
                                      <v-col md="9" class="pl-1">
                                        <v-text-field
                                            clearable
                                            outlined
                                            dense
                                            required
                                            :rules="[(v)=> !!v || 'Field is required' ]"
                                            label="Option"
                                            class="q-text-field shadow-0"
                                            background-color="#fff"
                                            v-model="option.value"
                                            hide-details="auto"
                                        ></v-text-field>
                                      </v-col>
                                      <v-col md="2">
                                        <v-btn
                                            small
                                            class="remove-button mt-1"
                                            color="white"
                                            style="height:30px; width:20% !important"
                                            icon
                                            @click="deleteOption(cindex,key)"
                                        >
                                          <v-icon dark>
                                            mdi-trash-can-outline
                                          </v-icon>
                                        </v-btn>
                                      </v-col>
                                    </div>
                                  </v-col>
                                </v-row>
                                <div class="add-new-step my-4" @click="addOption(cindex)">
                                  + Add Choice
                                </div>
                              </div>
                              <v-row>
                                <v-col cols="12" sm="6" md="3">
                                  <v-checkbox
                                      class="mt-0 mb-3"
                                      v-model="cfield.is_visible"
                                      label="View"
                                      color="success"
                                      value="cfield.is_visible"
                                      v-bind:false-value="0"
                                      v-bind:true-value="1"
                                      hide-details
                                      @change="!cfield.is_visible ? (cfield.is_required = 0) : ''"
                                  ></v-checkbox>
                                </v-col>
                                <v-col cols="12" sm="6" md="3">
                                  <v-checkbox
                                      class="mt-0 mb-3"
                                      v-model="cfield.is_required"
                                      label="Mandatory"
                                      color="success"
                                      value="cfield.is_required"
                                      hide-details
                                      v-bind:false-value="0"
                                      v-bind:true-value="1"
                                      @change="cfield.is_required ? (cfield.is_visible = 1) : ''"
                                  ></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-card-text>
                            <div class="delete-field">
                              <v-tooltip  bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <v-btn
                                      absolute
                                      color="red"
                                      dark
                                      elevation="0"
                                      fab
                                      right
                                      style="top:-5px;"
                                      top
                                      v-bind="attrs"
                                      x-small
                                      @click="deleteCustomField(cindex)"
                                      v-on="on"
                                  >
                                    <DeleteIcon/>
                                  </v-btn>
                                </template>
                                Delete
                              </v-tooltip>
                            </div>
                          </v-card>
                        </v-col>
                      </v-row>
                      <div class="add_btn">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                                v-bind="attrs"
                                v-on="on"
                                color="blue-color"
                                fab
                                x-small
                                dark
                                @click="addCustomField"
                            >
                              <v-icon>mdi-plus-circle</v-icon>
                            </v-btn>
                          </template>
                          Add Custom Field
                        </v-tooltip>
                      </div>
                    </v-form>
                  </v-col>
                </v-row>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>

        <v-btn  class="ma-2 black--text" color="darken-1" right style="background-color: rgba(17, 42, 70, 0.1)" text @click="close">Close</v-btn>
        <v-btn  class="ma-2 white--text blue-color" color="darken-1" right text @click="setConfiguration">Save</v-btn>
      </v-card-actions>
    </v-card>
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>

    <additional-field-modal
        :openModal="additional_field_dialogue"
        @closeModal="closeAdditionalFieldModal"
        :field_config="field_config"
        @addFormItem="addNewFormItem"
    />
  </v-dialog>
</template>

<script>
// import TextEditor from "@/components/Marketing/TextEditor";
import TickIcon from "@/assets/images/misc/config-table-tick-icon.svg";
import CloseIcon from "@/assets/images/misc/config-table-close-icon.svg";
import constants from "@/utils/constants";
import DeleteIcon from "@/assets/images/retail/delete-bg-icon.svg";
import GeneralMembership from "@/components/Membership/GeneralMembership.vue";
import AdditionalFieldModal from "@/components/Common/AdditionalFieldModal";

import RichEditor from "@/components/Common/RichEditor.vue";
export default {
  props: {
    viewConfig: { type: Boolean, default: false },
  },
  components: {
    RichEditor,
    GeneralMembership,
    CloseIcon,
    TickIcon,
    // TextEditor,
    DeleteIcon,
    AdditionalFieldModal
  },
  data() {
    return {
      benefit_workshop: [],
      membershipForm: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      configuration: {
        tnc: "",
        tnc_enable: "0",
        field_document_configurations: [],
        deleted_field_documents: [],
        field_configurations: []
      },
      viewRestrictedFields: constants.VIEWRESTRICTEDFIELDS,
      mandatoryRestrictedFields: constants.MANTATORYRESTRICTEDFIELDS,
      tnc: "",
      custom_fields:[],
      deleted_custom_fields:[],
      additional_field_dialogue:false,
      field_config: {
        type: 'text_box',
        field_name: 'Family name',
        ar_field_name: null,
        is_required: true,
        arabic_enabled: false,
        allowMarkdown: false
      },
    };
  },
  watch: {
    viewConfig: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getConfiguration();
        }
      },
    },
  },

  computed:{
    sortedFieldConfigurations() {
      // Step 1: Clone the array
      let fields = this.configuration.field_configurations.slice();

      // Step 2: Separate into default and additional fields
      const defaultFields = fields.filter(field => !field.is_additional);
      const additionalFields = fields.filter(field => field.is_additional);

      // Step 3: Assign incremental sort_id to any missing values
      let sortCounter = 1;

      // Sort and assign sort_id to default fields
      defaultFields.sort((a, b) => {
        return (a.sort_id ?? Infinity) - (b.sort_id ?? Infinity);
      }).forEach(field => {
        if (field.sort_id == null) {
          field.sort_id = sortCounter++;
        } else {
          sortCounter = Math.max(sortCounter, field.sort_id + 1);
        }
      });

      // Sort and assign sort_id to additional fields
      additionalFields.sort((a, b) => {
        return (a.sort_id ?? Infinity) - (b.sort_id ?? Infinity);
      }).forEach(field => {
        if (field.sort_id == null) {
          field.sort_id = sortCounter++;
        } else {
          sortCounter = Math.max(sortCounter, field.sort_id + 1);
        }
      });

      // Step 4: Merge and return
      const sortedFields = [...defaultFields, ...additionalFields].sort((a, b) => {
        // Sort primarily by is_additional, secondarily by sort_id
        if (!a.is_additional && b.is_additional) return -1;
        if (a.is_additional && !b.is_additional) return 1;
        return a.sort_id - b.sort_id;
      });

      console.log(`sortedFields`, sortedFields);
      return sortedFields;
    },
  },
  methods: {
    updateFormWithMembership(data) {
      this.membershipForm = [];
      this.membershipForm = data;
      console.log(data,'data');
      this.$forceUpdate();
    },
    close() {
      this.custom_fields = [];
      this.$emit("close");
    },

    setTncContent(content) {
      this.tnc = content;
    },

    getConfiguration() {
      this.$http
        .get(`venues/workshops/configuration`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.configuration = data;
            if (this.configuration) {
              this.tnc = this.configuration.tnc;
              this.custom_fields = this.configuration.custom_fields?this.configuration.custom_fields:[];
            } else {
              this.configuration = {
                tnc: "",
                tnc_enable: "0",
                field_document_configurations: [],
                custom_fields: [],
              };
            }
            if (this.configuration.field_document_configurations && this.configuration.field_document_configurations.length == 0) {
              this.configuration.field_document_configurations.push({
                name: null,
                file: null,
                is_required: 0,
                is_visible: 0,
              });
            } else if (this.configuration.field_document_configurations) {
              this.configuration.field_document_configurations.forEach((element) => {
                  if (!element.document_id) {
                    element.file = null;
                  }
                }
              );
            }
            this.configuration.deleted_field_documents = [];
            if (this.configuration && this.configuration.tnc_enable) {
              this.configuration.tnc_enable = this.configuration.tnc_enable.toString();
            }
            if(this.configuration.benefit_workshop){
              this.benefit_workshop = this.configuration.benefit_workshop;
            }
            if (this.$refs.pform) {
              this.$refs.pform.resetValidation();
            }
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    setConfiguration() {
      if (!this.$refs.cfform.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      let formData = new FormData();
      formData.append(`tnc_enable`, this.configuration.tnc_enable);

      if (this.configuration.tnc_enable == 1) {
        formData.append(`tnc`, this.utf8ToBase64(this.sanitizeHTML(this.tnc)));
      }
      if (this.configuration.field_document_configurations.length) {
        this.configuration.field_document_configurations.forEach(
          (item, index) => {
            if (item.name) {
              for (let key in item) {
                formData.append(
                  `field_document_configurations[${index}][${key}]`,
                  item[key]
                );
              }
            }
          }
        );
      }



      if (this.configuration.field_configurations.length) {
        this.configuration.field_configurations.forEach((item, index) => {
          for (let key in item) {
            if (key === 'options' && Array.isArray(item[key]) && item[key].length > 0) {
              item[key].forEach((option, optIndex) => {
                if (option.value != null) {
                  formData.append(
                      `field_configurations[${index}][options][${optIndex}][value]`,
                      option.value
                  );
                }
                if (option.value_ar != null) {
                  formData.append(
                      `field_configurations[${index}][options][${optIndex}][value_ar]`,
                      option.value_ar
                  );
                }
              });
            } else if (item[key] != null) {
              formData.append(
                  `field_configurations[${index}][${key}]`,
                  item[key]
              );
            }
          }
        });
      }
      if (this.configuration.deleted_field_documents.length) {
        this.configuration.deleted_field_documents.forEach((item, index) => {
          formData.append(`deleted_field_documents[${index}]`, item);
        });
      }

      if (this.membershipForm.data && this.membershipForm.data.length > 0) {

        formData.append(`membership_benefits_enable`, this.membershipForm.enable);

        this.membershipForm.data.forEach((ele, index) => {
          formData.append(`membership_benefits[${index}][membership_id]`, ele.membership_id);
          formData.append(`membership_benefits[${index}][membership_package_id]`, ele.membership_package_id);
          formData.append(`membership_benefits[${index}][membership_ids]`, ele.membership_ids);
          formData.append(`membership_benefits[${index}][membership_package_ids]`, ele.membership_package_ids);
          formData.append(`membership_benefits[${index}][benefit_type]`, ele.benefit_type);
          formData.append(`membership_benefits[${index}][benefit]`, ele.benefit);
          formData.append(`membership_benefits[${index}][benefit_id]`, ele.benefit_id);
          formData.append(`membership_benefits[${index}][id]`, ele.id);
          formData.append(`membership_benefits[${index}][workshop_membership_configuration_id]`, ele.workshop_membership_configuration_id);
        })

        this.membershipForm.deleted.forEach((ele, index) => {
          formData.append(`deleted_membership_benefits[${index}][membership_id]`, ele.membership_id);
          formData.append(`deleted_membership_benefits[${index}][membership_package_id]`, ele.membership_package_id);
          formData.append(`deleted_membership_benefits[${index}][benefit_type]`, ele.benefit_type);
          formData.append(`deleted_membership_benefits[${index}][benefit]`, ele.benefit);
          formData.append(`deleted_membership_benefits[${index}][benefit_id]`, ele.benefit_id);
          formData.append(`deleted_membership_benefits[${index}][id]`, ele.id);
        })
      }

      if (this.custom_fields.length) {
        this.custom_fields.forEach( (field,index) => {
          if(field.uuid === 'null' && !field.uuid) {
            formData.append(`custom_fields[${index}][uuid]`, field.uuid);
          }
          formData.append(`custom_fields[${index}][field_name]`, field.field_name);
          formData.append(`custom_fields[${index}][field_type]`, field.field_type);
          formData.append(`custom_fields[${index}][is_visible]`, field.is_visible);
          if(field.options){
            field.options.forEach((ele1, index1) => {
              formData.append(`custom_fields[${index}][options][${index1}][value]`, ele1.value);
            });
          }
          formData.append(`custom_fields[${index}][is_required]`,field.is_required);
        });
      }else{
        formData.append(`custom_fields`, []);
      }

      this.$http({
        method: "post",
        url: `venues/workshops/configuration`,
        data: formData,
        headers: {"Content-Type": "multipart/form-data; boundary=${form._boundary}",},
      })
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Configuration saved");
            this.close();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    addDocuments() {
      this.configuration.field_document_configurations.push({
        name: null,
        file: null,
        is_visible: 0,
        is_required: 0,
      });
    },

    deleteDocuments(index) {
      if (
        !this.configuration.field_document_configurations[index].name &&
        !this.configuration.field_document_configurations[index].id &&
        !this.configuration.field_document_configurations[index]
          .document_type_id
      ) {
        this.configuration.field_document_configurations.splice(index, 1);
        if (!this.configuration.field_document_configurations.length) {
          this.configuration.field_document_configurations = [{}];
        }
      } else {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this custom field?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "delete_document",
        };
      }
    },

    checkAsMandatory(index) {
      let field = this.sortedFieldConfigurations[index];
      if (!this.mandatoryRestrictedFields.includes(field.slug)) {
        this.sortedFieldConfigurations[index].is_required =
            field.is_required == 1 ? 0 : 1;
        if (field.is_required == 1 && field.is_visible == 0) {
          this.sortedFieldConfigurations[index].is_visible = 1;
        }
      }
    },
    checkAsVisible(index) {
      let field = this.sortedFieldConfigurations[index];
      if (!this.viewRestrictedFields.includes(field.slug)) {
        this.sortedFieldConfigurations[index].is_visible =
            field.is_visible == 1 ? 0 : 1;
        if (field.is_required == 1 && field.is_visible == 0) {
          this.sortedFieldConfigurations[index].is_required = 0;
        }
      }
    },
    confirmActions(data) {
      if (data.type === "delete_document") {
        if (this.configuration.field_document_configurations[data.id].id != null) {
          this.configuration.deleted_field_documents.push(this.configuration.field_document_configurations[data.id].id);
        }
        this.configuration.field_document_configurations.splice(data.id, 1);
        if (!this.configuration.field_document_configurations.length) {
          this.configuration.field_document_configurations = [{}];
        }
      }else if(data.type === "custom_field"){
        let index = data.id;
        if (this.custom_fields[index].uuid != null) {
          this.custom_fields.splice(index, 1);
        }
      }else if(data.type=== 'delete_additional_field'){
        let deleteItem = this.sortedFieldConfigurations[data.id];
        const originalIndex = this.configuration.field_configurations.findIndex(
            f => f === deleteItem
        );
        if (originalIndex === -1) return;
        this.configuration.field_configurations.splice(originalIndex, 1);
        console.log(`originalIndex`,originalIndex)
        this.configuration.field_configurations.forEach(field => {
          if (field.sort_id > deleteItem.sort_id) {
            this.$set(field, 'sort_id', field.sort_id - 1);
          }
        });
      }
      this.$forceUpdate();
      this.confirmModel.id = null;
    },
    deleteCustomField(index) {
      let cf = this.custom_fields[index];
      if (cf.uuid == null) {
        this.custom_fields.splice(index, 1);
        this.$forceUpdate();
      } else {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this Custom Field?",
          description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
          type: "custom_field",
        };
      }
    },
    addCustomField(){
      this.custom_fields.push({
        uuid: null,
        field_name: null,
        field_type: null,
        field_value: null,
        is_visible: 0,
        is_required: 0,
        options:[],
      });
    },
    deleteOption(ind,key){
      this.custom_fields[ind].options.splice(key, 1);
    },
    addOption(index){
      this.custom_fields[index].options.push({
        value:'',
      });
    },


    closeAdditionalFieldModal(){
      this.additional_field_dialogue =  false;
      this.field_config = {
        type: 'text_box',
        field_name: 'Field name',
        ar_field_name: null,
        is_required: true,
        arabic_enabled: false,
        allowMarkdown: false
      };
    },
    addNewFormItem(fieldData) {
      let index = null;

      // Step 1: Find index (by id or uId)
      if (!fieldData.id) {
        index = this.configuration.field_configurations.findIndex(
            (e) => e.uId === fieldData.uId
        );
      } else {
        index = this.configuration.field_configurations.findIndex(
            (e) => e.id === fieldData.id
        );
      }

      // Step 2: Check for duplicate slug (excluding self)
      const isDuplicateSlug = this.configuration.field_configurations.some(
          (e, i) => e.slug === fieldData.slug && i !== index
      );
      if (isDuplicateSlug) {
        console.warn("Duplicate slug detected:", fieldData);
        this.showError(`Duplicate slug not allowed - ${fieldData.slug}`);
        return;
      }

      // Step 3: Add or update
      if (index !== -1) {
        // Edit existing field
        const existingItem = this.configuration.field_configurations[index];
        // Preserve existing sort_id unless overwritten
        if (fieldData.sort_id == null) {
          fieldData.sort_id = existingItem.sort_id;
        }
        this.$set(this.configuration.field_configurations, index, fieldData);
      } else {
        // Add new field — assign next sort_id based on max
        const maxSortId = this.configuration.field_configurations.reduce(
            (max, field) => field.sort_id != null ? Math.max(max, field.sort_id) : max,
            0
        );
        fieldData.sort_id = maxSortId + 1;
        this.configuration.field_configurations.push(fieldData);
      }

      // Step 4: Close dialog
      this.additional_field_dialogue = false;
    },
    editAdditionalField(field){
      console.log(field);
      this.additional_field_dialogue = true
      this.field_config = field;
    },
    deleteAdditionalField(cIndex) {
      this.confirmModel = {
        id: cIndex,
        title: "Do you want to delete this Additional Field?",
        description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "delete_additional_field",
      };

    },
    sortField(slug, direction) {
      const all = this.configuration.field_configurations;
      const additionalFields = all
          .filter(f => f.is_additional === 1)
          .sort((a, b) => a.sort_id - b.sort_id);

      const index = additionalFields.findIndex(f => f.slug === slug);
      const newIndex = index + direction;
      if (newIndex < 0 || newIndex >= additionalFields.length) return;

      // Swap sort_id between the two items
      const itemA = additionalFields[index];
      const itemB = additionalFields[newIndex];

      // Find actual references in original array
      const originalItemA = all.find(f => f.slug === itemA.slug);
      const originalItemB = all.find(f => f.slug === itemB.slug);

      const temp = originalItemA.sort_id;
      originalItemA.sort_id = originalItemB.sort_id;
      originalItemB.sort_id = temp;

      // Trigger reactivity
      this.configuration.field_configurations = [...all];
    },
  },
};
</script>

<style>
.v-btn.remove-button {
  color: #00000040 !important;
}

.add-new-step {
  color: var(--main-color, #4faeaf);
  font-family: Inter;
  font-size: 14px;
  width: max-content;
  font-weight: 600;
  margin-left: 10px;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
    text-underline-offset: 3px;
  }
}
</style>

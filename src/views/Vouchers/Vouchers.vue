<template>
  <v-container fluid no-gutters>
    <v-row>
      <v-col lg="4" sm="4"> </v-col>
      <v-spacer></v-spacer>
      <v-col lg="4" sm="4" style="text-align: center">
        <v-btn-toggle
          borderless
          class="q-tabs shadow-0"
          v-model="toggle_mode"
          mandatory
          @change="loadVouchers"
        >
          <v-btn value="1">Active</v-btn>
          <v-btn value="10">Expired</v-btn>
          <v-btn value="2">InActive</v-btn>
        </v-btn-toggle>
      </v-col>
      <v-spacer></v-spacer>
      <v-col class="text-lg-right" lg="4" sm="4">
        <v-btn
            v-if="checkWritePermission($modules.vouchers.management.slug)"
            class="teal-color mr-2"
            @click="scanRfid"
            dark
        >
          READ CARD
        </v-btn>
        <v-btn
          v-if="checkWritePermission($modules.vouchers.management.slug)"
          color="darken-1"
          class="mr-6 white--text blue-color"
          @click="addVoucherPopup"
        >
          GIFT CARD
          <v-icon right dark>mdi-plus-circle</v-icon>
        </v-btn>


      </v-col>
    </v-row>

    <v-row>
      <v-col v-for="voucher in voucherList" :key="voucher.id"  lg="4" md="6" sm="12" xl="3">
        <voucher-widget
          v-bind="voucher"
          @edit="populateEditVoucher"
          @delete="deleteVoucherWarning"
        />
      </v-col>
    </v-row>
    <h3 class="text-center mt-12" v-if="voucherList.length == 0">
      No
      {{
        toggle_mode == 1 ? "active" : toggle_mode == 10 ? "expired" : "inactive"
      }}
      gift cards present
    </h3>
    <v-pagination
      v-if="totalPages"
      v-model="page"
      :length="totalPages"
      class="mt-3 new-pagination"
    ></v-pagination>
    <v-dialog v-model="addVoucherDialoge" scrollable width="40%">
      <v-form ref="form" v-model="valid">
        <v-card>

          <v-card-text class="border-bottom">
            <div class="row pt-1 border-bottom">
              <div class="col-md-12">
                <div class="d-flex justify-space-between align-center mt-2">
                  <SvgIcon
                      class="text-2xl font-semibold"
                      :text="`${
                   voucher.id != null  ? 'Update ' : 'Add '
                  } Gift Card`"
                      style="color: black"
                  >
                  </SvgIcon>
                  <v-btn fab x-small class="shadow-0"   @click="cancelVoucherDialoge">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
            <v-row>
              <v-col md="12">
                <v-row no-gutters justify="center">
                  <v-col md="6"
                    ><image-uploader
                      @upload="
                        (data) => {
                          voucher.image = data;
                        }
                      "
                      @remove="
                        () => {
                          voucher.image = null;
                        }
                      "
                      :image_path="voucher.image_path"
                      :height="160"
                      defaultImage="default"
                      messageText=""

                    ></image-uploader
                  ></v-col>
                </v-row>
              </v-col>
              <v-col cols="12" sm="12" md="12">
                <label for="">Name*</label>
                <v-text-field
                  v-model="voucher.name"
                  outlined
                  background-color="#fff"
                  required
                  :dense="true"
                  hide-details="auto"
                  :rules="[(v) => !!v || 'Name is required']"
                  class="q-text-field shadow-0"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12" md="12">
                <label for="">Description</label>
                <v-textarea
                  v-model="voucher.description"
                  outlined
                  :dense="true"
                  hide-details="auto"
                  background-color="#fff"
                  required
                  rows="3"
                  class="q-text-field shadow-0"
                ></v-textarea>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <label for="">Type*</label>
                <v-select
                  v-model="voucher.voucher_type"
                  :dense="true"
                  hide-details="auto"
                  :items="[
                    { name: 'Custom', value: 'c' },
                    { name: 'With Face Value', value: 'v' },
                  ]"
                  :rules="[(v) => !!v || 'Voucher type is required']"
                  item-text="name"
                  item-value="value"
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  required
                  rows="3"
                  class="q-autocomplete shadow-0"
                ></v-select>
              </v-col>
              <v-col cols="12" sm="6" md="6" v-if="voucher.voucher_type != 'c'">
                <label for="">Price*</label>
                <v-text-field
                  v-model="voucher.price"
                  :dense="true"
                  hide-details="auto"
                  :prefix="currencyCode"
                  outlined
                  background-color="#fff"
                  :rules="[
                    (v) => !!v || 'Package Price is required',
                    (v) => !isNaN(v) || 'Package Price must be Number',
                  ]"
                  required
                  rows="3"
                  class="q-text-field shadow-0"

                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
                class="ma-2" text
              @click="cancelVoucherDialoge"
              >Close</v-btn
            >
            <v-btn
                class="ma-2 white--text blue-color"
              @click="addOrEditVoucher"
              >{{ voucher.id != null ? "Update" : "Add" }}</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>



    <v-dialog v-model="isRfidModalVisible" scrollable max-width="700px">
      <v-card>


        <v-card-text class="">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon
                    class="text-2xl font-semibold"
                    :text="`Scan Gift Card`"
                    style="color: black"
                >
                </SvgIcon>
                <v-btn fab x-small class="shadow-0" @click="isRfidModalVisible = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <v-row dense class="mt-4">
            <v-col cols="12">
              <v-card outlined class="pa-4 ">
                <v-row dense>
                  <v-col cols="6" class="text-subtitle-2 font-weight-medium">Voucher Code:</v-col>
                  <v-col cols="6">{{  rfidCardData && rfidCardData.sales_voucher &&  rfidCardData.sales_voucher.voucher_code || '-' }}</v-col>
                  <v-col cols="6" class="text-subtitle-2 font-weight-medium">Customer Name:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      rfidCardData.sales_voucher.initial_balance &&
                      rfidCardData.sales_voucher.customer &&
                      rfidCardData.sales_voucher.customer.first_name || '---'
                    }}
                  </v-col>

                  <v-col cols="6" class="text-subtitle-2 font-weight-medium">Issue Date:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      formatDate(rfidCardData.sales_voucher.issue_date ) || '-'
                    }}
                  </v-col>

                  <v-col cols="6" class="text-subtitle-2 font-weight-medium">Expiry Date:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      formatDate(rfidCardData.sales_voucher.expiry_date ) || '-'
                    }}
                  </v-col>

                  <v-col cols="6" class="text-subtitle-2 font-weight-medium">Initial Balance:</v-col>
                  <v-col cols="6">
                    AED {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      rfidCardData.sales_voucher.initial_balance  || 0
                    }}
                  </v-col>

                  <v-col cols="6" class="text-subtitle-2 font-weight-medium">Current Balance:</v-col>
                  <v-col cols="6">
                    AED {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      rfidCardData.sales_voucher.current_balance || 0
                    }}
                  </v-col>

                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn  text  @click="isRfidModalVisible = false">
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>




    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
  </v-container>
</template>

<script>
import VoucherWidget from "@/components/Voucher/VoucherWidget";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";
import moment from "moment/moment";
import SvgIcon from "../../components/Image/SvgIcon.vue";
import ImageUploader from "../../components/Image/ImageUploader.vue";
import axios from "axios";

export default {
  components: {
    ImageUploader,
    SvgIcon,
    VoucherWidget,
    ConfirmModel,
  },
  mounted() {
    this.loadVouchers();
  },
  data() {
    return {
      voucher: {},
      addVoucherDialoge: false,
      valid: true,
      page: 1,
      voucherList: [],
      totalPages: 0,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      benefitIds: [],
      btnShow: false,
      toggle_mode: "1",
      isRfidModalVisible:false,
      rfidCardData:{}
    };
  },
  watch: {
    page() {
      this.loadVouchers();
    },
  },

  methods: {
    loadVouchers() {
      this.$http
        .get(
          `venues/vouchers?page=${this.page}&per_page=6&status_id=${this.toggle_mode}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.voucherList = response.data.data;
            this.totalPages = response.data.total_pages;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    populateEditVoucher(id) {
      this.$http
        .get("venues/vouchers/" + id)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.voucher.name = data.name;
            this.voucher.id = data.id;
            this.voucher.description = data.description;
            this.voucher.voucher_type = data.voucher_type;
            this.voucher.price = data.price;
            this.voucher.image = null;
            this.voucher.product_type_id = data.product_type_id;
            this.voucher.image_path = data.image;
            this.addVoucherDialoge = true;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    addVoucherPopup() {
      this.voucher = {};
      this.addVoucherDialoge = true;
      setTimeout(() => {
        this.$refs.form.resetValidation();
      }, 0);
    },
    addOrEditVoucher() {
      if (!this.$refs.form.validate()) {
        this.showError("Please enter all required fields");
        return;
      }
      let formData = new FormData();
      formData.set("name", this.voucher.name);
      formData.set("id", this.voucher.id);
      if (this.voucher.description)
        formData.set("description", this.voucher.description);
      formData.set("voucher_type", this.voucher.voucher_type);
      if (this.voucher.price) {
        if (this.voucher.price <= 0) {
          this.showError("Price should be greater than zero");
          return;
        }
        formData.set("price", this.voucher.price);
      }
      if (this.voucher.product_type_id)
        formData.set("product_type_id", this.voucher.product_type_id);
      if (this.voucher.image) formData.set("image", this.voucher.image);
      this.showLoader();
      this.$http
        .post(
          `venues/vouchers${this.voucher.id ? "/" + this.voucher.id : ""}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data; boundary=${form._boundary}",
            },
          }
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            let message =
              this.voucher.id != null
                ? "Voucher updated successfully"
                : "Voucher added successfully";
            this.showSuccess(message);
            this.loadVouchers();
            this.hideLoader();
            this.addVoucherDialoge = false;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    changeVoucherStatus(id) {
      this.$http
        .delete("venues/vouchers/" + id)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.loadVouchers();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    confirmActions(data) {
      if (data.type == "delete") {
        this.changeVoucherStatus(data.id);
      }
      this.$forceUpdate();
      this.confirmModel.id = null;
    },
    deleteVoucherWarning(data) {
      this.confirmModel = {
        id: data.id,
        title: `Do you want to ${data.type} this Voucher?`,
        description:
          "By clicking <b>Yes</b> you can confirm deleting this Voucher.  Do you need to continue your action ?",
        type: "delete",
      };
    },
    cancelVoucherDialoge() {
      setTimeout(() => {
        this.$refs.form.resetValidation();
      }, 0);
      this.addVoucherDialoge = false;
    },

    scanRfid(){
      this.showLoader('scanning')

      axios
          .get("http://localhost:5000/read")
          .then((response) => {
            this.hideLoader()
            if (response.status == 200) {
              this.$http
                  .get(`venues/vouchers/scanRfid?voucher_code=${response.data.voucher_code}`)
                  .then((response) => {
                    this.hideLoader()
                    if (response.status == 200) {
                      this.rfidCardData = {...response.data.data };
                      this.isRfidModalVisible = true;
                    }
                  })
                  .catch((error) => {
                    this.hideLoader()
                    this.errorChecker(error);
                  });
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error);
          });
    },
    formatDate(arg) {
      return moment(arg).format("Do MMM YYYY");
    }
  },
};
</script>

<style scoped></style>

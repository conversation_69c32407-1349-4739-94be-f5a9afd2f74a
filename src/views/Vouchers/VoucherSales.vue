<template>
  <v-container fluid>
    <v-row class=" p-y-6 mb-5">
      <v-col lg="3" sm="12">
        <BackButton :handler="goToVoucher"/>
      </v-col>
      <v-col lg="6" md="6" sm="12">
        <div class="d-flex justify-center align-center gap-x-6">
          <div style="max-width: 16rem;max-height: 10rem">
            <view-image :image="voucherDetails.image ? voucherDetails.image : ''"  :width="165" :height="165" class="rounded-lg" defaultImage="user"/>
          </div>
          <div class="mt-2">
            <h3 class="text-xl m-b-4">
              {{ voucherDetails.name }}
            </h3>
            <div class="d-flex gap-x-8 ">
              <div class="stats_data">
                <p class="text-dark-gray text-base font-normal">Total Issues</p>
                <p  class="text-blue font-semibold">{{ Number(voucherDetails.sales) | numberFormatter }}</p>
              </div>
              <div class="stats_data" >
                <p class="text-dark-gray text-base font-normal">Pending Issues</p>
                <p  class="text-blue font-semibold">{{ Number(voucherDetails.pending) | numberFormatter }}</p>
              </div>

              <div class="stats_data">
                <p class="text-dark-gray text-base font-normal">Sales</p>
                <p  class="text-blue font-semibold">{{ Number(voucherDetails.revenue) | toCurrency }}</p>
              </div>
            </div>
          </div>
        </div>

      </v-col>

    </v-row>
    <v-card flat rounded class="shadow-0" height="auto" width="100%">
      <div class="text-center p-4">
        {{ voucherDetails.description }}
      </div>

    </v-card>

    <div class="md-card md-theme-default mt-8 shadow rounded-5">
      <div class="md-card-content md-card-table">
        <div>
          <div class="d-flex justify-space-between align-center">
            <SvgIcon class="text-2xl font-semibold" text="Card Issues"> </SvgIcon>
            <v-spacer />
            <v-select
                v-model="perPage"
                :items="[10, 15, 25, 50]"
                :menu-props="{ bottom: true, offsetY: true }"
                class="q-autocomplete shadow-0 m-r-3 mt-2"
                hide-details
                dense
                outlined
                @change="getVoucherSales"
                placeholder="Per Page"
                style="max-width: 100px"
            ></v-select>


            <v-autocomplete
                v-model="searchParams.time_intervel"
                :items="timeDuration"
                class="q-autocomplete shadow-0 m-r-3 mt-2"
                hide-details
                dense
                item-text="title"
                item-value="name"
                outlined
                placeholder="Time Period"
                style="max-width: 200px"
                @change="changeFxn"
                height="20"
            >
              <template v-slot:prepend-inner>
                <SvgIcon>
                  <template v-slot:icon>
                    <EventIcon />
                  </template>
                </SvgIcon>
              </template>
            </v-autocomplete>

            <div v-if="searchParams.time_intervel == 'custom'" class="d-flex align-center gap-x-4 mt-2">
              <v-menu
                  v-model="menu1"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  filled
                  min-width="290px"
                  offset-y
                  transition="scale-transition"
              >
                <template v-slot:activator="{ on }">
                  <v-text-field
                      v-model="date1Formatted"
                      class="shadow-0 q-text-field"
                      dense
                      flat
                      hide-details
                      outlined
                      readonly
                      style="max-width: 180px !important"
                      v-on="on"
                  ></v-text-field>
                </template>
                <v-date-picker
                    v-model="searchParams.start_date"
                    @input="menu1 = false"
                ></v-date-picker>
              </v-menu>
              <v-menu
                  v-model="menu2"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  min-width="290px"
                  offset-y
                  transition="scale-transition"
              >
                <template v-slot:activator="{ on }">
                  <v-text-field
                      v-model="date2Formatted"
                      class="shadow-0 q-text-field"
                      dense
                      flat
                      hide-details
                      outlined
                      readonly
                      v-on="on"
                      style="max-width: 180px !important"
                  ></v-text-field>
                </template>
                <v-date-picker
                    v-model="searchParams.end_date"
                    @input="menu2 = false"
                ></v-date-picker>
              </v-menu>
              <v-btn
                  class="mr-2 bg-blue text-white"
                  height="40"
                  elevation="0"
                  @click="getVoucherSales"
              >
                Go
              </v-btn>
            </div>

            <v-btn
                v-if="checkExportPermission($modules.vouchers.issues.slug)"
                class="export-button mt-2"
                elevation="0"
                height="40"
                @click="exportData"
            >
              <SvgIcon text="Export">
                <template v-slot:icon>
                  <ExportIcon />
                </template>
              </SvgIcon>
            </v-btn>
            <v-btn
                v-if="
                        checkWritePermission($modules.vouchers.issues.slug) &&
                        voucherDetails.status_id == 1
                      "
                @click="addCardCustomer"
                dark
                style="background-color: rgba(17, 42, 70, 0.1) ; color:#112A46 "
                class="mt-2 ml-2  text_capitalize "
                outlined
            >
              Card
              <v-icon class="pl-2" right dark
              >mdi-credit-card-plus</v-icon
              >
            </v-btn>
          </div>
          <br /><br />
          <div class="table-responsive">
            <table class="table border-collapse">
              <thead >
              <tr class="opacity-70 tr-neon tr-rounded">
                <th >
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label cursor-pointer">
                      Timestamp
                    </div>
                    <div class="search_column">
                      <date-menu
                          v-model="searchParams.timestamp"
                          @change="getVoucherSales()"
                      >
                      </date-menu>
                    </div>
                  </div>
                </th>
                <th>
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label cursor-pointer">
                      Issue Date
                    </div>
                    <div class="search_column">
                      <date-menu
                          v-model="searchParams.issue_date"
                          @change="getVoucherSales()"
                      >
                      </date-menu>
                    </div>
                  </div>
                </th>
                <th>
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label cursor-pointer">
                      Expiry Date
                    </div>
                    <div class="search_column">
                      <date-menu
                          v-model="searchParams.expiry_date"
                          @change="getVoucherSales()"
                      >
                      </date-menu>
                    </div>
                  </div>
                </th>
                <th >
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label">Customer</div>
                    <div class="search_column">
                      <v-text-field
                          label="Name"
                          clearable
                          solo
                          v-model="searchParams.customer"
                          append-icon="mdi-account-search"
                          @click:append="getVoucherSales()"
                          @click:clear="
                                    () => {
                                      searchParams.customer = null;
                                      getVoucherSales();
                                    }
                                  "
                      ></v-text-field>
                    </div>
                  </div>
                </th>
                <th style="position: relative">
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label">
                      Voucher Code
                    </div>
                    <div class="search_column">
                      <v-text-field
                          solo
                          label="Voucher Code"
                          clearable
                          v-model="searchParams.voucher_code"
                          append-icon="mdi-credit-card-search"
                          @click:append="getVoucherSales()"
                          @click:clear="
                                    () => {
                                      searchParams.voucher_code = null;
                                      getVoucherSales();
                                    }
                                  "
                      ></v-text-field>
                    </div>
                  </div>
                </th>
                <th  style="position: relative">
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label">Status</div>
                    <div class="search_column">
                      <v-select
                          solo
                          :items="[
                                    { name: 'All', status: null },
                                    { name: 'Active', status: 1 },
                                    { name: 'Unpaid', status: 5 },
                                    { name: 'Expired', status: 10 },
                                  ]"
                          item-text="name"
                          label="Status"
                          v-model="searchParams.status_id"
                          item-value="status"
                          @change="getVoucherSales()"
                      ></v-select>
                    </div>
                  </div>
                </th>

                <th >
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label text-center">
                      Initial Balance
                    </div>
                    <div class="search_column">
                      <v-menu
                          v-model="initialMenu"
                          :close-on-content-click="false"
                          max-width="180"
                      >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field
                              label="Initial Balance"
                              solo
                              v-bind="attrs"
                              v-on="on"
                              max-width="180"
                          >
                          </v-text-field>
                        </template>
                        <v-card width="180">
                          <v-card-text>
                            <v-text-field
                                dense
                                v-model="searchParams.initial_from"
                                label="Initial From"
                            ></v-text-field>
                            <v-text-field
                                dense
                                v-model="searchParams.initial_to"
                                label="Initial To"
                            ></v-text-field>
                          </v-card-text>
                          <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                                text
                                @click="
                                          () => {
                                            searchParams.initial_to = null;
                                            searchParams.initial_from = null;
                                            initialMenu = false;
                                            getVoucherSales();
                                          }
                                        "
                            >Clear</v-btn
                            >
                            <v-btn
                                color="primary"
                                text
                                @click="
                                          () => {
                                            initialMenu = false;
                                            getVoucherSales();
                                          }
                                        "
                            >Filter</v-btn
                            >
                          </v-card-actions>
                        </v-card>
                      </v-menu>
                    </div>
                  </div>
                </th>
                <th >
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label text-center">
                      Current Balance
                    </div>
                    <div class="search_column">
                      <v-menu
                          v-model="balanceMenu"
                          :close-on-content-click="false"
                          max-width="180"
                      >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field
                              label="Current Balance"
                              solo
                              v-bind="attrs"
                              v-on="on"
                          >
                          </v-text-field>
                        </template>
                        <v-card width="180">
                          <v-card-text>
                            <v-text-field
                                dense
                                v-model="searchParams.balance_from"
                                label="Current From"
                            ></v-text-field>
                            <v-text-field
                                dense
                                v-model="searchParams.balance_to"
                                label="Current To"
                            ></v-text-field>
                          </v-card-text>
                          <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                                text
                                @click="
                                          () => {
                                            searchParams.balance_from = null;
                                            searchParams.balance_to = null;
                                            balanceMenu = false;
                                            getVoucherSales();
                                          }
                                        "
                            >Clear</v-btn
                            >
                            <v-btn
                                color="primary"
                                text
                                @click="
                                          () => {
                                            balanceMenu = false;
                                            getVoucherSales();
                                          }
                                        "
                            >Filter</v-btn
                            >
                          </v-card-actions>
                        </v-card>
                      </v-menu>
                    </div>
                  </div>
                </th>
                <th >
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label">Receipt</div>
                  </div>
                </th>

                <th >
                  <div
                      class="
                                md-table-head-container md-ripple md-disabled
                              "
                  >
                    <div class="md-table-head-label">CARD</div>
                  </div>
                </th>
              </tr>
              </thead>

              <tbody>
              <tr
                  class="md-table-row"
                  v-for="data in voucherIssues"
                  :key="data.id"
              >
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    {{ data.created | timeStamp }}
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    <div class="user_date" v-if="data.issue_date">
                      {{ data.issue_date | dayFormat }}
                    </div>
                    <div class="user_date" v-else>NA</div>
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    <div class="user_date" v-if="data.expiry_date">
                      {{ data.expiry_date | dayFormat }}
                    </div>
                    <div
                        class="user_date"
                        v-else-if="data.issue_date"
                    >
                      Open
                    </div>
                    <div class="user_date" v-else>NA</div>
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    <router-link
                        v-if="
                                  checkReadPermission(
                                    $modules.clients.customers.id
                                  ) && data.customer_id
                                "
                        :to="`/clients/customers/` + data.customer_id"
                    >{{ data.first_name }}
                      {{ data.last_name }}</router-link
                    >
                    <span v-else-if="data.customer_id"
                    >{{ data.first_name }}
                                {{ data.last_name }}</span
                    >
                    <span v-else>Anonymous</span>
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    <v-btn
                        small
                        color="#00b0af"
                        dark
                        v-if="data.voucher_code == null"
                        @click="issueCard(data)"
                    >Issue</v-btn
                    >
                    <span v-else>{{ data.voucher_code }}</span>
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    {{ data.status }}
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    {{ Number(data.initial_balance) | toCurrency }}
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    {{ Number(data.current_balance) | toCurrency }}
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    <v-btn
                        normal
                        small
                        @click="getOrderDetails(data.order_id)"
                    >{{
                        data.status == "Unpaid" ? "Pay" : "Receipt"
                      }}</v-btn
                    >
                  </div>
                </td>
                <td class="md-table-cell">
                  <div class="md-table-cell-container">
                    <v-btn
                        normal
                        small
                        @click="scanRfid(data)"
                    >READ</v-btn>
                  </div>
                </td>
              </tr>
              </tbody>

            </table>
          </div>
        </div>
      </div>
    </div>

    <v-row>
      <v-col cols="4"></v-col>
      <v-col cols="4">
        <v-pagination
            v-model="page"
            :length="totalPages"
            class="new-pagination"
            total-visible="7"
        ></v-pagination>
      </v-col>
      <v-col class="d-flex align-center justify-end" cols="4">
        <div class="d-flex align-center justify-end text-sm gap-x-2">
          <span class=""> Result </span>
          <div style="width: 80px">
            <v-select
                v-model="perPage"
                :items="[10, 15, 25, 50]"
                :menu-props="{ bottom: true, offsetY: true }"
                class="q-autocomplete text-sm"
                flat
                hide-details
                solo
                @change="getVoucherSales"
            ></v-select>
          </div>
          <span>Per Page</span>
        </div>
      </v-col>
    </v-row>

    <v-dialog v-model="purchaseModel" scrollable width="50%">
      <v-form ref="purchase_form" v-model="valid">
        <v-card>
          <v-card-text >
            <div class="row pt-1 border-bottom">
              <div class="col-md-12">
                <div class="d-flex justify-space-between align-center mt-2">
                  <SvgIcon
                      class="text-2xl font-semibold"
                      :text="`Card Purchase`"
                      style="color: black"
                  >
                  </SvgIcon>

                  <div class="d-flex justify-space-between gap-x-2">
                    <div class="d-flex justify-space-between gap-x-4" v-if="!orderId">
                      <card-data-button
                          label="HID Omnikey"
                          @data="
                    (data) => {
                      setCardData(data);
                    }
                  "
                      ></card-data-button>
                      <card-reader-button
                          label="Samsotech Reader"
                          @data="
                    (data) => {
                      setCardData(data);
                    }
                  "
                      ></card-reader-button>
                    </div>
                    <v-btn fab x-small class="shadow-0"  @click="cancelVoucherSale" >
                      <v-icon>mdi-close</v-icon>
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>



            <v-row dense justify="center" class="mt-4">
              <v-col md="6">
                <image-uploader
                    @upload="
                      (data) => {
                        customer.profile_image = data;
                      }
                    "
                    @remove="
                      () => {
                        customer.profile_image = null;
                      }
                    "
                    ref="image_upload"
                    :height="150"
                    :image_path="customer.image_path"
                    defaultImage="default"
                    messageText=""
                ></image-uploader>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12" sm="6" md="6">
                <label for="">Mobile No*</label>
                <v-mobile-search
                    :selected="customer.mobile"
                    v-model="customer.search"
                    @updateCustomer="setCustomerData"
                    :show-label="false"
                    background-color=""
                    class-name1="q-text-field shadow-0"
                    hide-details="auto"
                    :dense="true"
                    label=""
                    outlined
                ></v-mobile-search>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <label for="">Name*</label>
                <v-name-search
                    outlined
                    :selected="customer.name"
                    :mobile="customer.mobile"
                    :email="customer.email"
                    @updateCustomer="setCustomerData"
                    readonly
                    background-color=""
                    class-name="q-text-field shadow-0"
                    hide-details="auto"
                    label=""
                    :dense="true"
                    v-model="customer.nameSearch"
                ></v-name-search>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <label for="">Email</label>
                <v-text-field
                    v-model="customer.email"
                    background-color="#fff"
                    :readonly="existingUser"
                    required
                    :rules="[
                      (v) => {
                        if (v) {
                          return /.+@.+\..+/.test(v);
                        }
                        return true;
                      },
                    ]"
                    outlined
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                ></v-text-field>
              </v-col>
              <v-col md="6">
                <label for="">Date Of Birth</label>
                <date-of-birth v-model="customer.dob"
                               class-name="q-text-field shadow-0 add-on"
                               hide-details="auto"
                               label=""
                               :dense="true"

                ></date-of-birth>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <label for="">Nationality</label>
                <v-autocomplete
                    v-model="customer.country_id"
                    :items="countries"
                    item-value="id"
                    item-text="name"
                    outlined
                    background-color="#fff"
                    label=""
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                    validate-on-blur
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <label for="">Gender</label>
                <v-select
                    v-model="customer.gender"
                    :items="['Male', 'Female']"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    label=""
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                    validate-on-blur
                ></v-select>
              </v-col>
              <v-col md="6">
                <label for="">Issue Date</label>
                <date-field
                    v-model="customer.issue_date"
                    :backFill="
                      checkBackfillPermission($modules.vouchers.issues.slug)
                    "
                    class-name="q-text-field shadow-0"
                    :dense="true"
                    :hide-details="true"
                    label=""
                >
                </date-field>
              </v-col>
              <v-col md="6">
                <label for="">Expiry Date</label>
                <date-field
                    v-model="customer.expiry_date"
                    :backFill="
                      checkBackfillPermission($modules.vouchers.issues.slug)
                    "
                    class-name="q-text-field shadow-0"
                    :dense="true"
                    :hide-details="true"
                    label=""
                >
                </date-field>
              </v-col>

              <v-col md="6">
                <label for="">Voucher Code</label>
                <v-text-field
                    v-model="customer.voucher_code"
                    outlined
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                >
                </v-text-field>
              </v-col>
              <v-col cols="6">
                <label for="">Purchase From Service*</label>

                <v-select
                    :items="venueServices"
                    item-text="name"
                    item-value="venue_service_id"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Please select service']"
                    v-model="customer.venue_service_id"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                ></v-select>
              </v-col>
              <v-col cols="6">
                <label for="">Card Type*</label>
                <v-select
                    :items="[voucherDetails]"
                    item-text="name"
                    item-value="id"
                    :rules="[(v) => !!v || 'Please select card type']"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    v-model="customer.voucher_id"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                ></v-select>
              </v-col>
              <v-col cols="6">
                <label for="">Price*</label>
                <v-text-field
                    :prefix="currencyCode"
                    :rules="[(v) => !!v || 'Price is required']"
                    :readonly="
                      voucherDetails.voucher_type == 'p' ||
                      customer.voucher_sales_id != null
                    "
                    v-model="customer.initial_balance"
                    outlined
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              class="ma-2 " text
              @click="cancelVoucherSale"
              >Close</v-btn
            >

            <v-btn   class="ma-2 white--text blue-color" @click="sellVoucher">
              Save</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>

    <order-details
      :id="orderId"
      @close="orderId = null"
      @paymentDone="getVoucherSales"
    ></order-details>


    <v-dialog v-model="isRfidModalVisible" scrollable width="60%">
      <v-card>
        <v-card-text class="">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon
                    class="text-2xl font-semibold"
                    :text="`Scan Gift Card`"
                    style="color: black"
                >
                </SvgIcon>
                <v-btn fab x-small class="shadow-0" @click="isRfidModalVisible = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <v-row dense class="mt-4">
            <!-- Current Data Section -->
            <v-col cols="12" md="6">
              <v-card outlined class="pa-4 mb-4">
                <div class="text-h6 text-center font-weight-bold mb-4">Card Data</div>

                <!-- Card Info -->
                <v-row dense>
                  <v-col cols="6" class="font-weight-medium">Voucher Code:</v-col>
                  <v-col cols="6">{{rfidCardData && rfidCardData.sales_voucher && rfidCardData.sales_voucher.voucher_code|| '-' }}</v-col>

                  <v-col cols="6" class="font-weight-medium">Customer Name:</v-col>
                  <v-col cols="6">{{ rfidCardData && rfidCardData.sales_voucher && rfidCardData.sales_voucher.customer  &&  rfidCardData.sales_voucher.customer.first_name  || '---' }}</v-col>

                  <v-col cols="6" class="font-weight-medium">Issue Date:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      formatDate( rfidCardData.sales_voucher.issue_date) || '-'
                    }}
                  </v-col>

                  <v-col cols="6" class="font-weight-medium">Expiry Date:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      formatDate( rfidCardData.sales_voucher.expiry_date) || '-'
                    }}
                  </v-col>

                  <v-col cols="6" class="font-weight-medium">Initial Balance:</v-col>
                  <v-col cols="6">
                    AED {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      rfidCardData.sales_voucher.initial_balance || 0
                    }}
                  </v-col>

                  <v-col cols="6" class="font-weight-medium">Current Balance:</v-col>
                  <v-col cols="6">
                    AED  {{
                      rfidCardData && rfidCardData.sales_voucher &&
                      rfidCardData.sales_voucher.current_balance || 0
                    }}
                  </v-col>

                </v-row>
              </v-card>
            </v-col>

            <!-- New Data Section -->
            <v-col cols="12" md="6">
              <v-card outlined class="pa-4 mb-4">
                <div class="text-h6 text-center font-weight-bold mb-4">New Data</div>

                <!-- Card Info -->
                <v-row dense>
                  <v-col cols="6" class="font-weight-medium">Voucher Code:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.new_voucher &&
                      rfidCardData.new_voucher.voucher_code
                    }}
                  </v-col>

                  <v-col cols="6" class="font-weight-medium">Customer Name:</v-col>
                  <v-col cols="6">{{ rfidCardData && rfidCardData.new_voucher && rfidCardData.new_voucher.first_name || '-' }}</v-col>


                  <v-col cols="6" class="font-weight-medium">Issue Date:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.new_voucher &&
                      formatDate(rfidCardData.new_voucher.issue_date) || '-'
                    }}
                  </v-col>

                  <v-col cols="6" class="font-weight-medium">Expiry Date:</v-col>
                  <v-col cols="6">
                    {{
                      rfidCardData && rfidCardData.new_voucher &&
                      formatDate(rfidCardData.new_voucher.expiry_date) || '-'
                    }}
                  </v-col>


                  <v-col cols="6" class="font-weight-medium">Initial Balance:</v-col>
                  <v-col cols="6">
                    AED {{
                      rfidCardData && rfidCardData.new_voucher &&
                      rfidCardData.new_voucher.initial_balance || 0
                    }}
                  </v-col>

                  <v-col cols="6" class="font-weight-medium">Current Balance:</v-col>
                  <v-col cols="6">
                    AED {{
                      rfidCardData && rfidCardData.new_voucher &&
                      rfidCardData.new_voucher.current_balance || 0
                    }}
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2" text @click="isRfidModalVisible = false">
            Close
          </v-btn>
          <v-btn class="ma-2 white--text blue-color" @click="writeToRFID">
            Update  Card
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>


  </v-container>
</template>

<script>
import moment from "moment";
import OrderDetails from "../../components/Order/OrderDetails.vue";
import SvgIcon from "../../components/Image/SvgIcon.vue";
import BackButton from "../../components/Common/BackButton.vue";
import ImageUploader from "../../components/Image/ImageUploader.vue";
import ExportIcon from "@/assets/images/misc/export-icon.svg";
import EventIcon from "@/assets/images/misc/calendar.svg";
import axios from "axios";

export default {
  components: {
    ImageUploader,
    BackButton,
    SvgIcon,
    OrderDetails,
    ExportIcon,
    EventIcon
  },
  data() {
    return {
      menu1: false,
      menu2: false,
      voucherIssues: [],
      searchParams: {
        start_date: moment().subtract(30, "days").format("YYYY-MM-DD"),
        end_date: moment().format("YYYY-MM-DD"),
      },
      timeDuration: [
        { name: "All", title: "All" },
        { name: "week", title: "This Week" },
        { name: "year", title: "This Year" },
        { name: "month", title: "This Month" },
        { name: "custom", title: "Custom Duration" },
      ],
      page: 1,
      totalPages: 1,
      perPage: null,
      receiptData: { id: null },
      customer: { dob: null, voucher_id: null, price: null },
      existingUser: false,
      purchaseModel: false,
      valid: false,
      voucherDetails: {},
      balanceMenu: false,
      initialMenu: false,
      pay_dialog: false,
      voucherId: null,
      orderId: null,
      isEmiratesIdCheck: false,
      isRfidModalVisible:false,
      rfidCardData:{}
    };
  },
  mounted() {
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$route.params.data != null) {
      this.voucherId = atob(this.$route.params.data);
      this.getVoucherDetails();
    }
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
  },
  watch: {
    page() {
      this.getVoucherSales();
    },
  },
  computed: {
    countries() {
      return this.$store.getters.getCountries.data;
    },
    venueServices() {
      return this.$store.getters.getVenueServices.data;
    },
    date1Formatted() {
      return moment(this.searchParams.start_date, "YYYY-MM-DD").format(
          "Do MMM YYYY"
      );
    },
    date2Formatted() {
      return moment(this.searchParams.end_date, "YYYY-MM-DD").format(
          "Do MMM YYYY"
      );
    },
  },
  methods: {
    setCardData(data) {
      this.setCustomerData(data);
      // if (!data.customer_id) {
      //   this.$set(this.customer, "customer_id", null);
      // }

      // if (!data.name && data.first_name) {
      //   this.$set(this.customer, "name", data.first_name);
      // }

      // if (!data.customer_id && this.customer.name != data.name) {
      //   this.$set(this.customer, "mobile", null);
      //   this.customer.search = null;
      //   this.customer.nameSearch = null;
      //   this.$set(this.customer, "email", null);
      //   this.$set(this.customer, "gender", null);
      //   this.$set(this.customer, "name", null);
      //   this.$set(this.customer, "customer_id", null);
      //   this.$set(this.customer, "first_name", null);
      //   this.$set(this.customer, "image_path", null);
      //   this.$set(this.customer, "dob", null);
      //   this.$set(this.customer, "country_id", null);
      //   this.$set(this.customer, "last_name", null);
      //   this.$set(this.customer, "opt_marketing", false);
      //   this.$forceUpdate();
      // }

      // if (data.mobile) {
      //   this.isEmiratesIdCheck = true;
      //   this.$set(this.customer, "mobile", data.mobile);
      // }
      // if (data.email) this.$set(this.customer, "email", data.email);
      // if (data.country_id) {
      //   this.$set(this.customer, "country_id", data.country_id);
      // } else {
      //   this.$set(this.customer, "country_id", null);
      // }
      // if (data.country_id) {
      //   this.$set(this.customer, "id_proof_type_id", data.id_proof_type_id);
      // }

      // if (data.id_proof_number) {
      //   this.$set(this.customer, "id_proof_number", data.id_proof_number);
      // }

      // if (data.gender) {
      //   this.$set(this.customer, "gender", data.gender);
      // } else {
      //   this.$set(this.customer, "gender", null);
      // }
      // if (data.dob) {
      //   this.$set(this.customer, "dob", data.dob);
      // } else {
      //   this.$set(this.customer, "dob", null);
      // }

      // if (data.image) {
      //   this.$set(this.customer, "image", data.image);
      // }

      // if (data.name) this.$set(this.customer, "name", data.name);
      // if (data.last_name) {
      //   this.$set(this.customer, "last_name", data.last_name);
      // } else {
      //   this.$set(this.customer, "last_name", null);
      // }
      // if (data.first_name)
      //   this.$set(this.customer, "first_name", data.first_name);
      // if (data.customer_id)
      //   this.$set(this.customer, "customer_id", data.customer_id);
      // if (data.image_path) {
      //   this.$set(this.customer, "image_path", data.image_path);
      // } else {
      //   this.$set(this.customer, "image_path", null);
      // }
      // if (data.opt_marketing) {
      //   if (data.opt_marketing == 1) {
      //     this.$set(this.customer, "opt_marketing", true);
      //   } else {
      //     this.$set(this.customer, "opt_marketing", false);
      //   }
      // }
      // this.$forceUpdate();
    },
    setCustomerData(data) {
      if (data.isEmiratesIdCheck) {
        this.isEmiratesIdCheck = true;
      }
      if (data.mobile && data.first_name && data.customer_id) {
        this.isEmiratesIdCheck = false;
      }
      if (!data.customer_id) {
        this.$set(this.customer, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.customer, "name", data.first_name);
      }

      if (
        this.customer.customer_id &&
        !data.customer_id &&
        this.customer.name != data.name &&
        this.customer.mobile != data.mobile
      ) {
        this.$set(this.customer, "mobile", null);
        this.customer.search = null;
        this.customer.nameSearch = null;
        this.$set(this.customer, "email", null);
        this.$set(this.customer, "gender", null);
        this.$set(this.customer, "name", null);
        this.$set(this.customer, "customer_id", null);
        this.$set(this.customer, "first_name", null);
        this.$set(this.customer, "image_path", null);
        this.$set(this.customer, "dob", null);
        this.$set(this.customer, "country_id", null);
        this.$set(this.customer, "last_name", null);
        this.$forceUpdate();
      }

      if (data.mobile) this.$set(this.customer, "mobile", data.mobile);
      if (data.email) this.$set(this.customer, "email", data.email);
      if (data.country_id) {
        this.$set(this.customer, "country_id", data.country_id);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.customer, "country_id", null);
        }
      }
      if (data.gender) {
        this.$set(this.customer, "gender", data.gender);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.customer, "gender", null);
        }
      }
      if (data.dob) {
        this.$set(this.customer, "dob", data.dob);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.customer, "dob", null);
        }
      }
      if (data.name) this.$set(this.customer, "name", data.name);
      if (data.last_name) {
        this.$set(this.customer, "last_name", data.last_name);
      } else {
        this.$set(this.customer, "last_name", null);
      }
      if (data.first_name)
        this.$set(this.customer, "first_name", data.first_name);

      if (data.customer_id)
        this.$set(this.customer, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.customer, "image_path", data.image_path);
      } else {
        this.$set(this.customer, "image_path", null);
      }
      //this.$refs.form.resetValidation();
      this.$forceUpdate();
    },
    addCardCustomer() {
      this.customer = {};
      this.customer.voucher_id = this.voucherDetails.id;
      this.customer.initial_balance =
        this.voucherDetails.voucher_type == "c"
          ? null
          : parseFloat(this.voucherDetails.price);
      this.purchaseModel = true;
      setTimeout(() => {
        this.$refs["image_upload"].cancel();
        this.$refs.purchase_form.resetValidation();
      });
    },
    getVoucherDetails() {
      this.$http
        .get(`venues/vouchers/${this.voucherId}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.voucherDetails = response.data.data;
            this.getVoucherSales();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getVoucherSales() {
      let url = this.getFilterUrl();
      this.$http
        .get(
          `venues/vouchers/sales?voucher_id=${this.voucherId}&page=${
            this.page
          }&per_page=${this.perPage != null ? this.perPage : 10}${url}`
        )
        .then((response) => {
          if (response.status == 200) {
            this.voucherIssues = response.data.data;
            this.totalPages = response.data.total_pages;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    sellVoucher() {
      if (!this.$refs.purchase_form.validate()) {
        this.showError("Please enter all required fields");
        return;
      }
      if (this.customer.initial_balance <= 0) {
        this.showError("Initial balance should be greater than zero");
        return;
      }
      let formData = new FormData();
      Object.keys(this.customer).forEach((key) => {
        let value = this.customer[key];
        if (value != null) {
          formData.set(key, value);
        }
      });
      this.showLoader();
      this.$http
        .post(`venues/vouchers/sales`, formData, {
          headers: {
            "Content-Type": "multipart/form-data; boundary=${form._boundary}",
          },
        })
        .then((response) => {
          if (response.status == 200) {
            this.purchaseModel = false;
            this.hideLoader();
            this.getVoucherSales();
            this.orderId = response.data.data.order_id;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getFilterUrl() {
      let url = "";
      if (
        this.searchParams.time_intervel != "All" &&
        this.searchParams.time_intervel != null
      ) {
        if (this.searchParams.time_intervel == "week") {
          this.searchParams.start_date = moment()
            .startOf("week")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "month") {
          this.searchParams.start_date = moment()
            .startOf("month")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "year") {
          this.searchParams.start_date = moment()
            .startOf("year")
            .format("YYYY-MM-DD");
        }
        url += "&end_date=" + this.searchParams.end_date;
        url += "&start_date=" + this.searchParams.start_date;
      }
      if (this.searchParams.timestamp != null) {
        url += "&timestamp=" + this.searchParams.timestamp;
      }
      if (this.searchParams.issue_date != null) {
        url += "&issue_date=" + this.searchParams.issue_date;
      }
      if (this.searchParams.expiry_date != null) {
        url += "&expiry_date=" + this.searchParams.expiry_date;
      }
      if (this.searchParams.initial_from != null) {
        url += "&initial_from=" + this.searchParams.initial_from;
      }
      if (this.searchParams.initial_to != null) {
        url += "&initial_to=" + this.searchParams.initial_to;
      }
      if (this.searchParams.balance_from != null) {
        url += "&balance_from=" + this.searchParams.balance_from;
      }
      if (this.searchParams.balance_to != null) {
        url += "&balance_to=" + this.searchParams.balance_to;
      }
      if (this.searchParams.status_id != null) {
        url += "&status_id=" + this.searchParams.status_id;
      }
      if (this.searchParams.voucher_code != null) {
        url += "&voucher_code=" + this.searchParams.voucher_code;
      }
      if (this.searchParams.customer != null) {
        url += "&customer=" + this.searchParams.customer;
      }
      return url;
    },
    getOrderDetails(id) {
      this.orderId = id;
    },
    issueCard(data) {
      this.customer.issue_date = moment().format("YYYY-MM-DD");
      this.customer.voucher_sales_id = data.voucher_sales_id;
      this.customer.venue_service_id = data.venue_service_id;
      this.customer.voucher_id = this.voucherDetails.id;
      this.customer.initial_balance = parseFloat(data.initial_balance);
      this.customer.image_path = data.profile_image;
      this.customer.customer_id = data.customer_id;
      this.$set(this.customer, "mobile", data.mobile);
      this.$set(this.customer, "email", data.email);
      this.$set(this.customer, "country_id", data.country_id);
      this.$set(this.customer, "gender", data.gender);
      this.$set(this.customer, "dob", data.dob);
      this.$set(
        this.customer,
        "name",
        `${data.first_name} ${data.last_name || ""}`
      );
      this.$set(this.customer, "last_name", data.last_name);
      this.$set(this.customer, "first_name", data.first_name);
      this.purchaseModel = true;
    },
    cancelVoucherSale() {
      this.purchaseModel = false;
    },
    exportData() {
      let url = this.getFilterUrl();
      this.$http
        .get(`venues/vouchers/export?voucher_id=${this.voucherId}${url}`, {
          responseType: "blob",
        })
        .then((response) => {
          if (response.status == 200) {
            this.downloadFile(response, "Voucher_Export_");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    goToVoucher() {
      this.$router.push({ name: "Vouchers" }, () => {});
    },
    dateChange(data) {
      this.searchParams.start_date = data.date1;
      this.searchParams.end_date = data.date2;
      this.getVoucherSales();
    },

    changeFxn() {
      this.searchParams.end_date = moment().format("YYYY-MM-DD");
      if (this.searchParams.time_intervel == "custom") {
        this.searchParams.start_date = moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD");
      } else {
        this.getVoucherSales();
      }
    },

    scanRfid(data){
      this.showLoader('scanning')
      axios
          .get("http://localhost:5000/read")
          .then((response) => {
            this.hideLoader()
            if (response.status == 200) {
              this.$http
                  .get(`venues/vouchers/scanRfid?voucher_code=${response.data.voucher_code}`)
                  .then((response) => {
                    this.hideLoader()
                    if (response.status == 200) {
                      this.rfidCardData = {...response.data.data , new_voucher:data};
                      this.isRfidModalVisible = true;
                    }
                  })
                  .catch((error) => {
                    this.hideLoader()
                    this.errorChecker(error);
                  });
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error);
          });
    },

    scanRfidssss(data){
      this.showLoader('scanning')
      this.$http
          .get("venues/vouchers/scanRfid")
          .then((response) => {
            this.hideLoader()
            if (response.status == 200) {
              this.rfidCardData = {...response.data.data , new_voucher:data};
              console.log(`this.da`,this.rfidCardData.new_voucher)
              this.isRfidModalVisible = true;
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error);
          });
    },
    writeToRFID(){
      this.showLoader()
      axios
          .post("http://localhost:5000/write",{voucher_code:this.rfidCardData.new_voucher.voucher_code, customer_id:`${this.rfidCardData.new_voucher.customer_id}`})
          .then((response) => {
            this.hideLoader()
            if (response.status == 200) {
              this.isRfidModalVisible = false;
              this.rfidCardData = {}
              this.showSuccess('data written successfully');
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error);
          });
    },
    formatDate(arg) {
      return moment(arg).format("Do MMM YYYY");
    }
  },
};
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
tbody tr:hover {
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -webkit-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -moz-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  z-index: 1;
}
</style>

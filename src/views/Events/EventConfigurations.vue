<template>
  <v-container>
    <v-row class="bg-ghost-white p-y-6">
      <v-col lg="3" sm="12">
        <BackButton :handler="goToEvents" />
      </v-col>
    </v-row>
    <v-row class="mt-0">
      <v-col cols="12" lg="12" md="12">
        <div class="text-base font-semibold black-text ml-1 mt-6">
          Event Booking Configurations
        </div>
        <v-card class="mb-8 rounded-2 bordered shadow-0 mt-2">
          <v-card-text>
            <v-col cols="12" lg="3" md="4" sm="6" class="mb-4">
              <v-switch
                v-model="configuration.enable_tag_restriction"
                :false-value="0"
                :true-value="1"
                class="mx-4"
                dense
                hide-details="auto"
                label="Participant Tag Restriction"
                required
              ></v-switch>
            </v-col>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-card class="rounded-2 shadow-0 bordered my-8">
      <v-card-text>
        <v-row dense>
          <v-col cols="12">
            <div class="d-flex justify-space-between align-center">
              <h3 class="text-base font-semibold black-text">
                Website Terms & Conditions
              </h3>
              <v-switch
                v-model="configuration.tnc_enable"
                dense
                :false-value="0"
                hide-details="auto"
                required
                :true-value="1"
              ></v-switch>
            </div>
          </v-col>
        </v-row>
        <v-row v-if="configuration.tnc_enable == 1" dense>
          <v-col cols="12">
            <!--                <text-editor-->
            <!--                    :message="configurations.venue_service_configuration.tnc"-->
            <!--                    style="width: 100%"-->
            <!--                    @complete="setTncContent"/>-->
            <RichEditor v-model="configuration.tnc" />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-row class="mt-0">
      <v-col cols="12" lg="12" md="12">
        <v-form ref="cform">
          <div class="text-base font-semibold black-text ml-1 mb-2">
            Quick Scan Tags
          </div>
          <v-row>
            <v-col
              v-for="(code, cIndex) in colorCodes"
              :key="`index${cIndex}`"
              cols="12"
              lg="4"
              md="6"
              sm="12"
              style="position: relative"
            >
              <v-card class="shadow-2">
                <v-container>
                  <v-row>
                    <v-col class="pb-0" cols="12" md="12" sm="12">
                      <label for=""> Tag name </label>
                      <v-text-field
                        v-model="code.name"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        light
                        outlined
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <label for=""> Color Code </label>
                      <v-text-field
                        v-model="code.color_code"
                        :rules="[(v) => !!v || 'Color code is required']"
                        background-color="#fff"
                        class="q-text-field shadow-0 color-picker"
                        dense
                        hide-details="auto"
                        light
                        outlined
                        required
                      >
                        <template v-slot:append>
                          <v-menu
                            :close-on-content-click="false"
                            nudge-bottom="105"
                            nudge-left="16"
                            top
                          >
                            <template v-slot:activator="{ on }">
                              <div :style="swatchStyle(cIndex)" v-on="on" />
                            </template>
                            <v-card>
                              <v-card-text class="pa-0">
                                <v-color-picker
                                  v-model="code.color_code"
                                  :swatches="swatches"
                                  flat
                                  show-swatches
                                />
                              </v-card-text>
                            </v-card>
                          </v-menu>
                        </template>
                      </v-text-field>
                    </v-col>
                    <v-col class="pl-0" md="4">
                      <v-switch
                        v-model="code.status_id"
                        :false-value="14"
                        :true-value="1"
                        class="mx-4 mt-7"
                        dense
                        hide-details="auto"
                        label="Active"
                        required
                      ></v-switch>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>

              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    absolute
                    color="red"
                    dark
                    elevation="0"
                    fab
                    right
                    style="top: -5px"
                    top
                    v-bind="attrs"
                    x-small
                    @click="deleteCode(cIndex)"
                    v-on="on"
                  >
                    <DeleteIcon />
                  </v-btn>
                </template>
                Delete
              </v-tooltip>
            </v-col>
          </v-row>
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  color="blue-color"
                  dark
                  fab
                  v-bind="attrs"
                  x-small
                  @click="addColorCode()"
                  v-on="on"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add new tag
            </v-tooltip>
          </div>
        </v-form>
      </v-col>
      <v-spacer></v-spacer>
    </v-row>

    <v-row class="mt-0">
      <v-col cols="12" lg="12" md="12">
        <v-form ref="tagForm">
          <div class="text-base font-semibold black-text ml-1 mb-2">
            Event Tags
          </div>
          <v-row>
            <v-col
              v-for="(tag, tagIndex) in eventTags"
              :key="`index${tagIndex}`"
              cols="12"
              lg="4"
              md="6"
              sm="12"
              style="position: relative"
            >
              <v-card class="shadow-2">
                <v-container>
                  <v-row>
                    <v-col class="pb-0" cols="12" md="6" sm="12">
                      <label for=""> Tag name </label>
                      <v-text-field
                        v-model="tag.name"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        light
                        outlined
                        required
                        :rules="[(v) => !!v || 'Tag name is required']"
                      ></v-text-field>
                    </v-col>
                    <v-col class="pb-0" cols="12" md="6" sm="12">
                      <label for=""> Tag name (AR) </label>
                      <v-text-field
                        v-model="tag.ar_name"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        light
                        outlined
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col class="pl-0" md="4">
                      <v-switch
                        v-model="tag.status_id"
                        :false-value="14"
                        :true-value="1"
                        class="mx-4 mt-7"
                        dense
                        hide-details="auto"
                        label="Active"
                        required
                      ></v-switch>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>

              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    absolute
                    color="red"
                    dark
                    elevation="0"
                    fab
                    right
                    style="top: -5px"
                    top
                    v-bind="attrs"
                    x-small
                    @click="deleteTag(tagIndex)"
                    v-on="on"
                  >
                    <DeleteIcon />
                  </v-btn>
                </template>
                Delete
              </v-tooltip>
            </v-col>
          </v-row>
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  color="blue-color"
                  dark
                  fab
                  v-bind="attrs"
                  x-small
                  @click="addEventTag()"
                  v-on="on"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add new tag
            </v-tooltip>
          </div>
        </v-form>
      </v-col>
      <v-spacer></v-spacer>
    </v-row>

    <div class="text-base font-semibold black-text ml-1 mt-6">
      Booking Fields Configurations
    </div>
    <v-card class="mb-8 rounded-2 bordered shadow-0 mt-2">
      <v-card-text>
        <v-row dense>
          <v-col cols="12">
            <table class="table text-center table-bordered">
              <thead>
                <tr
                  class="opacity-70 tr-neon tr-rounded text-center font-bold black-text"
                >
                  <th class="text-center">Field Name</th>
                  <th class="text-center">View</th>
                  <th class="text-center">Mandatory</th>
                <th class="text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(config, index) in sortedFieldConfigurations"
                  :key="config.slug"
                >
                  <td
                    class="black-text font-bold text-sm"
                    style="font-weight: 500 !important; width: 65%"
                  >
                    {{ config.name }}
                  </td>
                  <td>
                    <v-btn
                      :style="
                        viewRestrictedFields.includes(config.slug) &&
                        'cursor:not-allowed;'
                      "
                      icon
                      @click="checkAsVisible(index)"
                    >
                      <TickIcon v-if="config.is_visible == 1" />
                      <CloseIcon v-else />
                    </v-btn>
                  </td>
                  <td>
                    <v-btn
                      :style="
                        mandatoryRestrictedFields.includes(config.slug) &&
                        'cursor:not-allowed;'
                      "
                      icon
                      @click="checkAsMandatory(index)"
                    >
                      <TickIcon v-if="config.is_required == 1" />
                      <CloseIcon v-else /></v-btn>
                </td>

                <td>
                  <template v-if="config.is_additional">

                    <v-btn icon @click="sortField(config.slug, -1)">
                      <v-icon dark color="green">mdi-arrow-up</v-icon>
                    </v-btn>
                    <v-btn icon @click="sortField(config.slug, 1)">
                      <v-icon dark color="red">mdi-arrow-down</v-icon>
                    </v-btn>




                    <v-btn :disabled="!config.is_additional"
                           icon
                           @click="editAdditionalField(config)"
                    >
                      <v-icon dark>mdi-pencil-outline</v-icon>
                    </v-btn>

                    <v-btn
                        icon
                        :disabled="!config.is_additional"
                        @click="deleteAdditionalField(index)"
                    >
                      <v-icon dark>mdi-trash-can-outline</v-icon>
                    </v-btn>
                  </template>
                  <template v-else>
                    <span>-</span>
                  </template>
                  </td>
                </tr>
              </tbody>
            </table>
          </v-col>

          <v-col cols="12">
            <v-btn
                class="ma-2 white--text blue-color float-right"
                height="45"
                text
                @click="additional_field_dialogue = true"
            >Add Field
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-row class="mt-0">
      <v-col cols="12" lg="12" md="12">
        <v-form ref="cfform">
        <div class="text-base font-semibold black-text ml-1 mt-6">Event Additional Fields</div>
            <v-row>
              <v-col cols="12" md="4" sm="12" v-for="(cfield, cindex) in custom_fields" :key="'d_' + cindex">
                <v-card class="rounded-2 bordered shadow-0 mt-2" :style="cardStyle" >
                  <v-card-text>
                    <v-row class="mt-1">
                      <v-col cols="12" sm="6" md="6">
                        <v-text-field
                            v-model="cfield.field_name"
                            class="q-text-field shadow-0"
                            hide-details="auto"
                            dense
                            background-color="#fff"
                            outlined
                            :rules="[(v) => !!v || 'Field name is required']"
                            label="Field Name"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" sm="6" md="6">
                        <v-select
                            v-model="cfield.field_type"
                            :items="['Text','File','Single-select dropdown','Multi-select dropdown']"
                            :menu-props="{ bottom: true, offsetY: true }"
                            :rules="[(v) => !!v || 'Field Type is required']"
                            class="q-autocomplete shadow-0"
                            hide-details="auto"
                            outlined
                            placeholder="Field Type"
                            dense
                        ></v-select>
                      </v-col>
                    </v-row>
                    <v-row v-if="['Single-select dropdown','Multi-select dropdown'].includes(cfield.field_type)" class="mt-4">
                      <v-row v-if="cfield.options.length > 0">
                        <v-col
                            cols="12"
                            md="6"
                            class="py-0 px-2"
                            v-for="(option, key) in cfield.options"
                            :key="`ci_key_${key}`"
                        >
                          <div class="d-flex items-center">
                            <v-col md="8">
                              <v-text-field
                                  clearable
                                  outlined
                                  dense
                                  required
                                  :rules="[(v)=> !!v || 'Field is required' ]"
                                  label="Option"
                                  background-color="#fff"
                                  v-model="option.value"
                                  hide-details="auto"
                              ></v-text-field>
                            </v-col>
                            <v-col md="2">
      <v-btn small
                                  class="remove-button"
                                  color="white"
                                  style="height:30px; width:20% !important"
                                  depressed
                                  @click="deleteOption(cindex,key)"
                              >
                                <v-icon dark>
                                  mdi-trash-can-outline
                                </v-icon>
                              </v-btn>
                            </v-col>
                          </div>
                        </v-col>
                      </v-row>
                      <div class="add-new-step mt-2" @click="addOption(cindex)">
                        + Add Choice
                      </div>
                    </v-row>
                    <v-row>
<!--                      <v-col cols="12" sm="6" md="6" v-if="cfield.field_type === 'File'">-->
<!--                          <v-file-input-->
<!--                              :label="cfield.field_value != null ? '' : 'Document'"-->
<!--                              v-model="cfield.field_value"-->
<!--                              prepend-inner-icon="mdi-paperclip"-->
<!--                              prepend-icon-->
<!--                              outlined-->
<!--                              background-color="#fff"-->
<!--                              class="q-text-field shadow-0"-->
<!--                              dense-->
<!--                              hide-details="auto"-->
<!--                          >-->
<!--                          <template v-slot:label>-->
<!--                            <span v-if="!cfield.field_value"> Select file </span>-->
<!--                            <span v-if="cfield.field_value && !cfield.field_value" class="font-weight-bold">-->
<!--                              <span style="width: 70%; display: inline-block" class="text-truncate">{{ cfield.field_value }}</span>-->
<!--                              <span style="width: 20%; display: inline-block" class="text-truncate">.{{ cfield.field_value.split(".")[cfield.field_value.split(".").length - 1] }}</span>-->
<!--                            </span>-->
<!--                          </template>-->
<!--                        </v-file-input>-->
<!--                      </v-col>-->
                      <v-col cols="12" sm="6" md="3">
                        <v-checkbox
                            class="mt-0 mb-3"
                            v-model="cfield.is_visible"
                            label="View"
                            color="success"
                            value="cfield.is_visible"
                            v-bind:false-value="0"
                            v-bind:true-value="1"
                            hide-details
                            @change="!cfield.is_visible ? (cfield.is_required = 0) : ''"
                        ></v-checkbox>
                      </v-col>
                      <v-col cols="12" sm="6" md="3">
                        <v-checkbox
                            class="mt-0 mb-3"
                            v-model="cfield.is_required"
                            label="Mandatory"
                            color="success"
                            value="cfield.is_required"
                            hide-details
                            v-bind:false-value="0"
                            v-bind:true-value="1"
                            @change="cfield.is_required ? (cfield.is_visible = 1) : ''"
                        ></v-checkbox>
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <div class="delete-field">
                    <v-tooltip  bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                            absolute
                            color="red"
                            dark
                            elevation="0"
                            fab
                            right
                            style="top:-5px;"
                            top
                            v-bind="attrs"
                            x-small
                            @click="deleteCustomField(cindex)"
                            v-on="on">
        <DeleteIcon/>
                        </v-btn>
                      </template>
                      Delete
                    </v-tooltip>
                  </div>
                </v-card>
              </v-col>
            </v-row>
            <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                    v-bind="attrs"
                    v-on="on"
                    color="blue-color"
                    fab
                    x-small
                    dark
                    @click="addCustomField"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add Custom Field
            </v-tooltip>
         </div>
        </v-form>
      </v-col>
    </v-row>

    <div class="d-flex justify-end">
      <v-btn class="white--text blue-color" @click="saveConfigurations($event)">Update</v-btn>
    </div>
    <additional-field-modal
        :openModal="additional_field_dialogue"
        @closeModal="closeAdditionalFieldModal"
        :field_config="field_config"
        @addFormItem="addNewFormItem"

    />
    <confirm-model
      v-bind="confirmModel"
      @close="confirmModel.id = null"
      @confirm="confirmActions"
    ></confirm-model>
  </v-container>
</template>

<script>
import BackButton from "@/components/Common/BackButton.vue";
import DeleteIcon from "@/assets/images/retail/delete-bg-icon.svg";
import TickIcon from "@/assets/images/misc/config-table-tick-icon.svg";
import CloseIcon from "@/assets/images/misc/config-table-close-icon.svg";
import constants from "@/utils/constants";
import RichEditor from "@/components/Common/RichEditor.vue";
import AdditionalFieldModal from "@/components/Common/AdditionalFieldModal";

export default {
  components: { RichEditor, CloseIcon, TickIcon, DeleteIcon, BackButton ,AdditionalFieldModal},
  computed: {
    sortedFieldConfigurations() {
      // Step 1: Clone the array
      let fields = this.field_configurations.slice();
      // Step 2: Separate into default and additional fields
      const defaultFields = fields.filter(field => !field.is_additional);
      const additionalFields = fields.filter(field => field.is_additional);

      // Step 3: Assign incremental sort_id to any missing values
      let sortCounter = 1;

      // Sort and assign sort_id to default fields
      defaultFields.sort((a, b) => {
        return (a.sort_id ?? Infinity) - (b.sort_id ?? Infinity);
      }).forEach(field => {
        if (field.sort_id == null) {
          field.sort_id = sortCounter++;
        } else {
          sortCounter = Math.max(sortCounter, field.sort_id + 1);
        }
      });

      // Sort and assign sort_id to additional fields
      additionalFields.sort((a, b) => {
        return (a.sort_id ?? Infinity) - (b.sort_id ?? Infinity);
      }).forEach(field => {
        if (field.sort_id == null) {
          field.sort_id = sortCounter++;
        } else {
          sortCounter = Math.max(sortCounter, field.sort_id + 1);
        }
      });

      // Step 4: Merge and return
      const sortedFields = [...defaultFields, ...additionalFields].sort((a, b) => {
        // Sort primarily by is_additional, secondarily by sort_id
        if (!a.is_additional && b.is_additional) return -1;
        if (a.is_additional && !b.is_additional) return 1;
        return a.sort_id - b.sort_id;
      });

      console.log(`sortedFields`, sortedFields);
      return sortedFields;
    },
  },
  data() {
    return {
      deleted_categories: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      configuration: {
        enable_tag_restriction: false,
        tnc_enable: 0,
        tnc: "",
      },
      colorCodes: [],
      eventTags:[],
      swatches: [
        ["#FF0000", "#AA0000", "#550000"],
        ["#FFFF00", "#AAAA00", "#555500"],
        ["#00FF00", "#00AA00", "#005500"],
        ["#00FFFF", "#00AAAA", "#005555"],
        ["#0000FF", "#0000AA", "#000055"],
      ],
      deleted_codes: [],
      deleted_tags: [],
      viewRestrictedFields: constants.VIEWRESTRICTEDFIELDS,
      mandatoryRestrictedFields: constants.MANTATORYRESTRICTEDFIELDS,
      field_configurations: [],
      custom_fields:[],
      deleted_custom_fields: [],
      additional_field_dialogue:false,
      field_config: {
        type: 'text_box',
        field_name: 'Family name',
        ar_field_name: null,
        is_required: true,
        arabic_enabled: false,
        allowMarkdown: false
      },
    };
  },
  mounted() {
    this.getConfiguration();
  },
  methods: {
    addColorCode() {
      this.colorCodes.push({
        name: null,
        status_id: 1,
        category_id: null,
        color_code: "#00AAAAFF",
      });
      if (this.$refs.cform) {
        this.$refs.cform.resetValidation();
      }
    },
    addEventTag() {
      this.eventTags.push({
        name: null,
        ar_name: null,
        status_id: 1,
      });
      if (this.$refs.cform) {
        this.$refs.tagForm.resetValidation();
      }
    },
    swatchStyle(cIndex) {
      const { color_code, menu } = this.colorCodes[cIndex];
      return {
        backgroundColor: color_code,
        cursor: "pointer",
        height: "30px",
        width: "30px",
        borderRadius: menu ? "50%" : "4px",
        transition: "border-radius 200ms ease-in-out",
      };
    },
    getConfiguration() {
      this.$store.dispatch("loadEventFieldConfigurations").then((response) => {
        if (response.status == 200 && response.data.status == true) {
          const data = response.data.data;
          if (data) {
            if (data.colorCodes.length) {
              this.colorCodes = data.colorCodes;
            }
            if (data.eventTags.length) {
              this.eventTags = data.eventTags;
            }
            // else {
            //   this.colorCodes = [
            //     {
            //       name: null,
            //       status_id: 1,
            //       category_id: null,
            //       color_code: "#00AAAAFF",
            //     },
            //   ];
            // }
            this.configuration = data.configuration;
            this.field_configurations = data.field_configurations;
            this.configuration.tnc_enable = Number(
              data.configuration.tnc_enable
            );
            this.custom_fields = data.custom_fields?data.custom_fields:[];
          }
          this.deleted_codes = [];
          this.$forceUpdate();
          if (this.$refs.cform) {
            this.$refs.cform.resetValidation();
          }
        }
      });
      // this.$http
      //     .get(`venues/events/configuration`)
      //     .then((response) => {
      //       if (response.status == 200 && response.data.status == true) {
      //         const data = response.data.data;
      //         if (data) {
      //           if (data.colorCodes.length) {
      //             this.colorCodes = data.colorCodes;
      //           }
      //           // else {
      //           //   this.colorCodes = [
      //           //     {
      //           //       name: null,
      //           //       status_id: 1,
      //           //       category_id: null,
      //           //       color_code: "#00AAAAFF",
      //           //     },
      //           //   ];
      //           // }
      //           this.configuration = data.configuration;
      //           this.field_configurations = data.field_configurations;
      //         }
      //         this.deleted_codes = [];
      //         this.$forceUpdate();
      //         if (this.$refs.cform) {
      //           this.$refs.cform.resetValidation();
      //         }
      //       }
      //     })
      //     .catch((error) => {
      //       this.errorChecker(error);
      //     });
    },
    deleteCode(cIndex) {
      let pkg = this.colorCodes[cIndex];
      if (pkg.name == null && pkg.color_code == null && pkg.id == null) {
        this.colorCodes.splice(cIndex, 1);
        this.$forceUpdate();
      } else {
        this.confirmModel = {
          id: cIndex,
          title: "Do you want to delete this Tag?",
          description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
          type: "color_code",
        };
      }
    },
    deleteTag(cIndex) {
      let tag = this.eventTags[cIndex];
      if (tag.name == null && tag.id == null) {
        this.eventTags.splice(cIndex, 1);
        this.$forceUpdate();
      } else {
        this.confirmModel = {
          id: cIndex,
          title: "Do you want to delete this Tag?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
          type: "event_tags",
        };
      }
    },
    saveConfigurations() {
      if (!this.$refs.cform.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (!this.$refs.cfform.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      let data = {
        color_codes: this.colorCodes,
        event_tags: this.eventTags,
        field_configurations: this.field_configurations,
        custom_fields: this.custom_fields,
        configuration: {
          ...this.configuration,
          tnc: this.configuration.tnc_enable?this.utf8ToBase64(this.configuration.tnc):""
        },
      };
      if (this.deleted_codes.length) {
        data.deleted_codes = this.deleted_codes;
      }
      if (this.deleted_tags.length) {
        data.deleted_tags = this.deleted_tags;
      }
      if (this.deleted_custom_fields.length) {
        data.deleted_custom_fields = this.deleted_custom_fields;
      }
      this.showLoader();
      this.$http
        .post(`venues/events/configuration`, data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Configuration saved");
            this.getConfiguration();
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    checkAsMandatory(index) {
      let field = this.sortedFieldConfigurations[index];
      if (!this.mandatoryRestrictedFields.includes(field.slug)) {
        this.sortedFieldConfigurations[index].is_required =
            field.is_required == 1 ? 0 : 1;
        if (field.is_required == 1 && field.is_visible == 0) {
          this.sortedFieldConfigurations[index].is_visible = 1;
        }
      }
    },
    checkAsVisible(index) {
      let field = this.sortedFieldConfigurations[index];
      if (!this.viewRestrictedFields.includes(field.slug)) {
        this.sortedFieldConfigurations[index].is_visible =
            field.is_visible == 1 ? 0 : 1;
        if (field.is_required == 1 && field.is_visible == 0) {
          this.sortedFieldConfigurations[index].is_required = 0;
        }
      }
    },
    confirmActions(data) {
      if (data.type === "color_code") {
        let index = data.id;
        if (this.colorCodes[index].id != null) {
          this.deleted_codes.push(this.colorCodes[index].id);
        }
        this.colorCodes.splice(index, 1);
        this.$forceUpdate();
      }else if(data.type === "custom_field"){
        let index = data.id;
        if (this.custom_fields[index].uuid != null) {
          this.custom_fields.splice(index, 1);
        }
        this.$forceUpdate();
      }
      if (data.type === "event_tags") {
        let index = data.id;
        if (this.eventTags[index].id != null) {
          this.deleted_tags.push(this.eventTags[index].id);
        }
        this.eventTags.splice(index, 1);
        this.$forceUpdate();
      }if(data.type=== 'delete_additional_field'){
        let deleteItem = this.sortedFieldConfigurations[data.id];
        const originalIndex = this.field_configurations.findIndex(
            f => f === deleteItem
        );
        if (originalIndex === -1) return;
        this.field_configurations.splice(originalIndex, 1);
        this.field_configurations.forEach(field => {
          if (field.sort_id > deleteItem.sort_id) {
            this.$set(field, 'sort_id', field.sort_id - 1);
          }
        });
      }
      if (data.type === "event_tags") {
        let index = data.id;
        if (this.eventTags[index].id != null) {
          this.deleted_tags.push(this.eventTags[index].id);
        }
        this.eventTags.splice(index, 1);
        this.$forceUpdate();
      }
      this.confirmModel.id = null;
    },
    goToEvents() {
      this.$router.push({ name: "Events" }, () => {});
    },
    deleteCustomField(index) {
      let cf = this.custom_fields[index];
      if (cf.uuid == null) {
        this.custom_fields.splice(index, 1);
        this.$forceUpdate();
      } else {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this Custom Field?",
          description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
          type: "custom_field",
        };
      }
    },
    addOption(index){
      this.custom_fields[index].options.push({
        value:'',
      });
    },
    deleteOption(ind,key){
      this.custom_fields[ind].options.splice(key, 1);
    },
    addCustomField(){
       this.custom_fields.push({
          uuid: null,
          field_name: null,
          field_type: null,
          field_value: null,
          is_visible: 0,
          is_required: 0,
          options:[],
        });
    },
    closeAdditionalFieldModal(){
      this.additional_field_dialogue =  false;
      this.field_config = {
        type: 'text_box',
        field_name: 'Field name',
        ar_field_name: null,
        is_required: true,
        arabic_enabled: false,
        allowMarkdown: false
      };
    },
    addNewFormItem(fieldData) {
      let index = null;

      // Step 1: Find index (by id or uId)
      if (!fieldData.id) {
        index = this.field_configurations.findIndex(
            (e) => e.uId === fieldData.uId
        );
      } else {
        index = this.field_configurations.findIndex(
            (e) => e.id === fieldData.id
        );
      }

      // Step 2: Check for duplicate slug (excluding self)
      const isDuplicateSlug = this.field_configurations.some(
          (e, i) => e.slug === fieldData.slug && i !== index
      );
      if (isDuplicateSlug) {
        console.warn("Duplicate slug detected:", fieldData);
        this.showError(`Duplicate slug not allowed - ${fieldData.slug}`);
        return;
      }

      // Step 3: Add or update
      if (index !== -1) {
        // Edit existing field
        const existingItem = this.field_configurations[index];
        // Preserve existing sort_id unless overwritten
        if (fieldData.sort_id == null) {
          fieldData.sort_id = existingItem.sort_id;
        }
        this.$set(this.field_configurations, index, fieldData);
      } else {
        // Add new field — assign next sort_id based on max
        const maxSortId = this.field_configurations.reduce(
            (max, field) => field.sort_id != null ? Math.max(max, field.sort_id) : max,
            0
        );
        fieldData.sort_id = maxSortId + 1;
        this.field_configurations.push(fieldData);
      }

      // Step 4: Close dialog
      this.additional_field_dialogue = false;
    },



    editAdditionalField(field){
      console.log(field);
      this.additional_field_dialogue = true
      this.field_config = field;
    },
    deleteAdditionalField(cIndex) {
      this.confirmModel = {
        id: cIndex,
        title: "Do you want to delete this Additional Field?",
        description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "delete_additional_field",
      };
    },
    sortField(slug, direction) {
      const all = this.field_configurations;
      const additionalFields = all
          .filter(f => f.is_additional === 1)
          .sort((a, b) => a.sort_id - b.sort_id);

      const index = additionalFields.findIndex(f => f.slug === slug);
      const newIndex = index + direction;
      if (newIndex < 0 || newIndex >= additionalFields.length) return;

      // Swap sort_id between the two items
      const itemA = additionalFields[index];
      const itemB = additionalFields[newIndex];

      // Find actual references in original array
      const originalItemA = all.find(f => f.slug === itemA.slug);
      const originalItemB = all.find(f => f.slug === itemB.slug);

      const temp = originalItemA.sort_id;
      originalItemA.sort_id = originalItemB.sort_id;
      originalItemB.sort_id = temp;

      // Trigger reactivity
      this.field_configurations = [...all];
    },
  },
};
</script>
<style>
.color-picker .v-input__append-inner {
  margin-top: 6px !important;
}

.v-btn.remove-button {
  color: #00000040 !important;
}

.add-new-step {
  color: var(--main-color, #4faeaf);
  font-family: Inter;
  font-size: 14px;
  width: max-content;
  font-weight: 600;
  margin-left: 10px;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
    text-underline-offset: 3px;
  }
}

</style>

<template>
  <v-container fluid>
    <div class="shadow rounded-5">
      <div class="md-card-content">
        <div>
         <div class="d-flex justify-space-between align-center">
           <SvgIcon class="text-base font-semibold text-black" text="Event Schedules">
             <template v-slot:icon>
               <ScheduleIcon/>
             </template>
           </SvgIcon>
         </div>
          <br><br>

          <div class="table-responsive">

            <table class="table border-collapse">
              <thead>
              <tr class="tr-neon tr-rounded opacity-70">
                <th class="text-left">
                  Name
                </th>
                <th class="text-left">
                  Email
                </th>
                <th class="text-left">
                  Phone
                </th>
                <th class="text-left">
                  Ticket
                </th>
                <th class="text-left">
                  Status
                </th>
              </tr>
              </thead>
              <tbody>
              <tr
                  v-for="inviteCustomer in invitedMembers"
                  :key="inviteCustomer.id"
              >
                <td>
                  {{ inviteCustomer.first_name}}
                </td>

                <td>
                  {{ inviteCustomer.last_name }}
                </td>

                <td>
                  {{ inviteCustomer.email }}
                </td>

                <td>
                  {{ inviteCustomer.ticket.product.name }}
                </td>

                <td>
                  {{ inviteCustomer.status }}
                </td>



              </tr>
              </tbody>
            </table>
          </div>

          <v-row>
            <v-col cols="4"></v-col>
            <v-col cols="4">
              <v-pagination v-model="page" :length="totalPages" class="new-pagination"></v-pagination>
            </v-col>
            <v-col class="d-flex align-center justify-end" cols="4">
              <div class="d-flex align-center justify-end text-sm">
                <span>Result</span>
                <div style="width: 80px">
                  <v-select
                      v-model="perPage"
                      :items="[50, 100, 500]"
                      :menu-props="{ bottom: true, offsetY: true }"
                      class="q-autocomplete text-sm"
                      flat
                      hide-details
                      solo
                      @change="getSchedules"
                  ></v-select>
                </div>
                <span>Per Page</span>
              </div>
            </v-col>
          </v-row>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script>
import {mapGetters} from "vuex";
import ScheduleIcon from "@/assets/images/nav_icon/Schedule.svg"
import SvgIcon from "@/components/Image/SvgIcon.vue";

export default {
  props: {
    event: Object,
  },
  components: {
    SvgIcon,
    ScheduleIcon
  },
  data() {
    return {
      btnShow: false,
      invitedMembers: [],
      isLoading: false,
      page: 1,
      perPage: 10,
      totalPages: 1,
    };
  },


  mounted() {
    this.getSchedules();
  },

  computed: {
    ...mapGetters({
      checkReadPermission: "checkReadPermission",
    }),
  },
  watch: {
    page() {
      this.getSchedules();
    },
  },
  methods: {
    getSchedules() {
      this.showLoader("Loading");
      this.$http
          .get(`venues/events/invites?page=${this.page}&perPage=10`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.invitedMembers = response.data.data;
              this.totalPages = response.data.total_pages;
            }
            this.hideLoader();
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },



  },
};
</script>

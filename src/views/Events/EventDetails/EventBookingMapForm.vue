<template>
  <div>
    <v-form>
      <v-dialog v-model="showeventForm" width="80%" scrollable persistent>
        <v-form ref="form" v-model="valid">
          <v-card>
            <v-card-title>
              {{ "Add Booking" }}
              <v-spacer></v-spacer>
              <div
                md="3"
                v-if="promotions.length > 0"
                style="margin-bottom: -20px"
              >
                <v-autocomplete
                  v-if="bookingForm.card_number == null"
                  :items="[
                    { name: 'None', promotion_code: null },
                    ...promotions,
                  ]"
                  item-text="name"
                  height="50"
                  item-value="promotion_code"
                  v-model="bookingForm.promotion_code"
                  background-color="rgb(206, 168, 0)"
                  outlined
                  @change="verifyBenefit('promotion')"
                  label="Promotions"
                >
                </v-autocomplete>
              </div>
            </v-card-title>
            <div class="d-flex justify-space-around headline pa-4">
              <div class="pitch">Event: {{ event_name }}</div>
              <div class="pitch">Service: {{ service }}</div>
              <div class="pitch">
                Price: {{ bookingForm.price | toCurrency }}
                <span
                  v-if="bookingForm.discount != null"
                  class="text-decoration-line-through"
                >
                  {{ bookingForm.discount.actual_price | toCurrency }}
                </span>
              </div>
            </div>

            <v-card-text class="form_bg">
              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4"
                outlined
              >
                <div class="titles">Event Details</div>
                <v-divider></v-divider>
                <v-row class="mt-1">
                  <v-col sm="12" md="12">
                    <v-card flat class="d-flex">
                      <v-img
                        class="ground_Img"
                        :src="
                          image_path
                            ? s3BucketURL + image_path
                            : require('@/assets/images/default_images/event_default.jpg')
                        "
                        height="180"
                      >
                        <v-row
                          class="fill-height ma-0 hoverImage"
                          align="center"
                          justify="center"
                        >
                          <div
                            align="center"
                            justify="center"
                            class="white--text eventbox pa-5"
                            style="width: 50%"
                          >
                            <div class="pa-0 title">
                              {{ location }}
                            </div>
                            <div class="pa-0 title">
                              {{ date | dateformat }}
                            </div>
                            <div class="pa-0 title">
                              {{ start_time | timeFormat }} to
                              {{ end_time | timeFormat }}
                            </div>
                            <v-row no-gutters>
                              <v-spacer></v-spacer>
                              <v-col col="12" md="5" sm="5">
                                <v-btn small block color="#008483" dark>
                                  Attendence:
                                  {{ participants | numberFormatter }}
                                </v-btn>
                              </v-col>
                              <v-spacer></v-spacer>
                              <v-col col="12" md="5" sm="5">
                                <v-btn small block color="#008483" dark>
                                  Sales:{{ sales | toCurrency }}
                                </v-btn>
                              </v-col>
                              <v-spacer></v-spacer>
                            </v-row>
                          </div>
                        </v-row>
                      </v-img>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card>

              <v-card
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4 mt-2"
                outlined
              >
                <div class="titles">Ticket Details</div>
                <v-row>
                  <v-col md="10">
                    <v-chip
                      label
                      color="cyan"
                      dark
                      class="ml-2"
                      v-for="(product, index) in bookingForm.products"
                      :key="index"
                      :close="!product.rental && !product.order_item_id"
                      @click.alt="removeProduct(index)"
                    >
                      <v-avatar left>
                        <view-image
                          :image="product.image_path"
                          :contain="false"
                        ></view-image>
                      </v-avatar>
                      {{ product.seat_name ? product.seat_name : "" }}:
                      {{ product.name }} x {{ product.quantity }} -

                      {{ product.price | toCurrency }}
                      <span
                        v-if="product.discount != null"
                        class="text-decoration-line-through pl-1"
                      >
                        {{ product.discount.actual_price | toCurrency }}</span
                      >
                    </v-chip>
                  </v-col>
                  <v-col
                    sm="2"
                    class="text-right"
                    v-if="
                      checkReadPermission($modules.salesTeam.management.slug)
                    "
                  >
                    <v-select
                      v-model="bookingForm.sales_team_id"
                      label="Sales Team"
                      :items="salesTeams"
                      item-text="name"
                      item-value="id"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                    ></v-select>
                  </v-col>
                </v-row>
              </v-card>
              <v-card
                v-for="(customer, index) in event_customers"
                :key="index"
                color="#edf9ff"
                style="border: 1px #ccc solid"
                class="pa-4 mt-2"
                outlined
              >
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      v-on="on"
                      color="red"
                      fab
                      x-small
                      absolute
                      top
                      right
                      dark
                      v-if="event_customers.length > 1"
                      @click="deleteCustomer(index)"
                    >
                      <v-icon>mdi-delete</v-icon>
                    </v-btn>
                  </template>
                  Delete
                </v-tooltip>
                <v-row no-gutters>
                  <v-col md="8">
                    <div class="titles d-flex justify-space-between mt-5">
                      <div>Customer #{{ index + 1 }} Details</div>
                    </div>
                  </v-col>
                  <!-- Uncomment to enable emirates ID reader -->
                  <v-col
                    md="4"
                    class="text-right d-flex flex-row-reverse"
                    v-if="!order_id"
                  >
                    <card-data-button
                      class="mt-2"
                      label="HID Omnikey"
                      @data="
                        (data) => {
                          setCardData(data, index);
                        }
                      "
                    ></card-data-button>
                    <card-reader-button
                      class="mt-2"
                      label="Samsotech Reader"
                      @data="
                        (data) => {
                          setCardData(data, index);
                        }
                      "
                    ></card-reader-button>
                  </v-col>
                </v-row>
                <v-divider></v-divider>
                <v-row no-gutters>
                  <v-col md="3" v-if="index < 1">
                    <v-radio-group
                      v-model="customer.customer_type"
                      class="d-flex"
                      row
                      @change="customerTypeChange(index)"
                      mandatory
                      :readonly="order_id > 0"
                    >
                      <v-radio
                        label="Normal"
                        color="cyan"
                        value="normal"
                      ></v-radio>
                      <v-radio
                        label="Member"
                        color="cyan"
                        value="member"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col md="3" v-if="customer.customer_type == 'member'">
                    <v-member-search
                      v-model="customer.member"
                      @clear="clearBenefit(index)"
                      :selected="customer.card_number"
                      @input="setMemberData"
                      class="mt-4"
                    >
                    </v-member-search>
                  </v-col>
                  <v-spacer></v-spacer>
                  <v-col md="3">
                    <v-switch
                      style="float: right"
                      v-model="customer.opt_marketing"
                      label="Opt In Marketing"
                    ></v-switch>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col sm="4" md="4">
                    <v-mobile-search
                      label="Mobile No*"
                      :selected="customer.mobile"
                      :index="index"
                      @updateCustomer="setCustomerData($event, index)"
                      v-model="customer.search"
                    ></v-mobile-search>
                  </v-col>

                  <v-col sm="4" md="4">
                    <v-name-search
                      :selected="customer.name"
                      :mobile="customer.mobile"
                      :email="customer.email"
                      @updateCustomer="setCustomerData($event, index)"
                      v-model="customer.nameSearch"
                    ></v-name-search>
                  </v-col>

                  <v-col sm="4" md="4">
                    <v-text-field
                      :readonly="customer.customer_id != null"
                      outlined
                      background-color="#fff"
                      v-model="customer.email"
                      :label="`Email${field.email.is_required ? '*' : ''}`"
                      :placeholder="`Email${
                        field.email.is_required ? '*' : ''
                      }`"
                      :rules="emailRule"
                    ></v-text-field>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.gender.is_visible">
                    <v-select
                      :items="['Male', 'Female']"
                      :placeholder="`Gender${
                        field.gender.is_required ? '*' : ''
                      }`"
                      :label="`Gender${field.gender.is_required ? '*' : ''}`"
                      outlined
                      v-model="customer.gender"
                      :rules="genderRule"
                      background-color="#fff"
                      :menu-props="{ bottom: true, offsetY: true }"
                    ></v-select>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.dob.is_visible">
                    <date-of-birth
                      :placeholder="`Date of Birth${
                        field.dob.is_required ? '*' : ''
                      }`"
                      :label="`Date of Birth${
                        field.dob.is_required ? '*' : ''
                      }`"
                      :rules="dobRule()"
                      v-model="customer.dob"
                    >
                    </date-of-birth>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.nationality.is_visible">
                    <v-autocomplete
                      :items="countries"
                      :hint="`Nationality${
                        field.nationality.is_required ? '*' : ''
                      }`"
                      :label="`Nationality${
                        field.nationality.is_required ? '*' : ''
                      }`"
                      :rules="nationalityRule"
                      item-value="id"
                      item-text="name"
                      outlined
                      v-model="customer.country_id"
                      background-color="#fff"
                    ></v-autocomplete>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.idProof.is_visible">
                    <v-select
                      :hint="`ID Type${field.idProof.is_required ? '*' : ''}`"
                      :label="`ID Type${field.idProof.is_required ? '*' : ''}`"
                      :rules="idTypeRule"
                      outlined
                      item-value="id"
                      item-text="name"
                      :items="idProofTypes"
                      v-model="customer.id_proof_type_id"
                      @change="changeIdProofTypeId"
                      background-color="#fff"
                      :menu-props="{ bottom: true, offsetY: true }"
                    ></v-select>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.idProof.is_visible">
                    <v-row no-gutters>
                      <v-col md="7">
                        <v-text-field
                          :hint="`ID Number${
                            field.idProof.is_required ? '*' : ''
                          }`"
                          :label="`ID Number${
                            field.idProof.is_required ? '*' : ''
                          }`"
                          :rules="idTypeRule"
                          class="text_field1"
                          outlined
                          v-model="customer.id_proof_number"
                          background-color="#fff"
                        ></v-text-field>
                      </v-col>
                      <v-col md="5">
                        <v-file-input
                          v-model="customer.id_proof"
                          class="text_field2"
                          :placeholder="`Select image${
                            field.image.is_required ? '*' : ''
                          }`"
                          :label="`ID Proof${
                            field.image.is_required ? '*' : ''
                          }`"
                          :rules="idProofRule"
                          prepend-icon=""
                          prepend-inner-icon="mdi-card-account-details"
                          background-color="#fff"
                          outlined
                        >
                          <template v-slot:prepend-inner>
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on }">
                                <v-icon
                                  color="cyan"
                                  v-if="customer.id_proof_path"
                                  @click="openFile(customer.id_proof_path)"
                                  v-on="on"
                                >
                                  mdi-download-box
                                </v-icon>
                                <v-icon v-else v-on="on">
                                  mdi-card-account-details
                                </v-icon>
                              </template>
                              <span v-if="customer.id_proof_path"
                                >Download uploaded file</span
                              >
                              <span v-else>Upload ID Proof</span>
                            </v-tooltip>
                          </template>
                          <template v-slot:selection="{ index, text }">
                            <v-chip
                              v-if="index == 0"
                              color="cyan accent-4"
                              dark
                              label
                              small
                            >
                              <span style="width: 40px" class="text-truncate">{{
                                text
                              }}</span>
                            </v-chip>
                          </template>
                        </v-file-input>
                      </v-col>
                    </v-row>
                    <div style="margin-top: -110px"></div>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.image.is_visible">
                    <v-row no-gutters>
                      <v-col md="8">
                        <v-file-input
                          v-model="customer.image"
                          class="text_field1"
                          prepend-icon=""
                          :label="`Customer Image${
                            field.image.is_required ? '*' : ''
                          }`"
                          :placeholder="`Image${
                            field.image.is_required ? '*' : ''
                          }`"
                          :rules="imageRule"
                          prepend-inner-icon="mdi-image"
                          background-color="#fff"
                          outlined
                          show-size
                        >
                          <template v-slot:selection="{ index, text }">
                            <v-chip
                              v-if="index == 0"
                              color="cyan accent-4"
                              dark
                              label
                              small
                            >
                              <span
                                style="width: 120px"
                                class="text-truncate"
                                >{{ text }}</span
                              >
                            </v-chip>
                          </template>
                        </v-file-input>
                      </v-col>
                      <v-col md="4">
                        <v-btn
                          large
                          block
                          style="background-color: #fff"
                          outlined
                          height="56"
                          color="blue-grey"
                          class="white--text text_field2"
                          @click="webcamDialog = true"
                        >
                          <v-icon dark>mdi-camera</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col sm="4" md="4" v-if="field.tag.is_visible">
                    <v-select
                      :items="tags"
                      :placeholder="`Tags${field.tag.is_required ? '*' : ''}`"
                      :label="`Tags${field.tag.is_required ? '*' : ''}`"
                      outlined
                      item-value="id"
                      item-text="name"
                      :menu-props="{ bottom: true, offsetY: true }"
                      v-model="customer.customer_tag"
                      :rules="tagRule"
                      background-color="#fff"
                      multiple
                      return-object
                    ></v-select>
                  </v-col>
                </v-row>
              </v-card>

              <div
                class="add_btn pb-4"
                style="margin-top: -30px"
                v-if="displayed_participant < participant_count"
              >
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      v-on="on"
                      color="teal"
                      fab
                      x-small
                      dark
                      @click="addCustomer()"
                    >
                      <v-icon small>mdi-plus-circle</v-icon>
                    </v-btn>
                  </template>
                  Add
                </v-tooltip>
              </div>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                v-if="bookingForm.status_id == 5"
                @click="$emit('pay', bookingForm.order_id)"
                class="ma-2 white--text teal-color"
                text
              >
                Pay
              </v-btn>
              <v-btn
                v-if="bookingForm.status_id == 5"
                @click="confirmCancel"
                class="ma-2 white--text red-color"
                text
                >Cancel Booking
              </v-btn>
              <v-btn @click="close()" class="ma-2 white--text blue-color" text
                >Close
              </v-btn>
              <v-btn
                class="ma-2 white--text teal-color"
                @click="confirmBooking"
                text
              >
                Confirm Booking
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>
    </v-form>
    <capture-image
      :open="webcamDialog"
      @close="webcamDialog = false"
      @confirm="confirmImageCapture"
    />
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
  </div>
</template>
<script>
import VMemberSearch from "@/components/Customer/VMemberSearch";
import CaptureImage from "@/components/Image/CaptureImage";
import moment from "moment";
import bookingFields from "@/mixins/bookingFieldValidation";

export default {
  props: {
    showeventForm: { type: Boolean },
    start_time: { type: String },
    end_time: { type: String },
    date: { type: String },
    facility_id: { type: Number },
    order_id: { type: Number },
    id: { type: Number, default: 0 },
    event_name: { type: String },
    venue_service_id: { type: Number },
    service: { type: String },
    event_id: { type: Number },
    location: { type: String },
    event_type_id: { type: Number },
    start_date: { type: String },
    end_date: { type: String },
    image_path: { type: String },
    participants: { type: Number, default: 0 },
    sales: { type: Number, default: 0 },
    selectedProducts: { type: Array, default: () => [] },
  },
  components: {
    CaptureImage,
    VMemberSearch,
  },
  mixins: [bookingFields],
  data() {
    return {
      ticketTypes: {},
      event: {},
      participant_count: 1,
      bookingForm: { price: 0, opt_marketing: false },
      event_customers: [
        {
          customer_type: null,
          member: null,
          company_id: null,
          company_sale_id: null,
          opt_marketing: null,
          mobile: null,
          name: null,
          email: null,
          nameSearch: null,
          customer_id: null,
          gender: null,
          country_id: null,
          id_proof_type_id: null,
          id_proof_number: null,
          id_proof: null,
          id_proof_path: null,
          image: null,
        },
      ],
      selectedTicket: {},
      productCategoryId: null,
      webcamDialog: false,
      endTimes: [],
      categories: [],
      companies: [],
      valid: false,
      repeatData: {
        available: [],
        excluded: [],
      },
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      isEmiratesIdCheck: false,
      displayed_participant: 1,
      actualPrice: 0,
    };
  },
  watch: {
    showeventForm(val) {
      if (val === true) {
        this.bookingForm = { price: 0 };
        this.event_customers = [
          {
            customer_type: null,
            member: null,
            company_id: null,
            company_sale_id: null,
            opt_marketing: null,
            mobile: null,
            name: null,
            email: null,
            nameSearch: null,
            customer_id: null,
            gender: null,
            country_id: null,
            id_proof_type_id: null,
            id_proof_number: null,
            id_proof: null,
            id_proof_path: null,
            image: null,
          },
        ];
        this.selectedTicket = {};
        this.getEventDetails();
        this.$store.dispatch("loadPromotions", {
          date: this.date,
          product_type: "Event",
        });
        if (this.$refs.form) {
          this.$refs.form.resetValidation();
        }
      }
    },
  },
  mounted() {
    this.checkPermission = this.checkExportPermission(
      this.$modules.salesTeam.dashboard.slug
    );
    if (this.checkPermission) {
      this.$store.dispatch("loadSalesTeams", "Event");
      this.$forceUpdate();
    }
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$store.getters.getIdProofTypes.status == false) {
      this.$store.dispatch("loadIDProofTypes");
    }
    if (this.$store.getters.getPaymentMethods.status == false) {
      this.$store.dispatch("loadPaymentMethods", "normal");
    }
    if (this.$store.getters.getTags.status == false) {
      this.$store.dispatch("loadTags");
    }
  },
  computed: {
    tags() {
      return this.$store.getters.getTags.data;
    },
    countries() {
      return this.$store.getters.getCountries.data;
    },
    promotions() {
      return this.$store.getters.getPromotions.data;
    },
    idProofTypes() {
      return this.$store.getters.getIdProofTypes.data.filter(i => !['Scanned document','Unified ID'].includes(i.name));
    },
    salesTeams() {
      return this.$store.getters.getSalesTeams.data;
    },
  },
  methods: {
    close() {
      this.event_customers = [];
      this.participant_count = 1;
      this.displayed_participant = 1;
      this.$emit("close");
    },

    getOrderDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/events/bookings/${this.order_id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.hideLoader();
            const data = response.data.data;
            // let customers = [];

            this.$set(
              this.bookingForm,
              "name",
              data.customer.first_name +
                `${
                  data.customer.last_name ? " " + data.customer.last_name : ""
                }`
            );
            data.customer.customer_id = data.customer.id;
            // this.setCustomerData(data.customer);

            if (data.customer) {
              this.displayed_participant = 1;
              this.bookingForm.name = `${data.customer.first_name}${
                data.customer.last_name ? " " + data.customer.last_name : ""
              }`;
              this.bookingForm.first_name = data.customer.first_name;
              this.bookingForm.last_name = data.customer.last_name;
              this.bookingForm.mobile = data.customer.customer_contact.mobile;
              this.bookingForm.email = data.customer.customer_contact.email;
              this.bookingForm.gender = data.customer.gender;
              this.bookingForm.dob = data.customer.dob;
              this.bookingForm.country_id = data.customer.country_id;
              this.bookingForm.opt_marketing =
                data.customer.opt_marketing == 1 ? true : false;
              if (
                this.bookingForm.customer_type == "member" &&
                !this.bookingForm.card_number
              ) {
                this.searchMember(
                  data.customer.mobile,
                  data.customer.customer_id,
                  data.customer.first_name,
                  data.customer.last_name
                );
              }
            }

            if (data.event_booking) {
              this.bookingForm.id_proof_number =
                data.event_booking.id_proof_number;
              this.bookingForm.id_proof_type_id =
                data.event_booking.id_proof_type_id;
              this.bookingForm.id_proof_number =
                data.event_booking.id_proof_number;
              this.bookingForm.id_proof_file_name =
                data.event_booking.id_proof_file_name;
              this.bookingForm.customer_type = data.event_booking.customer_type;
            }

            if (data.discount != null) {
              if (data.discount.promotion != null) {
                this.bookingForm.promotion_code =
                  data.discount.promotion.promotion_code;
              }
              if (data.discount.member != null) {
                this.bookingForm.card_number = data.discount.member.card_number;
              }
              this.bookingForm.discount = data.discount;
            }
            this.bookingForm.price = data.price;
            this.event_customers.push(this.bookingForm);

            data.event_group_customers.forEach((item) => {
              this.event_customers.push({
                name: `${item.customer.first_name}${
                  item.customer.last_name ? " " + item.customer.last_name : ""
                }`,
                first_name: item.customer.first_name,
                last_name: item.customer.first_name,
                mobile: item.customer.customer_contact.mobile,
                email: item.customer.customer_contact.email,
                gender: item.customer.gender,
                dob: item.customer.dob,
                country_id: item.customer.country_id,
              });
              this.displayed_participant++;
            });

            data.items.forEach((item) => {
              this.bookingForm.products.push({
                order_item_id: item.id,
                price: item.price,
                name: item.name,
                quantity: item.quantity,
                product_id: item.product_id,
                product_type_id: item.product_type_id,
                event_ticket_id: item.event_ticket_id,
                event_id: this.event_id,
              });
            });
            this.calculateMaxParticipants();

            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getEventDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/events/${this.id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.editMode = true;
            let eventData = response.data.data;
            this.event = eventData;
            if (eventData.lineups.length == 0) {
              this.event.lineups = [{}];
            }

            if (eventData.tickets.length == 0) {
              this.event.tickets = [{}];
            }

            this.bookingForm.products = [];
            if (this.order_id) {
              this.getOrderDetails();
            }
            if (this.selectedProducts.length) {
              this.bookingForm.products = this.selectedProducts;
              let price = 0;
              this.selectedProducts.forEach((product) => {
                price += product.price * product.quantity;
              });
              this.bookingForm.price = price;
              this.actualPrice = price;
              this.calculateMaxParticipants();
            }
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    setCardData(data, index) {
      this.setCustomerData(data, index);
    },
    setCustomerData(data, index = 0) {
      //   console.log(data);
      if (data.mobile && data.first_name && data.customer_id) {
        this.isEmiratesIdCheck = false;
        this.searchMember(
          data.mobile,
          data.customer_id,
          data.first_name,
          data.last_name,
          index
        );
      }
      if (!data.customer_id) {
        this.$set(this.event_customers[index], "customer_id", null);
      }
      if (!data.name && data.first_name) {
        this.$set(this.event_customers[index], "name", data.first_name);
      }
      if (
        this.event_customers[index].customer_id &&
        !data.customer_id &&
        this.event_customers[index].name != data.name &&
        this.event_customers[index].mobile != data.mobile
      ) {
        this.$set(this.event_customers[index], "mobile", null);
        this.event_customers[index].search = null;
        this.event_customers[index].nameSearch = null;
        this.$set(this.event_customers[index], "email", null);
        this.$set(this.event_customers[index], "gender", null);
        this.$set(this.event_customers[index], "name", null);
        this.$set(this.event_customers[index], "customer_id", null);
        this.$set(this.event_customers[index], "first_name", null);
        this.$set(this.event_customers[index], "image_path", null);
        this.$set(this.event_customers[index], "dob", null);
        this.$set(this.event_customers[index], "country_id", null);
        this.$set(this.event_customers[index], "last_name", null);
        this.$set(this.event_customers[index], "opt_marketing", false);
        this.$set(this.event_customers[index], "id_proof_type_id", null);
        this.$set(this.event_customers[index], "id_proof_number", null);
        this.$set(this.bookingForm, "customer_tag", null);
        this.$set(this.event_customers[index], "id_proof_path", null);
        this.$forceUpdate();
      }
      if (data.mobile)
        this.$set(this.event_customers[index], "mobile", data.mobile);
      if (data.email)
        this.$set(this.event_customers[index], "email", data.email);
      if (data.country_id) {
        this.$set(this.event_customers[index], "country_id", data.country_id);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.event_customers[index], "country_id", null);
        }
      }
      if (data.customer_tag) {
        this.$set(
          this.event_customers[index],
          "customer_tag",
          data.customer_tag
        );
      } else {
        this.$set(this.event_customers[index], "customer_tag", null);
      }
      if (data.gender) {
        this.$set(this.event_customers[index], "gender", data.gender);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.event_customers[index], "gender", null);
        }
      }
      if (data.dob) {
        this.$set(this.event_customers[index], "dob", data.dob);
      } else {
        if (!this.isEmiratesIdCheck) {
          this.$set(this.event_customers[index], "dob", null);
        }
      }
      if (data.name) this.$set(this.event_customers[index], "name", data.name);
      if (data.last_name) {
        this.$set(this.event_customers[index], "last_name", data.last_name);
      } else {
        this.$set(this.event_customers[index], "last_name", null);
      }
      if (data.first_name)
        this.$set(this.event_customers[index], "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.event_customers[index], "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.event_customers[index], "image_path", data.image_path);
      } else {
        this.$set(this.event_customers[index], "image_path", null);
      }
      if (data.id_proof_type_id) {
        this.$set(
          this.event_customers[index],
          "id_proof_type_id",
          data.id_proof_type_id
        );
      }
      if (data.id_proof_path) {
        this.$set(
          this.event_customers[index],
          "id_proof_path",
          data.id_proof_path
        );
      } else {
        if (!this.isEmiratesIdCheck && data.customer_id) {
          this.$set(this.event_customers[index], "id_proof_path", null);
        }
      }
      if (data.id_proof) {
        this.$set(this.event_customers[index], "id_proof", data.id_proof);
      } else {
        if (!this.isEmiratesIdCheck && data.customer_id) {
          this.$set(this.event_customers[index], "id_proof", null);
        }
      }
      if (data.id_proof_number) {
        this.$set(
          this.event_customers[index],
          "id_proof_number",
          data.id_proof_number
        );
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.event_customers[index], "opt_marketing", true);
        } else {
          this.$set(this.event_customers[index], "opt_marketing", false);
        }
      }
      if (data.customer_documents) {
        this.event_customers[index].customer_documents =
          data.customer_documents;
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_type_id
        ) {
          this.$set(
            this.event_customers[index],
            "id_proof_type_id",
            data.customer_documents[0].id_proof_type_id
          );
        }
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_number
        ) {
          this.$set(
            this.event_customers[index],
            "id_proof_number",
            data.customer_documents[0].id_proof_number
          );
        }
        if (
          data.customer_documents[0] &&
          data.customer_documents[0].id_proof_path
        ) {
          this.$set(
            this.event_customers[index],
            "id_proof_path",
            data.customer_documents[0].id_proof_path
          );
        }
      } else {
        if (data.customer_id) {
          this.event_customers[index].customer_documents = [];
        }
      }
      this.$forceUpdate();
    },
    setMemberData(data) {
      this.$set(this.bookingForm, "card_number", data.card_number);
      this.bookingForm.customer_type = "member";
      this.setCustomerData(data);
      this.verifyBenefit("membership");
    },
    setCustomerName(data) {
      this.$set(this.bookingForm, "last_name", data.last_name);
      this.$set(this.bookingForm, "first_name", data.first_name);
    },
    confirmImageCapture(image) {
      image.name = this.bookingForm.name
        ? this.bookingForm.name + "_" + moment().format("YYYYMMDDHHSS")
        : "user_image_" + moment().format("YYYYMMDDHHSS");
      this.bookingForm.image = image;
      this.webcamDialog = false;
    },

    calculateMaxParticipants() {
      let old = this.participant_count;
      let max = this.bookingForm.products.reduce(
        (acc, num) =>
          acc +
          parseInt(num.quantity) *
            this.findTicketParticipantCount(num.product_id),
        0
      );
      if (old > max && max < this.displayed_participant) {
        this.displayed_participant = max;
      }
      this.participant_count = max;
    },
    findTicketParticipantCount(productId) {
      let currentTicket = this.event.tickets.findIndex(
        (x) => x.product_id == productId
      );
      return parseInt(this.event.tickets[currentTicket].participant_count ?? 0);
    },
    refreshCustomers() {
      this.event_customers.splice(this.participant_count);
    },

    addCustomer() {
      if (this.displayed_participant < this.participant_count) {
        this.event_customers.push({
          customer_type: "normal",
          member: null,
          company_id: null,
          company_sale_id: null,
          opt_marketing: null,
          mobile: null,
          name: null,
          email: null,
          nameSearch: null,
          customer_id: null,
          gender: null,
          country_id: null,
          id_proof_type_id: null,
          id_proof_number: null,
          id_proof: null,
          id_proof_path: null,
          image: null,
        });
        this.displayed_participant++;
        this.$forceUpdate();
      }
    },

    removeProduct(index) {
      let data = this.bookingForm.products[index];
      this.bookingForm.products.splice(index, 1);
      this.bookingForm.price -= data.price;
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      if (this.bookingForm.products.length == 0) {
        this.clearBenefit();
        this.bookingForm.promotion_code = null;
        this.bookingForm.price = 0;
      }
      this.calculateMaxParticipants();
      //   this.refreshCustomers();
      this.$forceUpdate();
    },
    verifyBenefit(type) {
      if (this.bookingForm.products.length == 0) {
        this.showError("Please add atleast one product");
        return;
      }
      let data = {
        products: [],
      };
      if (type == "promotion") {
        data.promotion_code = this.bookingForm.promotion_code;
        if (data.promotion_code == null) {
          this.clearBenefit();
          return;
        }else{
          if (this.bookingForm.card_number) {
            this.bookingForm.card_number = null;
          }
        }
      } else {
        if (this.bookingForm.card_number) {
          data.card_number = this.bookingForm.card_number;
        } else if (
          this.event_customers[0] &&
          this.event_customers[0].card_number
        ) {
          data.card_number = this.event_customers[0].card_number;
        }
      }
      if (this.bookingForm.mobile) {
        data.mobile = this.bookingForm.mobile;
      }
      if (this.bookingForm.discount) {
        data.products = [];
        this.bookingForm.products.forEach((product) => {
          let pdata = product;
          if (product.discount) {
            pdata.price = product.discount.actual_price;
            delete pdata.discount;
          }
          pdata.tax = pdata.tax_amount ?? pdata.tax;
          pdata.price = pdata.product_price;
          data.products.push(pdata);
        });
      } else {
        data.products = this.bookingForm.products;
        data.products.forEach((element) => {
          if (element) {
            // element.price = element.price / element.quantity;
            element.tax = element.tax_amount ?? element.tax;
          }
        });
      }
      let url = "venues/benefits/verify";
      this.$http
        .post(url, data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            if(data.discount){
              this.bookingForm.discount = data.discount?data.discount:null;
              this.bookingForm.price = data.price;
              this.bookingForm.products = data.products;
            }else{
              this.clearBenefit();
            }
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    clearBenefit() {
      if (this.bookingForm.discount) {
        this.bookingForm.price = this.bookingForm.discount.actual_price;
        this.bookingForm.products.forEach((product, index) => {
          if (product.discount) {
            // this.bookingForm.products[index].price = product.discount.actual_price;
            this.bookingForm.products[index].price = product.actual_price;
          }
          this.bookingForm.products[index].discount = null;
        });
        setTimeout(() => {
          this.bookingForm.discount = null;
          this.$forceUpdate();
        });
      }
    },
    customerTypeChange() {
      if (this.bookingForm.promotion_code == null) {
        this.clearBenefit();
      }
      if (this.event_customers[0] && this.event_customers[0].customer_type == "normal") {
        this.bookingForm.promotion_code = null;
        this.bookingForm.card_number = null;
        this.event_customers[0].promotion_code = null;
        this.event_customers[0].card_number = null;
        this.event_customers[0].customer_type == "normal";
        this.clearBenefit();
      }
      if (this.event_customers[0] && this.event_customers[0].customer_type == "member") {
        this.bookingForm.promotion_code = null;
        this.event_customers[0].promotion_code = null
        this.clearBenefit();
      }
    },

    confirmSinglePayment() {
      let booking = this.bookingForm.repeat_dates.find(
        (item) => item.date == this.date
      );
      this.confirmModel = {
        id: booking.id,
        title: `Pay only for ${moment(booking.date).format("Do MMM YYYY")}?`,
        description: `Do you want to seperately invoice for ${moment(
          booking.date
        ).format(
          "Do MMM YYYY"
        )} from other repeat dates? By clicking <b>Yes</b> you can confirm creating new invoice`,
        type: "singleInvoice",
      };
    },
    cancelSingleDate() {
      let booking = this.bookingForm.repeat_dates.find(
        (item) => item.date == this.date
      );
      this.confirmModel = {
        id: booking.id,
        title: `Cancel ${moment(booking.date).format("Do MMM YYYY")} booking?`,
        description: `Do you want to cancel booking for ${moment(
          booking.date
        ).format(
          "Do MMM YYYY"
        )} from repeated dates? By clicking <b>Yes</b> you can confirm cancelation`,
        type: "singleCancel",
      };
    },
    confirmActions(data) {
      if (data.type == "customer") {
        this.event_customers.splice(data.id, 1);
        this.displayed_participant--;
        // this.isEmiratesIdCheck.splice(data.id, 1);
      }
      this.confirmModel.id = null;
      this.$forceUpdate();
    },
    deleteCustomer(index) {
      this.confirmModel = {
        id: index,
        title: "Do you want to delete this customer?",
        description:
          "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "customer",
      };
      this.$forceUpdate();
    },

    confirmBooking() {
      var formData = new FormData();
      for (let key in this.bookingForm) {
        if (
          this.bookingForm[key] != null &&
          ![
            "id",
            "products",
            "search",
            "member",
            "discount",
            "customer_type",
            "card_number",
          ].includes(key)
        ) {
          if (key == "first_name" || key == "last_name") {
            if (!this.bookingForm["first_name"]) {
              formData.append(`first_name`, this.bookingForm["last_name"]);
            } else {
              formData.append(`first_name`, this.bookingForm["first_name"]);
              if (
                this.bookingForm["last_name"] &&
                this.bookingForm["last_name"] != null
              ) {
                formData.append(`last_name`, this.bookingForm["last_name"]);
              }
            }
          } else {
            formData.append(`${key}`, this.bookingForm[key]);
          }
        }
      }
      this.event_customers.forEach((element, index) => {
        if (index == 0) {
          if (element["card_number"]) {
            formData.append(`card_number`, element["card_number"]);
          }
        }
        for (let key in element) {
          if (
            key != "discount" &&
            key != "customer_tag" &&
            element[key] != null
          ) {
            formData.append(`customer[${index}][${key}]`, element[key]);
          } else if (key == "customer_tag" && element[key] != null) {
            element[key].forEach((tag, tagKey) => {
              formData.append(
                `customer[${index}][${key}][${tagKey}][id]`,
                tag.id
              );
              formData.append(
                `customer[${index}][${key}][${tagKey}][name]`,
                tag.name
              );
            });
          }
        }
      });

      this.bookingForm.products.forEach((element, index) => {
        for (let key in element) {
          if (key != "discount" && key != "seats") {
            formData.append(`products[${index}][${key}]`, element[key]);
          }
          if (key == "seats") {
            element[key].forEach((seat, seatKey) => {
              formData.append(
                `products[${index}][${key}][${seatKey}][seat_name]`,
                seat.seat_name
              );
              formData.append(
                `products[${index}][${key}][${seatKey}][seat_map_section_id]`,
                seat.seat_map_section_id
              );
            });
          }
        }
      });

      formData.append("event_id", this.event.id);
      formData.append("seat_map_id", this.event.seat_map_id);
      formData.append("date", this.date);
      if (this.bookingForm.sales_team_id)
        formData.append("sales_team_id", this.bookingForm.sales_team_id);
      this.$http
        .post(
          `venues/events/booking${this.order_id ? "/" + this.order_id : ""}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data; boundary=${form._boundary}",
            },
          }
        )
        .then((response) => {
          this.hideLoader();
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess(
              this.order_id
                ? "Booking successfully updated"
                : "Booking successfully added"
            );
            let data = response.data.data;
            this.event_customers = [];
            this.participant_count = 1;
            this.displayed_participant = 1;
            this.$emit("booked", data.id);
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    timeFormat(time) {
      return moment(time, "HH:mm:ss").format("hh:mm a");
    },

    confirmCancel() {
      this.confirmModel = {
        id: this.order_id,
        title: `Do you want cancel this booking?`,
        description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel",
      };
    },

    searchMember(mobile, id, first_name, last_name, index = 0) {
      console.log(id, first_name, last_name, mobile);
      this.isSearchLoading = true;
      let query = "";
      if (typeof id != "undefined" && id != null) {
        query = `field=id&search=${id}`;
      } else {
        if (typeof mobile != "undefined") {
          query = `field=mobile_number&search=${mobile}`;
        }
      }
      if (query != "") {
        this.$http
          .get(`venues/memberships/members/filters?${query}`)
          .then((response) => {
            if (response.status == 200) {
              let data = response.data.data;
              if (data.length > 0) {
                this.bookingForm.promotion_code = null;
                if (data[0].card_number) {
                  this.bookingForm.card_number = data[0].card_number;
                  this.event_customers[index].customer_type = "member";
                  this.$set(
                    this.event_customers[index],
                    "card_number",
                    data[0].card_number
                  );
                  this.$forceUpdate();
                  if (
                    this.bookingForm &&
                    this.bookingForm.products.length > 0
                  ) {
                    this.verifyBenefit("membership");
                  }
                }
              } else {
                this.bookingForm.member = null;
                this.event_customers[index].customer_type = 'normal';
                this.$set(this.bookingForm, "card_number", null);
                if (this.bookingForm.promotion_code == null) {
                  this.clearBenefit();
                }
              }
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
      }
    },
    changeIdProofTypeId() {
      if (
        this.bookingForm.customer_documents &&
        this.bookingForm.customer_documents.length
      ) {
        let objType = this.bookingForm.customer_documents.find((x) => {
          return x.id_proof_type_id === this.bookingForm.id_proof_type_id;
        });
        if (typeof objType !== "undefined" && objType.id_proof_type_id) {
          this.bookingForm.id_proof_number = objType.id_proof_number;
          this.bookingForm.id_proof_path = objType.id_proof_path;
        } else {
          this.bookingForm.id_proof_number = null;
          this.bookingForm.id_proof_path = null;
        }
      } else {
        // console.log("document length 0");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$card-outlined-border-width: 3px;
</style>

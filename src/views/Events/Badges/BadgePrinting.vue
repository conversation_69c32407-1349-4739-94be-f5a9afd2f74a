<template>
  <div class="editor-wrapper w-full pb-12">
    <div class="d-flex justify-space-between align-center editor-container">
      <BackButton :handler="goToEvent"/>
      <v-btn text @click="goToBadgeConfiguration" class="text-capitalize">
        <SvgIcon text="Configuration">
          <template #icon>
            <ConfigurationIcon opacity="1"/>
          </template>
          </SvgIcon>
      </v-btn>
    </div>
    <!-- Canvas -->
    <div class="d-flex justify-center align-center h-full editor-container gap-y-8 flex-column relative">
      <div
          :style="{ width: actualWidth + 'px', height: (actualHeight + 30) + 'px' }"
          v-if="this.design && this.design.front"
          class="canvas-container"
      >
        <p>
          Preview Design (Front)
        </p>
        <canvas ref="canvas" :height="actualHeight" :width="actualWidth"/>
      </div>
      <div
          v-if="this.design && this.design.back && this.design.back.file"
          :style="{ width: actualWidth + 'px', height: (actualHeight + 30) + 'px' }"
          class="canvas-container"
      >
        <p>
        Preview Design (Back)
      </p>
        <canvas ref="canvasBack" :height="actualHeight" :width="actualWidth"/>
      </div>
    </div>
    <div class="sidebar-right">
      <div class="d-flex align-center px-4 pb-2 mb-4 border-bottom mt-12">
        <h2 class="px-0">
          Scanned Tickets
        </h2>
        <p class="text-neon mb-0 ml-auto">
          Total : {{ customers.length }}
        </p>
      </div>
      <div class="px-4" style="height: inherit">
        <v-text-field
            v-model="search"
            class="q-text-field shadow-0 search-input "
            dense
            hide-details="auto"
            outlined

        >
          <template #prepend-inner>
            <SearchIcon/>
          </template>
        </v-text-field>
        <div v-if="filteredCustomers.length > 0" class="overflow-y-auto pr-4" style="height: 75%">
          <div v-for="customer in filteredCustomers"
               :key="customer.ticket_code" class="d-flex justify-space-between align-center py-4 border-bottom gap-x-4">
            <div>
              <p class="mb-1">
                {{ customer.first_name }} {{ customer.last_name }}
              </p>
              <p class="mb-1 text-sm text-light-gray">
                {{ customer.ticket_name }}
              </p>
            </div>
            <v-btn class="ml-auto p-0" elevation="0" min-width="32" style="min-width: 32px !important;"
                   @click="viewDesign(customer)">
              <EyeIcon/>
            </v-btn>
            <v-btn class="bg-blue text-white px-0"  min-width="32px" style="min-width: 32px !important;" height="32px" elevation="0" @click="printBadge(customer)">
              <PrintIcon/>
            </v-btn>
          </div>
        </div>
        <div v-else class="p-4 font-semibold text-lg">
          No Customers Found
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import BackButton from '@/components/Common/BackButton.vue'
import SearchIcon from '@/assets/images/misc/search.svg'
import PrintIcon from '@/assets/images/misc/print-icon.svg'
import EyeIcon from '@/assets/images/events/eye.svg'
import ConfigurationIcon from '@/assets/images/partners/configuration.svg'
import SvgIcon from '@/components/Image/SvgIcon.vue'
import moment from 'moment'

export default {
  components: {
    SvgIcon,
    ConfigurationIcon,
    SearchIcon,
    BackButton,
    EyeIcon,
    PrintIcon
  },
  watch: {
    design: {
      handler () {
        this.draw()
        this.drawBack()
      },
      deep: true,
    }
  },
  data () {
    return {
      selectedTicket: null,
      canvasWidth: 600,
      canvasHeight: 350,
      design: {},
      tickets: [],
      badges: [],
      customers: [],
      search: '',
    }
  },
  computed: {
    actualWidth () {
      return this.canvasWidth
    },
    actualHeight () {
      return this.canvasHeight
    },
    filteredCustomers () {
      if (this.search.length === 0) return this.customers
      return this.customers.filter(customer => {
        const name = customer.first_name + (customer.last_name?' ' + customer.last_name:'');
        return name.toLowerCase().includes(this.search.toLowerCase());
      })
    }
  },
  methods: {
    draw () {
      let ctx = this.$refs.canvas?.getContext('2d');
      if (!ctx) return;
      ctx.clearRect(0, 0, this.actualWidth, this.actualHeight)
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, this.actualWidth, this.actualHeight)
      const canvasData = this.design.front
      if (canvasData.imageLoaded && canvasData.image && this.isRenderAbleImageSource(canvasData.image)) {
        ctx.drawImage(canvasData.image, canvasData.imageOffset.x, canvasData.imageOffset.y)
      }

      canvasData.texts.forEach((text) => {
        ctx.fillStyle = text.color
        ctx.font = `${text.style} ${text.weight} ${text.size}px sans-serif`
        ctx.fillText(text.content, text.x + 20, text.y)
        // ctx.textAlign = 'center'
        // ctx.textBaseline = 'middle'
      })
    },
    drawBack () {
      if (!this.design?.back?.file || !this.$refs.canvasBack) {
        return
      }
      const ctx = this.$refs.canvasBack.getContext('2d')
      ctx.clearRect(0, 0, this.actualWidth, this.actualHeight)
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, this.actualWidth, this.actualHeight)
      const canvasData = this.design.back
      if (canvasData.imageLoaded && canvasData.image && this.isRenderAbleImageSource(canvasData.image)) {
        ctx.drawImage(canvasData.image, canvasData.imageOffset.x, canvasData.imageOffset.y)
      }

      canvasData.texts.forEach((text) => {
        ctx.fillStyle = text.color
        ctx.font = `${text.style} ${text.weight} ${text.size}px sans-serif`
        ctx.fillText(text.content, text.x + 20, text.y)
        // ctx.textAlign = 'center'
        // ctx.textBaseline = 'middle'
      })
    },
    isRenderAbleImageSource (value) {
      return (
          value instanceof HTMLImageElement ||
          value instanceof SVGImageElement ||
          value instanceof HTMLCanvasElement ||
          value instanceof HTMLVideoElement ||
          (typeof OffscreenCanvas !== 'undefined' && value instanceof OffscreenCanvas) ||
          (typeof ImageBitmap !== 'undefined' && value instanceof ImageBitmap)
      )
    },
    loadTickets () {
      this.$http
          .get('venues/events/badge/tickets/' + atob(this.$route.params.id))
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let eventData = response.data.data
              if (eventData) {
                this.tickets = eventData
                this.selectedTicket = this.tickets[0]
                this.loadTicketConfigurations()
              }
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    loadCheckinCustomer () {
      this.$http.get('venues/events/checkin/customer', {
        params: {
          event_id: atob(this.$route.params.id),
        }
      }).then((response) => {
        if (response.status == 200 && response.data.status == true) {
          this.customers = response.data.data
        }
      }).catch((error) => {
        this.errorChecker(error)
      })
    },
    loadTicketConfigurations () {
      this.loading = true
      this.$http
          .get('venues/events/badge/tickets/configurations', {
            params: {
              event_ticket_ids: this.tickets.map(i => i.id)
            }
          })
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.badges = response.data.data
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    goToEvent () {
      this.$router.push({
        name: 'EventsView',
        params: { data: this.$route.params.id },
      })
    },
    goToBadgeConfiguration () {
      this.$router.push({
        name: 'BadgeConfigurations',
        params: { id: this.$route.params.id },
      })
    },
    async printBadge (customer) {
      const ticketDesign = this.badges.find(i => {
        return i.event_ticket_id === customer.event_ticket_id
      })
      if (!ticketDesign) {
        this.showError('Badge Design not configured')
        return
      }
      this.viewDesign(customer)
      await this.$nextTick()

      setTimeout(() => {
        const frontCanvas = this.$refs.canvas
        let backCanvas = this.$refs.canvasBack
        if (this.design && this.design.back.file) {
          backCanvas = this.$refs.canvasBack
        }
        const frontDataURL = frontCanvas.toDataURL('image/png')
        let backDataURL = null
        if (backCanvas) {
          backDataURL = backCanvas.toDataURL('image/png')
        }

        const printWindow = window.open('', '_blank')
        let html = `<html>
        <head>
          <title>Print Badge</title>
          <style>
            body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: sans-serif;
          }
            .page {
              page-break-after: always;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
          }
            img {
              max-width: 100%;
            height: auto;
          }
          </style>
        </head>
        <body>
        <div class="page">
          <img src="${frontDataURL}" style="margin-bottom: 1rem;display: block" /></div>`
        if (backDataURL) {
          html += `<div class="page"><img src='${backDataURL}' /></div>`
        }
        html += `
        <script>
          window.onload = function() {
          window.print();
          window.onafterprint = function() {
          window.close();
        };
        };`
         // eslint-disable-next-line
        html +=`<\/script>
        </body>
        </html>`
        printWindow.document.write(html)

        printWindow.document.close()
      }, 500)
    },
    viewDesign (customer) {
      const ticketDesign = this.badges.find(i => {
        return i.event_ticket_id === customer.event_ticket_id
      })
      if (!ticketDesign) {
        this.showError('Badge Design not configured')
        return
      }
      const config = JSON.parse(JSON.stringify(ticketDesign.badge_configuration))
      config.front.texts = config.front.texts.map((text) => {
        let content = ''
        const slug = text.slug.toLowerCase();
        if (slug === 'name') {
          content = customer.first_name + ' ' + customer.last_name
        } else if (slug === 'gender') {
          content = customer.gender
        } else if (slug === 'email') {
          content = customer.email
        } else if (slug === 'mobile') {
          content = customer.mobile
        } else if (slug === 'dob') {
          content = customer.dob?moment(customer.dob).format("DD MMM, YYYY"):'';
        } else if (slug === 'nationality') {
          content = customer.nationality
        } else if (customer.additional_data) {
          let data = JSON.parse(customer.additional_data);
          content = data[slug] || '';
        }
        return {
          ...text,
          content: content,
        }
      })
      config.back.texts = config.back.texts.map((text) => {
        let content = ''
        const slug = text.slug.toLowerCase();
        if (slug === 'name') {
          content = customer.first_name + ' ' + customer.last_name
        } else if (slug === 'gender') {
          content = customer.gender
        } else if (slug === 'email') {
          content = customer.email
        } else if (slug === 'mobile') {
          content = customer.mobile
        } else if (slug === 'dob') {
          content = customer.dob?moment(customer.dob).format("DD MMM, YYYY"):'';
        } else if (slug === 'nationality') {
          content = customer.nationality
        } else if (customer.additional_data) {
          let data = JSON.parse(customer.additional_data);
          content = data[slug] || '';
        }
        return {
          ...text,
          content: content,
        }
      })
      this.design = config
      this.canvasWidth = ticketDesign.width
      this.canvasHeight = ticketDesign.height
      if (this.design.front.file) {
        const frontImageUrl = this.s3BucketURL + this.design.front.file
        const frontImage = new Image()
        frontImage.crossOrigin = 'anonymous'
        frontImage.onload = () => {
          this.design.front.image = frontImage
          this.createFileFromImageUrl(frontImageUrl, 'front', 'image').then(file => {
            this.design.front.file = file
            this.draw()
            this.drawBack()
          })
        }
        frontImage.src = frontImageUrl
      }

      if (this.design.back.file) {
        const backImageUrl = this.s3BucketURL + this.design.back.file
        const backImage = new Image()
        backImage.crossOrigin = 'anonymous'
        backImage.onload = () => {
          this.design.back.image = backImage
          this.createFileFromImageUrl(backImageUrl, 'back', 'image').then(file => {
            this.design.back.file = file
            this.draw()
            this.drawBack()
          })
        }
        backImage.src = backImageUrl
      }
      setTimeout(()=>{
        this.draw();
        this.drawBack()
      },10)
    }
  },
  mounted () {
    this.loadTickets()
    this.loadCheckinCustomer()
  },
}
</script>

<style lang="scss" scoped>
.search-input{
  margin-top: 12px !important;
}
.editor-wrapper {
  font-family: Arial, sans-serif;

  .editor-container {
    width: calc(100% - 25rem);
  }

  .controls {
    padding: 20px;
  }

  .text-controls {
    bottom: 10%;
  }

  .text-controls,
  .actions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;

    input[type='number'] {
      width: 60px;
    }

    input[type='color'] {
      width: 40px;
      height: 30px;
      padding: 0;
      border: none;
    }
  }

  .canvas-container {
    //border: 1px solid #ccc;
    position: relative;
    user-select: none;
    cursor: grab;
  }
}

.sidebar-right {
  position: fixed;
  top: 0;
  right: 0;
  width: 25rem;
  height: 100%;
  background-color: #FFFFFF;
  color: #000000;
  padding-top: 20px;
  padding-bottom: 4rem;
  overflow-y: hidden;
  z-index: 3;
  display: flex;
  flex-direction: column;
  h2 {
    margin-top: 0;
    padding-left: 20px;
    padding-right: 20px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 10px 0;
    }
  }
}

.image-input {
  margin-bottom: 1rem;
  margin-top: 1rem;
  display: block;

  input {
    display: none;
  }

  span {
    margin-top: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    height: 3rem;
    border-radius: 0.375rem;
    border: 1px solid #F2F2F2;
    background: #F5F8FA;
  }
}

.image-sides {
  button {
    padding: 0.5rem 2rem;
    border-radius: 0.375rem;
    border-top: 1px solid rgba(17, 42, 70, 0.10);
    border-bottom: 1px solid rgba(17, 42, 70, 0.10);
  }

  button:first-child {
    border-left: 1px solid rgba(17, 42, 70, 0.10);
    margin-right: -5px;

    &.active {
      border-right: 1px solid;
      border-color: #0F2A4D;
      background: #fff;
    }
  }

  button:last-child {
    border-right: 1px solid rgba(17, 42, 70, 0.10);

    &.active {
      border-left: 1px solid;
      border-color: #0F2A4D;
      background: #fff;
    }
  }
}

.number-input {
  position: relative;

  .size-input {
    width: 4.3125rem;
    height: 3rem;
    flex-shrink: 0;
    border-radius: 0.375rem;
    border: 1px solid rgba(17, 42, 70, 0.10);
    padding-left: 0.375rem;
    font-size: 1.25rem;
    /* Chrome, Safari, Edge, Opera */
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &[type="number"] {
      -moz-appearance: textfield;
    }
  }

  .button-group {
    position: absolute;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    right: 1px;
    background: white;
    height: 39px;
    top: 2px;
    z-index: 1;
    border-left: 1px solid rgba(17, 42, 70, 0.10);

    p {
      cursor: pointer;
      line-height: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      width: 16px;

      &:first-child {
        border-bottom: 1px solid rgba(17, 42, 70, 0.10);
      }
    }
  }
}

.active-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: rgba(0, 177, 175);
  border-radius: 0.5rem;

  svg {
    color: #fff;

    path {
      stroke-width: 1.5px;
    }
  }
}

.preview-button {
  border-radius: 0.25rem;
  background: #E8ECF0;
  color: #0F2A4D;

  &:hover {
    background: #0F2A4D !important;
    color: #fff;
  }
}
</style>

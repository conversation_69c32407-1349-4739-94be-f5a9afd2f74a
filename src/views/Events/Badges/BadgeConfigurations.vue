<template>
  <div class="editor-wrapper w-full">
    <BackButton :handler="goToBadgePrinting"/>
    <!-- Canvas -->
    <div class="d-flex justify-center align-center h-full editor-container flex-column relative pb-16">
      <h2 v-if="selectedTicket" class="mb-8 text-xl text-neon font-bold">
        {{ selectedTicket.name }}
      </h2>
      <p
          :style="{ width: actualWidth + 'px' }"
          class="text_capitalize text-left mb-2"
      >
        {{ activeSide }} Side
      </p>
      <div
          :style="{ width: actualWidth + 'px', height: actualHeight + 'px' }"
          class="canvas-container"
          @mousedown="startDrag"
          @mouseleave="stopDrag"
          @mousemove="onDrag"
          @mouseup="stopDrag"
      >
        <canvas ref="canvas" :height="actualHeight" :width="actualWidth"/>
      </div>
      <div class="d-flex justify-center align-center image-sides mt-8">
        <button :class="{'active':activeSide === 'front'}" type="button" @click="activeSide='front'">Front</button>
        <button :class="{'active':activeSide === 'back'}" type="button" @click="activeSide='back'">Back</button>
      </div>
      <div v-if="activeCanvas.selectedTextIndex !== null"
           class="text-controls absolute align-center bg-white shadow p-4 rounded-lg" ref="textControls">
        <div class="number-input d-flex align-center">
          <input :value="activeCanvas.texts[activeCanvas.selectedTextIndex].size" class="size-input" type="number"
                 @input="activeCanvas.texts[activeCanvas.selectedTextIndex].size = +($event.target.value>12?$event.target.value:12)">
          <div class="button-group">
            <p class="mb-0" @click="increase(activeCanvas.selectedTextIndex)">
              <IncreaseIcon/>
            </p>
            <p class="mb-0" @click="decrease(activeCanvas.selectedTextIndex)">
              <DecreaseIcon/>
            </p>
          </div>
        </div>
        <div class="text-center py-1" style="height: 37px">
          <button type="button" @click="activeCanvas.textColorDialog=true">
            <ColorIcon/>
          </button>
          <v-dialog v-model="activeCanvas.textColorDialog" max-width="290" persistent>
            <v-card>
              <v-card-title class="text-h5">
                Select text color
              </v-card-title>
              <v-card-text>
                <v-color-picker
                    v-model="activeCanvas.texts[activeCanvas.selectedTextIndex].color"
                    mode="hexa"
                ></v-color-picker>
              </v-card-text>
              <v-card-actions>
                <v-spacer></v-spacer>
                <!-- <v-btn color="red" text @click="dialogSecondary = false">
                  Cancel
                </v-btn> -->
                <v-btn
                    color="green darken-1"
                    outlined
                    @click="activeCanvas.textColorDialog = false"
                >
                  Done
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </div>
        <div :class="{'active-svg':activeCanvas.texts[activeCanvas.selectedTextIndex].weight === '700'}" class="pa-1 d-flex align-center">
          <BoldIcon class="pointer" @click="toggleFontWeight(activeCanvas.selectedTextIndex)"/>
        </div>
        <div :class="{'active-svg':activeCanvas.texts[activeCanvas.selectedTextIndex].style === 'italic'}" class="pa-1 d-flex align-center">
          <ItalicIcon class="pointer" @click="toggleItalic(activeCanvas.selectedTextIndex)"/>
        </div>
      </div>
    </div>
    <div class="sidebar-right">
      <h2 class="border-bottom pb-2">Badge Configuration</h2>
      <div class="controls">
        <div class="controls-width">
          <div>
            <label for="">Select Ticket</label>
            <v-select
                v-model="selectedTicket"
                :items="tickets"
                :menu-props="{ bottom: true, offsetY: true }"
                class="q-autocomplete shadow-0"
                dense
                hide-details="auto"
                item-text="name"
                item-value="id"
                outlined
                return-object
            />
          </div>
          <div>

            <label for="">Select Field(s)</label>
            <v-select
                v-model="activeCanvas.fields"
                :items="fields"
                :menu-props="{ bottom: true, offsetY: true }"
                class="q-autocomplete shadow-0"
                dense
                hide-details="auto"
                item-text="name"
                item-value="slug"
                multiple
                outlined
            />
          </div>
          <v-row class="mx-0">
            <v-col class="pl-0">
              <label for="">
                Width
              </label>
              <v-text-field
                  v-model.number="canvasWidth"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  outlined
                  required
                  type="number"
              ></v-text-field>
            </v-col>
            <v-col class="pr-0">
              <label for="">
                Height
              </label>
              <v-text-field
                  v-model.number="canvasHeight"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  outlined
                  required
                  type="number"
              ></v-text-field>
            </v-col>
          </v-row>
          <label class="image-input">
            Front Side (.jpeg, .jpg, .png )
            <input accept="image/*" type="file" @change="onFileChange($event,'front')"/>
            <span class="text-capitalize text-truncate">
              <UploadIcon height="22" width="22"/> {{
                this.design.front && this.design.front.file ? this.design.front.file.name : 'Upload File'
              }}
            </span>
          </label>
          <label class="image-input">
            Back Side (.jpeg, .jpg, .png )
            <input accept="image/*" type="file" @change="onFileChange($event,'back')"/>
            <span class="text-capitalize text-truncate">
              <UploadIcon height="22" width="22"/> {{
                this.design.back && this.design.back.file ? this.design.back.file.name : 'Upload File'
              }}
            </span>
          </label>
        </div>
      </div>
      <div class="mt-auto px-6">
        <v-btn block class="mb-4 preview-button text_capitalize font-semibold" elevation="0" @click="previewDesign">
          Preview
        </v-btn>
        <v-btn
            v-if="checkReadPermission($modules.events.badge_printing.slug)"
            block
            class="bg-blue text-white text_capitalize font-semibold"
            @click="saveTicketConfiguration"
        >
          Save
        </v-btn>
      </div>
    </div>
    <v-dialog v-model="previewDesignModal" :width="actualWidth+160" @input="previewDesignModal = false">
      <v-card>
        <v-card-title class="border-bottom">
          <div class=" w-full">
            <div class="d-flex justify-space-between align-center">
              <p class="mb-0 font-medium">
                Design Preview
              </p>
              <v-btn class="shadow-0" fab x-small @click="previewDesignModal = false">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </v-card-title>
        <v-card-text class="mt-4 d-flex justify-center align-center gap-x-4 gap-y-4 flex-column pb-8 pt-4">
          <div
              :style="{ width: actualWidth + 'px', height: (actualHeight + 30) + 'px' }"
              class="canvas-container"
          >
            <p class="mb-2 font-semibold">Front</p>
            <canvas ref="previewCanvasFront" :height="actualHeight" :width="actualWidth"/>
          </div>
          <div
              v-if="this.design.back && this.design.back.file"
              :style="{ width: actualWidth + 'px', height: (actualHeight + 30) + 'px' }"
              class="canvas-container"
          >
            <p class="mb-2 font-semibold">Back</p>
            <canvas ref="previewCanvasBack" :height="actualHeight" :width="actualWidth"/>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import UploadIcon from '@/assets/images/misc/upload-arrow.svg'
import BoldIcon from '@/assets/images/misc/bold.svg'
import IncreaseIcon from '@/assets/images/misc/increase.svg'
import DecreaseIcon from '@/assets/images/misc/decrease.svg'
import ColorIcon from '@/assets/images/misc/color-icon.svg'
import ItalicIcon from '@/assets/images/misc/italic.svg'
import BackButton from '@/components/Common/BackButton.vue'

export default {
  components: {
    BackButton,
    UploadIcon,
    BoldIcon,
    IncreaseIcon,
    DecreaseIcon,
    ColorIcon,
    ItalicIcon
  },
  data () {
    return {
      previewDesignModal: false,
      initialState: {
        front: {
          fields: [],
          imageLoaded: false,
          imageOffset: { x: 0, y: 0 },
          image: null,
          file: null,
          isDraggingImage: false,
          isDraggingText: false,
          dragStart: { x: 0, y: 0 },
          startOffset: { x: 0, y: 0 },
          texts: [],
          selectedTextIndex: null,
          textColorDialog: false,
          newFileUploaded: false
        },
        back: {
          fields: [],
          imageLoaded: false,
          imageOffset: { x: 0, y: 0 },
          image: null,
          file: null,
          isDraggingImage: false,
          isDraggingText: false,
          dragStart: { x: 0, y: 0 },
          startOffset: { x: 0, y: 0 },
          texts: [],
          selectedTextIndex: null,
          textColorDialog: false,
          newFileUploaded: false
        },
      },
      activeSide: 'front',
      selectedTicket: null,
      canvasWidth: 600,
      canvasHeight: 350,
      design: {},
      tickets: [],
      fields: [],
      debounceTimeout:null
    }
  },
  watch: {
    canvasWidth () {
      this.debouncedDraw()
    },
    canvasHeight () {
      this.debouncedDraw()
    },
    'design.front.texts': {
      handler () {
        setTimeout(() => {
          this.draw()
        }, 10)
      },
      deep: true,
    },
    'design.back.texts': {
      handler () {
        setTimeout(() => {
          this.draw()
        }, 10)
      },
      deep: true,
    },
    'design.front.fields' () {
      this.activeCanvas.selectedTextIndex = null
      this.activeCanvas.fields.forEach(field => {
        const exist = this.activeCanvas.texts.findIndex(ele => ele.slug === field)
        if (exist >= 0) {
          return
        }
        this.addText(field, field)
      })
      this.activeCanvas.texts = this.activeCanvas.texts.filter(ele => {
        return this.activeCanvas.fields.findIndex(field => ele.slug === field) > -1
      })

      setTimeout(() => {
        this.draw()
      }, 10)
    },
    'design.back.fields' () {
      this.activeCanvas.selectedTextIndex = null
      this.activeCanvas.fields.forEach(field => {
        const exist = this.activeCanvas.texts.findIndex(ele => ele.slug === field)
        if (exist >= 0) {
          return
        }
        this.addText(field, field)
      })
      this.activeCanvas.texts = this.activeCanvas.texts.filter(ele => {
        return this.activeCanvas.fields.findIndex(field => ele.slug === field) > -1
      })
      setTimeout(() => {
        this.draw()
      }, 10)
    },
    activeSide () {
      setTimeout(() => {
        this.draw()
      }, 10)
    },
    selectedTicket () {
      if (this.selectedTicket) {
        this.loadTicketConfiguration()
      }
    },
  },
  computed: {
    actualWidth () {
      return this.canvasWidth
    },
    actualHeight () {
      return this.canvasHeight
    },
    activeCanvas: {
      get: function () {
        return this.design[this.activeSide]
      },
      set: function (val) {
        this.design[this.activeSide] = val
      }
    }
  },
  methods: {
    debouncedDraw() {
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = setTimeout(() => {
        this.draw();
      }, 200);
    },
    toggleItalic (selectedTextIndex) {
      this.activeCanvas.texts[selectedTextIndex].style = this.activeCanvas.texts[selectedTextIndex].style === 'normal' ? 'italic' : 'normal'
    },
    toggleFontWeight (selectedTextIndex) {
      this.activeCanvas.texts[selectedTextIndex].weight = this.activeCanvas.texts[selectedTextIndex].weight === '700' ? '400' : '700'
    },
    increase (index) {
      this.activeCanvas.texts[index].size = this.activeCanvas.texts[index].size + 1
    },
    decrease (index) {
      if ((this.activeCanvas.texts[index].size - 1) < 12){
        return;
      }
      this.activeCanvas.texts[index].size = this.activeCanvas.texts[index].size - 1
    },
    onFileChange (event, side) {
      const file = event.target.files[0]
      if (file && file.type.startsWith('image/')) {
        const reader = new FileReader()
        const canvasData = this.design[side]
        reader.onload = (e) => {
          const img = new Image()
          img.onload = () => {
            canvasData.image = img
            canvasData.file = file
            canvasData.imageLoaded = true
            canvasData.imageOffset = { x: 0, y: 0 }
            canvasData.newFileUploaded = true
            this.draw()
          }
          img.src = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    addText (text, slug) {
      this.activeCanvas.texts.push({
        slug,
        content: text || 'New Text',
        x: this.canvasWidth / 2,
        y: (this.canvasHeight / 2) + (this.activeCanvas.texts.length * 20),
        size: 16,
        weight: '400',
        color: '#000000',
        style: 'normal'
      })
      // this.activeCanvas.selectedTextIndex = this.activeCanvas.texts.length - 1
      this.draw()
    },
    draw () {
      console.log("drawing");
      const ctx = this.$refs.canvas.getContext('2d')
      ctx.clearRect(0, 0, this.actualWidth, this.actualHeight)
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, this.actualWidth, this.actualHeight)

      if (this.activeCanvas.imageLoaded && this.activeCanvas.image && this.isRenderAbleImageSource(this.activeCanvas.image)) {
        ctx.drawImage(this.activeCanvas.image, this.activeCanvas.imageOffset.x, this.activeCanvas.imageOffset.y)
      }

      this.activeCanvas.texts.forEach((text, index) => {
        ctx.fillStyle = text.color
        ctx.font = `${text.style} ${text.weight} ${text.size}px sans-serif`
        ctx.fillText(text.content, text.x, text.y)

        if (index === this.activeCanvas.selectedTextIndex) {
          const width = ctx.measureText(text.content).width
          ctx.strokeStyle = '#4FAEAF'
          ctx.strokeRect(text.x - 2, text.y - text.size, width + 4, text.size + 4)
          this.configureControls(text)
        }
      })
    },
    configureControls (text) {
      if (!text) return
      setTimeout(() => {
        const textControls = this.$refs.textControls
        if (!textControls) return
        const canvasEl = this.$refs.canvas

        const rect = canvasEl.getBoundingClientRect()
        const canvasScaleX = canvasEl.width / rect.width
        const canvasScaleY = canvasEl.height / rect.height

        const left = rect.left + (text.x - 280) / canvasScaleX
        const top = rect.top + (text.y - text.size) / canvasScaleY

        // Position the .text-controls element
        Object.assign(textControls.style, {
          position: 'absolute',
          left: `${left}px`,
          top: `${top}px`,
          pointerEvents: 'auto',
          height: '70px',
          zIndex: 2
        })
      }, 10)
    },
    isRenderAbleImageSource (value) {
      return (
          value instanceof HTMLImageElement ||
          value instanceof SVGImageElement ||
          value instanceof HTMLCanvasElement ||
          value instanceof HTMLVideoElement ||
          (typeof OffscreenCanvas !== 'undefined' && value instanceof OffscreenCanvas) ||
          (typeof ImageBitmap !== 'undefined' && value instanceof ImageBitmap)
      )
    },
    startDrag (e) {
      const rect = this.$refs.canvas.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      const ctx = this.$refs.canvas.getContext('2d')

      this.activeCanvas.selectedTextIndex = null
      for (let i = this.activeCanvas.texts.length - 1; i >= 0; i--) {
        const text = this.activeCanvas.texts[i]
        ctx.font = `${text.style} ${text.weight} ${text.size}px sans-serif`
        const width = ctx.measureText(text.content).width
        const height = text.size
        if (x >= text.x && x <= text.x + width && y <= text.y && y >= text.y - height) {
          this.activeCanvas.selectedTextIndex = i
          this.activeCanvas.isDraggingText = true
          this.activeCanvas.dragStart = { x, y }
          this.draw();
          return
        }else{
          this.draw();
        }
      }

      if (this.activeCanvas.imageLoaded) {
        this.activeCanvas.isDraggingImage = true
        this.activeCanvas.dragStart = { x, y }
        this.activeCanvas.startOffset = { ...this.activeCanvas.imageOffset }
      }
    },
    onDrag (e) {
      const rect = this.$refs.canvas.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top
      const dx = x - this.activeCanvas.dragStart.x
      const dy = y - this.activeCanvas.dragStart.y

      if (this.activeCanvas.isDraggingText && this.activeCanvas.selectedTextIndex !== null) {
        const text = this.activeCanvas.texts[this.activeCanvas.selectedTextIndex]
        text.x += dx
        text.y += dy
        this.activeCanvas.dragStart = { x, y }
        this.draw()
        this.configureControls(text)
      } else if (this.activeCanvas.isDraggingImage && this.activeCanvas.image) {
        const newX = this.activeCanvas.startOffset.x + dx
        const newY = this.activeCanvas.startOffset.y + dy

        this.activeCanvas.imageOffset.x = Math.min(0, Math.max(this.actualWidth - this.activeCanvas.image.width, newX))
        this.activeCanvas.imageOffset.y = Math.min(0, Math.max(this.actualHeight - this.activeCanvas.image.height, newY))
        this.draw()
      }
    },
    stopDrag () {
      this.activeCanvas.isDraggingText = false
      this.activeCanvas.isDraggingImage = false
    },
    loadTickets () {
      this.$http
          .get('venues/events/badge/tickets/' + atob(this.$route.params.id))
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let eventData = response.data.data
              if (eventData) {
                this.tickets = eventData
                this.selectedTicket = this.tickets[0]
              }
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    loadTicketConfiguration () {
      this.loading = true
      this.$http
          .get('venues/events/badge/tickets/' + this.selectedTicket.id + '/configuration')
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let ticketConfiguration = response.data.data
              if (ticketConfiguration) {
                this.design = ticketConfiguration.badge_configuration
                this.canvasHeight = ticketConfiguration.height
                this.canvasWidth = ticketConfiguration.width
                this.design.front.newFileUploaded = false
                this.design.back.newFileUploaded = false

                if (this.design.front.file) {
                  const frontImageUrl = this.s3BucketURL + this.design.front.file
                  const frontImage = new Image()
                  frontImage.crossOrigin = 'anonymous'
                  frontImage.onload = () => {
                    this.design.front.image = frontImage
                    const fileName = frontImageUrl.split('/').pop().split('_').pop()
                    this.createFileFromImageUrl(frontImageUrl, fileName, 'image').then(file => {
                      this.design.front.file = file
                      this.draw()
                    })
                  }
                  frontImage.src = frontImageUrl
                }

                if (this.design.back.file) {
                  const backImageUrl = this.s3BucketURL + this.design.back.file
                  const backImage = new Image()
                  backImage.crossOrigin = 'anonymous'
                  backImage.onload = () => {
                    this.design.back.image = backImage
                    const fileName = backImageUrl.split('/').pop().split('_').pop()
                    this.createFileFromImageUrl(backImageUrl, fileName, 'image').then(file => {
                      this.design.back.file = file
                      this.draw()
                    })
                  }
                  backImage.src = backImageUrl
                }
              } else {
                this.design = JSON.parse(JSON.stringify(this.initialState))
              }
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    saveTicketConfiguration () {
      if (!this.design['front']?.file) {
        this.showError('Please upload a file for front')
        return
      }
      this.showLoader('Saving')
      const formData = new FormData()
      Object.keys(this.design).forEach(side => {
        const sideData = this.design[side]

        Object.keys(sideData).forEach(key => {
          const value = sideData[key]
          const formKey = `design[${side}][${key}]`

          if (key === 'file') {
            if (value) {
              formData.append(formKey, value)
            }
          } else if (Array.isArray(value) || typeof value === 'object') {
            formData.append(formKey, JSON.stringify(value))
          } else if (key !== 'image') {
            formData.append(formKey, value)
          }
        })
      })
      formData.append('event_ticket_id', this.selectedTicket.id)
      formData.append('width', this.canvasWidth)
      formData.append('height', this.canvasHeight)
      this.$http.post('venues/events/badge/tickets/' + this.selectedTicket.id + '/configuration', formData)
          .then(response => {
            if (response && response.status == 200 && response.data.status == true) {
              this.showSuccess('Badge configuration saved.')
              this.loadTicketConfiguration()
            }
          }).catch(error => {
        this.errorChecker(error)
      }).finally(() => {
        this.hideLoader()
      })
    },
    goToBadgePrinting () {
      this.$router.push({
        name: 'BadgePrinting',
        params: { id: this.$route.params.id },
      })
    },
    previewDesign () {
      if (!this.design['front']?.file) {
        this.showError('Please upload a file for front')
        return
      }
      this.activeCanvas.selectedTextIndex = null;
      this.draw();
      const canvasData = JSON.parse(JSON.stringify(this.design))
      this.previewDesignModal = true
      setTimeout(() => {
        const frontData = canvasData['front']
        const backData = canvasData['back']
        frontData.image = this.design.front.image
        backData.image = this.design.back.image
        const ctx = this.$refs.previewCanvasFront.getContext('2d')
        ctx.clearRect(0, 0, this.actualWidth, this.actualHeight)
        ctx.fillStyle = '#fff'
        ctx.fillRect(0, 0, this.actualWidth, this.actualHeight)
        if (frontData.imageLoaded && frontData.image && this.isRenderAbleImageSource(frontData.image)) {
          ctx.drawImage(frontData.image, frontData.imageOffset.x, frontData.imageOffset.y)
        }

        frontData.texts.forEach((text, index) => {
          ctx.fillStyle = text.color
          ctx.font = `${text.style} ${text.weight} ${text.size}px sans-serif`
          ctx.fillText(text.content, text.x, text.y)

          if (index === frontData.selectedTextIndex) {
            const width = ctx.measureText(text.content).width
            ctx.strokeStyle = '#4FAEAF'
            ctx.strokeRect(text.x - 2, text.y - text.size, width + 4, text.size + 4)
          }
        })

        const ctxBack = this.$refs.previewCanvasBack?.getContext('2d')
        if (ctxBack) {
          ctxBack.clearRect(0, 0, this.actualWidth, this.actualHeight)
          ctxBack.fillStyle = '#fff'
          ctxBack.fillRect(0, 0, this.actualWidth, this.actualHeight)

          if (backData.imageLoaded && backData.image && this.isRenderAbleImageSource(backData.image)) {
            ctxBack.drawImage(backData.image, backData.imageOffset.x, backData.imageOffset.y)
          }

          backData.texts.forEach((text, index) => {
            ctxBack.fillStyle = text.color
            ctxBack.font = `${text.style} ${text.weight} ${text.size}px sans-serif`
            ctxBack.fillText(text.content, text.x, text.y)

            if (index === backData.selectedTextIndex) {
              const width = ctxBack.measureText(text.content).width
              ctxBack.strokeStyle = '#4FAEAF'
              ctxBack.strokeRect(text.x - 2, text.y - text.size, width + 4, text.size + 4)
            }
          })
        }
      }, 100)
    },
    loadFieldsConfiguration () {
      this.$http.get('/venues/events/badge/tickets/fields')
          .then(response => {
            if (response && response.status == 200) {
              this.fields = response.data.data
            }
          })
          .catch(error => {
            this.errorChecker(error)
          })
    }
  },
  beforeMount () {
    this.design = JSON.parse(JSON.stringify(this.initialState))
  },
  mounted () {
    this.loadTickets()
    this.loadFieldsConfiguration()
    this.draw()
  },
}
</script>

<style lang="scss" scoped>
.editor-wrapper {
  font-family: Arial, sans-serif;

  .editor-container {
    width: calc(100% - 25rem);
  }

  .controls {
    padding: 20px;
  }

  .text-controls {
    position: fixed;
    bottom: 0;
    gap: 1rem !important;
  }

  .text-controls,
  .actions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;

    input[type='number'] {
      width: 60px;
    }

    input[type='color'] {
      width: 40px;
      height: 30px;
      padding: 0;
      border: none;
    }
  }

  .canvas-container {
    //border: 1px solid #ccc;
    position: relative;
    user-select: none;
    cursor: grab;
  }
}

.sidebar-right {
  position: fixed;
  top: 5%;
  right: 0;
  width: 25rem;
  height: 92%;
  background-color: #FFFFFF;
  color: #000000;
  padding-top: 20px;
  padding-bottom: 20px;
  overflow-y: auto;
  z-index: 2;
  display: flex;
  flex-direction: column;

  h2 {
    margin-top: 0;
    padding-left: 20px;
    padding-right: 20px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 10px 0;
    }
  }
}

.image-input {
  margin-bottom: 1rem;
  margin-top: 1rem;
  display: block;

  input {
    display: none;
  }

  span {
    margin-top: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    height: 3rem;
    border-radius: 0.375rem;
    border: 1px solid #F2F2F2;
    background: #F5F8FA;
  }
}

.image-sides {
  button {
    padding: 0.5rem 2rem;
    border-radius: 0.375rem;
    border-top: 1px solid rgba(17, 42, 70, 0.10);
    border-bottom: 1px solid rgba(17, 42, 70, 0.10);
  }

  button:first-child {
    border-left: 1px solid rgba(17, 42, 70, 0.10);
    margin-right: -5px;

    &.active {
      border-right: 1px solid;
      border-color: #0F2A4D;
      background: #fff;
    }
  }

  button:last-child {
    border-right: 1px solid rgba(17, 42, 70, 0.10);

    &.active {
      border-left: 1px solid;
      border-color: #0F2A4D;
      background: #fff;
    }
  }
}

.number-input {
  position: relative;

  .size-input {
    width: 4.3125rem;
    height: 3rem;
    flex-shrink: 0;
    border-radius: 0.375rem;
    border: 1px solid rgba(17, 42, 70, 0.10);
    padding-left: 0.375rem;
    font-size: 1.25rem;
    /* Chrome, Safari, Edge, Opera */
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &[type="number"] {
      -moz-appearance: textfield;
    }
  }

  .button-group {
    position: absolute;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    right: 1px;
    background: white;
    height: 39px;
    top: 2px;
    z-index: 1;
    border-left: 1px solid rgba(17, 42, 70, 0.10);

    p {
      cursor: pointer;
      line-height: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      width: 16px;

      &:first-child {
        border-bottom: 1px solid rgba(17, 42, 70, 0.10);
      }
    }
  }
}

.active-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: rgba(0, 177, 175);
  border-radius: 0.5rem;

  svg {
    color: #fff;

    path {
      stroke-width: 1.5px;
    }
  }
}

.preview-button {
  border-radius: 0.25rem;
  background: #E8ECF0;
  color: #0F2A4D;

  &:hover {
    background: #0F2A4D !important;
    color: #fff;
  }
}
</style>

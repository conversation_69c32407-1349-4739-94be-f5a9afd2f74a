<template>
  <v-container>
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="titles">Event Details</div>
      <v-card rounded flat :style="cardStyle">
        <v-btn
          small
          @click="goToEvents()"
          absolute
          top
          right
          style="top: -42px;"
        >
          <v-icon small>mdi-backburger</v-icon>Back
        </v-btn>
        <v-btn
          small
          @click="enableArabic"
          absolute
          top
          right
          style="top: -42px; margin-right: 7%;"
        >
          <v-icon small>mdi-translate</v-icon> ARA Lang
        </v-btn>
        <v-card-text class="pb-0">
          <v-row>
            <v-col cols="12" sm="3" md="3">
              <image-upload
                @upload="
                  (data) => {
                    event.image = data;
                  }
                "
                @remove="
                  () => {
                    event.image = null;
                  }
                "
                :image_path="event.image_path"
                :height="280"
                defaultImage="event"
              ></image-upload>
            </v-col>
            <v-col cols="12" sm="9" md="9">
              <v-row dense>
                <v-col cols="12" sm="4" md="4">
                  <v-text-field
                    v-model="event.name"
                    label="Event Name*"
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Event Name is required']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <v-select
                    v-model="event.venue_service_id"
                    label="Service*"
                    :items="venueServices"
                    item-text="name"
                    item-value="venue_service_id"
                    outlined
                    background-color="#fff"
                    @change="getFacilities"
                    :rules="[(v) => !!v || 'Service is required']"
                    required
                  ></v-select>
                </v-col>
                <v-col cols="4" md="4">
                  <v-select
                    v-model="event.facility_id"
                    item-value="id"
                    item-text="name"
                    :items="[...facilities, { id: null, name: 'External' }]"
                    label="Facility*"
                    outlined
                    background-color="#fff"
                    @change="changeFacility"
                  ></v-select>
                </v-col>
              </v-row>
              <v-row dense v-if="isEnableArabic">
                <v-col cols="12" sm="12" md="12">
                  <v-text-field
                    v-model="event.ara_name"
                    label="Event Name (AR)*"
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Event Name (AR) is required']"
                    reverse
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="event.type"
                    :items="[
                      { slug: 'F', name: 'Free' },
                      { slug: 'P', name: 'Paid' },
                    ]"
                    label="Event Type*"
                    :rules="[(v) => !!v || 'Event Type is required']"
                    outlined
                    item-text="name"
                    item-value="slug"
                    background-color="#fff"
                    @change="eventTypeChange"
                    :readonly="editMode"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="event.attendees_privacy"
                    :items="[
                      { slug: 'public', name: 'Public' },
                      { slug: 'private', name: 'Private' },
                    ]"
                    label="Attendees Privacy*"
                    :rules="[(v) => !!v || 'Attendees Privacy is required']"
                    outlined
                    item-text="name"
                    item-value="slug"
                    background-color="#fff"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="4" v-if="facility_has_seat_map">
                  <v-select
                    v-model="event.seat_map_id"
                    label="Seat Map*"
                    :items="allMapPlans"
                    item-text="map_name"
                    item-value="id"
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Seat Map is required']"
                    :readonly="editMode"
                    required
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="6" md="6">
                  <date-field
                    v-model="event.start_date"
                    :rules="[(v) => !!v || 'Start date is required']"
                    label="Start date*"
                    :dayName="true"
                    :backFill="
                      checkBackfillPermission($modules.events.management.slug)
                    "
                  ></date-field>
                </v-col>
                <v-col cols="12" sm="6" md="6">
                  <date-field
                    v-model="event.end_date"
                    :rules="[(v) => !!v || 'End date is required']"
                    label="End date*"
                    :dayName="true"
                    :backFill="
                      checkBackfillPermission($modules.events.management.slug)
                    "
                  ></date-field>
                </v-col>
              </v-row>
              <v-row dense> </v-row>
              <v-row no-gutters>
                <v-col cols="12">
                  <v-textarea
                      v-model="event.description"
                      name="description"
                      label="Description"
                      rows="2"
                      outlined
                      background-color="#fff"
                  >
                    <v-tooltip slot="append" top>
                      <template v-slot:activator="{ on }">
                        <v-icon v-on="on" color="primary" dark>
                          mdi-information
                        </v-icon>
                      </template>
                      <span
                      >
                        To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text)
                      </span
                      >
                    </v-tooltip>
                  </v-textarea>
                </v-col>
              </v-row>
              <v-row dense v-if="isEnableArabic">
                <v-col cols="12" sm="12" md="12">
                  <v-textarea
                      v-model="event.ara_description"
                      name="description"
                      label="Description (AR)"
                      outlined
                      background-color="#fff"
                      reverse
                      rows="2"
                      style="direction: rtl"
                  >
                    <v-tooltip slot="append" top>
                      <template v-slot:activator="{ on }">
                        <v-icon v-on="on" color="primary" dark>
                          mdi-information
                        </v-icon>
                      </template>
                      <span
                      >
                        To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text)
                      </span
                      >
                    </v-tooltip>
                  </v-textarea>
                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col cols="4">
                  <v-switch
                      class="mx-0 my-0"
                      v-model="event.is_enable_booking_approval"
                      label="Enable Booking Approval"
                  ></v-switch>
                </v-col>
                <v-col cols="4">
                  <v-switch
                      class="mx-0 my-0"
                      v-model="event.is_public"
                      label="Enable Online"
                  ></v-switch>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      <!--Event Schedules -->
      <div class="titles pt-6">Event Schedules</div>
      <v-card>
        <v-card-text class="pb-10">
          <v-card
            rounded
            flat
            :style="cardStyle"
            class="mb-8"
            v-for="(eTiming, index) in event.event_schedules"
            :key="'et_' + index"
          >
            <v-card-text class="pb-0">
              <v-row>
                <v-col sm="12" md="12">
                  <v-row dense>
                    <v-col cols="3" sm="3" md="3">
                      <date-field
                        v-model="eTiming.start_date"
                        label="Start Date*"
                        :dayName="true"
                        :minDate="event.start_date"
                        :maxDate="event.end_date"
                        :rules="[(v) => !!v || 'Start date is required']"
                        @change="$forceUpdate()"
                      ></date-field>
                    </v-col>
                    <v-col cols="12" sm="3" md="3">
                      <v-select
                        :items="timings.slice(0, timings.length - 1)"
                        label="Start Time*"
                        item-text="text"
                        item-value="value"
                        v-model="eTiming.start_time"
                        outlined
                        background-color="#fff"
                        :rules="[(v) => !!v || 'Start Time is required']"
                      ></v-select>
                    </v-col>
                    <v-col cols="3" sm="3" md="3">
                      <date-field
                        v-model="eTiming.end_date"
                        label="End Date*"
                        :minDate="eTiming.start_date"
                        :maxDate="event.end_date"
                        @change="$forceUpdate()"
                        :dayName="true"
                        :rules="[(v) => !!v || 'End Date is required']"
                      ></date-field>
                    </v-col>
                    <v-col cols="12" sm="2" md="2">
                      <v-select
                        :items="timings.slice(1)"
                        label="End Time*"
                        item-text="text"
                        item-value="value"
                        v-model="eTiming.end_time"
                        outlined
                        background-color="#fff"
                        :rules="[(v) => !!v || 'End Time is required']"
                      ></v-select>
                    </v-col>
                    <v-col md="1">
                      <div class="d-flex justify-space-around">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              fab
                              outlined
                              v-bind="attrs"
                              v-on="on"
                              small
                              class="mt-2 ml-2"
                              @click="duplicateTiming(index)"
                              ><v-icon color="info"
                                >mdi-content-duplicate</v-icon
                              ></v-btn
                            >
                          </template>
                          <span>Duplicate row</span>
                        </v-tooltip>
                        <v-tooltip bottom v-if="index > 0">
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              fab
                              outlined
                              v-bind="attrs"
                              v-on="on"
                              small
                              class="mt-2"
                              @click="deleteTiming(index)"
                              ><v-icon color="red">mdi-delete</v-icon></v-btn
                            >
                          </template>
                          <span>Delete benefit</span>
                        </v-tooltip>
                      </div>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="teal-color"
                  fab
                  x-small
                  dark
                  @click="addTimeInterval"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add Time Interval
            </v-tooltip>
          </div>
        </v-card-text>
      </v-card>

      <!-- End Event Timings-->
      <v-row>
        <v-col cols="6" md="6">
          <div v-if="event.facility_id == null" class="titles mt-8">
            Select External Location
          </div>
          <v-card
            rounded
            flat
            :style="cardStyle"
            v-if="event.facility_id == null"
          >
            <v-btn
              fab
              absolute
              top
              right
              x-small
              elevation="1"
              @click="mapFullScreenDialoge = true"
            >
              <v-icon>mdi-fullscreen</v-icon>
            </v-btn>
            <v-card-text>
              <v-row>
                <v-col cols="12" sm="12" md="12">
                  <v-row no-gutters v-if="!mapFullScreenDialoge">
                    <v-col md="12">
                      <v-autocomplete
                        label="Location"
                        v-model="autocompleteLocationModel"
                        :items="locationEntries"
                        :search-input.sync="locationSearchText"
                        item-text="value"
                        item-value="value"
                        hide-no-data
                        :loading="isLoading"
                        :rules="[(v) => !!v || 'Service is required']"
                        outlined
                        background-color="#fff"
                        @change="changeLocation"
                      ></v-autocomplete>
                    </v-col>
                  </v-row>
                  <GmapMap
                    v-bind:center="{
                      lat: parseFloat(event.latitude),
                      lng: parseFloat(event.longitude),
                    }"
                    :zoom="12"
                    map-type-id="terrain"
                    @click="updateCoordinates"
                    style="width: 100%; height: 200px"
                  >
                    <GmapMarker
                      ref="mapRef"
                      :position="{
                        lat: parseFloat(event.latitude),
                        lng: parseFloat(event.longitude),
                      }"
                      :clickable="true"
                      :draggable="true"
                      @dragend="updateCoordinates"
                    />
                  </GmapMap>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col :md="event.facility_id == null ? 6 : 12">
          <div class="titles mt-6">Document</div>
          <v-card
            rounded
            flat
            :style="cardStyle"
            class="mb-8"
            v-for="(document, k) in event.documents"
            :key="'d_' + k"
          >
            <v-card-text>
              <v-row>
                <v-col cols="12" sm="4" md="4">
                  <v-text-field
                    label="Name"
                    required
                    outlined
                    background-color="#fff"
                    v-model="document.name"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <v-select
                    v-model="document.document_type_id"
                    :items="documentTypes"
                    label="Type"
                    outlined
                    item-text="name"
                    item-value="id"
                    background-color="#fff"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="4" md="4">
                  <v-file-input
                    :label="
                      editMode && document.original_file_name
                        ? ''
                        : 'Select File'
                    "
                    v-model="document.file"
                    prepend-inner-icon="mdi-paperclip"
                    prepend-icon
                    outlined
                    background-color="#fff"
                  >
                    <template v-slot:label>
                      <span v-if="!editMode || !document.original_file_name"
                        >Select file</span
                      >
                      <span
                        v-if="
                          editMode == true &&
                            document.file == null &&
                            document.original_file_name
                        "
                        class="font-weight-bold"
                      >
                        <span
                          style="width: 70%; display: inline-block"
                          class="text-truncate"
                          >{{ document.original_file_name }}</span
                        >
                        <span
                          style="width: 20%; display: inline-block"
                          class="text-truncate"
                        >
                          .{{
                            document.original_file_name.split(".")[
                              document.original_file_name.split(".").length - 1
                            ]
                          }}
                        </span>
                      </span>
                    </template>
                  </v-file-input>
                </v-col>

                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      v-on="on"
                      color="red"
                      @click="deleteDocuments(k)"
                      fab
                      x-small
                      dark
                      absolute
                      top
                      right
                    >
                      <v-icon>mdi-delete</v-icon>
                    </v-btn>
                  </template>
                  Delete
                </v-tooltip>
              </v-row>
            </v-card-text>
          </v-card>
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="teal-color"
                  fab
                  x-small
                  dark
                  @click="addDocuments"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add Document
            </v-tooltip>
          </div>
        </v-col>
      </v-row>
      <div class="titles pt-6">Lineup</div>
      <v-card
        rounded
        flat
        :style="cardStyle"
        class="mb-8"
        v-for="(lineup, index) in event.lineups"
        :key="'l_' + index"
      >
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              color="red"
              @click="deleteLineUps(index)"
              fab
              x-small
              dark
              absolute
              top
              right
            >
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </template>
          Delete
        </v-tooltip>
        <v-card-text class="pb-0">
          <v-row>
            <v-col sm="4" md="4">
              <image-upload
                @upload="
                  (data) => {
                    event.lineups[index].image = data;
                  }
                "
                @remove="
                  () => {
                    event.lineups[index].image = null;
                  }
                "
                ref="lineup_image"
                :image_path="lineup.image_path"
                :height="260"
                defaultImage="user"
              ></image-upload>
            </v-col>
            <v-col sm="8" md="8">
              <v-row dense>
                <v-col cols="6">
                  <v-text-field
                    label="Name or Person"
                    required
                    outlined
                    background-color="#fff"
                    v-model="lineup.name"
                  ></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    label="Type of Profession"
                    required
                    outlined
                    background-color="#fff"
                    v-model="lineup.designation"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6">
                  <date-field
                    v-model="lineup.date"
                    label="Date"
                    :minDate="event.start_date"
                    :maxDate="event.end_date"
                    :backFill="
                      checkBackfillPermission($modules.events.management.slug)
                    "
                  ></date-field>
                </v-col>
                <v-col cols="12" sm="3" md="3">
                  <v-select
                    :items="timings.slice(0, timings.length - 1)"
                    label="Start Time"
                    item-text="text"
                    item-value="value"
                    v-model="lineup.start_time"
                    outlined
                    background-color="#fff"
                  ></v-select>
                </v-col>

                <v-col cols="12" sm="3" md="3">
                  <v-select
                    :items="timings.slice(1)"
                    label="End Time"
                    item-text="text"
                    item-value="value"
                    v-model="lineup.end_time"
                    outlined
                    background-color="#fff"
                  ></v-select>
                </v-col>
                <v-col md="12">
                  <v-textarea
                    v-model="lineup.description"
                    name="description"
                    label="Description"
                    dense
                    outlined
                    background-color="#fff"
                    rows="3"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      <div class="add_btn">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              color="teal-color"
              fab
              x-small
              dark
              @click="addLineUp"
            >
              <v-icon>mdi-plus-circle</v-icon>
            </v-btn>
          </template>
          Add Lineup
        </v-tooltip>
      </div>

      <div class="titles pt-6">Tickets</div>
      <v-card
        rounded
        flat
        :style="cardStyle"
        class="mb-8"
        v-for="(ticket, k) in event.tickets"
        :key="'t_' + k"
      >
        <!-- <v-card-text class="pb-0">
          <v-row>
            <v-col cols="12" :sm="event.type != 'F' ? 4 : 6">
              <v-text-field
                label="Name*"
                required
                outlined
                background-color="#fff"
                v-model="ticket.name"
                :rules="[(v) => !!v || 'Ticket Name is required']"
              ></v-text-field>
            </v-col>
            <v-col cols="12" :sm="event.type != 'F' ? 2 : 6">
              <v-text-field
                label="Quantity*"
                required
                outlined
                background-color="#fff"
                v-model="ticket.quantity"
                :rules="[
                  (v) => !!v || 'Quantity is required',
                  (v) => !isNaN(v) || 'Quantity must be Number',
                ]"
              ></v-text-field>
            </v-col>
            <v-col md="2" v-if="event.type != 'F'">
              <v-select
                label="Tax*"
                @change="taxChange(ticket)"
                v-model="ticket.tax_type_id"
                item-value="value"
                item-text="text"
                :items="taxTypes"
                outlined
                background-color="#fff"
                :rules="[(v) => !!v || 'Tax is required']"
              ></v-select>
            </v-col>
            <v-col v-if="event.type != 'F'" cols="12" sm="2" md="2">
              <v-text-field
                label="Price (Pre Tax)*"
                required
                @change="calculateTaxVariation($event, ticket, 'pre')"
                outlined
                background-color="#fff"
                :prefix="currencyCode"
                :disabled="event.type == 'F'"
                v-model="ticket.pre_tax_price"
                :rules="[
                  (v) => {
                    if (event.type == 'F') return true;
                    if (v <= 0) return 'Price should greater than zero';
                    if (v && isNaN(v)) return 'Price must be Number';
                    return true;
                  },
                ]"
              ></v-text-field>
            </v-col>
            <v-col v-if="event.type != 'F'" cols="12" sm="2" md="2">
              <v-text-field
                label="Price (Post Tax)*"
                required
                @change="calculateTaxVariation($event, ticket, 'post')"
                outlined
                background-color="#fff"
                :prefix="currencyCode"
                :disabled="event.type == 'F'"
                v-model="ticket.total_price"
                :rules="[
                  (v) => {
                    if (event.type == 'F') return true;
                    if (v <= 0) return 'Price should greater than zero';
                    if (v && isNaN(v)) return 'Price must be Number';
                    return true;
                  },
                ]"
              ></v-text-field>
            </v-col>
            <v-col :lg="2">
              <v-select
                label="Type*"
                required
                v-model="ticket.ticket_type"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
                :items="packageTypes"
                item-text="name"
                item-value="type"
                :rules="[(v) => !!v || 'Type is required']"
              ></v-select>
            </v-col>

            <v-col v-if="ticket.ticket_type == 'G'" lg="2">
              <v-text-field
                label="Participant count*"
                required
                outlined
                background-color="#fff"
                v-model="ticket.participant_count"
                :rules="[
                  (v) => !!v || 'Participant count',
                  (v) => !isNaN(v) || 'Participant count must be Number',
                ]"
              ></v-text-field>
            </v-col>

            <v-col cols="10" sm="10" md="10" :lg="ticket.ticket_type == 'G' ? 8 : 10">
              <v-textarea
                v-model="ticket.description"
                name="description"
                label="Description"
                dense
                outlined
                background-color="#fff"
                rows="2"
              ></v-textarea>
            </v-col>
            <v-col md="12">
              <v-row>
                <v-spacer></v-spacer>
                <v-col md="2" class="text-right">
                  <v-btn
                    v-if="ticket.image || ticket.data_url"
                    class="teal-color"
                    dark
                    small
                    left
                    @click="showPopupImage(ticket)"
                  >
                    View
                    <v-icon right dark>mdi-eye</v-icon>
                  </v-btn>
                </v-col>
                <v-col md="1" class="mb-2" style="margin-right: 50px">
                  <image-upload
                    @upload="
                      (data) => {
                        ticket.file = data;
                      }
                    "
                    @remove="
                      () => {
                        ticket.image = null;
                        ticket.data_url = null;
                      }
                    "
                    @result="
                      (dUrl) => {
                        ticket.data_url = dUrl;
                        $forceUpdate();
                      }
                    "
                    :image_path="ticket.image"
                    :defaultBtn="true"
                    :defaultImage="'ground'"
                  ></image-upload>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                v-on="on"
                color="red"
                @click="deleteTickets(k)"
                fab
                x-small
                dark
                absolute
                top
                right
              >
                <v-icon>mdi-delete</v-icon>
              </v-btn>
            </template>
            Delete
          </v-tooltip>
        </v-card-text> -->
        <v-card-text class="pb-0">
          <v-row>
            <v-col cols="6" :sm="6">
              <v-row>
                <v-col cols="12" :sm="12">
                  <v-text-field
                    class="pa-0"
                    label="Name*"
                    required
                    outlined
                    background-color="#fff"
                    v-model="ticket.name"
                    :rules="[(v) => !!v || 'Ticket Name is required']"
                  ></v-text-field>
                </v-col>
                <v-col cols="6" :sm="12">
                  <v-text-field
                    class="pa-0"
                    label="Name (Ar)"
                    outlined
                    background-color="#fff"
                    v-model="ticket.ar_name"
                  ></v-text-field>
                </v-col>
              </v-row>

              <v-row>
                <v-col :lg="4">
                  <v-select
                    label="Type*"
                    required
                    v-model="ticket.ticket_type"
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    :items="packageTypes"
                    item-text="name"
                    item-value="type"
                    :rules="[(v) => !!v || 'Type is required']"
                  ></v-select>
                </v-col>
                <v-col v-if="ticket.ticket_type == 'G'" lg="4">
                  <v-text-field
                    label="Participant count*"
                    required
                    outlined
                    background-color="#fff"
                    v-model="ticket.participant_count"
                    :rules="[
                      (v) => !!v || 'Participant count',
                      (v) => !isNaN(v) || 'Participant count must be Number',
                    ]"
                  ></v-text-field>
                </v-col>
                <v-col :sm="ticket.ticket_type == 'G' ? 4 : 8">
                  <v-text-field
                    label="Quantity*"
                    required
                    outlined
                    background-color="#fff"
                    v-model="ticket.quantity"
                    :rules="[
                      (v) => !!v || 'Quantity is required',
                      (v) => !isNaN(v) || 'Quantity must be Number',
                    ]"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col md="4" v-if="event.type != 'F'">
                  <v-select
                    label="Tax*"
                    @change="taxChange(ticket)"
                    v-model="ticket.tax_type_id"
                    item-value="value"
                    item-text="text"
                    :items="taxTypes"
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Tax is required']"
                  ></v-select>
                </v-col>
                <v-col v-if="event.type != 'F'" cols="12" sm="4" md="4">
                  <v-text-field
                    label="Price (Pre Tax)*"
                    required
                    @change="calculateTaxVariation($event, ticket, 'pre')"
                    outlined
                    background-color="#fff"
                    :prefix="currencyCode"
                    :disabled="event.type == 'F'"
                    v-model="ticket.pre_tax_price"
                    :rules="[
                      (v) => {
                        if (event.type == 'F') return true;
                        if (v <= 0) return 'Price should greater than zero';
                        if (v && isNaN(v)) return 'Price must be Number';
                        return true;
                      },
                    ]"
                  ></v-text-field>
                </v-col>
                <v-col v-if="event.type != 'F'" cols="12" sm="4" md="4">
                  <v-text-field
                    label="Price (Post Tax)*"
                    required
                    @change="calculateTaxVariation($event, ticket, 'post')"
                    outlined
                    background-color="#fff"
                    :prefix="currencyCode"
                    :disabled="event.type == 'F'"
                    v-model="ticket.total_price"
                    :rules="[
                      (v) => {
                        if (event.type == 'F') return true;
                        if (v <= 0) return 'Price should greater than zero';
                        if (v && isNaN(v)) return 'Price must be Number';
                        return true;
                      },
                    ]"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row v-if="event.type != 'F' && isEnableDctErp">
                <v-col cols="12" sm="6" md="6">
                  <v-text-field
                    outlined
                    background-color="#fff"
                    v-model="projectNumber"
                    hide-details="auto"
                    label="Project Number *"
                    placeholder="Project Number"
                    required
                    :readonly="projectNumber?true:false"
                    :rules="[(v) => !!v || 'Project number is required']"
                  ></v-text-field>
                  <!-- <v-text-field
                    outlined
                    background-color="#fff"
                    v-model="ticket.project_no"
                    hide-details="auto"
                    label="Project Number *"
                    placeholder="Project Number"
                    required
                    :rules="[(v) => !!v || 'Project number is required']"
                  ></v-text-field> -->
                </v-col>
                <v-col cols="12" sm="6" md="6">
                  <v-text-field
                    outlined
                    background-color="#fff"
                    v-model="transactionType"
                    hide-details="auto"
                    label="Transaction Type*"
                    placeholder="Transaction Type"
                    required
                    :readonly="transactionType?true:false"
                    :rules="[(v) => !!v || 'Transaction Type is required']"
                  ></v-text-field>
                    <!-- <v-text-field
                      outlined
                      background-color="#fff"
                      v-model="ticket.task_name"
                      hide-details="auto"
                      label="Task Name *"
                      placeholder="Task Name"
                      required
                      :rules="[(v) => !!v || 'Task name is required']"
                    ></v-text-field> -->
                </v-col>
                <v-col cols="12" sm="6" md="6" >
                  <v-select
                    label="Task Name*"
                    v-model="ticket.task_name"
                    item-value="value"
                    item-text="text"
                    hint="Required Task Name"
                    :menu-props="{ bottom: true, offsetY: true }"
                    :rules="[(v) => !!v || 'Task name is required']"
                    :items="taskNames"
                    hide-details="auto"
                    outlined
                    background-color="#fff"
                  ></v-select>
                    <!-- <v-text-field
                    outlined
                    background-color="#fff"
                    v-model="ticket.gl_code"
                    hide-details="auto"
                    label="Gl Code *"
                    placeholder="Gl Code"
                    required
                    :rules="[(v) => !!v || 'Gl Code is required']"
                  ></v-text-field> -->
                </v-col>
                <v-col cols="12" sm="6" md="6" >
                  <v-select
                    label="GL Code*"
                    v-model="ticket.gl_code"
                    item-value="value"
                    item-text="text"
                    hint="Required GL Code"
                    :menu-props="{ bottom: true, offsetY: true }"
                    :rules="[(v) => !!v || 'GL code is required']"
                    :items="glCodes"
                    hide-details="auto"
                    outlined
                    background-color="#fff"
                  ></v-select>
                    <!-- <v-text-field
                    outlined
                    background-color="#fff"
                    v-model="ticket.transaction_type"
                    hide-details="auto"
                    label="Transaction Type*"
                    placeholder="Transaction Type"
                    required
                    :rules="[(v) => !!v || 'Transaction Type is required']"
                  ></v-text-field> -->
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="6" sm="6" md="6" :lg="6">
              <v-textarea
                v-model="ticket.description"
                name="description"
                label="Description"
                dense
                outlined
                background-color="#fff"
                :rows="event.type == 'F' ? 7 : 11"
              ></v-textarea>
            </v-col>
            <v-col md="12">
              <v-row>
                <v-spacer></v-spacer>
                <v-col md="2" class="text-right">
                  <v-btn
                    v-if="ticket.image || ticket.data_url"
                    class="teal-color"
                    dark
                    small
                    left
                    @click="showPopupImage(ticket)"
                  >
                    View
                    <v-icon right dark>mdi-eye</v-icon>
                  </v-btn>
                </v-col>
                <v-col md="1" class="mb-2" style="margin-right: 50px">
                  <image-upload
                    @upload="
                      (data) => {
                        ticket.file = data;
                      }
                    "
                    @remove="
                      () => {
                        ticket.image = null;
                        ticket.data_url = null;
                      }
                    "
                    @result="
                      (dUrl) => {
                        ticket.data_url = dUrl;
                        $forceUpdate();
                      }
                    "
                    :image_path="ticket.image"
                    :defaultBtn="true"
                    :defaultImage="'ground'"
                  ></image-upload>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                v-on="on"
                color="red"
                @click="deleteTickets(k)"
                fab
                x-small
                dark
                absolute
                top
                right
              >
                <v-icon>mdi-delete</v-icon>
              </v-btn>
            </template>
            Delete
          </v-tooltip>
        </v-card-text>
      </v-card>
      <div class="add_btn">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              color="teal-color"
              fab
              x-small
              dark
              @click="addTickets"
            >
              <v-icon>mdi-plus-circle</v-icon>
            </v-btn>
          </template>
          Add Ticket
        </v-tooltip>
      </div>
      <v-row>
        <v-spacer></v-spacer>
        <!-- <v-btn
          color=" darken-1"
          class="ma-2 white--text teal-color"
          text
          @click="goToEvents"
          >Back</v-btn
        >-->
        <v-btn
          color="darken-1"
          class="ma-2 white--text yellow-color"
          @click="updateEvent"
          text
          >Draft</v-btn
        >
        <v-btn
          color="darken-1"
          class="ma-2 white--text blue-color"
          @click="publishEvent"
          text
          >Publish</v-btn
        >
      </v-row>

      <v-dialog v-model="locationDialoge" scrollable persistent width="40%">
        <v-card>
          <v-card-title class="headline">Select Location</v-card-title>
          <v-card-text class="pa-5">
            <v-row>
              Please select a location from the list. If you don't see correct
              location name in the list below try changing location on map by
              dragging the marker or clicking on the preffered location.
            </v-row>
            <v-row>
              <v-select
                :items="mapLocations"
                item-text="formatted_address"
                item-value="formatted_address"
                v-model="event.location"
                outlined
              ></v-select>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="blue-color white--text" text @click="closeLocation"
              >Close</v-btn
            >
            <v-btn class="teal-color white--text" text @click="dragMapChange"
              >Done</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="mapFullScreenDialoge" scrollable width="80%">
        <v-card>
          <v-card-text class="pa-4">
            <v-row no-gutters v-if="mapFullScreenDialoge">
              <v-col md="12">
                <v-autocomplete
                  label="Location"
                  v-model="autocompleteLocationModel"
                  :items="locationEntries"
                  :search-input.sync="locationSearchText"
                  item-text="value"
                  item-value="value"
                  hide-no-data
                  :loading="isLoading"
                  outlined
                  background-color="#fff"
                  @change="changeLocation"
                ></v-autocomplete>
              </v-col>
            </v-row>
            <GmapMap
              v-bind:center="{
                lat: parseFloat(event.latitude),
                lng: parseFloat(event.longitude),
              }"
              :zoom="12"
              map-type-id="terrain"
              @click="updateCoordinates"
              style="width: 100%; height: 600px"
            >
              <GmapMarker
                ref="mapRef"
                :position="{
                  lat: parseFloat(event.latitude),
                  lng: parseFloat(event.longitude),
                }"
                :clickable="true"
                :draggable="true"
                @dragend="updateCoordinates"
              />
            </GmapMap>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-form>
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
    <view-image-popup
      :image_path="currentImage.image_path"
      :data_url="currentImage.data_url"
      @resetImagePath="resetImagePath"
    ></view-image-popup>
  </v-container>
</template>
<script>
import { GetSuggestions, placeDetails } from "@/utils/placesUtils";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";
import ViewImagePopup from "@/components/Image/ViewImagePopup";
import moment from "moment";
import constants from "@/utils/constants";
export default {
  components: {
    ConfirmModel,
    ViewImagePopup,
  },
  data() {
    return {
      addStageDialog: false,
      locationDialoge: false,
      mapFullScreenDialoge: false,
      mapLocations: [],
      locationHistory: {},
      valid: true,
      isEnableArabic: false,
      event: {
        name: null,
        ar_name: null,
        type: null,
        attendees_privacy: null,
        description: null,
        ar_description: null,
        venue_service_id: null,
        start_time: null,
        end_time: null,
        status_id: 1,
        latitude: 24.46436049078158,
        longitude: 54.37532545660189,
        location: null,
        start_date: null,
        end_date: null,
        facility_id: null,
        event_schedules: [
          {
            start_date: null,
            start_time: null,
            end_date: null,
            end_time: null,
          },
        ],
        lineups: [
          {
            name: null,
            designation: null,
            description: null,
            image: null,
            date: null,
          },
        ],
        documents: [
          {
            name: null,
            document_type_id: null,
            file: null,
          },
        ],
        tickets: [
          {
            name: null,
            quantity: null,
            price: null,
            ticket_type: null,
            participant_count: null,
            description: null,
            image: null,
            image_url: null,
          },
        ],
        deletedTickets: [],
        deletedDocuments: [],
        deletedLineups: [],
        is_enable_booking_approval:false,
        is_public: 1,
      },
      facilities: [],
      editMode: false,
      timings: [],
      isLoading: false,
      autocompleteLocationModel: null,
      locationSearchText: null,
      locationEntries: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      currentImage: {},
      packageTypes: [
        { type: "I", name: "Individual" },
        { type: "C", name: "Couple" },
        { type: "G", name: "Group" },
      ],
      facility_has_seat_map: false,
      seat_map_id: null,
      allMapPlans:[],
      taskNames: constants.TASK_NAMES,
      glCodes: constants.GL_CODES,
      projectNumber: null,
      transactionType: null,
      isEnableDctErp: false,
    };
  },
  watch: {
    locationSearchText(newVal) {
      var _vue = this;
      if (
        newVal == null ||
        newVal.length == 0 ||
        this.autocompleteLocationModel == newVal
      )
        return;
      this.isLoading = true;
      GetSuggestions(newVal)
        .then(function(res) {
          _vue.isLoading = false;
          _vue.locationEntries = res;
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
  },
  computed: {
    venueServices() {
      return this.$store.getters.getVenueServices.data.filter((service) => service.name != "POS");
    },
    documentTypes() {
      return this.$store.getters.getDocumentTypes.data;
    },
    taxTypes() {
      return this.$store.getters.getTaxTypes.data;
    },
    // isEnableDctErp() {
    //   if (this.$store.getters.userVenues && this.$store.getters.userVenues.length > 0) {
    //     return this.$store.getters.userVenues[0].enable_dct_erp;
    //   }
    //   return 0;
    // },
  },
  created() {},
  mounted() {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
    if (this.$store.getters.getTaxTypes.status == false) {
      this.$store.dispatch("loadTaxTypes").then(() => {
        if (typeof this.$route.params.data != "undefined") {
          this.event.id = parseInt(atob(this.$route.params.data));
          this.getEventDetails();
        }
      });
    } else {
      if (typeof this.$route.params.data != "undefined") {
        this.event.id = parseInt(atob(this.$route.params.data));
        this.getEventDetails();
      }
    }
    if (this.$store.getters.getDocumentTypes.status == false) {
      this.$store.dispatch("loadDocumentTypes");
    }
    if (this.$store.getters.venueInfo) {
      if (this.$store.getters.venueInfo.enable_dct_erp) {
        this.isEnableDctErp = true;
        if(this.$store.getters.venueInfo.dct_erp_configuration){
          this.projectNumber = this.$store.getters.venueInfo.dct_erp_configuration.project_no;
          this.transactionType = this.$store.getters.venueInfo.dct_erp_configuration.transaction_type;
        }
      } else {
        this.isEnableDctErp = false;
      }
    }
  },
  methods: {
    showPopupImage(product) {
      this.currentImage = {
        image_path: product.image,
        data_url: product.data_url,
      };
    },
    resetImagePath() {
      this.currentImage = {};
    },
    changeLocation() {
      this.event.location = this.autocompleteLocationModel;
      let placeId = this.locationEntries.find(
        (val) => val.value == this.autocompleteLocationModel
      ).id;
      placeDetails(placeId)
        .then((data) => {
          var lat = data[0].geometry.location.lat();
          var lng = data[0].geometry.location.lng();
          this.event.latitude = lat;
          this.event.longitude = lng;
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    dragMapChange() {
      this.locationEntries = [{ value: this.event.location, id: "0" }];
      this.autocompleteLocationModel = this.event.location;
      this.locationSearchText = this.event.location;
      this.locationDialoge = false;
    },
    addTimeInterval() {
      this.event.event_schedules.push({
        start_date: null,
        start_time: null,
        end_date: null,
        end_time: null,
      });
      this.$forceUpdate();
    },
    addLineUp() {
      this.event.lineups.push({
        name: "",
        type: "",
        description: "",
        date: null,
        start_time: null,
        end_time: null,
      });
    },
    addDocuments() {
      this.event.documents.push({
        name: "",
        document_type_id: "",
        file: null,
      });
    },
    addTickets() {
      this.event.tickets.push({
        name: "",
        quantity: "",
        price: this.event.type == "Free" ? 0 : "",
        ticket_type: null,
        participant_count: null,
        description: null,
      });
    },
    eventTypeChange() {
      if (this.event.type == "Free") {
        this.event.tickets.map((item) => {
          item.price = 0;
        });
      }
    },
    updateEvent() {
      this.event.status_id = 11;
      this.addOrEditEvent();
    },
    publishEvent() {
      this.event.status_id = 1;
      this.addOrEditEvent();
    },
    duplicateTiming(index) {
      this.event.event_schedules.push({
        start_date: this.event.event_schedules[index].start_date,
        start_time: this.event.event_schedules[index].start_time,
        end_date: this.event.event_schedules[index].end_date,
        end_time: this.event.event_schedules[index].end_time,
      });
      this.$forceUpdate();
    },
    deleteTiming(index) {
      if (
        !this.event.event_schedules[index].start_date &&
        !this.event.event_schedules[index].end_date
      ) {
        this.event.event_schedules.splice(index, 1);
        this.$forceUpdate();
        return true;
      } else {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this Time Interval?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "event_schedules",
        };
      }
    },
    deleteLineUps(index) {
      if (
        !this.event.lineups[index].name &&
        !this.event.lineups[index].designation
      ) {
        this.$refs[`lineup_image`][index].cancel();
        this.event.lineups.splice(index, 1);
        return true;
      } else {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this lineup?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "lineup",
        };
      }
    },
    deleteDocuments(index) {
      if (this.event.documents[index].event_document_id) {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this document?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "document",
        };
      } else {
        this.event.documents.splice(index, 1);
        if (this.event.documents.length == 0) {
          this.event.documents = [{}];
        }
      }
    },
    deleteTickets(index) {
      if (this.event.tickets[index].event_ticket_id) {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this ticket type?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "ticket",
        };
      } else {
        this.event.tickets.splice(index, 1);
        if (this.event.tickets.length == 0) {
          this.event.tickets = [{}];
        }
      }
    },
    confirmActions(data) {
      if (data.type == "lineup") {
        if (this.event.lineups[data.id].event_lineup_id != null)
          this.event.deletedLineups.push(
            this.event.lineups[data.id].event_lineup_id
          );
        if (this.event.lineups[data.id].image) {
          this.$refs[`lineup_image`][data.id].cancel();
        }
        setTimeout(() => {
          this.event.lineups.splice(data.id, 1);
          if (this.event.lineups.length == 0) {
            this.event.lineups = [{}];
          }
        });
      } else if (data.type == "document") {
        if (this.event.documents[data.id].event_document_id != null)
          this.event.deletedDocuments.push(
            this.event.documents[data.id].event_document_id
          );
        if (this.event.documents[data.id].file) {
          this.event.documents[data.id].file = null;
        }
        this.event.documents.splice(data.id, 1);
        if (this.event.documents.length == 0) {
          this.event.documents = [{}];
        }
      } else if (data.type == "ticket") {
        if (this.event.tickets[data.id].event_ticket_id != null)
          this.event.deletedTickets.push(
            this.event.tickets[data.id].event_ticket_id
          );
        this.event.tickets.splice(data.id, 1);
        if (this.event.tickets.length == 0) {
          this.event.tickets = [{}];
        }
      } else if (data.type == "event_schedules") {
        if (this.event.event_schedules[data.id].event_timing_id != null)
          this.event.deletedTimings.push(
            this.event.event_schedules[data.id].event_timing_id
          );
        setTimeout(() => {
          this.event.event_schedules.splice(data.id, 1);
          if (this.event.event_schedules.length == 0) {
            this.event.event_schedules = [{}];
          }
          this.$forceUpdate();
        });
      }
      this.$forceUpdate();
      this.confirmModel.id = null;
    },
    /* eslint-disable */
    addOrEditEvent() {
      event.preventDefault();
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (this.venueServices.length == 0) {
        this.showError(
          "No Facilities found. Please add facilities to add event."
        );
        return;
      }
      this.showLoader("Saving");
      let formData = new FormData();
      formData.append("name", this.event.name);
      if (this.event.description)
        formData.append("description", this.event.description);
      if (
        typeof this.event.image != "undefined" &&
        typeof this.event.image != "string" &&
        this.event.image != null
      ) {
        formData.append("image", this.event.image);
      }
      formData.append("type", this.event.type);
      formData.append("attendees_privacy", this.event.attendees_privacy);
      formData.append("event_type", "Event");
      formData.append("venue_service_id", this.event.venue_service_id);
      formData.append("start_time", this.event.start_time);
      formData.append("end_time", this.event.end_time);
      formData.append("status_id", this.event.status_id);
      formData.append("start_date", this.event.start_date);
      formData.append("end_date", this.event.end_date);
      formData.append("is_enable_booking_approval", this.event.is_enable_booking_approval);
      let is_public = this.event.is_public?1:0;
      formData.append("is_public", is_public);
      if (this.facility_has_seat_map && this.event.seat_map_id) {
        formData.append("seat_map_id", this.event.seat_map_id);
      }
      /** Event Arabic Name and description */
      if (this.isEnableArabic) {
        formData.append("ara_name", this.event.ara_name);
        formData.append("ara_description", this.event.ara_description);
      }
      /** end Event Arabic name an description */
      // formData.append("status", this.event.status);
      if (this.event.deletedLineups.length) {
        this.event.deletedLineups.forEach((element, index) => {
          formData.append(`deleted_lineups[${index}]`, element);
        });
      }
      if (this.event.deletedTickets.length) {
        this.event.deletedTickets.forEach((element, index) => {
          formData.append(`deleted_tickets[${index}]`, element);
        });
      }
      if (this.event.deletedDocuments.length) {
        this.event.deletedDocuments.forEach((element, index) => {
          formData.append(`deleted_documents[${index}]`, element);
        });
      }

      if (this.event.facility_id == null) {
        formData.append("latitude", this.event.latitude);
        formData.append("longitude", this.event.longitude);
        formData.append("location", this.event.location);
        if (this.event.heatmap != null)
          formData.append("heatmap", this.event.heatmap);
      } else {
        formData.append("facility_id", this.event.facility_id);
      }
      this.event.lineups.forEach((element, index) => {
        Object.keys(element).forEach((key) => {
          if (element[key] != null)
            formData.append(`lineups[${index}][${key}]`, element[key]);
        });
      });
      this.event.documents.forEach((element, index) => {
        Object.keys(element).forEach((key) => {
          if (element[key] != null)
            formData.append(`documents[${index}][${key}]`, element[key]);
        });
      });
      this.event.tickets.forEach((element, index) => {
        if(this.isEnableDctErp){
          formData.append(`tickets[${index}]['project_no']`, this.projectNumber);
          formData.append(`tickets[${index}]['transaction_type']`, this.transactionType);
        }
        Object.keys(element).forEach((key) => {
          if (element[key] != null && key != "image") {
            formData.append(`tickets[${index}][${key}]`, element[key]);
          }
        });
        if (element.file) {
          formData.append(`tickets[${index}][image]`, element.file);
        }
      });
      this.event.event_schedules.forEach((element, index) => {
        Object.keys(element).forEach((key) => {
          if (element[key] != null)
            formData.append(`event_schedules[${index}][${key}]`, element[key]);
        });
      });

      if (typeof this.event.id != "undefined" && this.event.id != null) {
        formData.append("id", Number(this.event.id));
      }
      this.$http
        .post(
          `venues/events${this.event.id != null ? "/" + this.event.id : ""}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data; boundary=${form._boundary}",
            },
          }
        )
        .then((response) => {
          if (response.status == 200 && response.data.status) {
            this.goToEvents();
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    updateCoordinates(e) {
      this.locationHistory.lat = this.event.latitude;
      this.locationHistory.lng = this.event.longitude;

      let lat = e.latLng.lat();
      let lng = e.latLng.lng();

      let geocoder = new google.maps.Geocoder();
      var locationss = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
      };
      let that = this;
      geocoder.geocode({ location: locationss }, function(results, status) {
        if (status === "OK") {
          if (results.length > 0) {
            that.mapLocations = results;
            that.event.location = results[0].formatted_address;
            that.locationDialoge = true;
          }
        }
      });
      this.event.latitude = lat;
      this.event.longitude = lng;
    },
    closeLocation() {
      this.locationDialoge = false;
      this.event.latitude = this.locationHistory.lat;
      this.event.longitude = this.locationHistory.lng;
      this.event.location = null;
    },
    getFacilities() {
      //TODO: Available weekdays check
      // let dayname = moment(this.event.date, "YYYY-MM-DD").format("dddd");
      this.$http
        .get(
          `venues/facilities/short?venue_service_id=${this.event.venue_service_id}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.facilities = response.data.data;
            this.getMinBookingTimes();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getEventDetails() {
      this.showLoader("Loading");
      this.$http
        .get(`venues/events/${this.event.id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.editMode = true;
            let eventData = response.data.data;
            this.event.status_id = 1;
            this.event = eventData;
            if (
              eventData.ara_name &&
              eventData.ara_name != "" &&
              eventData.ara_name !== null
            ) {
              this.isEnableArabic = true;
            }
            if (!this.event.location) {
              this.event.latitude = 24.46436049078158;
              this.event.longitude = 54.37532545660189;
              this.event.location = null;
              this.locationEntries = [];
              this.autocompleteLocationModel = null;
              this.locationSearchText = null;
            } else {
              this.locationEntries = [{ value: this.event.location, id: "0" }];
              this.autocompleteLocationModel = this.event.location;
              this.locationSearchText = this.event.location;
            }

            if (eventData.lineups.length == 0) {
              this.event.lineups = [{}];
            }
            if (eventData.documents.length == 0) {
              this.event.documents = [{}];
            }
            if (eventData.tickets.length == 0) {
              this.event.tickets = [{}];
            }

            if (eventData.event_schedules === null) {
              this.event.event_schedules = [
                {
                  start_date: null,
                  start_time: null,
                  end_date: null,
                  end_time: null,
                },
              ];
            } else {
              this.event.event_schedules = eventData.event_schedules;
            }
            this.event.deletedDocuments = [];
            this.event.deletedTickets = [];
            this.event.deletedLineups = [];
            this.hideLoader();
            this.getFacilities();
            if (eventData.seat_map_id) {
              this.event.seat_map_id = eventData.seat_map_id;
              this.facility_has_seat_map = true;
              this.getFacilitySeatMaps();
              this.setIndividualPackageType();
            }
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          console.log("error");
          console.log(error);
          this.errorChecker(error);
        });
    },
    goToEvents() {
      this.$router.push({ name: "Events" }, () => {});
    },

    calculateTaxVariation(amount, ticket, type) {
      let taxTypeId = ticket.tax_type_id;
      let taxPercentage = 0;
      if (taxTypeId && this.taxTypes.length) {
        taxPercentage = this.taxTypes.find((item) => item.value == taxTypeId)
          .percentage;
      }
      const priceData = this.getTaxVariation(type, taxPercentage, amount);
      if (priceData) {
        ticket.price = priceData.price.toFixed(4);
        ticket.pre_tax_price = priceData.price.toFixed(4);
        ticket.total_price = priceData.total_price.toFixed(4);
      }
      this.$forceUpdate();
    },

    taxChange(ticket) {
      if (ticket.price) {
        this.calculateTaxVariation(ticket.price, ticket, "pre");
      } else if (ticket.total_price) {
        this.calculateTaxVariation(ticket.total_price, ticket, "post");
      }
    },

    getMinBookingTimes() {
      const venueService = this.venueServices.find(
        (item) => item.venue_service_id == this.event.venue_service_id
      );
      const timeIncrement = venueService ? venueService.time_increment : 60;
      if (timeIncrement) {
        let currentTime = moment("00:00:00", "HH:mm:ss");
        this.timings = [];
        this.timings.push({
          value: currentTime.format("HH:mm:ss"),
          text: currentTime.format("hh:mm a"),
        });
        let hour = 0;
        while (hour < 24) {
          currentTime = currentTime.add(timeIncrement, "minutes");
          let data = {
            value: currentTime.format("HH:mm:ss"),
            text: currentTime.format("hh:mm a"),
          };
          if (data.value == "00:00:00") {
            data.value = "23:59:59";
            this.timings.push(data);
            hour = 24;
          } else {
            this.timings.push(data);
            hour = currentTime.get("hours");
          }
        }
      }
    },
    enableArabic() {
      if (this.isEnableArabic) {
        this.isEnableArabic = false;
      } else {
        this.isEnableArabic = true;
      }
    },
    /** Seat Map functions */
    changeFacility() {
      console.log(this.event.facility_id);
      const facility = this.facilities.find((f) => f.id === this.event.facility_id);
      if (facility !== undefined) {
        console.log(facility);
        if (facility.is_enabled_seat_map) {
          this.facility_has_seat_map = true;
          this.getFacilitySeatMaps();
          this.setIndividualPackageType();
        } else {
          this.facility_has_seat_map = false;
          this.allMapPlans = [];
          this.resetPackageType();
        }
      }
    },
    getFacilitySeatMaps() {
      if (!this.event.facility_id) {
        this.showError("Facility Id is required");
        return false;
      }
      if (!this.facility_has_seat_map) {
        return false;
      }
      this.showLoader("Loading");
      this.$http.get(`venues/seat-maps?facility_id=${this.event.facility_id}&status_id=1`).then((response) => {
        if (response.status == 200) {
          const data = response.data.data;
          if(data && data.length){
              this.allMapPlans = data;
          }else{
              this.allMapPlans = [];
          }
          this.hideLoader();
        }
      })
      .catch((error) => {
          console.log(error);
          this.hideLoader();
      });
    },
    resetPackageType() {
      this.packageTypes = [
        { type: "I", name: "Individual" },
        { type: "C", name: "Couple" },
        { type: "G", name: "Group" },
      ];
    },
    setIndividualPackageType() {
      this.packageTypes = [{type: "I", name: "Individual"}];
    }
  },
};
</script>

<style scoped>
.add_ground_containers {
  width: -webkit-fill-available;
}
.imageInput {
  padding-top: 0;
}
.add_btn {
  margin-top: -20px;
}
</style>

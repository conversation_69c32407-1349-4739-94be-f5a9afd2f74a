<template>
  <v-container fluid>
    <v-row no-gutters class="pos">
      <v-col
        :md="checkWritePermission($modules.outlet.productsOrder.slug) ? 6 : 12"
        class="pa-4"
      >
        <h1>Products</h1>
      </v-col>
      <v-spacer></v-spacer>
      <v-col md="6">
        <div
          class="ptBtn float-right"
          v-if="checkWritePermission($modules.outlet.productsOrder.slug)"
        >
          <v-btn @click="goToProductsManagement" class="main-color"
            ><v-icon class="main-color">mdi-cog-outline</v-icon> Product
            Management</v-btn
          >
        </div>
      </v-col>
      <v-col md="12" class="pa-4">
        <v-row v-if="products.length">
          <v-col
            md="2"
            sm="2"
            v-for="(product, index) in products"
            :key="index"
          >
            <PosProductItem
              v-bind="product"
              :product_image="getImage(product.image)"
              :key="`p_${index}`"
            />
          </v-col>
        </v-row>
      </v-col>
      <v-row sm="12">
        <v-col>
          <v-pagination
            v-if="totalPages > 0"
            v-model="page"
            :length="totalPages"
          ></v-pagination>
        </v-col>
      </v-row>
    </v-row>
  </v-container>
</template>
<script>
import PosProductItem from "@/views/Pos/PosProductItem.vue";
import images from "@/utils/images";
export default {
  props: {
    outletId: {
      type: Number,
      default: null,
    },
  },
  components: {
    PosProductItem,
  },
  data() {
    return {
      selectedCategory: null,
      productNameSearch: null,
      products: [],
      totalPages: 1,
      page: 1,
      perPage: 24,
    };
  },
  mounted() {
    this.getProducts();
  },
  computed: {},
  watch: {
    page() {
      this.getProducts();
    },
  },
  methods: {
    goToProductsManagement() {
      this.$router.push({
        name: "OutletsProductManagement",
        params: { data: btoa(this.outletId) },
      });
    },
    getImage(image, type = "product") {
      return image
        ? this.s3BucketURL + image
        : type == "product"
        ? images["default"]
        : images["empty_cart"];
    },
    getProducts() {
      this.showLoader("Loading");
      let url = `venues/outlets/product-management?outlet_id=${this.outletId}&page=${this.page}&per_page=${this.perPage}`;

      if (this.selectedCategory) {
        url += `&category=${this.selectedCategory}`;
      }

      url += `${
        this.productNameSearch ? "&product_name=" + this.productNameSearch : ""
      }`;

      this.$http
        .get(url)
        .then((response) => {
          if (response.status == 200) {
            const data = response.data.data;
            if (data && data.length) {
              this.products = data;
              this.totalPages = response.data.total_pages;
            } else {
              this.products = [];
              this.totalPages = 1;
            }
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.hideLoader();
          this.errorChecker(error);
        });
    },
  },
};
</script>
<style scoped>
.pos .pos-right-drawer {
  position: relative;
  width: 98%;
}
.pos .fixed-height-container {
  height: 90vh;
  position: fixed;
  top: 66px;
  right: 2px;
  width: 20%;
  bottom: 40px;
  max-width: 250px;
}
.v-list.cart-sidebar-item {
  height: 70%;
  overflow-y: scroll;
}

/deep/ {
  .pos .v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
    height: 38px;
  }
  .pos .main-color {
    color: #4faeaf;
  }
}
.ptBtn a {
  width: 200px;
  padding: 8px 10px;
  display: block;
  text-align: center;
  border-radius: 4px;
  border: 1px solid rgba(17, 42, 70, 0.1);
  background: #fff;
}
.clear-cart-text {
  font-size: 12px;
  color: #666;
  cursor: pointer;
}
.pos .v-text-field .v-text-field__details {
  margin-bottom: 0px;
  display: none;
}

.pos .v-tabs:not(.v-tabs--vertical) .v-tab {
  white-space: normal;
  margin-right: 10px;
  min-width: 50px;
  height: 40px;
  padding: 10px 16px;
  align-items: flex-start;
  border-radius: 4px;
  border: 1px solid rgba(17, 42, 70, 0.1);
  background: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: var(--black, #333);
}
.pos .v-tab.v-tab--active {
  border: 1px solid #4faeaf !important;
  background: rgba(79, 174, 175, 0.1) !important;
  color: #4faeaf !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
/deep/ {
  .pos .v-tabs.cat-tab > div.v-tabs-bar {
    background-color: transparent;
  }
  .pos .v-tabs-slider {
    display: none;
  }
  .pos .product-box {
    width: 100%;
    flex-shrink: 0;
    border-radius: 8px;
    border: 1px solid #eaeaea;
    background: #fff;
    padding: 4px;
    cursor: pointer;
  }
  .pos .product-box .product-image {
    min-height: 140px;
    border-radius: 4px;
    padding: 4px;
    position: relative;
  }
  .pos .product-box .product-image img {
    /* aspect-ratio: 3/2; */
    width: 100%;
    border-radius: 4px;
  }
  .pos .product-box .product-meta {
    padding: 12px 10px 10px 12px;
  }
  .pos .product-box .product-meta .product-title {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    padding: 4px 0px;
    min-height: 40px;
  }
  .pos .product-box .product-meta .product-price {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 4px 0px;
  }
  .pos .product-box .pos-title-text {
    weight: 600;
    font-size: 14px;
    color: Black;
  }
  .pos .product-box .pos-small-text {
    weight: 400;
    font-size: 12px;
    color: Grey;
  }
  .pos .product-box .product-price.main-color > span {
    display: inline-block;
  }

  .pos .product-box span.price {
    width: 50%;
  }

  .pos .product-box span.p-variant {
    width: 49%;
  }
}
.pos .cart-product-title {
  color: var(--dark-blue, #112a46);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.pos .cart-product-description {
  padding: 10px;
}
.pos .cart-product-image {
  width: 20%; /* Adjust as per your requirements */
  height: 56px; /* Same as width to make it square */
  margin: 5px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
.pos .cart-product-image img {
  width: 100%;
  height: 100%;
}
/* Add custom styles to control the layout */
.cart-product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/* Optional: Add styles for the quantity and plus-minus buttons */
.cart-product-qty {
  display: flex;
  align-items: center;
}
.pos .input-qty {
  padding: 10px;
  text-align: center;
  color: #000;
}
.plus-qty button,
.minus-qty button {
  /* Add your styles for the plus-minus buttons here */
  border: 1px solid #000 !important;
}

.pos .v-btn--icon.v-size--default .v-icon,
.pos .v-btn--fab.v-size--default .v-icon {
  height: 24px;
  font-size: 16px;
  width: 24px;
}
.pos button.v-btn.v-btn--icon.v-btn--round.theme--light.v-size--default {
  width: 16px;
  height: 16px;
}
.cart-product-list-item.v-list-item {
  padding: 0px;
  margin: 0px 10px;
  border-bottom: 1px solid #f1f1f1;
}
.pos .cart-product-summary {
  border-radius: 4px;
  background: #f8fafb;
  padding: 5px 10px;
  margin: 0px 10px;
  position: fixed;
  bottom: 20px;
  width: 92%;
}

.pos .cart-subtotal-price {
  text-align: right;
  color: #333;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.pos .cart-total-price {
  text-align: right;
  color: var(--black, #333);
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.pos button.cart-checkout-btn {
  display: block;
  width: 100%;
  border-radius: 4px;
  background: #112a46;
  height: 48px;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  color: #fff;
}

.pos .cart-subtotal {
  color: rgba(51, 51, 51, 0.7);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.pos .cart-total {
  color: var(--black, #333);
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.empty-cart img {
  width: 100px;
  height: 100px;
  display: block;
  margin: 40% 30%;
}
/* Styles for tablet screens */
@media screen and (max-width: 1920px) {
  .v-list.cart-sidebar-item {
    height: 60%;
  }
}
/* Styles for tablet screens */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .v-list.cart-sidebar-item {
    height: 60%;
  }
}
</style>

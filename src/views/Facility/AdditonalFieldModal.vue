```vue
<template>
  <v-dialog v-model="openModal" max-width="1000" persistent>
    <v-card>
      <v-card-title>
        Additional Field
      </v-card-title>
      <v-card-text>
        <v-container fluid>
          <v-row dense>
            <v-col>
              <v-select
                  v-model="selectedControl"
                  :items="controls"
                  item-text="value"
                  item-value="name"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  hint="Select field type"
                  outlined
                  required
                  @change="updateType"
              >
              </v-select>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col cols="12" sm="6" md="6"  v-if="selectedControl === 'text_box'">
              <v-text-field
                  v-model="name"
                  clearable
                  outlined
                  dense
                  required
                  :rules="[(v) => !!v || 'Field is required']"
                  label="Field Title"
                  background-color="#fff"
              >
                <v-tooltip slot="append" v-if="field_config.allowMarkdown" top>
                  <template v-slot:activator="{ on }">
                    <v-icon color="primary" dark v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span> To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text) </span>
                </v-tooltip>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="6" v-else-if="selectedControl === 'radio_buttons'">
              <v-text-field
                  v-model="name"
                  clearable
                  outlined
                  dense
                  required
                  :rules="[(v) => !!v || 'Field is required']"
                  label="Field Title"
                  background-color="#fff"
              >
                <v-tooltip slot="append" v-if="field_config.allowMarkdown" top>
                  <template v-slot:activator="{ on }">
                    <v-icon color="primary" dark v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span> To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text) </span>
                </v-tooltip>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="6" v-else-if="selectedControl === 'check_boxes'">
              <v-text-field
                  v-model="name"
                  clearable
                  outlined
                  dense
                  required
                  :rules="[(v) => !!v || 'Field is required']"
                  label="Field Title"
                  background-color="#fff"
              >
                <v-tooltip slot="append" v-if="field_config.allowMarkdown" top>
                  <template v-slot:activator="{ on }">
                    <v-icon color="primary" dark v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span> To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text) </span>
                </v-tooltip>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="6" v-else-if="selectedControl === 'drop_down'">
              <v-text-field
                  v-model="name"
                  clearable
                  outlined
                  dense
                  required
                  :rules="[(v) => !!v || 'Field is required']"
                  label="Field Title"
                  background-color="#fff"
              >
                <v-tooltip slot="append" v-if="field_config.allowMarkdown" top>
                  <template v-slot:activator="{ on }">
                    <v-icon color="primary" dark v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span> To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text) </span>
                </v-tooltip>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="6"  v-else-if="selectedControl === 'text_area'">
              <v-text-field
                  v-model="name"
                  clearable
                  outlined
                  dense
                  required
                  :rules="[(v) => !!v || 'Field is required']"
                  label="Field Title"
                  background-color="#fff"
              >
                <v-tooltip slot="append" v-if="field_config.allowMarkdown" top>
                  <template v-slot:activator="{ on }">
                    <v-icon color="primary" dark v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span> To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text) </span>
                </v-tooltip>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="6"  v-else-if="selectedControl === 'file'">
              <v-text-field
                  v-model="name"
                  clearable
                  outlined
                  dense
                  required
                  :rules="[(v) => !!v || 'Field is required']"
                  label="Attachment label"
                  background-color="#fff"
              >
                <v-tooltip slot="append" v-if="field_config.allowMarkdown" top>
                  <template v-slot:activator="{ on }">
                    <v-icon color="primary" dark v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span> To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text) </span>
                </v-tooltip>
              </v-text-field>

            </v-col>
            <v-col cols="12" sm="6" md="6" v-else-if="selectedControl === 'date'">
              <v-text-field
                  v-model="name"
                  clearable
                  outlined
                  dense
                  required
                  :rules="[(v) => !!v || 'Field is required']"
                  label="Calendar label"
                  background-color="#fff"
              >
                <v-tooltip slot="append" v-if="field_config.allowMarkdown" top>
                  <template v-slot:activator="{ on }">
                    <v-icon color="primary" dark v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span> To turn any word into a clickable hyperlink, use square brackets as illustrated here, and include the desired text within parentheses: [https://example.com] (text) </span>
                </v-tooltip>
              </v-text-field>

            </v-col>

            <v-col cols="12" md="2">
              <v-checkbox
                  v-model="isRequired"
                  false-value="0"
                  true-value="1"
                  label="Required"
                  class="ml-3"
                  style="margin-top:5px !important"
                  hide-details
              ></v-checkbox>
            </v-col>
            <v-col cols="12" md="2">
              <v-checkbox
                  v-model="isVisible"
                  false-value="0"
                  true-value="1"
                  label="Visible"
                  class="ml-3"
                  style="margin-top:5px !important"
                  hide-details
              ></v-checkbox>
            </v-col>
          </v-row>
          <v-row v-if="enableOptions.includes(selectedControl)" dense>
            <v-col cols="12" lg="12" xl="8">
              <v-row v-if="options.length > 0">
                <v-col cols="12" lg="6" v-for="(option, index) in options" :key="index">
                  <div class="d-flex align-center justify-space-between">

                    <v-text-field
                        v-model="option.value"
                        clearable
                        outlined
                        dense
                        required
                        :rules="[(v) => !!v || 'Option is required']"
                        label="Option"
                        background-color="#fff"
                    ></v-text-field>

                    <v-btn
                        style="margin-top: -22px"
                        small
                        color="white"
                        depressed
                        @click="deleteOption(index)"
                    >
                      <v-icon dark>mdi-trash-can-outline</v-icon>
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
              <div
                  class="add-new-option"
                  style="cursor: pointer; color: #4FAEAF; margin-top: 10px;"
                  @click="addOption"
              >
                + Add Choice
              </div>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions class="" style="border-top: 1px solid rgba(0, 0, 0, 0.1)">
        <v-btn
            color=""
            class="ma-2"
            text
            @click="closeModal"
        > Close </v-btn>
        <v-spacer></v-spacer>
        <v-btn
            color=""
            class="ma-2"
            text
            :disabled="!isFormValid"
            @click="addFormItem"
        > Add </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    openModal: { type: Boolean, default: false },
    field_config: {
      type: Object,
      default: () => ({
        is_required: false,
        is_visible:false,
        allowMarkdown: false,
        type: null,
        name: null,
        slug: null,
        id: null,
        is_additional: 1,
        options: [] // Add options to field_config for editing existing fields
      })
    }
  },
  data() {
    return {
      selectedControl: this.field_config.type || null,
      name: this.field_config.name || null,
      slug: this.field_config.slug || null,
      id: this.field_config.id || null,
      isRequired: this.field_config.is_required ? 1 : 0,
      isVisible: this.field_config.is_visible ? 1 : 0,
      options: this.field_config.options || [], // Initialize with existing options
      controls: [
        { name: "text_box", value: "Text Field" },
        { name: "radio_buttons", value: "Radio Buttons" },
        { name: "check_boxes", value: "Check Boxes" },
        { name: "text_area", value: "Text Area" },
        { name: "drop_down", value: "Dropdown list" },
        { name: "date", value: "Date" }
      ],
      enableOptions: ["radio_buttons", "check_boxes", "drop_down"],
      disableName: ["full_name", "phone_number", "email"]
    }
  },
  computed: {
    isFormValid() {
      // Validate field name (required for non-disabled types)
      const isFieldNameValid = this.disableName.includes(this.selectedControl) || !!this.name

      // Validate options for radio_buttons, check_boxes, drop_down
      const areOptionsValid = !this.enableOptions.includes(this.selectedControl) ||
          (this.options.length > 0 && this.options.every(option =>
              !!option.value
          ))
      return isFieldNameValid  && areOptionsValid
    }
  },
  methods: {
    closeModal(){
      this.$emit("closeModal")
    },
    addFormItem() {
      const fieldData = {
        type: this.selectedControl,
        name: this.name,
        id: this.id,
        slug: this.slug && this.id ? this.slug : this.slugify(this.name),
        is_additional: 1,
        is_required: this.isRequired,
        is_visible: this.isVisible,
        allow_markdown: this.field_config.allowMarkdown,
        options: this.enableOptions.includes(this.selectedControl) ? this.options : []
      }
      this.$emit("addFormItem", fieldData)
    },

    updateType() {
      this.isRequired = '0'
      this.isVisible = '0'
      this.options = [] // Reset options when type changes
    },
    resetForm() {
      this.selectedControl = null
      this.name = null
      this.isRequired = '0'
      this.isVisible = '0'
      this.options = []
    },
    slugify(text) {
      return text
          .toString()
          .toLowerCase()
          .trim()
          .replace(/[\s\W-]+/g, '-') // Replace spaces and non-word chars with -
          .replace(/^-+|-+$/g, '');  // Remove leading/trailing hyphens
    },
    addOption() {
      this.options.push({ value: '' })
    },
    deleteOption(index) {
      this.options.splice(index, 1)
    }
  },
  watch: {
    'field_config.type'(newVal) {
      this.selectedControl = newVal
    },
    'field_config.name'(newVal) {
      this.name = newVal
    },
    'field_config.id'(newVal) {
      this.id = newVal
    },
    'field_config.slug'(newVal) {
      this.slug = newVal
    },
    'field_config.is_required'(newVal) {
      this.isRequired = newVal ? '1' : '0'
    },
    'field_config.is_visible'(newVal) {
      this.isVisible = newVal ? '1' : '0'
    },
    'field_config.options'(newVal) {
      this.options = newVal || []
    }
  }
}
</script>

<style lang="scss" scoped>
.form-item-list {
  border-radius: 4px;
  display: flex;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  padding-left: 10px;
  padding-top: 10px;
  padding-bottom: 10px;
  gap: 8px;

  .add-button {
    display: none;
    font-weight: 500;
    font-size: 16px;
    padding-right: 10px;
  }
  &:hover {
    .add-button {
      display: block;
    }
    color: rgba(79, 174, 175, 1);
    background: rgba(79, 174, 175, 0.1);
    .theme--light.v-icon {
      color: rgba(79, 174, 175, 1) !important;
    }
  }
}

.add-new-option {
  cursor: pointer;
  color: #4FAEAF;
  margin-top: 10px;
  font-weight: 500;
}

.remove-button {
  min-width: 36px !important;
}
</style>
<template>
  <v-container fluid>
    <FacilitiesTabs
        :configurations="configurations"
        :venue-service-id="venueServiceId"
        :venue-services="venueServices"
        @serviceChange="goToTimings"
        @update:venueServiceId="(value)=>venueServiceId = value"
        :venue-id="venueId"
        @venueChange="venueChange"
        @update:venueId="(value)=>venueId = value"
    />
    <v-divider class="mt-4" style="border-color: rgba(17, 42, 70, 0.14) !important;"/>
    <v-container fluid style="max-width: 100%" class="ttm">
      <v-form ref="conf_form">
        <div class="text-base font-semibold black-text ml-1 mt-6 mb-6">Timing Templates</div>
        <v-row>
          <v-col
              v-for="(template, templateIndex) in timingTemplates"
              :key="templateIndex"
              cols="12"
              md="6"
          >
            <v-expansion-panels flat multiple  v-model="templatePanel[templateIndex]">
              <v-expansion-panel class="rounded-4">
                <v-expansion-panel-header class="main-panel-header font-semibold" color="00B0AF">
                  {{ template.name || 'Timing Template Name' }}
                </v-expansion-panel-header>

                <v-expansion-panel-content >
                  <v-card>
                    <v-card-title>
                      <v-text-field
                          v-model="template.name"
                          :rules="[(v) => !!v || 'Name is required']"
                          placeholder="Name"
                          outlined
                          dense
                          required
                          class="q-text-field shadow-0 rounded-4"
                          hide-details="auto"
                      />
                      <v-btn icon @click="removeTemplate(templateIndex)">
                        <DeleteIcon stroke="#000000"/>
                      </v-btn>
                    </v-card-title>

                    <v-card-text class="rounded-4">
                      <v-expansion-panels multiple >
                        <v-expansion-panel
                            v-for="(group, groupIndex) in template.dayGroups"
                            :key="groupIndex"
                            class="mt-2 mb-2 rounded-4 bg-gray"
                        >
                          <v-expansion-panel-header class="bg-white child-panel-header">
                            <div class="d-flex justify-space-between align-center" style="width: 100%">
                              <span>Selected Days: {{ summarizeDays(group.weekdays) || 'N/A' }}</span>
                              <v-btn icon @click.stop="removeDayGroup(templateIndex, groupIndex)" width="16px" height="16px">
                                <DeleteIcon stroke="#000000"/>
                              </v-btn>
                            </div>
                          </v-expansion-panel-header>

                          <v-expansion-panel-content class="pl-6 pr-6 pt-3 pb-3 rounded-4">
                            <v-chip-group
                                v-model="group.weekdays"
                                multiple
                                column
                                active-class="selected-chip"
                                class="mb-3"
                            >
                              <v-chip
                                  v-for="(day, i) in days"
                                  :key="i"
                                  :value="day.value"
                                  outlined
                                  filter

                                  style="min-width: 56px; justify-content: center;"
                              >
                                {{ day.label }}
                              </v-chip>
                            </v-chip-group>
                            <div
                                v-for="(range, rangeIndex) in group.timeRanges"
                                :key="rangeIndex"
                                class="d-flex align-center mb-2"
                            >
                              <v-select
                                  v-model="range.start_time"
                                  :items="timingData.slice(0, (timingData.length -1))"
                                  :menu-props="{ bottom: true, offsetY: true }"
                                  :rules="[(v) => !!v || 'Start time is required']"
                                  background-color="#fff"
                                  placeholder="Start Time"
                                  class="q-autocomplete-rounded shadow-0 mr-2 rounded-4 equal-width-select"
                                  dense
                                  hide-details="auto"
                                  item-text="text"
                                  item-value="value"
                                  outlined
                                  required
                                  prepend-inner-icon="mdi-clock-outline"
                              ></v-select>
                              <v-select
                                  v-model="range.end_time"
                                  :items="timingData.slice(1)"
                                  :menu-props="{ bottom: true, offsetY: true }"
                                  :rules="[(v) => !!v || 'End time is required']"
                                  background-color="#fff"
                                  placeholder="End Time"
                                  class="q-autocomplete-rounded shadow-0 mr-2 rounded-4 equal-width-select"
                                  dense
                                  hide-details="auto"
                                  item-text="text"
                                  item-value="value"
                                  outlined
                                  required
                                  prepend-inner-icon="mdi-clock-outline"
                              ></v-select>
                              <v-btn icon @click="removeTimeRange(templateIndex, groupIndex, rangeIndex)" class="bg-white">
                                <DeleteIcon stroke="#000000"/>
                              </v-btn>
                            </div>

                            <v-btn small text @click="addTimeRange(templateIndex, groupIndex)" color="#00B0AF" class="toCapitalize">+ Add Time Range</v-btn>
                          </v-expansion-panel-content>
                        </v-expansion-panel>
                      </v-expansion-panels>

                      <v-btn small text class="mt-2 text-neon toCapitalize" @click="addDayGroup(templateIndex)">+ Add Days</v-btn>
                    </v-card-text>
                  </v-card>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-col>
        </v-row>
        <v-btn  class="mt-4 text-white bg-neon toCapitalize t" @click="addTemplate">+ Add Timing Template</v-btn>
        <v-btn color="primary" class="ml-3 mt-4 toCapitalize"  @click="saveTemplates">Save</v-btn>
      </v-form>
      <confirm-model
          v-bind="confirmModel"
          @close="confirmModel.id = null"
          @confirm="confirmActions"
      ></confirm-model>
    </v-container>
  </v-container>
</template>
<script>
import FacilitiesTabs from "@/views/Facility/FacilitiesTabs.vue";
import moment from "moment";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
export default {
  name: "FacilityTimingTemplateManagement",
  components: { FacilitiesTabs,DeleteIcon },
  computed: {
    hasChildVenues() {
      return this.$store.getters.venueInfo.sub_venues;
    },
    venueServices() {
      return this.hasChildVenues ?
          (this.venueId == this.$store.getters.userInfo.venue_id ?
              this.$store.getters.getSportsService.filter((service) => service.name != "POS")
              : this.$store.getters.getSubVenueServicesByVenueId(this.venueId).map((ele) => {
                ele.venue_service_id = ele.id;
                return ele;
              }).filter((service) => service.name != "POS"))
          : this.$store.getters.getSportsService.filter((service) => service.name != "POS");
    },
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
    venues(){
      const subVenues = this.$store.getters.getSubVenues?.data || [];
      const currentVenueId = this.$store.getters.userInfo?.venue_id;

      return subVenues.filter(venue => venue.id !== currentVenueId);
    },
  },
  data() {
    return {
      templatePanel: [],
      days: [
        { label: 'Mon', value: 2 },
        { label: 'Tue', value: 4 },
        { label: 'Wed', value: 8 },
        { label: 'Thu', value: 16 },
        { label: 'Fri', value: 32 },
        { label: 'Sat', value: 64 },
        { label: 'Sun', value: 1 },
      ],
      timingTemplates: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      deleted_timing_templates: [],
      deleted_timing_days: [],
      deleted_timing_ranges: [],
      defaultTiming: {
        name: null,
        weekdays: [],
        start_time: null,
        end_time: null,
      },
      timingData: [],
      timeIncrement: 60,
      configurations: {
        field_configurations: [],
        venue_service_documents: [],
        venue_service_configuration: {tnc_enable: 0, tnc: ""},
        deleted_service_documents: [],
        game_formations: [{name: ""}],
        facility_surface: [{}],
        per_capacity: 0,
        qube_configuration: null
      },
      venueServiceId: null,
      venueId:null,
      copyChildVenueDialogue: false,
      copyServiceDialogue: false,
      sourceTemplate: null,
      sourceTemplateName: null,
      sourceVenueServiceId: null,
      targetVenueIds:[],
      targetServiceIds:[],
    }
  },
  mounted() {
    if (this.$store.getters.getWeekdays.status == false) {
      this.$store.dispatch("loadWeekdays");
    }
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices").then((res) => {
        if (res.length) {
          this.checkVenueService();
        }
      });
    } else {
      this.checkVenueService();
    }
    // this.venueId = this.$store.getters.userInfo.venue_id;
  },
  methods: {
    dayName(bit) {
      const dayMap = {
        2: 'Monday',
        4: 'Tuesday',
        8: 'Wednesday',
        16: 'Thursday',
        32: 'Friday',
        64: 'Saturday',
        1: 'Sunday',
      };
      return dayMap[bit] || 'Unknown Day';
    },
    closeCopyChildDialogue() {
      this.copyChildVenueDialogue = false;
      this.sourceTemplate = null;
      this.sourceTemplateName = null;
      this.sourceVenueServiceId = null;
      this.targetVenueIds = [];
    },
    confirmCopyChildDialogue() {
      let data = {
        source_template_id: this.sourceTemplate.id,
        source_venue_service_id: this.sourceVenueServiceId,
        target_venue_ids: this.targetVenueIds,
      };

      this.showLoader("Copying...")
      this.$http
          .post(`venues/facilities/copy-timing-to-venue`, data)
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.showSuccess(response.data.message);
              // this.$emit('reload');
              this.closeCopyChildDialogue();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          })
          .finally(() => {
            this.hideLoader()
          });
    },
    copyToVenue(template) {
      console.log(template);
      this.copyChildVenueDialogue = true;
      this.sourceTemplate = template;
      this.sourceTemplateName = template.name;
      this.sourceVenueServiceId = this.venueServiceId;
      this.targetVenueIds = [];
    },
    venueChange() {
      if (this.venueServices.length > 0) {
        this.venueServiceId = this.venueServices[0].id;
        this.goToTimings();
      } else {
        this.venueServiceId = 0;
      }
    },
    generateTiming() {
      let currentTime = moment("00:00:00", "HH:mm:ss");
      this.timingData = [];
      this.timingData.push({
        value: currentTime.format("HH:mm:ss"),
        text: currentTime.format("hh:mm a"),
      });
      let hour = 0;
      while (hour < 24) {
        currentTime = currentTime.add(this.timeIncrement, "minutes");
        let data = {
          value: currentTime.format("HH:mm:ss"),
          text: currentTime.format("hh:mm a"),
        };
        if (data.value == "00:00:00") {
          data.value = "23:59:59";
          this.timingData.push(data);
          hour = 24;
        } else {
          this.timingData.push(data);
          hour = currentTime.get("hours");
        }
      }
    },
    async initializeFacilityServices() {
      if (!this.$store.getters.getConfigurationStatus(this.venueServiceId)) {
        await this.$store
            .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
            .then((response) => {
              if (response.status == 200) {
                this.configurations = this.$store.getters.getConfigurationByVenueServiceId(
                    this.venueServiceId
                );
                this.timeIncrement =
                    this.configurations.time_increment != null ? this.configurations.time_increment : 60;
              }
            });
      } else {
        this.configurations = this.$store.getters.getConfigurationByVenueServiceId(
            this.venueServiceId
        );

        this.timeIncrement =
            this.configurations.time_increment != null ? this.configurations.time_increment : 60;
      }

    },
    checkVenueService() {
      if (this.$route.params.data != null) {
        let data = JSON.parse(atob(this.$route.params.data));
        this.venueServiceId = data.venue_service_id;
        this.venueId = data.venue_id ?? this.$store.getters.userInfo.venue_id;
      } else {
        if (this.$store.getters.getSportsService.length) {
          this.venueServiceId = this.$store.getters.getSportsService[0].venue_service_id;
          this.venueId = this.$store.getters.userInfo.venue_id;
        }
      }
      setTimeout(async () => {
        await this.initializeFacilityServices();
        await this.getTimings();
      }, 10);
    },
    async getTimings() {
      this.generateTiming();
      await this.getTimingTemplates();
    },
    async getTimingTemplates() {
      this.showLoader("Loading");
      await this.$store
          .dispatch("loadTimingTemplatesByVenueServiceId", this.venueServiceId)
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              const templates = response.data.advanced || [];
              console.log("tt",templates);
              this.timingTemplates = templates.map(template => ({
                id: template.id,
                venue_service_id: template.venue_service_id,
                name: template.name || '',
                dayGroups: (template.days || []).map(day => ({
                  id: day.id,
                  weekdays: day.weekdays || [],
                  timeRanges: (day.timings || []).map(timing => ({
                    id: timing.id,
                    start_time: timing.start_time,
                    end_time: timing.end_time
                  }))
                }))
              }));
              console.log("template",this.timingTemplates);
              this.defaultTiming = {
                name: null,
                weekdays: [],
                start_time: null,
                end_time: null,
              };
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    goToTimings() {
      if (!this.venueServiceId) {
        this.showError("Please add new service");
        return;
      }
      this.$router.push({
        name: "FacilityTiming",
        params: {
          data: btoa(JSON.stringify({venue_service_id: this.venueServiceId})),
        },
      });
      this.getTimings();
    },
    confirmActions(data) {
      if (data.type === 'delete_timing_template') {
        this.deleted_timing_templates.push(this.timingTemplates[data.data.templateIndex].id);
        this.timingTemplates.splice(data.data.templateIndex, 1);
      }else if(data.type === 'delete_timing_days'){
        this.deleted_timing_days.push(this.timingTemplates[data.data.templateIndex].dayGroups[data.data.groupIndex].id);
        this.timingTemplates[data.data.templateIndex].dayGroups.splice(data.data.groupIndex, 1);
      }else if(data.type === 'delete_timing_range'){
        this.deleted_timing_ranges.push(this.timingTemplates[data.data.templateIndex].dayGroups[data.data.groupIndex].timeRanges[data.data.rangeIndex].id);
        this.timingTemplates[data.data.templateIndex].dayGroups[data.data.groupIndex].timeRanges.splice(data.data.rangeIndex, 1);
      }
      this.confirmModel.id = null;
    },
    icon() {
      if (this.weekdays.length == this.defaultTiming.weekdays.length)
        return "mdi-close-box";
      if (this.defaultTiming.weekdays.length > 0) return "mdi-minus-box";
      return "mdi-checkbox-blank-outline";
    },
    toggle() {
      this.$nextTick(() => {
        if (this.weekdays.length == this.defaultTiming.weekdays.length) {
          this.defaultTiming.weekdays = [];
        } else {
          this.defaultTiming.weekdays = this.weekdays.map(
              (item) => item.bit_value
          );
          this.$forceUpdate();
        }
      });
    },
    addTemplate() {
      this.timingTemplates.push({
        name: '',
        dayGroups: [
          {
            weekdays: [],
            timeRanges: [{start_time: '', end_time: ''}]
          }
        ]
      });
      this.$nextTick(() => {
        const newIndex = this.timingTemplates.length - 1;
        this.$set(this.templatePanel, newIndex, [0]); // Open the first (only) inner panel
      });
    },
    removeTemplate(index) {
      if (this.timingTemplates[index].id) {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this templates?",
          description:
              "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "delete_timing_template",
          data: {templateIndex:index},
        };
      }else{
        this.timingTemplates.splice(index, 1);
      }
    },
    addDayGroup(templateIndex) {
      this.timingTemplates[templateIndex].dayGroups.push({
        weekdays: [],
        timeRanges: [{start_time: '10:00', end_time: '18:00'}]
      });
    },
    removeDayGroup(templateIndex, groupIndex){
      if (this.timingTemplates[templateIndex].dayGroups[groupIndex].id) {
        this.confirmModel = {
          id: groupIndex,
          title: "Do you want to delete this timing days?",
          description:
              "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "delete_timing_days",
          data: {templateIndex:templateIndex,groupIndex:groupIndex},
        };
      }else{
        this.timingTemplates[templateIndex].dayGroups.splice(groupIndex, 1);
      }
    },
    addTimeRange(templateIndex, groupIndex) {
      this.timingTemplates[templateIndex].dayGroups[groupIndex].timeRanges.push({
        start: '',
        end: ''
      });
    },
    removeTimeRange(templateIndex, groupIndex, rangeIndex) {
      if (this.timingTemplates[templateIndex].dayGroups[groupIndex].timeRanges[rangeIndex].id) {
        this.confirmModel = {
          id: rangeIndex,
          title: "Do you want to delete this timing range?",
          description:
              "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "delete_timing_range",
          data: {templateIndex:templateIndex,groupIndex:groupIndex,rangeIndex:rangeIndex},

        };
      }else{
        this.timingTemplates[templateIndex].dayGroups[groupIndex].timeRanges.splice(rangeIndex, 1);
      }
    },
    summarizeDays(bitList) {
      const map = this.days.reduce((acc, day) => {
        acc[day.value] = day.label;
        return acc;
      }, {});
      return bitList.map(bit => map[bit]).join(', ');
    },
    saveTemplates() {
      if (!this.$refs.conf_form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if(!this.validateChips()){
        return;
      }
      const res = this.validateGlobalOverlaps();
      if(res.isOverlapping){
        this.showError(res.message);
        return;
      }
      let formData = new FormData();
      formData.append(`venue_service_id`, this.venueServiceId);
      if (this.deleted_timing_templates.length) {
        this.deleted_timing_templates.forEach((item, index) => {
          formData.append(`deleted_timing_templates[${index}]`, item);
        });
      }
      if (this.deleted_timing_days.length) {
        this.deleted_timing_days.forEach((item, index) => {
          formData.append(`deleted_timing_days[${index}]`, item);
        });
      }
      if (this.deleted_timing_ranges.length) {
        this.deleted_timing_days.forEach((item, index) => {
          formData.append(`deleted_timing_ranges[${index}]`, item);
        });
      }
      this.timingTemplates.forEach((template, index) => {
        for (let key in template) {
          formData.append(`timing_templates[${index}][${key}]`, typeof template[key] != "object" ? template[key] : JSON.stringify(template[key]));
        }
        formData.set(`timing_templates[${index}][venue_service_id]`, this.venueServiceId);
      });
      this.showLoader('Saving..');
      this.$http({
        method: "post",
        data: formData,
        url: "venues/facilities/advance-timing-templates",
      }).then((response) => {
            if (response.status === 200) {
              this.$store.dispatch("loadVenueServices");
              this.$store.dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
                  .then(() => {
                    this.$nextTick(() => {
                      this.$forceUpdate();
                    });
                  });
              this.deleted_timing_templates = [];
              this.deleted_timing_days = [];
              this.deleted_timing_ranges = [];
              this.getTimings();
              this.hideLoader();
              this.showSuccess("Configuration successfully updated");
            }
          })
          .catch((error) => {
            this.errorChecker(error);
            this.hideLoader();
            this.$store.dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId);
            this.$store.dispatch("loadVenueServices");
          });
    },
    validateChips() {
      let isValid = true;
      this.timingTemplates.forEach((template) => {
        template.dayGroups.forEach((group) => {
          if (!group.weekdays || group.weekdays.length === 0) {
            this.showError(`${template.name} At least one day must be selected`);
            isValid = false;
          }
        });
      });
      return isValid
    },
    validateGlobalOverlaps() {
      const timeMap = {};
      const parseTime = (t) => {
        const [h, m] = t.split(':').map(Number);
        return h * 60 + m;
      };
      try {
        this.timingTemplates.forEach((template, tIndex) => {
          template.dayGroups.forEach((group, gIndex) => {
            group.timeRanges.forEach(r => r.error = '');
            group.weekdays.forEach((weekday) => {
              if (!timeMap[weekday]) timeMap[weekday] = [];
              group.timeRanges.forEach((range, rIndex) => {
                const start = parseTime(range.start_time);
                const end = parseTime(range.end_time);
                if (end <= start) {
                  throw new Error(`Template:${template.name} End time must be after start time (TimeRange ${rIndex + 1})`);
                }
                for (const entry of timeMap[weekday]) {
                  if (start < entry.end && entry.start < end) {
                    throw new Error(`Template:${template.name} Overlap on ${this.dayName(weekday)}: ${range.start_time}–${range.end_time}`);
                  }
                }
                timeMap[weekday].push({ start, end, tIndex, gIndex, rIndex });
              });
            });
          });
        });
        return {isOverlapping:false,message:""}; // no overlap
      } catch (e) {
        return {isOverlapping:true,message:e.message}; // overlap detected
      }
    }

  }
}
</script>
<style scoped>
.ttm .v-expansion-panel-header.main-panel-header{
  background: #00B0AF;
  color: #FFFFFF !important;
}
.ttm .v-expansion-panel-header {
  min-height: 48px;
}
.toCapitalize{
  text-transform: capitalize;
}
.ttm .v-expansion-panel .v-expansion-panel-header.main-panel-header{
  border-radius: 1rem;
}
.ttm .v-expansion-panel--active .v-expansion-panel-header.main-panel-header{
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 0rem;
  border-bottom-left-radius: 0rem;
}
.ttm .v-expansion-panel-header.child-panel-header {
  border-radius: 1rem !important;
}
.ttm .selected-chip.v-chip.v-chip--outlined.v-chip.v-chip{
  background-color: #f6f8fa !important;
}
.bg-gray{
  background-color: #f6f8fa !important;
}
.equal-width-select {
  width: 180px; /* adjust as needed */
  flex-shrink: 0;
}

</style>

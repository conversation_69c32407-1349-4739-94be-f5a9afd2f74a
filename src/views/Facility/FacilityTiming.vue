<template>
  <v-container fluid>
    <FacilitiesTabs
        :configurations="configurations"
        :venue-service-id="venueServiceId"
        :venue-services="venueServices"
        @serviceChange="goToTimings"
        @update:venueServiceId="(value)=>venueServiceId = value"
        :venue-id="venueId"
        @venueChange="venueChange"
        @update:venueId="(value)=>venueId = value"
    />
    <v-divider class="mt-4" style="border-color: rgba(17, 42, 70, 0.14) !important;"/>

    <v-container fluid style="max-width: 85%">
      <v-form ref="conf_form">
      <div class="text-base font-semibold black-text ml-1 mt-6">
        Default Timing
      </div>
      <v-card
          class="rounded-2 shadow-0 bordered mt-2"
          outlined
      >
        <v-card-text>
          <v-row dense>
            <v-col md="3">
              <label for="">Name*</label>
              <v-text-field
                  v-model="defaultTiming.name"
                  :rules="[(v) => !!v || 'Name is required']"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  outlined
                  required
              ></v-text-field>
            </v-col>
            <v-col lg="4">
              <label for="">Choose Days Applicable</label>
              <v-autocomplete
                  v-model="defaultTiming.weekdays"
                  :items="weekdays"
                  :rules="[(v) => v.length > 0 || 'Weekdays is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="name"
                  item-value="bit_value"
                  multiple
                  outlined
                  validate-on-blur
              >
                <template
                    v-if="weekdays.length == defaultTiming.weekdays.length"
                    v-slot:selection="{ index }"
                >
                  <span v-if="index == 0">All Days</span>
                </template>
                <template v-else v-slot:selection="{ item, index }">
                  <span v-if="index == 0">{{ item.name }}</span>
                  <span v-if="index == 1">, {{ item.name }}</span>
                  <span v-if="index == 2">, ...</span>
                </template>
                <template v-slot:prepend-item>
                  <v-list-item ripple @click="toggle()">
                    <v-list-item-action>
                      <v-icon :color="defaultTiming.weekdays.length > 0 ? 'indigo darken-4' : ''">{{ icon() }}</v-icon>
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title>Select All</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <v-divider class="mt-2"></v-divider>
                </template>

              </v-autocomplete>
            </v-col>
            <v-col lg="2">
              <label for="">From*</label>
              <v-select
                  v-model="defaultTiming.start_time"
                  :items="timingData.slice(0, (timingData.length -1))"
                  :menu-props="{ bottom: true, offsetY: true }"
                  :rules="[(v) => !!v || 'From time is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="text"
                  item-value="value"
                  outlined
                  required
              ></v-select>
            </v-col>
            <v-col lg="2">
              <label for="">To*</label>

              <v-select
                  v-model="defaultTiming.end_time"
                  :items="timingData.slice(1)"
                  :menu-props="{ bottom: true, offsetY: true }"
                  :rules="[(v) => !!v || 'To time is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="text"
                  item-value="value"
                  outlined
                  required
              ></v-select>
            </v-col>
            <v-col lg="1" v-if="hasChildVenues && checkWritePermission($modules.facility.child_facility.slug)">
              <v-menu
                  content-class="q-menu"
                  offset-y
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                      class="text_capitalize px-0 no-hover-effect mt-7"
                      elevation="0"
                      height="24"
                      style="background-color: transparent; min-width: 24px !important; "
                      v-bind="attrs"
                      v-on="on"
                  >
                    <DotsIcon height="24" width="24"/>
                  </v-btn>
                </template>
                <v-list class="pointer">
<!--                  <v-list-item-->
<!--                      @click.stop="copyToService(defaultTiming)">-->
<!--                    <SvgIcon class="font-medium text-sm gap-x-2" text="Copy to service">-->
<!--                      <template #icon>-->
<!--                        <CopyIcon stroke="#141B34"/>-->
<!--                      </template>-->
<!--                    </SvgIcon>-->
<!--                  </v-list-item>-->
                  <v-list-item
                      v-if="hasChildVenues"
                      @click.stop="copyToVenue(defaultTiming)">
                    <SvgIcon class="font-medium text-sm gap-x-2" text="Copy to venue">
                      <template #icon>
                        <CopyToChildIcon stroke="#141B34"/>
                      </template>
                    </SvgIcon>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <div v-if="
          checkWritePermission($modules.facility.management.slug) &&
            venueServices.length
        " class="text-base font-semibold black-text ml-1 mt-6">Timing Templates
      </div>
      <v-card v-if="
          checkWritePermission($modules.facility.management.slug) &&
            venueServices.length
        "
              class="rounded-2 shadow-0 bordered mt-2"
              outlined
      >
        <v-card-text>
          <v-row v-for="(timings, k) in timingTemplates" :key="k"
                 dense>
            <v-col md="3">
              <label for="">Name*</label>
              <v-text-field
                  v-model="timings.name"
                  :rules="[(v) => !!v || 'Name is required']"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  outlined
                  required
              ></v-text-field>
            </v-col>
            <v-col lg="4">
              <label for="">Choose Days Applicable</label>
              <v-autocomplete
                  v-model="timings.weekdays"
                  :items="weekdays"
                  :rules="[(v) => v.length > 0 || 'Weekdays is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="name"
                  item-value="bit_value"
                  multiple
                  outlined
                  validate-on-blur
              >
                <template
                    v-if="weekdays.length == timings.weekdays.length"
                    v-slot:selection="{ index }"
                >
                  <span v-if="index == 0">All Days</span>
                </template>
                <template v-else v-slot:selection="{ item, index }">
                  <span v-if="index == 0">{{ item.name }}</span>
                  <span v-if="index == 1">, {{ item.name }}</span>
                  <span v-if="index == 2">, ...</span>
                </template>
                <template v-slot:prepend-item>
                  <v-list-item ripple @click="toggleTemplate(k)">
                    <v-list-item-action>
                      <v-icon :color="defaultTiming.weekdays.length > 0 ? 'indigo darken-4' : ''">{{ iconTemplate(k) }}</v-icon>
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title>Select All</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <v-divider class="mt-2"></v-divider>
                </template>

              </v-autocomplete>
            </v-col>
            <v-col lg="2">
              <label for="">From*</label>
              <v-select
                  v-model="timings.start_time"
                  :items="timingData.slice(0, (timingData.length -1))"
                  :menu-props="{ bottom: true, offsetY: true }"
                  :rules="[(v) => !!v || 'From time is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="text"
                  item-value="value"
                  outlined
                  required
              ></v-select>
            </v-col>
            <v-col lg="2">
              <label for="">To*</label>

              <v-select
                  v-model="timings.end_time"
                  :items="timingData.slice(1)"
                  :menu-props="{ bottom: true, offsetY: true }"
                  :rules="[(v) => !!v || 'To time is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="text"
                  item-value="value"
                  outlined
                  required
              ></v-select>
            </v-col>
            <v-col lg="1">
              <v-btn v-if="timingTemplates.length > 1"
                     class="mt-6"
                     color="red"
                     dark
                     fab
                     icon
                     x-small
                     @click="deleteTimingTemplate(k)"
              >
                <DeleteIcon />
              </v-btn>
              <v-menu
                  v-if="hasChildVenues"
                  content-class="q-menu"
                  offset-y
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                      v-if="hasChildVenues"
                      class="text_capitalize no-hover-effect mt-6"
                      elevation="0"
                      dark
                      fab
                      icon
                      x-small
                      style="background-color: transparent; min-width: 32px !important;"
                      v-bind="attrs"
                      v-on="on"
                  >
                    <DotsIcon height="24" width="24"/>
                  </v-btn>
                </template>
                <v-list class="pointer">
<!--                  <v-list-item-->
<!--                      @click.stop="copyToService(timings)">-->
<!--                    <SvgIcon class="font-medium text-sm gap-x-2" text="Copy to service">-->
<!--                      <template #icon>-->
<!--                        <CopyIcon stroke="#141B34"/>-->
<!--                      </template>-->
<!--                    </SvgIcon>-->
<!--                  </v-list-item>-->
                  <v-list-item
                      v-if="hasChildVenues"
                      @click.stop="copyToVenue(timings)">
                    <SvgIcon class="font-medium text-sm gap-x-2" text="Copy to venue">
                      <template #icon>
                        <CopyToChildIcon stroke="#141B34"/>
                      </template>
                    </SvgIcon>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>

          </v-row>
          <v-row class="mt-4" dense>
            <v-col>
              <p class="text-base text-neon text-underline font-semibold pointer" @click="
                    timingTemplates.push({
                      name: null,
                      weekdays: [],
                      end_time: null,
                    })
                  ">+Add Timing Template</p>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      <v-card-actions v-if="checkWritePermission($modules.facility.management.slug)">
        <v-spacer></v-spacer>
        <v-btn
            class="ma-2 white--text blue-color"
            height="45"
            text
            @click="saveTimings"
        >Save
        </v-btn
        >
      </v-card-actions>
      </v-form>
    </v-container>
    <confirm-model
        v-bind="confirmModel"
        @close="confirmModel.id = null"
        @confirm="confirmActions"
    ></confirm-model>

    <v-dialog
        v-model="copyChildVenueDialogue"
        persistent
        max-width="500"
        @input="closeCopyChildDialogue"
    >
      <v-card>
        <v-card-title class="border-bottom px-5">
          <div class=" w-full">
            <div class="d-flex justify-space-between align-center">
              <p class="mb-0 font-medium">
                Copying '{{ sourceTemplateName }}'
              </p>
              <v-btn class="shadow-0" fab x-small @click="closeCopyChildDialogue">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </v-card-title>
        <v-card-text class="pa-5 border-bottom">
          <label for="">Select Target Venue(s) </label>
          <v-autocomplete
              :items="venues"
              v-model="targetVenueIds"
              item-value="id"
              item-text="name"
              outlined
              multiple
              :rules="[(v) => v.length > 0 || 'Venue is required']"
              background-color="#fff"
              class="q-autocomplete shadow-0"
              hide-details="auto"
              dense
          >
            <template v-slot:prepend-item>
              <v-list-item ripple @click="toggleVenueSelect()">
                <v-list-item-action>
                  <v-icon :color="targetVenueIds.length > 0 ? 'teal darken-4' : ''">{{
                      getVenueServiceIcon()
                    }}</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                  <v-list-item-title>All</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
            <template
                v-if="targetVenueIds.length === venues.length"
                v-slot:selection="{ index }"
            >
              <span v-if="index === 0">All Venues</span>
            </template>
            <template v-else v-slot:selection="{ item, index }">
              <span v-if="index === 0">{{ item.name }}</span>
              <span v-if="index === 1">, ...</span>
            </template>
          </v-autocomplete>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2 text-capitalize" text @click="closeCopyChildDialogue">No</v-btn>
          <v-btn class="ma-2 white--text blue-color shadow-0" @click="confirmCopyChildDialogue">Yes</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import FacilitiesTabs from "@/views/Facility/FacilitiesTabs.vue";
import DeleteIcon from "@/assets/images/misc/delete-bg-icon.svg";
import moment from "moment";
import CopyToChildIcon from "@/assets/images/misc/copy-child.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DotsIcon from "@/assets/images/misc/dots-lg.svg";
// import CopyIcon from "@/assets/images/misc/copy-icon.svg";

export default {
  name: "FacilityTiming",
  components: {/*CopyIcon,*/ DotsIcon, SvgIcon, CopyToChildIcon, DeleteIcon, FacilitiesTabs},
  computed: {
    hasChildVenues() {
      return this.$store.getters.venueInfo.sub_venues;
    },
    venueServices() {
      return this.hasChildVenues ?
          (this.venueId == this.$store.getters.userInfo.venue_id ?
              this.$store.getters.getSportsService.filter((service) => service.name != "POS")
              : this.$store.getters.getSubVenueServicesByVenueId(this.venueId).map((ele) => {
                ele.venue_service_id = ele.id;
                return ele;
              }).filter((service) => service.name != "POS"))
          : this.$store.getters.getSportsService.filter((service) => service.name != "POS");
    },
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
    venues(){
      const subVenues = this.$store.getters.getSubVenues?.data || [];
      const currentVenueId = this.$store.getters.userInfo?.venue_id;

      return subVenues.filter(venue => venue.id !== currentVenueId);
    },
  },
  data() {
    return {
      timingTemplates: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      deleted_timing_templates: [],
      defaultTiming: {
        name: null,
        weekdays: [],
        start_time: null,
        end_time: null,
      },
      timingData: [],
      timeIncrement: 60,
      configurations: {
        field_configurations: [],
        venue_service_documents: [],
        venue_service_configuration: {tnc_enable: 0, tnc: ""},
        deleted_service_documents: [],
        game_formations: [{name: ""}],
        facility_surface: [{}],
        per_capacity: 0,
        qube_configuration: null
      },
      venueServiceId: null,
      venueId:null,
      copyChildVenueDialogue: false,
      copyServiceDialogue: false,
      sourceTemplate: null,
      sourceTemplateName: null,
      sourceVenueServiceId: null,
      targetVenueIds:[],
      targetServiceIds:[],
    }
  },
  mounted() {
    if (this.$store.getters.getWeekdays.status == false) {
      this.$store.dispatch("loadWeekdays");
    }
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices").then((res) => {
        if (res.length) {
          this.checkVenueService();
        }
      });
    } else {
      this.checkVenueService();
    }
    // this.venueId = this.$store.getters.userInfo.venue_id;
  },
  methods: {
    toggleVenueSelect() {
      this.$nextTick(() => {
        if (this.targetVenueIds.length == this.venues.length) {
          this.targetVenueIds = [];
        } else {
          this.targetVenueIds = this.venues.map((item) => item.id);
        }
      });
    },
    getVenueServiceIcon() {
      if (this.targetVenueIds.length == 0) return "mdi-checkbox-blank-outline";
      if (this.venues.length == this.targetVenueIds.length)
        return "mdi-close-box";
      return "mdi-minus-box";
    },
    closeCopyChildDialogue(){
      this.copyChildVenueDialogue = false;
      this.sourceTemplate = null;
      this.sourceTemplateName = null;
      this.sourceVenueServiceId = null;
      this.targetVenueIds = [];
    },
    confirmCopyChildDialogue(){
      let data = {
        source_template_id:this.sourceTemplate.id,
        source_venue_service_id:this.sourceVenueServiceId,
        target_venue_ids:this.targetVenueIds,
      };

      this.showLoader("Copying...")
      this.$http
          .post(`venues/facilities/copy-timing-to-venue`, data)
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.showSuccess(response.data.message);
              // this.$emit('reload');
              this.closeCopyChildDialogue();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          })
          .finally(() => {
            this.hideLoader()
          });
    },
    copyToService(template){
      console.log(template);
    },
    copyToVenue(template){
      console.log(template);
      this.copyChildVenueDialogue = true;
      this.sourceTemplate = template;
      this.sourceTemplateName = template.name;
      this.sourceVenueServiceId = this.venueServiceId;
      this.targetVenueIds = [];
    },
    venueChange(){
      if(this.venueServices.length > 0){
        this.venueServiceId = this.venueServices[0].id;
        this.goToTimings();
      }else{
        this.venueServiceId = 0;
      }
    },
    generateTiming() {
      let currentTime = moment("00:00:00", "HH:mm:ss");
      this.timingData = [];
      this.timingData.push({
        value: currentTime.format("HH:mm:ss"),
        text: currentTime.format("hh:mm a"),
      });
      let hour = 0;
      while (hour < 24) {
        currentTime = currentTime.add(this.timeIncrement, "minutes");
        let data = {
          value: currentTime.format("HH:mm:ss"),
          text: currentTime.format("hh:mm a"),
        };
        if (data.value == "00:00:00") {
          data.value = "23:59:59";
          this.timingData.push(data);
          hour = 24;
        } else {
          this.timingData.push(data);
          hour = currentTime.get("hours");
        }
      }
    },
    async initializeFacilityServices() {
      if (!this.$store.getters.getConfigurationStatus(this.venueServiceId)) {
        await this.$store
            .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
            .then((response) => {
              if (response.status == 200) {
                this.configurations = this.$store.getters.getConfigurationByVenueServiceId(
                    this.venueServiceId
                );
                this.timeIncrement =
                    this.configurations.time_increment != null ? this.configurations.time_increment : 60;
              }
            });
      } else {
        this.configurations = this.$store.getters.getConfigurationByVenueServiceId(
            this.venueServiceId
        );

        this.timeIncrement =
            this.configurations.time_increment != null ? this.configurations.time_increment : 60;
      }

    },
    checkVenueService() {
      if (this.$route.params.data != null) {
        let data = JSON.parse(atob(this.$route.params.data));
        this.venueServiceId = data.venue_service_id;
        this.venueId = data.venue_id ?? this.$store.getters.userInfo.venue_id;
      } else {
        if (this.$store.getters.getSportsService.length) {
          this.venueServiceId = this.$store.getters.getSportsService[0].venue_service_id;
          this.venueId = this.$store.getters.userInfo.venue_id;
        }
      }
      setTimeout(async () => {
        await this.initializeFacilityServices();
        await this.getTimings();
      }, 10);
    },
    async getTimings() {
      this.generateTiming();
      await this.getTimingTemplates();
    },
    async getTimingTemplates() {
      this.showLoader("Loading");
      await this.$store
          .dispatch("loadTimingTemplatesByVenueServiceId", this.venueServiceId)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let templates = response.data.data;
              this.timingTemplates = JSON.parse(
                  JSON.stringify(templates.filter((i) => i.is_default === 0))
              );
              if (templates.filter((i) => i.is_default === 1).length > 0) {
                this.defaultTiming = templates.filter((i) => i.is_default === 1)[0];
              } else {
                this.defaultTiming = {
                  name: null,
                  weekdays: [],
                  start_time: null,
                  end_time: null,
                };
              }
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    goToTimings() {
      if (!this.venueServiceId) {
        this.showError("Please add new service");
        return;
      }
      this.$router.push({
        name: "FacilityTiming",
        params: {
          data: btoa(JSON.stringify({venue_service_id: this.venueServiceId})),
        },
      });
      this.getTimings();
    },
    deleteTimingTemplate(index) {
      if (this.timingTemplates[index].id) {
        this.confirmModel = {
          id: index,
          title: "Do you want to delete this templates?",
          description:
              "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "delete_timing_template",
        };
      } else {
        this.timingTemplates.splice(index, 1);
      }
    },
    confirmActions(data) {
      if (data.type == 'delete_timing_template') {
        this.deleted_timing_templates.push(this.timingTemplates[data.id].id);
        this.timingTemplates.splice(data.id, 1);
      }
      this.confirmModel.id = null;
    },
    saveTimings() {
      if (!this.$refs.conf_form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }

      let formData = new FormData();
      formData.append(`venue_service_id`, this.venueServiceId);


      for (let key in this.defaultTiming) {
        formData.append(
            `default_timing[0][${key}]`,
            typeof this.defaultTiming[key] != "object"
                ? this.defaultTiming[key]
                : JSON.stringify(this.defaultTiming[key])
        );
      }



      if (this.deleted_timing_templates.length) {
        this.deleted_timing_templates.forEach((item, index) => {
          formData.append(`deleted_timing_templates[${index}]`, item);
        });
      }

      this.timingTemplates.forEach((template, index) => {
        for (let key in template) {
          formData.append(
              `timing_templates[${index}][${key}]`,
              typeof template[key] != "object"
                  ? template[key]
                  : JSON.stringify(template[key])
          );
        }
        formData.set(
            `timing_templates[${index}][venue_service_id]`,
            this.venueServiceId
        );
      });
      this.showLoader('Saving..');
      this.$http({
        method: "post",
        data: formData,
        url: "venues/facilities/save-timing",
        headers: {
          "Content-Type": "multipart/form-data; boundary=${form._boundary}",
        },
      })
          .then((response) => {
            if (response.status == 200) {
              this.$store.dispatch("loadVenueServices");
              this.$store
                  .dispatch(
                      "loadConfigurationsByVenueServiceId",
                      this.venueServiceId
                  )
                  .then(() => {
                    this.$nextTick(() => {
                      this.$forceUpdate();
                    });
                  });
              this.getTimings();
              this.hideLoader();
              this.showSuccess("Configuration successfully updated");
            }
          })
          .catch((error) => {
            this.errorChecker(error);
            this.hideLoader();
            this.$store.dispatch(
                "loadConfigurationsByVenueServiceId",
                this.venueServiceId
            );
            this.$store.dispatch("loadVenueServices");
          });
    },
    icon() {
      if (this.weekdays.length == this.defaultTiming.weekdays.length)
        return "mdi-close-box";
      if (this.defaultTiming.weekdays.length > 0) return "mdi-minus-box";
      return "mdi-checkbox-blank-outline";
    },
    toggle() {
      this.$nextTick(() => {
        if (this.weekdays.length == this.defaultTiming.weekdays.length) {
          this.defaultTiming.weekdays = [];
        } else {
          this.defaultTiming.weekdays = this.weekdays.map(
              (item) => item.bit_value
          );
          this.$forceUpdate();
        }
      });
    },
    iconTemplate(index) {
      if (this.weekdays.length == this.timingTemplates[index].weekdays.length)
        return "mdi-close-box";
      if (this.timingTemplates[index].weekdays.length > 0) return "mdi-minus-box";
      return "mdi-checkbox-blank-outline";
    },
    toggleTemplate(index) {
      this.$nextTick(() => {
        if (this.weekdays.length == this.timingTemplates[index].weekdays.length) {
          this.timingTemplates[index].weekdays = [];
        } else {
          this.timingTemplates[index].weekdays = this.weekdays.map(
              (item) => item.bit_value
          );
          this.$forceUpdate();
        }
      });
    },
  }
}
</script>

<style scoped>

</style>

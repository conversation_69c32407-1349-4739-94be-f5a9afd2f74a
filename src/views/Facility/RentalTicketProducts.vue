<template>
  <v-container fluid>
    <FacilitiesTabs
        :key="venueServiceId"
        :configurations="configurations"
        :show-add-ticket="true"
        :venue-id="venueId"
        :venue-service-id="venueServiceId"
        :venue-services="venueServices"
        @addTicket="addTicket"
        @serviceChange="serviceChange"
        @toggleFilters="showFilters = !showFilters"
        @venueChange="venueChange"
        @update:venueServiceId="(value) => (venueServiceId = value)"
        @update:venueId="(value) => (venueId = value)"
    />

    <v-divider
        class="my-4"
        style="border-color: rgba(17, 42, 70, 0.14) !important"
    />
    <v-row v-if="showFilters" class="mx-0" dense>
      <v-col lg="2" sm="3">
        <label for="">Facilities</label>
        <v-select
            v-model="filters.facilities"
            :items="facilities"
            :menu-props="{ bottom: true, offsetY: true }"
            background-color="#fff"
            class="q-autocomplete shadow-0"
            dense
            hide-details
            item-text="name"
            item-value="id"
            multiple
            outlined
            placeholder="Facilities"
        >
          <template
              v-if="filters.facilities.length === facilities.length"
              v-slot:selection="{ index }"
          >
            <span v-if="index === 0">All Facilities</span>
          </template>
          <template v-else v-slot:selection="{ item, index }">
            <span v-if="index === 0">{{ item.name }}</span>
            <span v-if="index === 1">, ...</span>
          </template>
        </v-select>
      </v-col>
      <v-col v-if="isTicket === 1" lg="2" sm="3">
        <label for="">Ticket Type</label>
        <v-select
            v-model="filters.types"
            :items="packageTypes"
            :menu-props="{ bottom: true, offsetY: true }"
            background-color="#fff"
            class="q-autocomplete shadow-0"
            dense
            hide-details
            item-text="name"
            item-value="type"
            multiple
            outlined
            placeholder="Type"
        >
          <template
              v-if="filters.types.length === packageTypes.length"
              v-slot:selection="{ index }"
          >
            <span v-if="index === 0">All Types</span>
          </template>
          <template v-else v-slot:selection="{ item, index }">
            <span v-if="index === 0">{{ item.name }}</span>
            <span v-if="index === 1">, {{ item.name }}</span>
            <span v-if="index === 2">, ...</span>
          </template>
        </v-select>
      </v-col>
      <v-col v-if="isTicket === 1" lg="2" sm="3">
        <label for="">Sales Channels</label>
        <v-select
            v-model="filters.salesChannels"
            :items="salesChannels"
            :menu-props="{ bottom: true, offsetY: true }"
            background-color="#fff"
            class="q-autocomplete shadow-0"
            dense
            hide-details
            multiple
            outlined
            placeholder="Sales Channels"
        >
          <template
              v-if="filters.salesChannels.length === salesChannels.length"
              v-slot:selection="{ index }"
          >
            <span v-if="index === 0">All Types</span>
          </template>
          <template v-else v-slot:selection="{ item, index }">
            <span v-if="index === 0">{{ item }}</span>
            <span v-if="index === 1">, {{ item }}</span>
            <span v-if="index === 2">, ...</span>
          </template>
        </v-select>
      </v-col>
      <v-spacer/>
      <v-col lg="2" sm="3">
        <label for="">Search Ticket</label>
        <v-text-field
            v-model="filters.search"
            class="q-text-field shadow-0 search-input bg-white"
            dense
            hide-details="auto"
            outlined
        >
          <template #prepend-inner>
            <SearchIcon class="mt-1"/>
          </template>
        </v-text-field>
      </v-col>

    </v-row>
    <v-row v-if="filteredProducts.length > 0">
      <v-col
          v-for="(product, index) in filteredProducts"
          :key="index"
          cols="12"
          lg="4"
          md="6"
          xl="3"
      >

        <button class="p-0 w-full" @click="editTicket(product)">
          <div class="bg-white shadow rounded-lg p-3 relative">
            <div
                v-if="product.rental_parent_id"
                class="ribbon"
            >P</div>
            <div class="d-flex align-start gap-x-4">
              <div>
                <view-image v-if="product.image" :height="120" :image="product.image" :width="120" class="rounded-lg"
                            defaultImage="ground"/>
                <div v-else class="image-placeholder">
                  TK-{{ index + 1 }}
                </div>
              </div>
              <div class="px-2 w-full" style="max-width: calc(100% - 130px)">
                <div class="d-flex justify-space-between align-center mb-3">
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <p
                          class="mb-0 font-semibold text-left wrap-text line-clamp-2"
                          v-bind="attrs"
                          v-on="on"
                      >
                        {{ product.name }}
                      </p>
                    </template>
                    <span>{{ product.name }}</span>
                  </v-tooltip>
                  <v-menu
                      content-class="q-menu"
                      offset-y
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                          class="text_capitalize px-0 no-hover-effect"
                          elevation="0"
                          height="24"
                          style="background-color: transparent; min-width: 24px !important; "
                          v-bind="attrs"
                          v-on="on"
                      >
                        <DotsIcon height="24" width="24"/>
                      </v-btn>
                    </template>
                    <v-list class="pointer">
                      <v-list-item v-if="checkWritePermission($modules.facility.timeRates.slug)"
                                   @click.stop="editTicket(product)">
                        <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
                          <template #icon>
                            <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
                          </template>
                        </SvgIcon>
                      </v-list-item>
                      <v-list-item v-if="checkWritePermission($modules.facility.timeRates.slug)"
                                   @click.stop="confirmDuplicateProduct(product)">
                        <SvgIcon class="font-medium text-sm gap-x-2" text="Duplicate">
                          <template #icon>
                            <CopyIcon stroke="#141B34"/>
                          </template>
                        </SvgIcon>
                      </v-list-item>
                      <v-list-item v-if="checkWritePermission($modules.facility.timeRates.slug)"
                                   @click.stop="confirmDeleteRentals(product.id)">
                        <SvgIcon class="font-medium text-sm gap-x-2 text-red" text="Delete Ticket">
                          <template #icon>
                            <DeleteIcon stroke="#E50000"/>
                          </template>
                        </SvgIcon>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
                <div v-if="isTicket === 1" class="d-flex justify-space-between align-center mb-2">
                  <p class="mb-0 text-thin-gray text-sm">
                    Sales Channel
                  </p>
                  <p class="mb-0 text-truncate max-w-50 text-blue">
                    {{ getSalesChannels(product) }}
                  </p>
                </div>

                <div class="d-flex justify-space-between align-center mb-2">
                  <p class="mb-0 text-thin-gray text-sm">
                    Facility
                  </p>
                  <p class="mb-0 text-truncate max-w-50 text-blue">
                    {{ getProductFacilitiesName(product) }}
                  </p>
                </div>
                <div v-if="isTicket && checkReadPermission($modules.facility.ticket_dependency.slug)" class="d-flex justify-space-between align-center mb-2">
                  <p class="mb-0 text-thin-gray text-sm">
                    Dependency
                  </p>
                  <p class="mb-0 text-truncate max-w-50 text-blue">
                    {{ getProductDependenciesName(product) }}
                  </p>
                </div>
              </div>
            </div>
            <v-row class="mx-0 mt-2 bg-white">
              <v-col class="border-top text-center" cols="6">
                <p class="mb-0 text-thin-gray">Price</p>
                <p class="mb-0 text-blue">
                  {{ product.total_price | toCurrency }}
                </p>
              </v-col>
              <v-col v-if="isTicket" class="border-top text-center" cols="6">
                <p class="mb-0 text-thin-gray">Type</p>
                <p class="mb-0 text-blue">
                  {{ getProductType(product) }}
                </p>
              </v-col>
              <v-col v-else class="border-top text-center" cols="6">
                <p class="mb-0 text-thin-gray">Duration</p>
                <p class="mb-0 text-blue">
                  {{ formatDuration(product.duration) }}
                </p>
              </v-col>
            </v-row>
          </div>
        </button>

      </v-col>
      <v-col cols="12">
        <div class="">
          Total {{ isTicket === 1 ? 'Tickets' : 'Time Rates' }}: {{ filteredProducts.length }}
        </div>
      </v-col>
    </v-row>
    <div v-else class="text-center mt-5 text-lg font-semibold">
      No {{ isTicket === 1 ? 'Tickets' : 'Time Rates' }} found
    </div>
    <confirm-model
        v-bind="confirmModel"
        @close="confirmModel.id = null"
        @confirm="confirmActions"
    ></confirm-model>
    <FacilityTicketForm
        v-if="addTicketModal"
        :add-ticket-modal="addTicketModal"
        :configurations="configurations"
        :facilities="facilities"
        :is-ticket="isTicket === 1"
        :venue-id="venueId"
        :memberships="memberships"
        :selected-product="selectedProduct"
        :tickets="rentalProducts"
        :venue-service-id="venueServiceId"
        @close="addTicketModal = false"
        @refresh="getRentalProducts"
    />
  </v-container>
</template>

<script>
import constants from '@/utils/constants'
import FacilitiesTabs from '@/views/Facility/FacilitiesTabs.vue'
import SvgIcon from '@/components/Image/SvgIcon.vue'
import EditIcon from '@/assets/images/tables/edit.svg'
import DeleteIcon from '@/assets/images/misc/delete-icon.svg'
import DotsIcon from '@/assets/images/misc/dots.svg'
import CopyIcon from '@/assets/images/misc/copy-icon.svg'
import FacilityTicketForm from '@/views/Facility/Rentals/FacilityTicketForm.vue'
import SearchIcon from '@/assets/images/misc/search.svg'

export default {
  components: {
    SearchIcon,
    FacilityTicketForm,
    CopyIcon,
    DotsIcon,
    EditIcon,
    DeleteIcon,
    SvgIcon,
    FacilitiesTabs,
  },
  data () {
    return {
      facilities: [],
      filters: {
        facilities: [],
        salesChannels: [],
        types: [],
        search: ''
      },
      defaultTaxId: null,
      rentalProducts: [
        // {
        //   facility_rentals: [
        //     {
        //       assignedFacility: [],
        //       timing_template: null,
        //     },
        //   ],
        //   advancedConfigurations: false,
        //   weekdays: [],
        //   seasonal_pricing: [
        //     // {
        //     //   start_date:null,
        //     //   end_date:null,
        //     //   pre_tax_price:null,
        //     //   total_price:null,
        //     // }
        //   ],
        //   sunSystemAccountCode: null,
        //   sunSystemGLCode: null,
        //   //  sunSystemAnalysisCode0: null,
        //   sunSystemAnalysisCode1: null,
        //   sunSystemAnalysisCode2: null,
        //   sunSystemAnalysisCode7: null,
        //   inventory_enable: null,
        //   inventoryPeriod: 'T',
        //   inventory_quantity: null,
        // },
      ],
      packageTypes: [
        { type: 'I', name: 'Individual' },
        { type: 'C', name: 'Couple' },
        { type: 'G', name: 'Group' },
      ],
      radioGroupRules: [
        // Define validation rules
        (v) => !!v || 'Ticket type is required', // Required rule
        // Add additional custom rules as needed
      ],
      confirmModel: { id: null, title: null, description: null },
      viewImagePopupDialog: false,
      deletedProducts: [],
      deletedSeasonalPricing: [],
      deletedFacilityAssignment: [],
      configurations: {},
      duration: [],
      venueServiceId: null,
      timingDialog: false,
      currentImage: {},
      isTicket: 0,
      membership_ids: [],
      serviceMemberships: [],
      taskNames: constants.TASK_NAMES,
      glCodes: constants.GL_CODES,
      projectNumber: null,
      transactionType: null,
      isEnableDctErp: false,
      isEnableSunSystem: false,
      isEnableOpenDated: false,
      venueId: null,
      showFilters: false,
      salesChannels: [
        'Qube',
        'B2B',
        'B2G',
        'B2E',
        'Scanner',
        'Kiosk',
      ],
      addTicketModal: false,
      selectedProduct: {}
    }
  },
  async mounted () {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch('loadVenueServices').then((res) => {
        if (res.length) {
          this.checkVenueService()
        }
      })
    } else {
      this.checkVenueService()
    }
    if (this.$store.getters.getTaxTypes.status == false) {
      this.$store.dispatch('loadTaxTypes')
    }
    if (this.$store.getters.getWeekdays.status == false) {
      this.$store.dispatch('loadWeekdays')
    }
    if (this.$store.getters.venueInfo) {
      if (this.$store.getters.venueInfo.enable_dct_erp) {
        this.isEnableDctErp = true
        if (this.$store.getters.venueInfo.dct_erp_configuration) {
          this.projectNumber =
              this.$store.getters.venueInfo.dct_erp_configuration.project_no
          this.transactionType =
              this.$store.getters.venueInfo.dct_erp_configuration.transaction_type
        }
      } else {
        this.isEnableDctErp = false
      }
      if (this.$store.getters.venueInfo.enable_open_dated_tickets) {
        this.isEnableOpenDated = true
      } else {
        this.isEnableOpenDated = false
      }

      if (this.$store.getters.venueInfo.enable_sun_system) {
        this.isEnableSunSystem = true
      } else {
        this.isEnableSunSystem = false
      }
    }
    // this.venueId = this.$store.getters.userInfo.venue_id;
    this.loadServiceMemberships()
    this.getFacilities()
    await this.getTimingTemplates()
  },
  computed: {
    weekdays () {
      return this.$store.getters.getWeekdays.data
    },
    hasChildVenues () {
      return this.$store.getters.venueInfo.sub_venues
    },
    venueServices () {
      return this.hasChildVenues
          ? this.venueId == this.$store.getters.userInfo.venue_id
              ? this.$store.getters.getSportsService.filter(
                  (service) => service.name != 'POS'
              )
              : this.$store.getters
                  .getSubVenueServicesByVenueId(this.venueId)
                  .map((ele) => {
                    ele.venue_service_id = ele.id
                    return ele
                  })
                  .filter((service) => service.name != 'POS')
          : this.$store.getters.getSportsService.filter(
              (service) => service.name != 'POS'
          )
    },
    taxTypes () {
      return this.$store.getters.getTaxTypes.data
    },
    memberships () {
      return this.serviceMemberships
    },

    filteredProducts () {
      if (!this.showFilters) {
        return this.rentalProducts
      }
      let products = this.rentalProducts
      if (this.filters.facilities.length > 0) {
        products = products.filter(product => {
          return product.facility_rentals?.some(facility_rental =>
              facility_rental.assignedFacility.some(facility =>
                  this.filters.facilities.includes(facility)
              )
          )
        })
      }
      if (this.filters.types.length > 0 && this.isTicket === 1) {
        products = products.filter(product => {
          return this.filters.types.includes(product.ticket_type)
        })
      }

      if (this.filters.salesChannels.length > 0 && this.isTicket === 1) {
        const channelMapping = {
          Qube: 'enable_online_booking',
          B2B: 'enable_b2b_sales',
          B2G: 'enable_b2g_sales',
          B2E: 'enable_b2e_sales',
          Scanner: 'enable_scanner_product',
          Kiosk: 'enable_kiosk_sales',
        }

        products = products.filter(product =>
            this.filters.salesChannels.some(channel =>
                product[channelMapping[channel]]
            )
        )
      }

      if (this.filters.search) {
        products = products.filter(product => {
          return product.name.toString().toLowerCase().indexOf(this.filters.search.toLowerCase()) > -1
        })
      }
      return products
    }
  },
  methods: {
    addTicket () {
      this.selectedProduct = {}
      this.addTicketModal = true
      this.$forceUpdate()
    },
    editTicket (ticket) {
      this.selectedProduct = ticket
      this.addTicketModal = true
      this.$forceUpdate()
    },
    venueChange () {
      this.rentalProducts.length = 0

      // if(this.venueId != this.$store.getters.userInfo.venue_id){
      //   this.$store.dispatch('loadSubVenueServices',this.venueId);
      // }
      //
      if (this.venueServices.length > 0) {
        this.venueServiceId = this.venueServices[0].id
        this.serviceChange()
      } else {
        this.venueServiceId = 0
      }
    },
    getSalesChannels (product) {
      let channels = []

      if (product.enable_online_booking) {
        channels.push('Qube')
      }
      if (product.enable_b2b_sales) {
        channels.push('B2B')
      }
      if (product.enable_b2g_sales) {
        channels.push('B2G')
      }
      if (product.enable_b2e_sales) {
        channels.push('B2E')
      }
      if (product.enable_scanner_product) {
        channels.push('Scanner')
      }
      if (product.enable_kiosk_sales) {
        channels.push('Kiosk')
      }
      if (channels.length > 0) {
        return channels.join(', ')
      }
      return 'N/A'
    },
    async getTimingTemplates () {
      await this.$store.dispatch(
          'loadTimingTemplatesByVenueServiceId',
          this.venueServiceId
      )
      this.$forceUpdate()
    },
    getProductFacilitiesName (product) {
      const fac = []
      product.facility_rentals?.forEach((ele) => {
        if (!ele.assignedFacility) return;
        ele.assignedFacility.forEach((facilityId) => {
          fac.push(facilityId)
        })
      })
      if (!fac || fac.length === 0) {
        return '-'
      }
      return this.facilities.filter((facility) => {
        return fac.includes(facility.id)
      }).map(facility => facility.name).join(',')
    },
    getProductDependenciesName (product) {
      if (product.enable_dependency === 0) {
        return '-'
      }

      const names = []

      if (product.dependencies && product.dependencies.length > 0) {
        product.dependencies.forEach((dep) => {
          dep.ticket_types.forEach((dependentProductId) => {
            const dependentProduct = this.rentalProducts.find(rp => rp.product_id === dependentProductId)
            if (dependentProduct) {
              names.push(dependentProduct.name)
            }
          })
        })
      }

      if (names.length === 0) {
        return '-'
      }

      return names.join(', ')

    },
    getProductType (product) {
      return this.packageTypes.find(ele => ele.type === product.ticket_type)?.name
    },
    formatDuration (durationInMinutes) {
      if (!durationInMinutes) {
        return '-'
      }

      const hours = Math.floor(durationInMinutes / 60)
      const minutes = durationInMinutes % 60

      let text = ''

      if (hours > 0) {
        text += `${hours} Hour${hours > 1 ? 's' : ''}`
      }

      if (minutes > 0) {
        if (text) text += ' '
        text += `${minutes} Mins`
      }

      return text || '-'
    },
    getFacilities () {
      let venueServiceId = this.venueServiceId
      this.$http
          .get(`venues/facilities/short?venue_service_id=${venueServiceId}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.facilities = response.data.data
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    async serviceChange () {
      await this.loadServiceMemberships()
      await this.getRentalProducts()
      await this.initializeFacilityServices()
      await this.getFacilities()
      await this.getTimingTemplates()
    },
    checkVenueService () {
      if (this.$route.params.data != null) {
        let data = JSON.parse(atob(this.$route.params.data))
        this.venueServiceId = data.venue_service_id
        this.isTicket = data.is_ticket
        this.venueId = data.venue_id ?? this.$store.getters.userInfo.venue_id
      } else {
        if (this.$store.getters.getSportsService.length) {
          this.venueServiceId =
              this.$store.getters.getSportsService[0].venue_service_id
          this.venueId = this.$store.getters.userInfo.venue_id
        }
      }
      setTimeout(() => {
        this.getRentalProducts()
        this.initializeFacilityServices()
      }, 10)
    },
    initializeFacilityServices () {
      if (!this.$store.getters.getConfigurationStatus(this.venueServiceId)) {
        this.$store
            .dispatch('loadConfigurationsByVenueServiceId', this.venueServiceId)
            .then((response) => {
              if (response.status == 200) {
                let data = this.$store.getters.getConfigurationByVenueServiceId(
                    this.venueServiceId
                )
                this.configurations = data
                this.getMinBookingTimes()
                this.defaultTaxId = data.default_tax_id
                if (
                    this.defaultTaxId &&
                    this.rentalProducts[0] &&
                    !this.rentalProducts[0]?.tax_type_id
                )
                {
                  this.rentalProducts[0].tax_type_id = this.defaultTaxId
                }
              }
            })
      } else {
        let data = this.$store.getters.getConfigurationByVenueServiceId(
            this.venueServiceId
        )
        this.configurations = data
        this.getMinBookingTimes()
        this.defaultTaxId = data.default_tax_id
        if (
            this.defaultTaxId &&
            this.rentalProducts[0] &&
            !this.rentalProducts[0].tax_type_id
        )
        {
          this.rentalProducts[0].tax_type_id = this.defaultTaxId
        }
      }
    },

    getIcon (index) {
      let field = 'weekdays_value'
      let icon = 'mdi-checkbox-blank-outline'
      if (!this.rentalProducts[index][field]) {
        this.rentalProducts[index]['weekdays_value'] = []
      }
      if (
          this.rentalProducts[index][field] &&
          this.rentalProducts[index][field].length == this.weekdays.length
      )
      {
        icon = 'mdi-close-box'
      }
      if (
          this.rentalProducts[index][field] &&
          this.rentalProducts[index][field].length > 0 &&
          this.rentalProducts[index][field].length != this.weekdays.length
      )
        icon = 'mdi-minus-box'

      return icon
    },
    getRentalProducts () {
      this.showLoader('Loading')
      this.$store
          .dispatch('loadRentalProductsByVenueServiceId', this.venueServiceId)
          .then((response) => {
            if (response.status == 200) {
              const data = response.data.data
              if (data && data.length) {
                let filter = this.isTicket == 0 ? 'Rental' : 'Ticket'

                this.rentalProducts = data.filter(
                    (x) => x.category_name == filter
                )
                this.rentalProducts.map((element) => {
                  element.advancedConfigurations = false
                  if (element.is_membership_only == 1) {
                    element.membership_ids = element.product_memberships.map(
                        (x) => x.membership_id
                    )
                  } else {
                    element.membership_ids = []
                  }

                  let p = this.rentalProducts[0]
                  if (p.project_no) {
                    this.projectNumber = p.project_no
                  }
                  if (p.transaction_type) {
                    this.transactionType = p.transaction_type
                  }
                  if (element.dependencies) {
                    element.dependencies = [...element.dependencies, ...(element.depended_by || [])]
                    element.dependencies = element.dependencies.reduce((acc, dep) => {
                      let periodGroup = acc.find(group => group.period === dep.period)

                      if (!periodGroup) {
                        periodGroup = {
                          ticket_types: [],
                          period: dep.period
                        }
                        acc.push(periodGroup)
                      }
                      let productId = element.id === dep.product_id ? dep.dependent_id : dep.product_id
                      periodGroup.ticket_types.push(productId)

                      return acc
                    }, [])
                  }

                  // if (element.ticket_type == null) {
                  //   element.ticket_type = "I";
                  // }

                  // Group facility_rentals by timing_template_id
                  if (
                      element.facility_rentals &&
                      element.facility_rentals.length
                  )
                  {
                    const groupedFacilities = Object.values(
                        element.facility_rentals.reduce((acc, rental) => {
                          const timingTemplateId = rental.timing_template_id

                          if (!acc[timingTemplateId]) {
                            acc[timingTemplateId] = {
                              assignedFacility: [],
                              facilityRentalId: [],
                              timing_template: timingTemplateId,
                              assignedFacilities: [],
                              timing_templates: [],
                            }
                          }

                          // Add unique facility_id to facilities array
                          if (
                              !acc[timingTemplateId].assignedFacility.includes(
                                  rental.facility_id
                              )
                          )
                          {
                            acc[timingTemplateId].assignedFacility.push(
                                rental.facility_id
                            )
                          }
                          // Add unique facility rental id to facilities array
                          if (
                              !acc[timingTemplateId].facilityRentalId.includes(
                                  rental.id
                              )
                          )
                          {
                            acc[timingTemplateId].facilityRentalId.push(rental.id)
                          }

                          if (element.assigned_facilities && Array.isArray(element.assigned_facilities)) {
                            acc[timingTemplateId].assignedFacilities = Array.from(new Set([
                              ...acc[timingTemplateId].assignedFacilities,
                              ...element.assigned_facilities,
                              rental.facility_id,
                            ]));
                          } else {
                            acc[timingTemplateId].assignedFacilities = Array.from(new Set([
                              ...acc[timingTemplateId].assignedFacilities,
                              rental.facility_id,
                            ]));
                          }

                          // Merge timing_templates if exists
                          if (element.timing_templates && Array.isArray(element.timing_templates)) {
                            acc[timingTemplateId].timing_templates = Array.from(new Set([
                              ...acc[timingTemplateId].timing_templates,
                              ...element.timing_templates,
                              rental.timing_template_id,
                            ]));
                          } else {
                            acc[timingTemplateId].timing_templates = Array.from(new Set([
                              ...acc[timingTemplateId].timing_templates,
                              rental.timing_template_id,
                            ]));
                          }

                          return acc
                        }, {})
                    )

                    // Replace facility_rentals with the grouped format
                    element.facility_rentals = groupedFacilities
                  }

                  if (
                      element.sun_system_jv_configuration &&
                      element.sun_system_jv_configuration != null
                  )
                  {
                    if (
                        element.sun_system_jv_configuration.sun_system_account_code
                    )
                      element.sunSystemAccountCode =
                          element.sun_system_jv_configuration.sun_system_account_code
                    if (element.sun_system_jv_configuration.sun_system_gl_code)
                      element.sunSystemGLCode =
                          element.sun_system_jv_configuration.sun_system_gl_code
                    // if (
                    //   element.sun_system_jv_configuration
                    //     .sun_system_analysis_code_0
                    // )
                    //   element.sunSystemAnalysisCode0 =
                    //     element.sun_system_jv_configuration.sun_system_analysis_code_0;
                    if (
                        element.sun_system_jv_configuration
                            .sun_system_analysis_code_1
                    )
                      element.sunSystemAnalysisCode1 =
                          element.sun_system_jv_configuration.sun_system_analysis_code_1

                    if (
                        element.sun_system_jv_configuration
                            .sun_system_analysis_code_2
                    )
                      element.sunSystemAnalysisCode2 =
                          element.sun_system_jv_configuration.sun_system_analysis_code_2
                    if (
                        element.sun_system_jv_configuration
                            .sun_system_analysis_code_7
                    )
                      element.sunSystemAnalysisCode7 =
                          element.sun_system_jv_configuration.sun_system_analysis_code_7
                  }

                  if (element.inventory && element.inventory != null) {
                    if (element.inventory.inventory_period)
                      element.inventoryPeriod =
                          element.inventory.inventory_period
                    if (element.inventory.quantity)
                      element.inventory_quantity = element.inventory.quantity
                  }
                  return element
                })
              }
              // if (this.rentalProducts.length == 0) {
              //   this.rentalProducts = [
              //     { tax_type_id: this.defaultTaxId, weekdays_value: [] },
              //   ];
              // }
              if (data.length == 0) {
                this.rentalProducts = [
                  // {
                  //   tax_type_id: this.defaultTaxId,
                  //   weekdays_value: [],
                  //   seasonal_pricing: [],
                  // },
                ]
              }
              setTimeout(() => {
                this.$refs.form?.resetValidation()
              })
              this.dialog = true
              this.$forceUpdate()
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    confirmDuplicateProduct (product) {
      this.confirmModel = {
        id: product.id,
        title: 'Do you want to duplicate this product?',
        description:
            'By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?',
        type: 'duplicate',
      }
    },
    duplicateProduct (product) {
      this.showLoader('Duplicating...')
      this.$http.post(`venues/facilities/categories/rentals/${this.venueServiceId}/duplicate`, {
        product_id: product.id,
        per_capacity: this.isTicket
      })
          .then((response) => {
            if (response.status == 200) {
              this.getRentalProducts()
              this.hideLoader()
              this.showSuccess('Product duplicated successfully')
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    confirmDeleteRentals (productId) {
      this.confirmModel = {
        id: productId,
        title: 'Do you want to delete this product?',
        description:
            'By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?',
        type: 'delete',
      }
    },
    deleteProduct (rental_product_id) {
      this.showLoader('Deleting...')
      this.$http.delete(`venues/facilities/categories/rentals/${rental_product_id}`)
          .then((response) => {
            if (response.status == 200) {
              this.showSuccess(`${this.isTicket === 1 ? 'Ticket' : 'Time rate'} deleted successfully`)
              this.getRentalProducts()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    confirmActions (data) {
      if (data.type == 'delete') {
        let product = this.rentalProducts.find((product) => product.id === data.id)
        if (product?.id) {
          this.deleteProduct(product.rental_product_id)
        }
      }
      if (data.type == 'duplicate') {
        this.duplicateProduct({ id: data.id })
      }
      this.confirmModel.id = null
    },
    getMinBookingTimes () {
      this.duration = []
      const timeIncrement = this.configurations.time_increment
      if (timeIncrement) {
        let hour = 0
        let index = 0
        while (hour < 24) {
          index = index + 1
          let time = timeIncrement * index
          hour = parseInt(time / 60)
          let min =
              time / 60 - hour > 0 ? Math.round((time / 60 - hour) * 60) : 0
          let text = hour > 0 ? `${hour} Hour ` : ''
          text += min > 0 ? `${min} Min` : ''
          this.duration.push({ value: time, text: text })
        }
      }
      if (this.configurations.per_capacity == 0) {
        const hasObject = this.duration.some(
            (obj) => obj.value === 60 && obj.text === 'Full day'
        )

        // If the object doesn't exist, add it to the array
        if (!hasObject) {
          this.duration.push({ value: 2440, text: 'Full day' })
        }
      }
      if (this.configurations.per_capacity == 1) {
        const hasObject = this.duration.some(
            (obj) => obj.value === 1 && obj.text === 'No Duration'
        )

        // If the object doesn't exist, add it to the array
        if (!hasObject) {
          this.duration.unshift({ value: 1, text: 'No Duration' })
        }
      }
    },
    loadServiceMemberships () {
      if (
          this.checkWritePermission(this.$modules.memberships.management.slug)
      )
      {
        let url = `&venue_service_ids[0]=${this.venueServiceId}`
        this.$http
            .get(`venues/memberships?page=1&status_id=1${url}`)
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                this.hideLoader()
                //this.membershipList = response.data.data;
                this.serviceMemberships = response.data.data.map((item) => {
                  return { membership_id: item.id, name: item.name }
                })
              }
            })
            .catch((error) => {
              this.errorChecker(error)
            })
      } else {
        this.serviceMemberships = []
      }
    },
  },
  watch: {
    venueServiceId (val) {
      if (this.$route.params.data != null) {
        let data = JSON.parse(atob(this.$route.params.data))
        if (val) {
          let vs = this.venueServices.find((vs) => vs.id === val)
          if (vs && vs.per_capacity == 0) {
            this.isTicket = 0
          } else {
            this.isTicket = 1
          }
        } else {
          this.isTicket = data.is_ticket
        }
      }
    },
    // watch route data param
    '$route.params.data': {
      handler: function () {
        this.checkVenueService()
      },
      immediate: true,
    },
  },
}
</script>
<style lang="scss" scoped>
.image-placeholder {
  height: 120px;
  width: 120px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: #DFDFDF;
  color: #848484;
  border-radius: 16px;
  font-weight: 600;
}
</style>
<style scoped>
span.sales_heading {
  margin-left: 1%;
  margin-top: 1%;
  line-height: 20px;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.8);
  font-size: 16px;
}

span.seasonal_heading {
  margin-left: 1%;
  margin-top: 1%;
  line-height: 20px;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.8);
  font-size: 16px;
}

span.empty-seasonal-price {
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  margin-bottom: 10px;
}

.tab_is_active {
  color: #112a46;
  font-weight: 600;

  svg {
    opacity: 1 !important;
  }
}

.q-tab-nav {
  svg {
    fill: none !important;
    stroke: black !important;
    opacity: 0.4;
    min-width: 20px;
  }
}

.ribbon {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #3f51b5; /* Indigo or any primary */
  color: white;
  padding: 4px 12px;
  font-size: 13px;
  font-weight: 500;
  border-bottom-right-radius: 8px;
  border-top-left-radius: 8px;
  z-index: 7;
}
</style>

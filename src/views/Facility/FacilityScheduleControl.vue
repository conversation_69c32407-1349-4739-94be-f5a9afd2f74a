<template>
  <v-container fluid>
    <FacilitiesTabs
      :configurations="configurations"
      :venue-service-id="venueServiceId"
      :venue-services="venueServices"
      @serviceChange="goToTimings"
      @update:venueServiceId="(value) => (venueServiceId = value)"
      :venue-id="venueId"
      @venueChange="venueChange"
      @update:venueId="(value) => (venueId = value)"
    />
    <v-divider
      class="mt-4"
      style="border-color: rgba(17, 42, 70, 0.14) !important"
    />

    <v-container fluid style="max-width: 85%">
      <v-form ref="conf_form">
        <div class="text-base font-semibold black-text ml-1 mt-10">
          Schedule Configuration
        </div>
        <v-card class="mb-4 rounded-3 shadow-0 bordered">
          <v-card-text>
            <v-row
              v-for="(control, cIndex) in teeControls"
              dense
              :key="`cc_${cIndex}`"
            >
              <v-col lg="2">
                <label>Type *</label>
                <v-autocomplete
                  v-model="control.type"
                  outlined
                  item-value="value"
                  item-text="text"
                  :items="controlTypes"
                  class="q-autocomplete shadow-0"
                  hide-details
                  dense
                  :return-object="false"
                >
                  <template v-slot:selection="{ item, index }">
                    <span v-if="index === 0" class="text-elepsis">
                      {{ item.text }}</span
                    >
                    <span v-else-if="index === 1">, ..</span>
                  </template>
                </v-autocomplete>
              </v-col>

              <v-col lg="2">
                <label>Facilities * </label>
                <v-autocomplete
                  v-model="control.facilities"
                  outlined
                  item-value="id"
                  item-text="name"
                  :items="facilities"
                  multiple
                  class="q-autocomplete shadow-0"
                  hide-details
                  dense
                  :return-object="false"
                >
                  <template v-slot:selection="{ item, index }">
                    <span v-if="index == 0" class="text-elepsis">{{
                      item.name
                    }}</span>
                    <span v-if="index == 1">, ..</span>
                  </template>
                </v-autocomplete>
              </v-col>

              <v-col lg="2">
                <label>Start Date *</label>
                <date-field
                  v-model="control.date"
                  :rules="[(v) => !!v || 'Start Date is required']"
                  class-name="q-text-field shadow-0"
                  :dense="true"
                  :hide-details="true"
                  label=""
                >
                </date-field>
              </v-col>
              <v-col lg="2" :style="{ maxWidth: '12.5%' }">
                <label>Start Time *</label>
                <v-select
                  :items="timings.slice(0, timings.length - 1)"
                  item-text="text"
                  item-value="value"
                  :rules="[(v) => !!v || 'From time is required']"
                  v-model="control.start_time"
                  required
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details
                ></v-select>
              </v-col>
              <v-col lg="2">
                <label>End Date *</label>
                <date-field
                  v-model="control.end_date"
                  :rules="[(v) => !!v || 'End Date is required']"
                  class-name="q-text-field shadow-0"
                  :dense="true"
                  :hide-details="true"
                  label=""
                >
                </date-field>
              </v-col>

              <v-col lg="2" :style="{ maxWidth: '12.5%' }">
                <label>End Time *</label>
                <v-select
                  :items="timings.slice(1)"
                  item-text="text"
                  item-value="value"
                  v-model="control.end_time"
                  :rules="[(v) => !!v || 'To time is required']"
                  required
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details
                ></v-select>
              </v-col>
              <v-col md="1">
                <v-tooltip bottom >
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      v-if="checkWritePermission($modules.facility.control.slug)"
                      v-bind="attrs"
                      v-on="on"
                      class="del_btn mt-6"
                      color="red"
                      @click="deleteControl(cIndex)"
                      x-small
                      dark
                      fab
                    >
                      <v-icon small>mdi-delete</v-icon>
                    </v-btn>
                  </template>
                  Delete
                </v-tooltip>
              </v-col>
            </v-row>

            <v-row dense>
              <v-col cols="12">
                <p
                  class="text-base text-underline text-neon pointer w-fit"
                  v-if="checkWritePermission($modules.facility.control.slug)"
                  @click="addControl()"
                >
                  + Add new code
                </p>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            class="ma-2 white--text blue-color"
            height="45"
            text
            @click="saveControls"
            v-if="checkWritePermission($modules.facility.control.slug)"
            >Save
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-container>
    <confirm-model
      v-bind="confirmModel"
      @close="confirmModel.id = null"
      @confirm="confirmActions"
    ></confirm-model>
  </v-container>
</template>

<script>
import FacilitiesTabs from "@/views/Facility/FacilitiesTabs.vue";
import moment from "moment";

export default {
  name: "FacilityTiming",
  components: { FacilitiesTabs },
  computed: {
    hasChildVenues() {
      return this.$store.getters.venueInfo.sub_venues;
    },
    venueServices() {
      return this.hasChildVenues
        ? this.venueId == this.$store.getters.userInfo.venue_id
          ? this.$store.getters.getSportsService.filter(
              (service) => service.name != "POS"
            )
          : this.$store.getters
              .getSubVenueServicesByVenueId(this.venueId)
              .map((ele) => {
                ele.venue_service_id = ele.id;
                return ele;
              })
              .filter((service) => service.name != "POS")
        : this.$store.getters.getSportsService.filter(
            (service) => service.name != "POS"
          );
    },
  },
  data() {
    return {
      timingTemplates: [],
      intervalTypes: [
        { text: "Continuous", value: 0 },
        { text: "Interval", value: 1 },
      ],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      teeControls: [
        {
          type: "oo",
          date: null,
          start_time: null,
          end_date: null,
          end_time: null,
          interval_type: 0,
          facilities: [],
        },
      ],
      rentalProducts: [],
      deleted_timing_templates: [],
      defaultTiming: {
        name: null,
        weekdays: [],
        start_time: null,
        end_time: null,
      },
      timingData: [],
      timings: [],
      facilities: [],
      deleted_controls: [],
      controlTypes: [
        {
          text: "Operator Only",
          value: "oo",
        },
        {
          text: "Operator and Members",
          value: "om",
        },
      ],
      timeIncrement: null,
      configurations: {
        field_configurations: [],
        facilities: [],
        venue_service_documents: [],
        venue_service_configuration: { tnc_enable: 0, tnc: "" },
        deleted_service_documents: [],
        game_formations: [{ name: "" }],
        facility_surface: [{}],
        per_capacity: 0,
        qube_configuration: null,
      },
      venueServiceId: null,
      venueId: null,
    };
  },
  mounted() {
    if (this.$store.getters.getWeekdays.status == false) {
      this.$store.dispatch("loadWeekdays");
    }
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices").then((res) => {
        if (res.length) {
          this.checkVenueService();
        }
      });
    } else {
      this.checkVenueService();
    }
    this.generateTiming();
  },
  methods: {
    venueChange() {
      if (this.venueServices.length > 0) {
        this.venueServiceId = this.venueServices[0].id;
        this.goToTimings();
      } else {
        this.venueServiceId = 0;
      }
    },

    addControl() {
      this.teeControls.push({
        type: "oo",
        date: null,
        end_date: null,
        start_time: null,
        end_time: null,
        interval_type: 0,
        facilities: [],
      });
      if (this.$refs.cform) {
        this.$refs.cform.resetValidation();
      }
    },

    deleteControl(cIndex) {
      let pkg = this.teeControls[cIndex];
      if (pkg.type == null && pkg.date == null && pkg.end_date == null) {
        this.teeControls.splice(cIndex, 1);
        this.$forceUpdate();
      } else {
        this.confirmModel = {
          id: cIndex,
          title: "Do you want to delete this control?",
          description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
          type: "tee_control",
        };
      }
    },
    generateTiming() {
      let currentTime = moment("00:00:00", "HH:mm:ss");
      this.timings = [];
      this.timings.push({
        value: currentTime.format("HH:mm:ss"),
        text: currentTime.format("hh:mm a"),
      });
      let hour = 0;
      while (hour < 24) {
        currentTime = currentTime.add(this.timeIncrement, "minutes");
        // currentTime = currentTime.add(10, "minutes");
        let data = {
          value: currentTime.format("HH:mm:ss"),
          text: currentTime.format("hh:mm a"),
        };
        if (data.value == "00:00:00") {
          data.value = "23:59:59";
          this.timings.push(data);
          hour = 24;
        } else {
          this.timings.push(data);
          hour = currentTime.get("hours");
        }
      }
    },
    getFacilities() {
      let venueServiceId = this.venueServiceId;
      this.$http
        .get(
          `venues/facilities/short?type=ground&venue_service_id=${venueServiceId}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.facilities = response.data.data;
            console.log(this.facilities);
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    async initializeFacilityServices() {
      if (!this.$store.getters.getConfigurationStatus(this.venueServiceId)) {
        await this.$store
          .dispatch("loadConfigurationsByVenueServiceId", this.venueServiceId)
          .then((response) => {
            if (response.status == 200) {
              console.log(response);
              this.configurations =
                this.$store.getters.getConfigurationByVenueServiceId(
                  this.venueServiceId
                );
              console.log(this.configurations);
              this.timeIncrement = this.configurations.time_increment
                this.configurations.time_increment != null
                  ? this.configurations.time_increment
                  : 60;
            }
          });
      } else {
        this.configurations =
          this.$store.getters.getConfigurationByVenueServiceId(
            this.venueServiceId
          );
        this.timeIncrement =
          this.configurations.time_increment != null
            ? this.configurations.time_increment
            : 60;
      }
    },
    checkVenueService() {
      if (this.$route.params.data != null) {
        let data = JSON.parse(atob(this.$route.params.data));
        this.venueServiceId = data.venue_service_id;
        this.venueId = data.venue_id ?? this.$store.getters.userInfo.venue_id;
      } else {
        if (this.$store.getters.getSportsService.length) {
          this.venueServiceId =
            this.$store.getters.getSportsService[0].venue_service_id;
          this.venueId = this.$store.getters.userInfo.venue_id;
        }
      }
      setTimeout(async () => {
        this.showLoader('Loading')
        await this.initializeFacilityServices();
        await this.getFacilities();
        this.getTimings();
        await this.getControls();
        this.hideLoader()
      }, 10);
    },
    async getTimings() {
      this.generateTiming();
    },

    goToTimings() {
      if (!this.venueServiceId) {
        this.showError("Please add new service");
        return;
      }
      this.$router.push({
        name: "FacilityScheduleControl",
        params: {
          data: btoa(JSON.stringify({ venue_service_id: this.venueServiceId })),
        },
      });
      this.checkVenueService();
    },

    confirmActions(data) {
      let index = data.id;
      if (this.teeControls[index].date != null) {
        this.deleted_controls.push(this.teeControls[index].control_ids);
      }
      this.teeControls.splice(index, 1);
      this.confirmModel.id = null;
    },

    saveControls() {
      let data = {
        controls: this.teeControls,
      };
      if (this.deleted_controls.length > 0) {
        data.deleted_controls = this.deleted_controls;
      }
      this.showLoader();
      this.$http
        .post(`venues/facilities/schedule-controls/v2`, data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Controls saved");
            this.getControls();
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    icon() {
      if (this.weekdays.length == this.defaultTiming.weekdays.length)
        return "mdi-close-box";
      if (this.defaultTiming.weekdays.length > 0) return "mdi-minus-box";
      return "mdi-checkbox-blank-outline";
    },
    getControls() {
      this.$http
        .get(
          `venues/facilities/schedule-controls/v2?venueServiceId=${this.venueServiceId}`
        )
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            if (data.length) {
              this.teeControls = data;
            } else {
              this.teeControls = [
                {
                  type: "oo",
                  date: null,
                  start_time: null,
                  end_time: null,
                  facilities: [],
                },
              ];
            }
            this.deleted_controls = [];
            this.$forceUpdate();
            if (this.$refs.cform) {
              this.$refs.cform.resetValidation();
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    toggle() {
      this.$nextTick(() => {
        if (this.weekdays.length == this.defaultTiming.weekdays.length) {
          this.defaultTiming.weekdays = [];
        } else {
          this.defaultTiming.weekdays = this.weekdays.map(
            (item) => item.bit_value
          );
          this.$forceUpdate();
        }
      });
    },
  },
};
</script>

<style scoped>
.text-elepsis {
  white-space: nowrap; /* Prevents the text from wrapping */
  text-overflow: ellipsis; /* Adds ellipsis (...) to indicate text overflow */
  max-width: 85%;
  overflow: hidden;
}
</style>

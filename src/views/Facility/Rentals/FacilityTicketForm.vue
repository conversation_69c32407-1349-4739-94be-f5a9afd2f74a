<template>
  <v-dialog v-model="addTicketModal" persistent width="1080px" @input="$emit('close')">
    <v-form ref="form" lazy-validation>
      <v-card
          :style="cardStyle"
          class="mb-6 rounded shadow-0 bordered ticket-form"
      >
        <v-card-title class="w-full border-bottom pb-2 mb-4">
          <p class="mb-0">
            {{ this.product.id ? 'Update' : 'Add' }} Ticket
          </p>
          <v-btn class="no-hover-effect ml-auto px-0" elevation="0" min-width="32px" text @click="$emit('close')">
            <ModalCloseIcon/>
          </v-btn>
        </v-card-title>
        <v-card-text style="max-height: 600px" class="overflow-y-auto overflow-x-hidden">
          <v-row dense>
            <v-col cols="12">
              <div class="d-flex gap-x-5">
                <div
                    style="
                          min-width: 200px;
                          min-height: 200px;
                          aspect-ratio: 1/1;
                        "
                >
                  <image-uploader
                      ref="image_upload"
                      :defaultImage="'ground'"
                      :height="220"
                      :image_path="product.image"
                      message-text=""
                      text="Ticket Image"
                      @remove="
                            () => {
                              product.image = null;
                              product.data_url = null;
                            }
                          "
                      @result="
                            (dUrl) => {
                              product.data_url = dUrl;
                              $forceUpdate();
                            }
                          "
                      @upload="
                            (data) => {
                              product.file = data;
                            }
                          "
                  ></image-uploader>
                </div>
                <div>
                  <v-row class="mt-3" dense>
                    <v-col class="border-bottom" cols="12">
                      <h3 class="text-base font-bold black-text">
                        General Information:
                      </h3>
                    </v-col>

                    <v-col md="4">
                      <label for="">Product Name*</label>
                      <v-text-field
                          v-model="product.name"
                          :rules="[
                                (v) => !!v || 'Product name is required',
                              ]"
                          :readonly="isReadOnly"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          outlined
                          required
                      ></v-text-field>
                    </v-col>
                    <v-col md="2">
                      <label for="">Duration</label>
                      <v-select
                          v-model="product.duration"
                          :items="duration"
                          :readonly="isReadOnly"
                          :menu-props="{ bottom: true, offsetY: true }"
                          :rules="[(v) => !!v || 'Duration is required']"
                          background-color="#fff"
                          class="q-autocomplete shadow-0"
                          dense
                          hide-details="auto"
                          item-text="text"
                          item-value="value"
                          outlined
                          required
                      ></v-select>
                    </v-col>

                    <v-col md="2">
                      <label for="">Price (Pre Tax)*</label>
                      <v-text-field
                          v-model="product.pre_tax_price"
                          :prefix="currencyCode"
                          :rules="[
                                (v) => {
                                  if (v >= 0) {
                                    return true;
                                  }
                                  return 'Price is required';
                                },
                              ]"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          :readonly="isReadOnly"
                          hide-details="auto"
                          outlined
                          required
                          rows="2"
                          @change="
                                calculateTaxVariation($event, 'pre')
                              "
                      ></v-text-field>
                    </v-col>

                    <v-col md="2">
                      <label for="">Tax*</label>

                      <v-select
                          v-model="product.tax_type_id"
                          :items="taxTypes"
                          :menu-props="{ bottom: true, offsetY: true }"
                          :rules="[(v) => !!v || 'Tax type is required']"
                          background-color="#fff"
                          class="q-autocomplete shadow-0"
                          dense
                          :readonly="isReadOnly"
                          hide-details="auto"
                          item-text="text"
                          item-value="value"
                          outlined
                          @change="taxChange()"
                      ></v-select>
                    </v-col>

                    <v-col md="2">
                      <label for="">Price (Post Tax)*</label>
                      <v-text-field
                          v-model="product.total_price"
                          :prefix="currencyCode"
                          :rules="[
                                (v) => {
                                  if (v >= 0) {
                                    return true;
                                  }
                                  return 'Price is required';
                                },
                              ]"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          :readonly="isReadOnly"
                          hide-details="auto"
                          outlined
                          required
                          rows="2"
                          @change="
                                calculateTaxVariation($event, 'post')
                              "
                      ></v-text-field>
                    </v-col>
                    <v-col md="4">
                      <label for="">Product Name (Ar)</label>
                      <v-text-field
                          v-model="product.ar_name"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          :readonly="isReadOnly"
                          hide-details="auto"
                          outlined
                      ></v-text-field>
                    </v-col>

                    <v-col v-if="isTicket" md="4">
                      <label for="">Sales Channels</label>
                      <v-select
                          v-model="product.salesChannels"
                          :items="salesChannels"
                          :menu-props="{ bottom: true, offsetY: true }"
                          background-color="#fff"
                          class="q-autocomplete shadow-0"
                          dense
                          hide-details
                          multiple
                          outlined
                          placeholder="Sales Channels"
                      >
                        <template
                            v-if="product.salesChannels.length === salesChannels.length"
                            v-slot:selection="{ index }"
                        >
                          <span v-if="index === 0">All Types</span>
                        </template>
                        <template v-else v-slot:selection="{ item, index }">
                          <span v-if="index === 0">{{ item }}</span>
                          <span v-if="index === 1">, {{ item }}</span>
                          <span v-if="index === 2">, {{ item }}</span>
                          <span v-if="index === 3">, ...</span>
                        </template>

                        <template v-slot:prepend-item>
                          <v-list-item ripple @click="toggleChannels()">
                            <v-list-item-action>
                              <v-icon
                                  :color="product.salesChannels.length > 0? 'indigo darken-4': ''
                              "
                              >
                                {{ getChannelsIcon() }}
                              </v-icon>
                            </v-list-item-action>
                            <v-list-item-content>
                              <v-list-item-title>All Channels</v-list-item-title>
                            </v-list-item-content>
                          </v-list-item>
                        </template>
                      </v-select>
                    </v-col>

                    <v-col v-if="isTicket" :md="product.ticket_type === 'G'?3:4">
                      <label for="">Ticket Type</label>
                      <v-select
                          v-model="product.ticket_type"
                          :items="packageTypes"
                          :menu-props="{ bottom: true, offsetY: true }"
                          background-color="#fff"
                          class="q-autocomplete shadow-0"
                          dense
                          :readonly="isReadOnly"
                          hide-details
                          item-text="name"
                          item-value="type"
                          outlined
                          :rules="[(v) => !!v || 'Ticket type is required']"
                      />
                    </v-col>
                    <v-col v-if="product.ticket_type === 'G'" cols="1">
                      <label>Count*</label>
                      <v-text-field
                          v-model="product.participant_count"
                          :rules="[
                            (v) => !!v || 'Participant count',
                            (v) => !isNaN(v) || 'Participant count must be Number',
                           ]"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          :readonly="isReadOnly"
                          hide-details="auto"
                          outlined
                          required
                      ></v-text-field>
                    </v-col>
                    <v-col md="4" sm="6" v-if="hasChildVenues && checkCurrentVenue && checkWritePermission($modules.facility.child_facility.slug)">
                      <label>Venues *</label>
                      <v-autocomplete
                          :items="venues"
                          v-model="product.venues"
                          item-value="id"
                          item-text="name"
                          outlined
                          multiple
                          background-color="#fff"
                          class="q-autocomplete shadow-0"
                          hide-details="auto"
                          dense
                      >
                        <template v-slot:prepend-item>
                          <v-list-item ripple @click="toggleVenueSelect()">
                            <v-list-item-action>
                              <v-icon :color="product.venues.length > 0 ? 'teal darken-4' : ''">{{
                                  getVenueServiceIcon()
                                }}</v-icon>
                            </v-list-item-action>
                            <v-list-item-content>
                              <v-list-item-title>All</v-list-item-title>
                            </v-list-item-content>
                          </v-list-item>
                        </template>
                        <template
                            v-if="product.venues.length === venues.length"
                            v-slot:selection="{ index }"
                        >
                          <span v-if="index === 0">All Venues</span>
                        </template>
                        <template v-else v-slot:selection="{ item, index }">
                          <span v-if="index === 0">{{ item.name }}</span>
                          <span v-if="index === 1">, ...</span>
                        </template>
                      </v-autocomplete>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </v-col>
          </v-row>

          <v-expansion-panels>
            <v-row  class="mx-0 mt-4" dense>
              <v-col v-if="hasChildVenues && checkCurrentVenue && checkWritePermission($modules.facility.child_facility.slug)" :lg="checkWritePermission($modules.facility.seasonalPricing.slug)?6:12" md="12">
                <v-expansion-panel active-class="active">
                  <v-expansion-panel-header class="p-0 panel-header" hide-actions style="min-height: 44px !important;">
                    <template v-slot:default="{ open }">
                      <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom px-4 py-3">
                        <SvgIcon class="text-sm font-medium" text="Facility Assignments">
                          <template #icon>
                            <FacilityIcon :color="open?'#fff':'#0F2A4D'" height="18px" opacity="1" width="18px"/>
                          </template>
                        </SvgIcon>
                        <div class="d-flex justify-end align-start gap-x-2">
                          <MinusIcon v-if="open"/>
                          <PlusIcon v-else color="#0F2A4D"/>
                        </div>
                      </div>
                    </template>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="p-4">
                    <div
                        v-if="product.facility_rentals && product.facility_rentals.length > 0"
                        class="overflow-y-auto overflow-x-hidden pr-4 pt-2"
                        style="max-height: 200px"
                    >
                      <v-row v-for="(assignment, key) in product.facility_rentals"
                             :key="`${key}_facility_rentals`"
                             dense
                      >
                        <v-col md="5">
                          <label for="">Facility*</label>
                          <v-select
                              v-model="assignment.assignedFacilities"
                              :items="getAssignableFacilities"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :rules="[(v) => !!v[0] || 'Facility is required']"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="id"
                              multiple
                              outlined
                          >
                            <template v-slot:item="{ item, on, attrs }">
                              <v-list-item v-bind="attrs" v-on="on">
                                <v-list-item-action>
                                  <v-checkbox
                                      :input-value="assignment.assignedFacilities.includes(item.id)"
                                      @change="$event => $event ? assignment.assignedFacilities.push(item.id) : assignment.assignedFacilities.splice(assignment.assignedFacilities.indexOf(item.id), 1)"
                                      :ripple="false"
                                  />
                                </v-list-item-action>
                                <v-list-item-content>
                                  <v-list-item-title>
                                    {{ item.name }}
                                  </v-list-item-title>
                                  <v-list-item-subtitle class="text-caption">
                                    {{ item.service_name }} | {{ item.venue_name }}
                                  </v-list-item-subtitle>
                                </v-list-item-content>
                              </v-list-item>
                            </template>
                            <template
                                v-if="assignment.assignedFacilities.length === assignableFacilities.length"
                                v-slot:selection="{ index }"
                            >
                              <span v-if="index === 0">All Facilities</span>
                            </template>

                            <template v-else v-slot:selection="{ item, index }">
                              <span v-if="index === 0">{{ item.name }}</span>
                              <span v-if="index === 1">, ...</span>
                            </template>

                            <!-- 👇 Prepend 'All Facilities' option -->
                            <template v-slot:prepend-item>
                              <v-list-item ripple @click="toggleFacilities(key)">
                                <v-list-item-action>
                                  <v-icon
                                      :color="assignment.assignedFacilities.length === assignableFacilities.length
                                        ? 'indigo darken-4'
                                        : ''"
                                  >
                                    {{ assignment.assignedFacilities.length === assignableFacilities.length
                                      ? 'mdi-checkbox-marked'
                                      : 'mdi-checkbox-blank-outline' }}
                                  </v-icon>
                                </v-list-item-action>
                                <v-list-item-content>
                                  <v-list-item-title>All Facilities</v-list-item-title>
                                </v-list-item-content>
                              </v-list-item>
                              <v-divider class="mt-1 mb-2" />
                            </template>

                          </v-select>
                        </v-col>
                        <v-col md="5">
                          <label for="">Timing Template*</label>
                          <v-select
                              v-model="assignment.timing_templates"
                              :items="getAssignableTimingTemplates"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :rules="[(v) => !!v || 'Timing template is required']"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="id"
                              outlined
                              multiple
                              required
                          >
                            <template v-slot:item="{ item, on, attrs }">
                              <v-list-item v-bind="attrs" v-on="on">
                                <v-list-item-action>
                                  <v-checkbox
                                      :input-value="assignment.timing_templates.includes(item.id)"
                                      @change="$event => $event ? assignment.timing_templates.push(item.id) : assignment.timing_templates.splice(assignment.timing_templates.indexOf(item.id), 1)"
                                      :ripple="false"
                                  />
                                </v-list-item-action>
                                <v-list-item-content>
                                  <v-list-item-title>
                                    {{ item.name }}
                                  </v-list-item-title>
                                  <v-list-item-subtitle class="text-caption">
                                    {{ item.service_name }} | {{ item.venue_name }}
                                  </v-list-item-subtitle>
                                </v-list-item-content>
                              </v-list-item>
                            </template>
                            <template
                                v-if="assignment.timing_templates.length === assignableTimingTemplates.length"
                                v-slot:selection="{ index }"
                            >
                              <span v-if="index === 0">All Timing templates</span>
                            </template>

                            <template v-else v-slot:selection="{ item, index }">
                              <span v-if="index === 0">{{ item.name }}</span>
                              <span v-if="index === 1">, ...</span>
                            </template>

                            <!-- 👇 Prepend 'All Facilities' option -->
                            <template v-slot:prepend-item>
                              <v-list-item ripple @click="toggleTimingTemplates(key)">
                                <v-list-item-action>
                                  <v-icon
                                      :color="assignment.timing_templates.length === assignableTimingTemplates.length
                                        ? 'indigo darken-4'
                                        : ''"
                                  >
                                    {{ assignment.timing_templates.length === assignableTimingTemplates.length
                                      ? 'mdi-checkbox-marked'
                                      : 'mdi-checkbox-blank-outline' }}
                                  </v-icon>
                                </v-list-item-action>
                                <v-list-item-content>
                                  <v-list-item-title>All Timing templates</v-list-item-title>
                                </v-list-item-content>
                              </v-list-item>
                              <v-divider class="mt-1 mb-2" />
                            </template>

                          </v-select>
                        </v-col>
                        <v-col class="d-flex justify-center" md="2">
                          <v-btn
                              class="mt-6"
                              color="red"
                              dark
                              fab
                              icon
                              x-small
                              @click="deleteFacilityAssignment(key)"
                          >
                            <DeleteIcon/>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </div>
                    <div v-else class="text-center font-semibold">
                      No facilities assigned.
                    </div>
                    <div class="mt-4">
                      <button
                          class="text-neon text-base pointer font-medium m-0 p-0 text-underline w-fit"
                          type="button"
                          @click="addFacilityAssignment()"
                      >
                        + Add
                      </button>
                    </div>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-col>
              <v-col v-else :lg="checkWritePermission($modules.facility.seasonalPricing.slug)?6:12" md="12">
                <v-expansion-panel active-class="active">
                  <v-expansion-panel-header class="p-0 panel-header" hide-actions style="min-height: 44px !important;">
                    <template v-slot:default="{ open }">
                      <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom px-4 py-3">
                        <SvgIcon class="text-sm font-medium" text="Facility Assignment">
                          <template #icon>
                            <FacilityIcon :color="open?'#fff':'#0F2A4D'" height="18px" opacity="1" width="18px"/>
                          </template>
                        </SvgIcon>
                        <div class="d-flex justify-end align-start gap-x-2">
                          <MinusIcon v-if="open"/>
                          <PlusIcon v-else color="#0F2A4D"/>
                        </div>
                      </div>
                    </template>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="p-4">
                    <div
                        v-if="product.facility_rentals && product.facility_rentals.length > 0"
                        class="overflow-y-auto overflow-x-hidden pr-4 pt-2"
                        style="max-height: 200px"
                    >
                      <v-row v-for="(assignment, key) in product.facility_rentals"
                             :key="`${key}_facility_rentals`"
                             dense
                      >
                        <v-col md="5">
                          <label for="">Facility*</label>
                          <v-select
                              v-model="assignment.assignedFacility"
                              :items="availableFacilities"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :rules="[(v) => !!v[0] || 'Facility is required']"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="id"
                              multiple
                              outlined
                          ></v-select>
                        </v-col>
                        <v-col md="5">
                          <label for="">Timing Template*</label>
                          <v-select
                              v-model="assignment.timing_template"
                              :items="timingTemplates()"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :rules="[(v) => !!v || 'Timing template is required']"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="id"
                              outlined
                              required
                          ></v-select>
                        </v-col>
                        <v-col class="d-flex justify-center" md="2">
                          <v-btn
                              class="mt-6"
                              color="red"
                              dark
                              fab
                              icon
                              x-small
                              @click="deleteFacilityAssignment(key)"
                          >
                            <DeleteIcon/>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </div>
                    <div v-else class="text-center font-semibold">
                      No facility assigned.
                    </div>
                    <div class="mt-4">
                      <button
                          class="text-neon text-base pointer font-medium m-0 p-0 text-underline w-fit"
                          type="button"
                          @click="addFacilityAssignment()"
                      >
                        + Add
                      </button>
                    </div>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-col>
              <v-col v-if="checkWritePermission($modules.facility.seasonalPricing.slug)" lg="6" md="12">
                <v-expansion-panel active-class="active">
                  <v-expansion-panel-header class="p-0 panel-header" hide-actions style="min-height: 44px !important;">
                    <template v-slot:default="{ open }">
                      <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom px-4 py-3">
                        <SvgIcon class="text-sm font-medium" text="Seasonal Pricing">
                          <template #icon>
                            <SeasonalPricingIcon :color="open?'#fff':'#0F2A4D'"/>
                          </template>
                        </SvgIcon>
                        <div class="d-flex justify-end align-start gap-x-2">
                          <MinusIcon v-if="open"/>
                          <PlusIcon v-else color="#0F2A4D"/>
                        </div>
                      </div>
                    </template>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="p-4">
                    <div
                        v-if="product.seasonal_pricing && product.seasonal_pricing.length > 0"
                        class="overflow-y-auto overflow-x-auto pr-4 pt-2"
                        style="max-height: 200px"
                    >
                      <div
                          v-for="(seasonalPricing, index1) in product.seasonal_pricing"
                          :key="index1"
                          class="bordered p-3 rounded-lg relative mb-4"
                      >
                        <v-row dense>
                          <v-col md="6">
                            <label for="">Start Date*</label>
                            <date-field
                                v-model="seasonalPricing.start_date"
                                :backFill="true"
                                :hide-details="true"
                                :rules="[(v) => !!v || 'Start date is required']"
                                class-name="q-text-field shadow-0"
                                dense
                                label=""
                            >
                            </date-field>
                          </v-col>
                          <v-col md="6">
                            <label for="">End Date*</label>
                            <date-field
                                v-model="seasonalPricing.end_date"
                                :hide-details="true"
                                :minDate="seasonalPricing.start_date"
                                :rules="[(v) => !!v || 'End date is required']"
                                class-name="q-text-field shadow-0"
                                dense
                                label=""
                            >
                            </date-field>
                          </v-col>
                          <v-col md="6">
                            <label for="">Price (Pre Tax)*</label>
                            <v-text-field
                                v-model="seasonalPricing.pre_tax_price"
                                :prefix="currencyCode"
                                :rules="[
                          (v) => {
                            if (v >= 0) {
                              return true;
                            }
                            return 'Price is required';
                          },
                        ]"
                                background-color="#fff"
                                class="q-text-field shadow-0"
                                dense
                                hide-details="auto"
                                outlined
                                required
                                rows="2"
                                @change="
                          calculateSeasonalPricingTaxVariation(
                            index1,
                            $event,
                            'pre'
                          )
                        "
                            ></v-text-field>
                          </v-col>
                          <v-col md="6">
                            <label for="">Price (Post Tax)*</label>

                            <v-text-field
                                v-model="seasonalPricing.total_price"
                                :prefix="currencyCode"
                                :rules="[
                          (v) => {
                            if (v >= 0) {
                              return true;
                            }
                            return 'Price is required';
                          },
                        ]"
                                background-color="#fff"
                                class="q-text-field shadow-0"
                                dense
                                hide-details="auto"
                                outlined
                                required
                                rows="2"
                                @change="
                          calculateSeasonalPricingTaxVariation(
                            index1,
                            $event,
                            'post'
                          )
                        "
                            ></v-text-field>
                          </v-col>
                        </v-row>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                                absolute
                                class="mt-6"
                                color="red"
                                dark
                                fab
                                icon
                                style="right: -10px;top:-35px"
                                v-bind="attrs"
                                x-small
                                @click="deleteSeasonalPricing(index1)"
                                v-on="on"
                            >
                              <DeleteIcon/>
                            </v-btn>
                          </template>
                          Delete
                        </v-tooltip>
                      </div>
                    </div>
                    <div v-else class="text-center font-semibold">
                      No Seasonal Pricing assigned.
                    </div>
                    <v-row class="mt-4" dense>
                      <v-col cols="12">
                        <p
                            class="text-neon text-base pointer font-medium m-0 p-0 text-underline w-fit"
                            @click="addSeasonalPricing()"
                        >
                          + Add
                        </p>
                      </v-col>
                    </v-row>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-col>
            </v-row>
            <v-expansion-panel active-class="active" class="mt-4">
              <v-expansion-panel-header class="p-0 panel-header" hide-actions style="min-height: 44px !important;">
                <template v-slot:default="{ open }">
                  <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom px-4 py-3">
                    <SvgIcon class="text-sm font-medium" text="Optional Configuration">
                      <template #icon>
                        <ConfigIcon :color="open?'#fff':'#0F2A4D'" height="18px" opacity="1" width="18px"/>
                      </template>
                    </SvgIcon>
                    <div class="d-flex justify-end align-start gap-x-2">
                      <MinusIcon v-if="open"/>
                      <PlusIcon v-else color="#0F2A4D"/>
                    </div>
                  </div>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content :key="this.product.id || 'configuration'" class="p-4">
                <div class="d-flex gap-x-5 gap-y-4 flex-wrap align-center">
                  <CustomSwitch
                      v-if="isEnableOpenDated && isTicket"
                      :false-value="0"
                      :model-value="product.is_open_dated"
                      :ripple="false"
                      :true-value="1"
                      class-name="d-flex flex-row-reverse"
                      label="Is Open Dated"
                      :readonly="isReadOnly"
                      @update:modelValue="(val)=>{
                        product.is_open_dated=val;
                      }"
                  />
                  <CustomSwitch
                      v-if="isTicket && checkWritePermission($modules.facility.ticket_dependency.slug)"
                      :false-value="0"
                      :model-value="product.enable_dependency"
                      :ripple="false"
                      :true-value="1"
                      class-name="d-flex flex-row-reverse"
                      label="Enable Dependency"
                      @update:modelValue="(val)=>{
                        product.enable_dependency=val;
                      }"
                  />
                  <CustomSwitch
                      v-if="isTicket"
                      :false-value="0"
                      :model-value="product.specific_day"
                      :ripple="false"
                      :true-value="1"
                      class-name="d-flex flex-row-reverse"
                      label="Specific Day"
                      @update:modelValue="(val)=>{
                        product.specific_day=val;
                      }"
                  />
                  <CustomSwitch
                      v-if="checkWritePermission($modules.facility.cashbackProducts.slug)"
                      :false-value="0"
                      :model-value="product.allow_cashback"
                      :ripple="false"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="Allow Cashback"
                      @update:modelValue="(val)=>{product.allow_cashback=val}"
                  />
                  <CustomSwitch
                      v-if="
                          isTicket &&
                          checkWritePermission(
                            $modules.memberships.management.slug
                          )
                        "
                      :false-value="0"
                      :model-value="product.is_membership_only"
                      :ripple="false"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="Member only"
                      @update:modelValue="(val)=>{product.is_membership_only=val}"
                  />
                  <CustomSwitch
                      :false-value="0"
                      :model-value="product.benefit_excluded"
                      :ripple="false"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="Exclude from promotions"
                      @update:modelValue="(val)=>{
                        product.benefit_excluded=val;
                      }"
                  />
                  <CustomSwitch
                      v-if="isTicket"
                      :false-value="0"
                      :model-value="product.show_end_time"
                      :ripple="false"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="Show End Time"
                      @update:modelValue="(val)=>{product.show_end_time=val}"
                  />
                  <CustomSwitch
                      v-if="isTicket && checkWritePermission($modules.facility.podProducts.slug)"
                      :false-value="0"
                      :model-value="product.is_pod"
                      :ripple="false"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="POD Product"
                      @update:modelValue="(val)=>{product.is_pod = val}"
                  />
                  <CustomSwitch
                      v-if="checkWritePermission($modules.facility.rental_product_inventory.slug) && isTicket"
                      :false-value="0"
                      :model-value="product.inventory_enable"
                      :ripple="false"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="Capacity"
                      @update:modelValue="(val)=>{product.inventory_enable=val}"
                  />
                  <CustomSwitch
                      v-if="checkWritePermission($modules.facility.ticketAdmission.slug) && isTicket"
                      :model-value="product.enable_admission"
                      :ripple="false"
                      :false-value="0"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="Admission"
                      @update:modelValue="(val)=>{product.enable_admission=val}"
                  />
                  <CustomSwitch
                      v-if="checkWritePermission($modules.facility.ticketApplicableVenues.slug) && isTicket"
                      :model-value="product.enable_applicable_venues"
                      :ripple="false"
                      :readonly="isReadOnly"
                      :false-value="0"
                      :true-value="1"
                      class="d-flex flex-row-reverse"
                      label="Applicable Venues"
                      @update:modelValue="(val)=>{product.enable_applicable_venues=val}"
                  />
                </div>
                <v-row class="mx-0 mt-4">
                  <v-col v-if="isTicket && product.enable_dependency == 1 && checkWritePermission($modules.facility.ticket_dependency.slug)" cols="12">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">
                        Dependency
                      </p>
                      <v-row dense class="px-3 pb-3">
                        <v-col cols="6" v-for="(dependency,index) in product.dependencies" :key="index">
                          <v-row class="mx-0" dense align="center">
                            <v-col cols="7">
                              <label for="">Ticket Type</label>
                              <v-select
                                  v-model="dependency.ticket_types"
                                  :items="dependencyTickets"
                                  :menu-props="{ bottom: true, offsetY: true }"
                                  :rules="[(v) => (v && v.length > 0) || 'Ticket(s) are required',]"
                                  background-color="#fff"
                                  class="q-text-field shadow-0 w-full"
                                  dense
                                  hide-details="auto"
                                  item-text="name"
                                  item-value="id"
                                  outlined
                                  validate-on-blur
                                  multiple
                              >
                                <template
                                    v-if="
                                          dependency.ticket_types &&
                                          dependency.ticket_types.length === dependencyTickets.length
                                        "
                                    v-slot:selection="{ index }"
                                >
                                  <span v-if="index === 0">All Tickets</span>
                                </template>
                                <template v-else v-slot:selection="{ item, index }">
                                  <span v-if="index === 0">{{ item.name }}</span>
                                  <span v-if="index === 1">...</span>
                                </template>
                              </v-select>
                            </v-col>
                            <v-col cols="4">
                              <label for="">Period</label>
                              <v-select
                                  v-model="dependency.period"
                                  :items="[
                                    { id: 'T', name: 'Time slot' },
                                    { id: 'D', name: 'Day' },
                                  ]"
                                  :menu-props="{ bottom: true, offsetY: true }"
                                  :rules="[(v) => (v && v.length > 0) || 'Period is required',]"
                                  background-color="#fff"
                                  class="q-text-field shadow-0"
                                  dense
                                  hide-details="auto"
                                  item-text="name"
                                  item-value="id"
                                  outlined
                                  style="width: 200px"
                                  validate-on-blur
                              >
                              </v-select>
                            </v-col>
                            <v-col cols="1">
                              <v-btn
                                  class="text-capitalize text-white px-0 mt-4"
                                  color="#fff"
                                  elevation="0"
                                  height="44px"
                                  type="button"
                                  width="32px"
                                  style="min-width: 32px !important;"
                                  @click="confirmRemoveDependency(index)"
                              >
                                <SvgIcon class="text-base font-medium text-red">
                                  <template #icon>
                                    <DeleteIcon stroke="#E50000"/>
                                  </template>
                                </SvgIcon>
                              </v-btn>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col cols="12">
                          <button
                              class="text-neon text-base pointer font-medium m-0 p-0 text-underline w-fit"
                              type="button"
                              @click="addTicketDependency"
                          >
                            + Add
                          </button>
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                  <v-col v-if="isEnableOpenDated && isTicket && product.is_open_dated == 1" cols="6">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">
                        Open dated product
                      </p>
                      <div class="px-3 pb-3">
                        <label for="">Expiry Days</label>
                        <v-text-field
                            v-model="product.expiry_days"
                            background-color="#fff"
                            class="q-text-field shadow-0"
                            dense
                            hide-details="auto"
                            outlined
                        />
                      </div>
                    </div>
                  </v-col>
                  <v-col v-if="product.specific_day === 1 && isTicket" cols="6">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">
                        Specific Day
                      </p>
                      <div class="px-3 pb-3">
                        <label for="">Days Applicable</label>
                        <v-select
                            v-model="product.weekdays_value"
                            :items="weekdays"
                            :menu-props="{ bottom: true, offsetY: true }"
                            :rules="[
                        (v) => v.length > 0 || 'Package days is required',
                      ]"
                            background-color="#fff"
                            class="q-text-field shadow-0"
                            dense
                            hide-details="auto"
                            item-text="name"
                            item-value="bit_value"
                            multiple
                            outlined
                            placeholder="Days Applicable"
                            style="width: 250px"
                            validate-on-blur
                            @change="rentalChange()"
                        >
                          <template
                              v-if="
                          product.weekdays_value &&
                          weekdays.length === product.weekdays_value.length
                        "
                              v-slot:selection="{ index }"
                          >
                            <span v-if="index === 0">All Days</span>
                          </template>
                          <template v-else v-slot:selection="{ item, index }">
                            <span v-if="index === 0">{{ item.name }}</span>
                            <span v-if="index === 1">{{ item.name }}</span>
                            <span v-if="index === 2">, ...</span>
                          </template>
                          <template v-slot:prepend-item>
                            <v-list-item ripple @click="toggle()">
                              <v-list-item-action>
                                <v-icon
                                    :color="
                                product.weekdays_value &&
                                product.weekdays_value.length > 0
                                  ? 'indigo darken-4'
                                  : ''
                              "
                                >{{ getIcon() }}
                                </v-icon
                                >
                              </v-list-item-action>
                              <v-list-item-content>
                                <v-list-item-title>All Days</v-list-item-title>
                              </v-list-item-content>
                            </v-list-item>
                          </template>
                        </v-select>
                      </div>
                    </div>
                  </v-col>
                  <v-col v-if="product.allow_cashback && checkWritePermission($modules.facility.cashbackProducts.slug)" cols="6">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">
                        Cashback
                      </p>
                      <div class="px-3 pb-3">
                        <label for="">Cashback</label>
                        <v-text-field
                            v-model="product.cashback_percentage"
                            :rules="[
                        (v) => {
                          if (v >= 0) {
                            return true;
                          }
                          return 'Cashback Percentage is required';
                        },
                      ]"
                            append-icon="mdi-percent"
                            background-color="#fff"
                            class="q-text-field shadow-0"
                            dense
                            hide-details="auto"
                            outlined
                            placeholder="Cashback"
                            required
                            style="width: 150px"
                        />
                      </div>
                    </div>

                  </v-col>
                  <v-col v-if="product.inventory_enable && checkWritePermission($modules.facility.rental_product_inventory.slug)" cols="6">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">
                        Capacity
                      </p>
                      <div class="px-3 pb-3 d-flex align-start gap-x-4">
                        <div>
                          <label for="">Period</label>
                          <v-select
                              v-model="product.inventoryPeriod"
                              :items="[
                                { id: 'T', name: 'Time slot' },
                                { id: 'D', name: 'Day' },
                              ]"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :rules="[
                                (v) =>
                                  (v && v.length > 0) || 'Inventory Period is required',
                              ]"
                              background-color="#fff"
                              class="q-text-field shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="id"
                              outlined
                              style="width: 200px"
                              validate-on-blur
                          >
                          </v-select>
                        </div>
                        <div>
                          <label for="">Capacity</label>
                          <v-text-field
                              v-model="product.inventory_quantity"
                              :rules="[
                                (v) => {
                                  if (v >= 0) {
                                    return true;
                                  }
                                  return 'Capacity is required';
                                },
                              ]"
                              background-color="#fff"
                              class="q-text-field shadow-0"
                              dense
                              hide-details="auto"
                              outlined
                              required
                              style="width: 130px"
                          ></v-text-field>
                        </div>
                      </div>
                    </div>
                  </v-col>
                  <v-col v-if="isTicket && checkWritePermission($modules.memberships.management.slug) && product.is_membership_only == 1" cols="6">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">
                        Memberships
                      </p>
                      <div class="px-3 pb-3">
                        <label for="">Memberships</label>
                        <v-select
                            v-model="product.membership_ids"
                            :items="memberships"
                            :menu-props="{ bottom: true, offsetY: true }"
                            :rules="[(v) => !!v[0] || 'Membership is required']"
                            background-color="#fff"
                            class="q-autocomplete shadow-0"
                            dense
                            hide-details="auto"
                            item-text="name"
                            item-value="membership_id"
                            multiple
                            outlined
                            placeholder="Memberships"
                            style="width: 250px"
                        >
                          <template
                              v-if="
                          product.membership_ids &&
                          product.membership_ids.length === memberships.length
                        "
                              v-slot:selection="{ index }"
                          >
                            <span v-if="index === 0">All Memberships</span>
                          </template>
                          <template v-else v-slot:selection="{ item, index }">
                            <span v-if="index === 0">{{ item.name }}</span>
                            <span v-if="index === 1">, ...</span>
                          </template>
                        </v-select>
                      </div>
                    </div>
                  </v-col>
                  <v-col v-if="isTicket && checkWritePermission($modules.facility.ticketAdmission.slug) && product && product.enable_admission" cols="6">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">Admission</p>
                      <v-row class="px-3 pb-3" dense>
                        <!-- Admission Type -->
                        <v-col cols="6">
                          <label>Type</label>
                          <v-select
                              v-model="product.admission_type"
                              :items="admissionTypes"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :rules="[(v) => !!v || 'Admission type is required']"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="value"
                              outlined
                              placeholder="Admission type"
                          />
                        </v-col>
                        <!-- Visit -->
                        <v-col cols="6" v-if="product.admission_type == 2">
                          <label>Visit</label>
                          <v-text-field
                              v-model="product.allow_visit"
                              :rules="[
                                (v) => !!v || 'Visit allowed',
                                (v) => !isNaN(v) || 'Visit count must be Number',
                                 (v) => v > 1 || 'Visit count greater than 1',
                              ]"
                              background-color="#fff"
                              class="q-text-field shadow-0"
                              dense
                              hide-details="auto"
                              outlined
                              required
                          />
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                  <v-col v-if="isTicket && checkWritePermission($modules.facility.ticketApplicableVenues.slug) && hasChildVenues && product && product.enable_applicable_venues" cols="6">
                    <div class="bordered">
                      <p class="pa-3 bg-light-neon font-semibold">Applicable Venues</p>
                      <div class="px-3 pb-3">
                        <label>Select Venues</label>
                        <v-autocomplete
                            :items="applicableVenues"
                            v-model="product.applicable_venues"
                            item-value="id"
                            item-text="name"
                            outlined
                            :readonly="isReadOnly"
                            multiple
                            background-color="#fff"
                            class="q-autocomplete shadow-0"
                            hide-details="auto"
                            dense
                        >
                          <template v-slot:prepend-item>
                            <v-list-item ripple @click="toggleApplicableVenueSelect()">
                              <v-list-item-action>
                                <v-icon :color="product.applicable_venues.length > 0 ? 'teal darken-4' : ''">{{ getVenueServiceIcon() }}</v-icon>
                              </v-list-item-action>
                              <v-list-item-content>
                                <v-list-item-title>All</v-list-item-title>
                              </v-list-item-content>
                            </v-list-item>
                          </template>
                          <template v-if="product.venues.length === venues.length" v-slot:selection="{ index }">
                            <span v-if="index === 0">All Venues</span>
                          </template>
                          <template v-slot:selection="{ item, index }">
                            <v-chip v-if="index < 3" small>
                              <span>{{ item.name }}</span>
                            </v-chip>
                            <span
                                v-if="index === 3"
                                class="grey--text text-caption"
                            >
                            (+{{ product.applicable_venues.length - 1 }} others)
                          </span>
                          </template>
<!--                          <template v-else v-slot:selection="{ item, index }">-->
<!--                            <span v-if="index < 3">{{ item.name }}</span>-->
<!--                            <span v-if="index === 3">, ...</span>-->
<!--                          </template>-->
                        </v-autocomplete>
                      </div>
                    </div>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel v-if="isEnableDctErp" active-class="active" class="mt-4">
              <v-expansion-panel-header class="p-0 panel-header" hide-actions style="min-height: 44px !important;">
                <template v-slot:default="{ open }">
                  <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom px-4 py-3">
                    <h3 class="text-sm font-medium">DCT ERP</h3>
                    <div class="d-flex justify-end align-start gap-x-2">
                      <MinusIcon v-if="open"/>
                      <PlusIcon v-else color="#0F2A4D"/>
                    </div>
                  </div>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content class="p-4">
                <v-row>
                  <v-col cols="12" md="3" sm="6">
                    <label for="">Project Number *</label>
                    <v-text-field
                        v-model="projectNumber"
                        :readonly="projectNumber ? true : false"
                        :rules="[(v) => !!v || 'Project number is required']"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                        required
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3" sm="6">
                    <label for="">Transaction Type*</label>
                    <v-text-field
                        v-model="transactionType"
                        :readonly="!!transactionType"
                        :rules="[(v) => !!v || 'Transaction Type is required']"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                        placeholder="Transaction Type"
                        required
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3" sm="6">
                    <label for="">Task Name*</label>
                    <v-select
                        v-model="product.task_name"
                        :items="taskNames"
                        :menu-props="{ bottom: true, offsetY: true }"
                        :rules="[(v) => !!v || 'Task name is required']"
                        background-color="#fff"
                        class="q-autocomplete shadow-0"
                        dense
                        hide-details="auto"
                        hint="Required Task Name"
                        item-text="text"
                        item-value="value"
                        outlined
                    ></v-select>
                    <!-- <v-text-field
                      outlined
                      background-color="#fff"
                      v-model="product.task_name"
                      hide-details="auto"
                      label="Task Name *"
                      placeholder="Task Name"
                      required
                      :rules="[(v) => !!v || 'Task name is required']"
                    ></v-text-field> -->
                  </v-col>
                  <v-col cols="12" md="3" sm="6">
                    <label for="">GL Code*</label>
                    <v-select
                        v-model="product.gl_code"
                        :items="glCodes"
                        :menu-props="{ bottom: true, offsetY: true }"
                        :rules="[(v) => !!v || 'GL code is required']"
                        background-color="#fff"
                        class="q-autocomplete shadow-0"
                        dense
                        hide-details="auto"
                        hint="Required GL Code"
                        item-text="text"
                        item-value="value"
                        outlined
                    ></v-select>
                    <!-- <v-text-field
                      outlined
                      background-color="#fff"
                      v-model="product.gl_code"
                      hide-details="auto"
                      label="Gl Code *"
                      placeholder="Gl Code"
                      required
                      :rules="[(v) => !!v || 'Gl Code is required']"
                    ></v-text-field> -->
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel v-if="isEnableSunSystem" active-class="active" class="mt-4">
              <v-expansion-panel-header class="p-0 panel-header" hide-actions style="min-height: 44px !important;">
                <template v-slot:default="{ open }">
                  <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom px-4 py-3">
                    <h3 class="text-sm font-medium">Sun System JV</h3>
                    <div class="d-flex justify-end align-start gap-x-2">
                      <MinusIcon v-if="open"/>
                      <PlusIcon v-else color="#0F2A4D"/>
                    </div>
                  </div>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content class="p-4">
                <v-row dense>
                  <v-col cols="12" md="3" sm="6">
                    <label for="">Account code</label>
                    <v-text-field
                        v-model="product.sunSystemAccountCode"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        maxlength="10"
                        outlined
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="3" sm="6">
                    <label for="">GL code</label>
                    <v-text-field
                        v-model="product.sunSystemGLCode"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        maxlength="15"
                        outlined
                    ></v-text-field>
                  </v-col>

                  <!-- <v-col cols="12" sm="6" md="3">
                    <label for="">Analysis code 0</label>
                    <v-text-field
                      outlined
                      background-color="#fff"
                      v-model="product.sunSystemAnalysisCode0"
                      hide-details="auto"
                      class="q-text-field shadow-0"
                      dense
                      maxlength="15"
                    ></v-text-field>
                  </v-col> -->

                  <v-col cols="12" md="3" sm="6">
                    <label for="">Analysis code 1</label>
                    <v-text-field
                        v-model="product.sunSystemAnalysisCode1"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        maxlength="15"
                        outlined
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="3" sm="6">
                    <label for="">Analysis code 2</label>
                    <v-text-field
                        v-model="product.sunSystemAnalysisCode2"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        maxlength="15"
                        outlined
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="3" sm="6">
                    <label for="">Analysis code 7</label>
                    <v-text-field
                        v-model="product.sunSystemAnalysisCode7"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        maxlength="15"
                        outlined
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
        <v-card-actions class="d-flex justify-space-between px-6 border-top">
          <v-btn v-if="product && product.id && checkWritePermission($modules.facility.timeRates.slug)"
                 class="text-capitalize text-red px-4"
                 color="#fff"
                 elevation="0"
                 height="44px"
                 type="button"
                 @click="confirmDeleteRentals(product.id)"
          >
            Delete
          </v-btn>
          <v-spacer/>
          <v-btn class="ma-2 text-capitalize" text @click="$emit('close')">Close</v-btn>
          <v-btn
              v-if="checkWritePermission($modules.facility.timeRates.slug)"
              class="bg-blue text-white text-capitalize font-semibold px-4"
              elevation="0"
              height="44px"
              @click="saveOrUpdateRentalProducts"
          >
            Save Ticket
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
    <confirm-model
        v-bind="confirmModel"
        @close="confirmModel.id = null"
        @confirm="confirmActions"
    ></confirm-model>
  </v-dialog>
</template>

<script>
import ImageUploader from '@/components/Image/ImageUploader.vue'
import DeleteIcon from '@/assets/images/misc/delete-icon.svg'
import constants from '@/utils/constants'
import ModalCloseIcon from '@/assets/images/misc/modal-close.svg'
import MinusIcon from '@/assets/images/misc/white-minus-icon.svg'
import PlusIcon from '@/assets/images/misc/white-plus-icon.svg'
import ConfigIcon from '@/assets/images/facilities/config.svg'
import FacilityIcon from '@/assets/images/facilities/facility.svg'
import SeasonalPricingIcon from '@/assets/images/misc/seasonal-pricing.svg'
import CustomSwitch from '@/components/Common/CustomSwitch.vue'
import SvgIcon from '@/components/Image/SvgIcon.vue'

export default {
  components: {
    SvgIcon,
    CustomSwitch,
    PlusIcon,
    ConfigIcon,
    FacilityIcon,
    MinusIcon,
    ModalCloseIcon,
    DeleteIcon,
    ImageUploader,
    SeasonalPricingIcon
  },
  props: {
    addTicketModal: {
      type: Boolean,
      default: false
    },
    facilities: {
      type: Array,
      default: () => [],
    },
    memberships: {
      type: Array,
      default: () => [],
    },
    isTicket: {
      type: Boolean,
      required: true,
    },
    venueServiceId: {
      type: [String, Number],
      required: true
    },
    venueId: {
      type: Number,
      default: null
    },
    configurations: {
      type: Object,
      required: true
    },
    selectedProduct: {
      type: Object,
      default: () => {},
      required: true
    },
    tickets:{
      type: Array,
      default: () => [],
    }
  },
  data () {
    return {
      product: {
        facility_rentals: [],
        weekdays: [],
        seasonal_pricing: [],
        sunSystemAccountCode: null,
        sunSystemGLCode: null,
        sunSystemAnalysisCode1: null,
        sunSystemAnalysisCode2: null,
        sunSystemAnalysisCode7: null,
        inventory_enable: null,
        inventoryPeriod: 'T',
        inventory_quantity: null,
        salesChannels: [],
        specific_day: 0,
        allow_cashback: 0,
        is_pod: 0,
        is_membership_only: 0,
        is_open_dated: 0,
        weekdays_value: [],
        enable_dependency:0,
        dependencies:[
          {
            ticket_types:[],
            period:"T"
          }
        ],
        venues:[],
        enable_admission: 0,
        enable_applicable_venues: 0,
        admission_type: 1,
        allow_visit: 1,
        applicable_venues:[],
        ticket_type:'I'
      },
      assignableFacilities:[],
      assignableTimingTemplates:[],
      isReadOnly:false,
      duration: [],
      taskNames: constants.TASK_NAMES,
      glCodes: constants.GL_CODES,
      projectNumber: null,
      transactionType: null,
      isEnableDctErp: false,
      isEnableOpenDated: false,
      isEnableSunSystem: false,
      deletedProducts: [],
      deletedSeasonalPricing: [],
      deletedFacilityAssignment: [],
      confirmModel: { id: null, title: null, description: null },
      packageTypes: [
        { type: 'I', name: 'Individual' },
        { type: 'C', name: 'Couple' },
        { type: 'G', name: 'Group' },
      ],
      radioGroupRules: [
        (v) => !!v || 'Ticket type is required',
      ],
      salesChannels: [
        'Qube',
        'B2B',
        'B2G',
        'B2E',
        'Scanner',
        'Kiosk',
      ],
      admissionTypes: [
        { value: 1, name: 'Single' },
        { value: 2,name: 'Multiple' },
      ],
    }
  },
  mounted () {
    if (this.$store.getters.venueInfo) {
      if (this.$store.getters.venueInfo.enable_dct_erp) {
        this.isEnableDctErp = true
        if (this.$store.getters.venueInfo.dct_erp_configuration) {
          this.projectNumber =
              this.$store.getters.venueInfo.dct_erp_configuration.project_no
          this.transactionType =
              this.$store.getters.venueInfo.dct_erp_configuration.transaction_type
        }
      } else {
        this.isEnableDctErp = false
      }
      this.isEnableOpenDated = !!this.$store.getters.venueInfo.enable_open_dated_tickets

      this.isEnableSunSystem = !!this.$store.getters.venueInfo.enable_sun_system
    }
    if (this.selectedProduct?.id) {
      const prod = JSON.parse(JSON.stringify(this.selectedProduct))
      prod.salesChannels = []
      const channelMapping = {
        Qube: 'enable_online_booking',
        B2B: 'enable_b2b_sales',
        B2G: 'enable_b2g_sales',
        B2E: 'enable_b2e_sales',
        Scanner: 'enable_scanner_product',
        Kiosk: 'enable_kiosk_sales',
      }
      Object.keys(channelMapping).forEach((channel) => {
        if (prod[channelMapping[channel]]) {
          prod.salesChannels.push(channel)
        }
      })
      if (!prod.dependencies) {
        prod.dependencies = [{
          ticket_types:[],
          period:"T"
        }]
        prod.enable_dependency = 0
      }
      if(!prod.enable_admission){
        prod.enable_admission = 0;
      }
      if(!prod.allow_visit){
        prod.allow_visit = 1;
      }
      if(!prod.admission_type){
        prod.admission_type = 1;
      }
      if(!prod.enable_applicable_venues){
        prod.enable_applicable_venues = 0;
      }
      if(!prod.applicable_venues){
        prod.applicable_venues = [];
      }else{
        prod.applicable_venues = prod.applicable_venues.map(p => p.venue_id);
      }


      this.product = prod

      this.isReadOnly = !!prod.rental_parent_id;
      this.$forceUpdate()
    }
    this.initializeFacilityServices()
    this.loadChildFacilitiesAndTimingTemplates();
  },
  methods: {
    loadChildFacilitiesAndTimingTemplates(){
      this.$http.get(`venues/facilities/child/facilities-and-templates?venue_service_id=${this.venueServiceId}`)
          .then((response) => {
            if (response.status == 200) {
              let data = response.data.data;
              this.assignableFacilities = data.facilities;
              this.assignableTimingTemplates = data.timing_templates;
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    toggleVenueSelect() {
      this.$nextTick(() => {
        if (this.product.venues.length == this.venues.length) {
          this.product.venues = [];
        } else {
          this.product.venues = this.venues.map((item) => item.id);
        }
      });
    },
    toggleApplicableVenueSelect(){
      this.$nextTick(() => {
        if (this.product.applicable_venues.length == this.venues.length) {
          this.product.applicable_venues = [];
        } else {
          this.product.applicable_venues = this.applicableVenues.map((item) => item.id);
        }
      });
    },
    getVenueServiceIcon() {
      if (this.product.venues.length == 0) return "mdi-checkbox-blank-outline";
      if (this.venues.length == this.product.venues.length)
        return "mdi-close-box";
      return "mdi-minus-box";
    },
    calculateTaxVariation (amount, type) {
      let taxTypeId = this.product.tax_type_id
      let taxPercentage = 0
      if (taxTypeId) {
        taxPercentage = this.taxTypes.find(
            (item) => item.value == taxTypeId
        ).percentage
      }
      const priceData = this.getTaxVariation(type, taxPercentage, amount)
      if (priceData) {
        this.product.price = priceData.price.toFixed(4)
        this.product.pre_tax_price = priceData.price.toFixed(4)
        this.product.total_price =
            priceData.total_price.toFixed(4)
      }
      this.$forceUpdate()
    },
    timingTemplates () {
      return JSON.parse(
          JSON.stringify(
              this.$store.getters.getTimingTemplatesByVenueServiceId(
                  this.venueServiceId
              )
          )
      )
    },
    rentalChange () {
      this.product.timing_template = null
      this.product.timing_template_id = null
    },
    calculateSeasonalPricingTaxVariation (subIndex, amount, type) {
      let taxTypeId = this.product.tax_type_id
      let taxPercentage = 0
      if (taxTypeId) {
        taxPercentage = this.taxTypes.find(
            (item) => item.value == taxTypeId
        ).percentage
      }
      const priceData = this.getTaxVariation(type, taxPercentage, amount)
      if (priceData) {
        this.product.seasonal_pricing[subIndex].price =
            priceData.price.toFixed(4)
        this.product.seasonal_pricing[subIndex].pre_tax_price =
            priceData.price.toFixed(4)
        this.product.seasonal_pricing[subIndex].total_price =
            priceData.total_price.toFixed(4)
      }
      this.$forceUpdate()
    },
    taxChange () {
      if (this.product.price) {
        this.calculateTaxVariation(
            this.product.price,
            'pre'
        )
      } else if (this.product.total_price) {
        this.calculateTaxVariation(
            this.product.total_price,
            'post'
        )
      }

      this.product.seasonal_pricing.forEach((pricing, index1) => {
        if (this.product.seasonal_pricing.length > 0) {
          if (
              this.product.seasonal_pricing[index1].pre_tax_price
          )
          {
            this.calculateSeasonalPricingTaxVariation(
                index1,
                this.product.seasonal_pricing[index1].pre_tax_price,
                'pre'
            )
          } else if (
              this.product.seasonal_pricing[index1].total_price
          )
          {
            this.calculateSeasonalPricingTaxVariation(
                index1,
                this.product.seasonal_pricing[index1].total_price,
                'post'
            )
          }
        }
      })
    },
    initializeFacilityServices () {
      if (!this.$store.getters.getConfigurationStatus(this.venueServiceId)) {
        this.$store
            .dispatch('loadConfigurationsByVenueServiceId', this.venueServiceId)
            .then((response) => {
              if (response.status == 200) {
                let data = this.$store.getters.getConfigurationByVenueServiceId(
                    this.venueServiceId
                )
                this.configurations = data
                this.getMinBookingTimes()
                this.defaultTaxId = data.default_tax_id
                if (
                    this.defaultTaxId &&
                    this.product &&
                    !this.product?.tax_type_id
                )
                {
                  this.product.tax_type_id = this.defaultTaxId
                }
              }
            })
      } else {
        let data = this.$store.getters.getConfigurationByVenueServiceId(
            this.venueServiceId
        )
        this.configurations = data
        this.getMinBookingTimes()
        this.defaultTaxId = data.default_tax_id
        if (
            this.defaultTaxId &&
            this.product &&
            !this.product.tax_type_id
        )
        {
          this.product.tax_type_id = this.defaultTaxId
        }
      }
    },
    getMinBookingTimes () {
      this.duration = []
      const timeIncrement = this.configurations.time_increment
      if (timeIncrement) {
        let hour = 0
        let index = 0
        while (hour < 24) {
          index = index + 1
          let time = timeIncrement * index
          hour = parseInt(time / 60)
          let min =
              time / 60 - hour > 0 ? Math.round((time / 60 - hour) * 60) : 0
          let text = hour > 0 ? `${hour} Hour ` : ''
          text += min > 0 ? `${min} Min` : ''
          this.duration.push({ value: time, text: text })
        }
      }
      if (this.configurations.per_capacity == 0) {
        const hasObject = this.duration.some(
            (obj) => obj.value === 60 && obj.text === 'Full day'
        )

        // If the object doesn't exist, add it to the array
        if (!hasObject) {
          this.duration.push({ value: 2440, text: 'Full day' })
        }
      }
      if (this.configurations.per_capacity == 1) {
        const hasObject = this.duration.some(
            (obj) => obj.value === 1 && obj.text === 'No Duration'
        )

        // If the object doesn't exist, add it to the array
        if (!hasObject) {
          this.duration.unshift({ value: 1, text: 'No Duration' })
        }
      }
    },
    addFacilityAssignment () {
      if (!this.product.facility_rentals) {
        this.product.facility_rentals = []
      }
      if(this.hasChildVenues && this.checkCurrentVenue){
        this.product.facility_rentals.push({
          assignedFacilities: [],
          timing_templates: [],
        })
      }else{
        this.product.facility_rentals.push({
          assignedFacility: [],
          timing_template: null,
        })
      }
      this.$forceUpdate()
    },
    deleteFacilityAssignment (subIndex) {
      let facilityRentals =
          this.product.facility_rentals[subIndex]

      if (facilityRentals.facilityRentalId) {
        // Check if facilityRentals.facilityRentalId is an array
        if (Array.isArray(facilityRentals.facilityRentalId)) {
          // Flatten and push all IDs in facilityRentalId array
          const ids = facilityRentals.facilityRentalId.flat()
          this.deletedFacilityAssignment.push(...ids)
        } else {
          // If facilityRentalId is not an array, push it directly
          this.deletedFacilityAssignment.push(facilityRentals.facilityRentalId)
        }
      }
      this.product.facility_rentals.splice(subIndex, 1)
      this.$forceUpdate()
    },
    addSeasonalPricing () {
      this.product.seasonal_pricing.push({
        start_date: null,
        end_date: null,
        pre_tax_price: null,
        total_price: null,
      })
    },
    deleteSeasonalPricing (subIndex) {
      if (this.product.seasonal_pricing[subIndex].id) {
        this.confirmModel = {
          id: subIndex,
          data: { subIndex: subIndex },
          title: 'Do you want to delete this seasonal pricing?',
          description:
              'By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?',
          type: 'deleteSeasonalPricing',
        }
      } else {
        this.product.seasonal_pricing.splice(subIndex, 1)
      }
    },
    getIcon () {
      let field = 'weekdays_value'
      let icon = 'mdi-checkbox-blank-outline'
      if (!this.product[field]) {
        this.product['weekdays_value'] = []
      }
      if (
          this.product[field] &&
          this.product[field].length == this.weekdays.length
      )
      {
        icon = 'mdi-close-box'
      }
      if (
          this.product[field] &&
          this.product[field].length > 0 &&
          this.product[field].length != this.weekdays.length
      )
        icon = 'mdi-minus-box'

      return icon
    },
    getChannelsIcon () {
      let field = 'salesChannels'
      let icon = 'mdi-checkbox-blank-outline'
      if (!this.product[field]) {
        this.product['salesChannels'] = []
      }
      if (
          this.product[field] &&
          this.product[field].length == this.salesChannels.length
      )
      {
        icon = 'mdi-close-box'
      }
      if (
          this.product[field] &&
          this.product[field].length > 0 &&
          this.product[field].length != this.salesChannels.length
      )
        icon = 'mdi-minus-box'

      return icon
    },
    toggleChannels () {
      let field = 'salesChannels'
      this.$nextTick(() => {
        if (this.product[field].length == this.salesChannels.length) {
          this.product[field] = []
        } else {
          this.product[field] = this.salesChannels.map(
              (item) => item
          )
        }
      })
      this.$forceUpdate()
    },
    toggleFacilities (k) {
      if (this.product.facility_rentals[k].assignedFacilities.length === this.assignableFacilities.length) {
        this.product.facility_rentals[k].assignedFacilities = [];
      } else {
        this.product.facility_rentals[k].assignedFacilities = this.assignableFacilities.map(f => f.id);
      }
    },
    toggleTimingTemplates (k) {
      if (this.product.facility_rentals[k].timing_templates.length === this.assignableTimingTemplates.length) {
        this.product.facility_rentals[k].timing_templates = [];
      } else {
        this.product.facility_rentals[k].timing_templates = this.assignableTimingTemplates.map(f => f.id);
      }
    },

    toggle () {
      let field = 'weekdays_value'
      this.$nextTick(() => {
        if (this.product[field].length == this.weekdays.length) {
          this.product[field] = []
        } else {
          this.product[field] = this.weekdays.map(
              (item) => item.bit_value
          )
        }

      })
      this.$forceUpdate()
    },
    confirmActions (data) {
      if (data.type === 'deleteSeasonalPricing') {
        let indices = data.data
        let pricing =
            this.product.seasonal_pricing[indices.subIndex]
        if (pricing.id) {
          this.deletedSeasonalPricing.push(pricing.id)
        }
        this.product.seasonal_pricing.splice(
            indices.subIndex,
            1
        )
      }
      if (data.type === 'dependency_delete') {
        this.product.dependencies.splice(data.id,1)
      }
      if (data.type === 'delete') {
        if (this.product?.id) {
          this.deleteProduct(this.product.rental_product_id)
        }
      }
      this.confirmModel.id = null
    },
    saveOrUpdateRentalProducts () {
      if (!this.$refs.form.validate()) {
        this.showError('Please fill all required fields')
        return
      }

      let formData = new FormData()
      if (this.deletedSeasonalPricing.length) {
        this.deletedSeasonalPricing.forEach((id, index) => {
          formData.append(`deleted_seasonal_pricing[${index}]`, id)
        })
      }
      if (this.deletedFacilityAssignment.length) {
        this.deletedFacilityAssignment.forEach((id, index) => {
          formData.append(`deleted_facility_assignment[${index}]`, id)
        })
      }
      const product = this.product
      formData.append(`products[name]`, product.name)
      if (product.ar_name) {
        formData.append(`products[ar_name]`, product.ar_name)
      }
      formData.append(`products[duration]`, product.duration)
      formData.append(`products[price]`, product.price)
      formData.append(`products[tax_type_id]`, product.tax_type_id)

      if (product.seasonal_pricing && product.seasonal_pricing.length > 0) {
        const invalidDates = product.seasonal_pricing.filter(pricing=>{
          return new Date(pricing.start_date) > new Date(pricing.end_date)
        });
        if (invalidDates.length > 0) {
          this.showError('Invalid Seasonal Pricing dates')
          return
        }
        product.seasonal_pricing.forEach((pricing, index1) => {
          if (pricing.id) {
            formData.append(
                `products[seasonal_pricing][${index1}][id]`,
                pricing.id
            )
          }
          formData.append(
              `products[seasonal_pricing][${index1}][start_date]`,
              pricing.start_date
          )
          formData.append(
              `products[seasonal_pricing][${index1}][end_date]`,
              pricing.end_date
          )
          formData.append(
              `products[seasonal_pricing][${index1}][pre_tax_price]`,
              pricing.pre_tax_price
          )
          formData.append(
              `products[seasonal_pricing][${index1}][total_price]`,
              pricing.total_price
          )
        })
      }

      if (this.isTicket) {
        formData.append(
            `products[is_open_dated]`,
            product.is_open_dated
        )
        if (product.is_open_dated == 1) {
          formData.append(
              `products[expiry_days]`,
              product.expiry_days
          )
        } else {
          product.expiry_days = null
        }
        formData.append(
            `products[is_membership_only]`,
            product.is_membership_only
        )

        if (product.is_membership_only == 1) {
          formData.append(
              `products[membership_ids]`,
              product.membership_ids
          )
        } else {
          product.membership_ids = []
        }
        formData.append(
            `products[ticket_type]`,
            product.ticket_type
        )
        formData.append(
            `products[enable_online_booking]`,
            product.enable_online_booking
        )
        // formData.append(
        //   `products[enable_kiosk_sales]`,
        //   product.enable_kiosk_sales
        // );
        formData.append(
            `products[enable_b2b_sales]`,
            product.enable_b2b_sales
        )
        formData.append(
            `products[enable_b2g_sales]`,
            product.enable_b2g_sales
        )
        formData.append(
            `products[enable_b2e_sales]`,
            product.enable_b2e_sales
        )
        formData.append(
            `products[enable_scanner_product]`,
            product.enable_scanner_product
        )
        formData.append(
            `products[enable_kiosk_sales]`,
            product.enable_kiosk_sales
        )

        formData.append(
            `products[participant_count]`,
            product.ticket_type == 'I'
                ? 1
                : product.ticket_type == 'C'
                    ? 2
                    : product.ticket_type == 'G'
                        ? product.participant_count
                        : 0
        )
      }

      if(product.venues.length){
        product.venues.forEach((venue,index) => {
          formData.append(
              `multi_venues[${index}]`,
              venue
          )
        })
      }

        let assignedFacility = []
      let assignedFacilities = []
      if (product.facility_rentals) {
        product.facility_rentals.forEach((fr) => {
          if(this.hasChildVenues && this.checkCurrentVenue){
            assignedFacilities.push({
              facilities: fr.assignedFacilities,
              timing_templates: fr.timing_templates,
            })
          }else{
            assignedFacility.push({
              facilities: fr.assignedFacility,
              timing_template: fr.timing_template,
            })
          }
        })
      }

      if (assignedFacility.length > 0) {
        formData.append(
            `products[assigned_facility]`,
            btoa(JSON.stringify(assignedFacility))
        )
      }
      if (assignedFacilities.length > 0) {
        formData.append(
            `products[assigned_facilities]`,
            btoa(JSON.stringify(assignedFacilities))
        )
      }

      if (product.allow_cashback) {
        formData.append(
            `products[allow_cashback]`,
            product.allow_cashback
        )
      }

      if (product.cashback_percentage) {
        formData.append(
            `products[cashback_percentage]`,
            product.cashback_percentage
        )
      }
      if (product.enable_dependency !== undefined && product.enable_dependency !== null) {
        formData.append(
            `products[enable_dependency]`,
            product.enable_dependency
        )
        if (product.enable_dependency == 1) {
          formData.append(
              `products[dependencies]`,
              btoa(JSON.stringify(product.dependencies))
          )
        } else {
          product.dependencies = []
        }
      }
      formData.append(
          `products[benefit_excluded]`,
          product.benefit_excluded
      )
      formData.append(
          `products[duplicate_booking]`,
          product.duplicate_booking
      )

      if (product.duplicate_booking == 1) {
        formData.append(
            `products[duplicate_booking_duration]`,
            product.duplicate_booking_duration
        )
      }
      formData.append(`products[is_pod]`, product.is_pod)
      if (product.specific_day == 1) {
        formData.append(
            `products[specific_day]`,
            product.specific_day
        )
        formData.append(
            `products[weekdays_value]`,
            product.weekdays_value
        )
      }
      if (product.product_id) {
        formData.append(`products[product_id]`, product.product_id)
      }
      if (product.file) {
        formData.append(`products[image]`, product.file)
      }
      formData.append(
          `products[show_end_time]`,
          product.show_end_time
      )

      //erp fields
      if (this.isEnableDctErp) {
        if (this.projectNumber) {
          formData.append(
              `products[project_no]`,
              this.projectNumber
          )
        }
        if (product.task_name) {
          formData.append(`products[task_name]`, product.task_name)
        }
        if (product.gl_code) {
          formData.append(`products[gl_code]`, product.gl_code)
        }
        if (this.transactionType) {
          formData.append(
              `products[transaction_type]`,
              this.transactionType
          )
        }
      }

      if (this.isEnableSunSystem) {
        if (product.sunSystemAccountCode) {
          formData.append(
              `products[sun_system_account_code]`,
              product.sunSystemAccountCode
          )
        }

        if (product.sunSystemGLCode) {
          formData.append(
              `products[sun_system_gl_code]`,
              product.sunSystemGLCode
          )
        }

        // if (product.sunSystemAnalysisCode0) {
        //   formData.append(
        //     `products[sun_system_analysis_code_0]`,
        //     product.sunSystemAnalysisCode0
        //   );
        // }

        if (product.sunSystemAnalysisCode1) {
          formData.append(
              `products[sun_system_analysis_code_1]`,
              product.sunSystemAnalysisCode1
          )
        }

        if (product.sunSystemAnalysisCode2) {
          formData.append(
              `products[sun_system_analysis_code_2]`,
              product.sunSystemAnalysisCode2
          )
        }

        if (product.sunSystemAnalysisCode7) {
          formData.append(
              `products[sun_system_analysis_code_7]`,
              product.sunSystemAnalysisCode7
          )
        }
      }

      formData.append(
          `products[inventory_enable]`,
          product.inventory_enable
      )

      if (product.inventory_enable) {
        if (product.inventoryPeriod) {
          formData.append(
              `products[inventory_period]`,
              product.inventoryPeriod
          )
        }
        if (product.inventory_quantity) {
          formData.append(
              `products[quantity]`,
              product.inventory_quantity
          )
        }
      }
      formData.append(`products[enable_applicable_venues]`, product.enable_applicable_venues);
      if(product.enable_applicable_venues){
        if(product.applicable_venues.length === 0){
          this.showError("Please select applicable venues");
          return false;
        }
        product.applicable_venues.forEach((venue,index) => {
          formData.append(`applicable_venues[${index}]`, venue)
        })
      }
      formData.append(`products[enable_admission]`, product.enable_admission);
      formData.append(`products[admission_type]`, product.admission_type);
      formData.append(`products[allow_visit]`, product.allow_visit);

      formData.append(`per_capacity`, this.isTicket ? 1 : 0)
      this.showLoader()
      this.$http.post(`venues/facilities/categories/rentals/${this.venueServiceId}/single`, formData)
          .then((response) => {
            if (response.status == 200) {
              // this.$store.dispatch(
              //   "loadRentalProductsByVenueServiceId",
              //   this.venueServiceId
              // );
              this.$emit('refresh')
              this.$emit('close')
              this.hideLoader()
              if (this.isTicket == 1) {
                this.showSuccess('Tickets saved successfully')
              } else {
                this.showSuccess('Time Rates saved successfully')
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    confirmDeleteRentals (productId) {
      this.confirmModel = {
        id: productId,
        title: 'Do you want to delete this product?',
        description:
            'By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?',
        type: 'delete',
      }
    },
    confirmRemoveDependency (index) {
      this.confirmModel = {
        id: index,
        title: 'Do you want to delete this dependency?',
        description:
            'By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?',
        type: 'dependency_delete',
      }
    },
    deleteProduct (rental_product_id) {
      this.showLoader('Deleting...')
      this.$http.delete(`venues/facilities/categories/rentals/${rental_product_id}`)
          .then((response) => {
            if (response.status >= 200 && response.status < 300) {
              this.showSuccess(`${this.isTicket ? 'Ticket' : 'Time rate'} deleted successfully`)
              this.$emit('refresh')
              this.$emit('close')
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    addTicketDependency(){
      this.product.dependencies.push({
        ticket_types: [],
        period: "T"
      })
      this.$forceUpdate()
    }
  },
  watch: {
    'product.salesChannels' () {
      const channelMapping = {
        Qube: 'enable_online_booking',
        B2B: 'enable_b2b_sales',
        B2G: 'enable_b2g_sales',
        B2E: 'enable_b2e_sales',
        Scanner: 'enable_scanner_product',
        Kiosk: 'enable_kiosk_sales',
      }
      Object.keys(channelMapping).forEach((channel) => {
        if (this.product.salesChannels.includes(channel)) {
          this.product[channelMapping[channel]] = 1
        } else {
          this.product[channelMapping[channel]] = 0
        }
      })
    },
    'product.dependencies': {
      handler() {
        this.$forceUpdate()
      },
      deep: true
    }
  },
  computed: {
    getAssignableFacilities(){
      return this.assignableFacilities.filter(f => this.product.venues.includes(f.venue_id) || f.venue_id == this.venueId)
    },
    getAssignableTimingTemplates(){

      return this.assignableTimingTemplates.filter(f => this.product.venues.includes(f.venue_id) || f.venue_id == this.venueId)
    },
    hasChildVenues () {
      return this.$store.getters.venueInfo.sub_venues
    },
    checkCurrentVenue(){
      return this.venueId == this.$store.getters.userInfo.venue_id;
    },
    venues(){
      const subVenues = this.$store.getters.getSubVenues?.data || [];
      const currentVenueId = this.$store.getters.userInfo?.venue_id;

      return subVenues.filter(venue => venue.id !== currentVenueId);
    },
    applicableVenues(){
      return this.$store.getters.getSubVenues?.data || [];
      //const currentVenueId = this.$store.getters.userInfo?.venue_id;

      //return subVenues;
    },
    availableFacilities () {
      return this.facilities?.filter((facility) => {
        return this.isTicket
            ? facility.per_capacity === 1
            : facility.per_capacity === 0
      })
    },
    weekdays () {
      return this.$store.getters.getWeekdays.data
    },
    taxTypes () {
      return this.$store.getters.getTaxTypes.data
    },
    dependencyTickets(){
      if (this.product.id){
       return this.tickets.filter(ticket => ticket.id != this.product.id)
      }
      return this.tickets;
    }
  }
}
</script>

<style lang="scss" scoped>
.ticket-form {
  .v-expansion-panel {
    border-radius: 0.25rem;
    border: 1px solid #EAEAEA;

    &::before {
      box-shadow: unset !important;
    }
  }

  .panel-header {
    background: #112A460D;
    color: #0F2A4D !important;
    min-height: 36px;

    .v-icon {
      color: #0F2A4D !important;
    }
  }

  .active > .panel-header {
    color: #fff !important;
    background: #112A46;

    .v-icon {
      color: #fff;
    }
  }
}
</style>

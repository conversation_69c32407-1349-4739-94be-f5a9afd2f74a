<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <BackButton :handler="goToMarketing"/>
      </v-col>
    </v-row>
    <div class="md-card md-theme-default mt-8 rounded-5">
      <div class="md-card-content md-card-table">
        <div class="d-flex justify-space-between align-center ">
          <SvgIcon class="text-2xl font-semibold" text="Contact Us">
          </SvgIcon>
          <v-spacer/>

          <v-autocomplete
              v-model="searchParams.time_intervel"
              :items="timeDuration"
              class="q-autocomplete   shadow-0 m-r-3 mt-2"
              hide-details
              dense
              item-text="title"
              item-value="name"
              outlined
              placeholder="Time Period"
              style="max-width: 200px"
              @change="changeFxn"
              height="20"

          >
            <template v-slot:prepend-inner>
              <SvgIcon>
                <template v-slot:icon>
                  <EventIcon/>
                </template>
              </SvgIcon>
            </template>
          </v-autocomplete>


          <div v-if="flag" class="d-flex align-center gap-x-4 mt-2">
            <v-menu
                v-model="menu1"
                :close-on-content-click="false"
                :nudge-right="40"
                filled
                min-width="290px"
                offset-y
                transition="scale-transition"
            >
              <template v-slot:activator="{ on }">
                <v-text-field
                    v-model="date1Formatted"
                    class="shadow-0 q-text-field"
                    flat
                    dense
                    hide-details
                    outlined
                    readonly
                    v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                  v-model="searchParams.start_date"
                  @input="menu1 = false"
              ></v-date-picker>
            </v-menu>
            <v-menu
                v-model="menu2"
                :close-on-content-click="false"
                :nudge-right="40"
                min-width="290px"
                offset-y
                transition="scale-transition"
            >
              <template v-slot:activator="{ on }">
                <v-text-field
                    v-model="date2Formatted"
                    class="shadow-0 q-text-field"
                    flat
                    dense
                    hide-details
                    outlined
                    readonly
                    v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                  v-model="searchParams.end_date"
                  @input="menu2 = false"
              ></v-date-picker>
            </v-menu>
            <v-btn class="mr-2 bg-blue text-white" height="40" elevation="0" @click="search">
              Go
            </v-btn>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-md-12">
            <div
                class="table-responsive"
            >
              <table class="table border-collapse text-center">
                <thead>
                <tr class="opacity-70 tr-neon tr-rounded text-center">
                  <th class="">
                    <div class="">Name</div>
                  </th>
                  <th class="text-center">
                    <div class="">Email</div>
                  </th>
                  <th class="text-center">
                    <div class="">Phone</div>
                  </th>
                  <th class="text-center">
                    <div>Submitted at</div>
                  </th>
                  <th class="text-center">
                    <div>Action</div>
                  </th>
                </tr>
                </thead>

                <tbody>
                <tr
                    class="md-table-row"
                    v-for="data in searchDatas"
                    :key="data.id"
                >
                  <td class="md-table-cell">
                    {{ data.name || 'NA' }}
                  </td>
                  <td class="md-table-cell">
                    {{ data.email || 'NA' }}
                  </td>
                  <td class="md-table-cell">
                    {{ data.phone || 'NA' }}
                  </td>
                  <td class="md-table-cell">
                    {{ data.created_at | timeStamp }}
                  </td>
                  <td class="md-table-cell">
                    <v-btn
                        class="confirm-btn"
                        color="darken-2"
                        text
                        @click="viewDetails(data)"
                    >
                      View
                    </v-btn>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="col-md-3"></div>
          <div class="col-md-6">
            <v-pagination v-model="page" :length="totalPages" class="new-pagination"></v-pagination>
          </div>
          <v-col class="d-flex align-center justify-end" cols="3">
            <div class="d-flex align-center justify-end text-sm gap-x-2">
                <span class="">
                  Result
                </span>
              <div style="width: 80px">
                <v-select
                    v-model="perPage"
                    :items="[10, 15, 25, 50]"
                    :menu-props="{ bottom: true, offsetY: true }"
                    class="q-autocomplete text-sm"
                    flat
                    hide-details
                    solo
                    @change="search"
                ></v-select>
              </div>
              <span>Per Page</span>
            </div>
          </v-col>
        </div>

      </div>
    </div>
    <v-dialog v-model="showDetailsModal" max-width="500" scrollable v-if="selectedData">
      <v-card>
        <v-toolbar color="teal" dark height="80">
          <v-toolbar-title>Details</v-toolbar-title>
        </v-toolbar>
        <v-card-text>
          <div style="margin-top:10px;">
            <v-row>
              <v-col cols="12" md="6" sm="12"><span class="label">Name:</span>
                <br>
                <span class="details">{{ selectedData.name || "N/A" }}</span>
              </v-col>
              <v-col cols="12" md="6" sm="12"><span class="label">Email:</span>
                <br>
                <span class="details">{{ selectedData.email || "N/A" }}</span>
              </v-col>
              <v-col cols="12" md="6" sm="12"><span class="label">Phone:</span>
                <br>
                <span class="details">{{ selectedData.phone || "N/A" }}</span>
              </v-col>
              <v-col cols="12" md="6" sm="12"><span class="label">Submitted at:</span>
                <br>
                <span class="details">{{ selectedData.date | dateformat }}</span>
              </v-col>
              <v-col cols="12"><span class="label">Message:</span>
                <br>
                <span class="details">{{ selectedData.message || "N/A" }}</span>
              </v-col>
            </v-row>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-btn class="ma-2 white--text blue-color" text @click="closeDetails()"
          >Close
          </v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import moment from 'moment'
import SvgIcon from '@/components/Image/SvgIcon.vue'
import EventIcon from '@/assets/images/misc/calendar.svg'
import BackButton from '@/components/Common/BackButton.vue'

export default {
  components: { BackButton, EventIcon, SvgIcon },
  props: {
    global: {
      type: Boolean,
      default: true,
    },
  },
  //   components: {
  //     CustomerModel,
  //     ConfirmModel,
  //   },
  data () {
    return {
      flag: false,
      currentPage: 0,
      pageSize: 10,
      emails: [],
      email: null,
      isLoading: false,
      searchDatas: [],
      searchFlag: false,
      searchParams: {
        start_date: moment()
            .subtract(30, 'days')
            .format('YYYY-MM-DD'),
        end_date: moment().format('YYYY-MM-DD'),
      },
      page: 1,
      perPage: 10,
      totalPages: 1,
      btnShow: false,
      timeDuration: [
        { name: 'All', title: 'All' },
        { name: 'day', title: 'This Day' },
        { name: 'week', title: 'This Week' },
        { name: 'month', title: 'This Month' },
        { name: 'year', title: 'This Year' },
        { name: 'custom', title: 'Custom Duration' },
      ],
      orderBy: 'id',
      orderDir: 'DESC',
      menu1:false,
      menu2:false,
      showDetailsModal:false,
      selectedData:null
    }
  },

  computed: {
    date1Formatted () {
      return moment(this.searchParams.start_date, 'YYYY-MM-DD').format(
          'Do MMM YYYY'
      )
    },
    date2Formatted () {
      return moment(this.searchParams.end_date, 'YYYY-MM-DD').format(
          'Do MMM YYYY'
      )
    },
  },
  mounted () {
    this.search()
  },
  created () {
    if (typeof this.$route.params.id != 'undefined') {
      this.searchParams.offline_user_id = this.$route.params.id
    }

    setTimeout(() => {
      this.btnShow = true
    }, 10)
  },
  watch: {
    page () {
      this.search()
    },
  },
  methods: {
    closeDetails(){
      this.showDetailsModal =false;
      this.selectedData = null;
    },
    viewDetails(data){
      this.selectedData = data;
      this.showDetailsModal =true;
    },
    goToMarketing () {
      this.$router.push({ name: 'Marketing' }, () => {
      })
    },
    getFilterData () {
      this.page = 1
      this.search()
    },
    search () {
      let url = ''
      url =
          `&order_by_custom=${this.orderBy}&sort_order=${this.orderDir}`

      if (
          this.searchParams.time_intervel != 'All' &&
          this.searchParams.time_intervel != null
      )
      {
        // this.searchParams.end_date = moment().format("YYYY-MM-DD");
        if (this.searchParams.time_intervel == 'custom') {
          this.flag = true
        } else if (this.searchParams.time_intervel == 'day') {
          this.searchParams.start_date = moment().format('YYYY-MM-DD')
        } else if (this.searchParams.time_intervel == 'week') {
          this.searchParams.start_date = moment()
              .startOf('week')
              .format('YYYY-MM-DD')
        } else if (this.searchParams.time_intervel == 'month') {
          this.searchParams.start_date = moment()
              .startOf('month')
              .format('YYYY-MM-DD')
        } else if (this.searchParams.time_intervel == 'year') {
          this.searchParams.start_date = moment()
              .startOf('year')
              .format('YYYY-MM-DD')
        }
        url += '&end_date=' + this.searchParams.end_date
        url += '&start_date=' + this.searchParams.start_date
      }
      this.showLoader('Loading Customer')
      this.$http
          .get(
              'venues/marketing/contact-us?page=' +
              this.page +
              '&per_page=' +
              (this.perPage != null ? this.perPage : 10) +
              url
          )
          .then((response) => {
            this.hideLoader()
            if (response.status == 200 && response.data.status) {
              const { data } = response.data
              this.searchDatas = data.data
              this.totalPages = data.last_page
              // if (typeof this.searchParams.offline_user_id != "undefined") {
              //   this.showUserModel(this.searchParams.offline_user_id);
              // }
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },

    changeFxn() {
      if (this.searchParams.time_intervel == "custom") {
        this.searchParams.start_date = moment()
            .subtract(30, "days")
            .format("YYYY-MM-DD");
        this.searchParams.end_date = moment().format("YYYY-MM-DD");
        this.flag = true;
      } else {
        this.flag = false;
        this.search();
      }
    },
  },
}
</script>

<style scoped>
.salesBtn {
  padding: 5px;
  font-size: 14px;
  font-weight: bold;
  color: #066a8c;
}

.btn_bg {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
}

tbody tr:hover {
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -webkit-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -moz-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  z-index: 1;
}
</style>

<template>
  <v-container fluid>
    <v-row no-gutters class="pos product-management">
        <v-col md="12" class="pa-4">
            <v-row>
                <v-col md="4">
                    <div class="pm-title"><v-icon class='dark-blue-color' @click="backToPos" title="Back to POS">mdi-arrow-left</v-icon> Attribute Management</div>
                </v-col>
                <v-col md="4">
                    <div class="center-div">
                       <template>
                            <v-row justify="center">
                                <v-col
                                    align="center"
                                    cols="6"
                                    md="3"
                                    :class="[pmClass ? 'btn_bg' : '']"
                                    >
                                    <router-link :to="`/pos/product-management`">
                                        <div class="salesBtn">Products</div>
                                    </router-link>
                                </v-col>
                                <v-col
                                    align="center"
                                    cols="6"
                                    md="3"
                                    :class="[amClass ? 'btn_bg' : '']"
                                    >
                                    <router-link :to="`/pos/attribute-management`">
                                        <div class="salesBtn">Attributes</div>
                                    </router-link>
                                </v-col>
                                <v-col
                                    align="center"
                                    cols="6"
                                    md="3"
                                    :class="[cmClass ? 'btn_bg' : '']"
                                    >
                                    <router-link :to="`/pos/category-management`">
                                        <div class="salesBtn">Category</div>
                                    </router-link>
                                </v-col>
                            </v-row>
                        </template>
                    </div>
                </v-col>
            </v-row>
        </v-col>
        <v-col md="12" class="mt-5">
            <v-card class="pa-4 pm-card">
                <v-row>
                    <v-col md="3" sm="6">
                        <div class="product-search">
                            <v-text-field
                                v-model="productNameSearch"
                                density="compact"
                                variant="solo"
                                :label="pmClass?'Search Product':cmClass?'Search Category':'Search Attribute'"
                                append-inner-icon="mdi-magnify"
                                single-line
                                hide-details
                                @keyup.enter="searchData"
                                @click:append="searchData"
                                clearable
                                @click:clear="clearSearch"
                                class="ps-field"
                            ></v-text-field>
                        </div>
                    </v-col>
                    <v-col md="9" sm="6" class="pb-0">
                        <div class="right-div" style="text-align: right;">
                            <v-btn class="btn-add-product" @click="addData" v-if="checkWritePermission($modules.pos.management.slug)">+ {{ pmClass?'Add Product':cmClass?'Add Category':'Add Attribute'}}</v-btn>
                        </div>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col md="12" class="">
                        <table class="pm-table mb-5" id="pm-table" with="100%" v-if="amClass">
                            <tr>
                                <th>Attribute Name</th>
                                <th>Action</th>
                            </tr>
                            <tr v-for="(attr, index) in attributes" :key="index">
                                <td>
                                    <div>
                                        {{ attr.attribute_name }}
                                    </div>
                                </td>
                                <td>
                                    <v-icon class='dark-blue-color ma-1' @click="editAttribute(attr.id)" title="Edit Attribute" v-if="checkWritePermission($modules.pos.management.slug)">mdi-pencil-outline</v-icon>
                                    <v-icon class='dark-blue-color ma-1' @click="deleteData(attr.id,'attribute')" title="Delete Attribute" v-if="checkDeletePermission($modules.pos.management.slug)">mdi-delete-outline</v-icon>
                                </td>
                            </tr>
                            
                            <tr v-if="attributes && !attributes.length">
                                <th colspan="5">Attribute Not Found</th>
                            </tr>
                        </table>
                        <v-pagination
                            v-if="totalPages > 0"
                            v-model="page"
                            :length="totalPages"
                        ></v-pagination>
                    </v-col>
                </v-row>
            </v-card>
        </v-col>
    </v-row>
    
    <PosAttributeForm :categoryDialog="categoryDialog" :venueServiceId="venueServiceId" @close="categoryDialog=false;currentCategoryId=null" @added="categoryDialog=false;getCategoryList()" :id="currentCategoryId"/>
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
    </v-container>
</template>

<script>
import PosAttributeForm from "@/views/Pos/PosAttributeForm.vue";
export default {
    components: {
        PosAttributeForm,
    },
    data() {
        return {
            image_path:'/../../src/assets/images/pos-img/1.png',
            cartItems: [],
            attributes: [],
            categories: [],
            products: [],
            right: null,
            productNameSearch:"",
            selectedCategory:'all',
            selectedTab:null,
            customerDialog: false,
            pmClass: false,
            cmClass: false,
            amClass: true,
            productDialog: false,
            categoryDialog: false,
            attributeDialog:false,
            page: 1,
            perPage: 10,
            totalPages: 1,
            currentCategoryId: null,
            currentProductId: null,
            currentAttributeId: null,
            confirmModel: {
                id: null,
                title: null,
                description: null,
            },
        }
    },
    mounted() {
        if (this.$store.getters.getVenueServices.status == false) {
            this.$store.dispatch("loadVenueServices").then(() => {
                this.getCategoryList();
            });
        }
        if (this.$store.getters.getTaxTypes.status == false) {
            this.$store.dispatch("loadTaxTypes");
        }
        if (this.$router.currentRoute.name == "AttributeManagement") {
            this.cmClass = false;
            this.pmClass = false;
            this.amClass = true;
            this.getAttributeList();
        }
    },
    watch: {
        page() {
            this.searchData();
        },
        $route(to) {
            if (to.name == "AttributeManagement") {
                this.productNameSearch = "";
                this.cmClass = false;
                this.pmClass = false;
                this.amClass = true;
                this.getAttributeList();
            }
        }
    },
    computed: {
        formTitle () {
            return this.editedIndex === -1 ? 'Add Product' : 'Edit Product'
        },
        venueServiceId() {
            const filteredService = this.$store.getters.getVenueServices.data.find(item => item.name.toLowerCase() === "pos");
            if (filteredService) {
                return filteredService.id;
            } else {
                return null;
            }
        },  
    },
    methods: {

        backToPos() {
            this.$router.push({name: "PosView"});
        },
        clearSearch() {
            setTimeout(() => {
                this.getProductList(this.currentYear, true);
            }, 100);
        },
        getAttributeList() {
            if (this.venueServiceId) {
                this.showLoader("Loading");
                // let url = `venues/pos?category=${this.selectedCategory}`;
                let url = `venues/pos/management/attributes?venue_service_id=${this.venueServiceId}&page=`;
                url += this.page + "&per_page=" + (this.perPage != null ? this.perPage : 10)
                url += `${this.productNameSearch ? "&name=" + this.productNameSearch : ""}`;

                this.$http.get(url).then((response) => {
                    if (response.status == 200) {
                        const data = response.data.data;
                        if (data && data.length) {
                            this.totalPages = response.data.total_pages;
                            this.attributes = data;
                        } else {
                            this.attributes = [];
                            this.totalPages = 1;
                        }
                        this.hideLoader();
                    }
                }).catch((error) => {
                    this.hideLoader();
                    this.errorChecker(error);
                });
            }
            
        },

        close() {
            this.dialog = false
            this.$nextTick(() => {
            this.editedItem = Object.assign({}, this.defaultItem)
            this.editedIndex = -1
            })
        },

        addData() {
            if (this.pmClass) {
                this.productDialog = true;
            } else if(this.cmClass) {
                this.categoryDialog = true;
            } else {
                this.productDialog = false;
                this.categoryDialog = false;
            }
        },
        searchData() {
            if (this.pmClass) {
                this.getProductList();
            } else if(this.cmClass) {
                this.getCategoryList();
            }else if(this.amClass) {
                this.getAttributeList();
            }
        },
        editAttribute(id){
            this.currentAttributeId = id;
            this.attributeDialog = true;
        },
        deleteData(id,type) {
            this.confirmModel = {
                id: id,
                title: `Do you want to delete this ${type}?`,
                description:
                "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
                type: type,
            };
        },
        confirmActions(data) {
            let type = "";
            if (data.type == "product") {
                type = "products/" + data.id;
            } else if (data.type == "category") {
                type = "category/" + data.id;
            } else {
                return false;
            }
            this.showLoader("Loading");
            let url = `venues/pos/management/${type}`;
            this.$http.delete(url).then((response) => {
                if (response.status == 200) {
                    const res = response.data;
                    if(res){
                        if (data.type == 'product') {
                            this.showSuccess("Product deleted successfully.");
                            this.getProductList();
                        } else if (data.type == "category") {
                            this.showSuccess("Category deleted successfully.");
                            this.getCategoryList();
                        }   
                    }
                }else{
                    this.hideLoader();
                }
            }).catch((error) => {
                 this.hideLoader();
                this.errorChecker(error);
            });

        },
    },
};
</script>
<style scoped>

table.pm-table {
  width: 100%;
}
table.pm-table tr th,
table.pm-table tr td {
  text-align: center;
  border-bottom: 1px solid #000;
}

.pm-table {
  border-collapse: collapse;
  border-radius: 6px;
  border-style: hidden; /* hide standard table (collapsed) border */
  box-shadow: 0 0 0 1px #dbdbdb; /* this draws the table border  */
}
.pm-table th,
.pm-table td,
.reciept_details_total_table th,
.reciept_details_total_table td {
  border: transparent !important;
  color: #112a45;
  border-bottom: 1px solid #dbdbdb !important;
  padding: 10px 0px;
}

.pm-table tr th {
  text-align: center;
  padding: 10px 0px;
}
.pm-table tr:first-child th {
  background-color: #E9F1F6;
}
.pm-table .order-number {
  text-decoration: underline;
  cursor: pointer;
  font-weight: 500;
}

table.pm-table.child {
  margin: 0 3%;
  width: 93%;
}
table.pm-table.child th {
  background-color: #112a45;
  color: #f7f7f5;
}

.dark-blue-color{
    color: #112A46;
}
.pm-title{
    color: var(--dark-blue, #112A46);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
}
.product-management .btn-tab{
    background: #FFF;
}
.product-management .btn-tab.active{
    color:#112A46;
}
.product-management .btn-add-product{
    color: var(--white, #FFF);
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    border-radius: 4px;
    background: var(--dark-blue, #112A46);
}
.product-management .pm-card {
    box-shadow: 0 0 0 1px #dbdbdb;
    border-radius: 6px;
}
.product-management .v-card__title.cart-title {
    padding: 20px 35px !important;
}
.product-management .v-card__title.cart-title .text-h5 {
    padding: 10px 0px;
    color: var(--dark-blue, #112A46);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}
</style>

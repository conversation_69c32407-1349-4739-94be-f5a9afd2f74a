<template>
  <v-container fluid no-gutters>
    <v-row v-if="global">
      <v-col sm="3"></v-col>
      <v-col sm="6" lg="6" style="text-align: center; min-height: 60px">
        <v-row
          justify="center"
          v-if="
            checkReadPermission($modules.sales.graph.slug) ||
            checkReadPermission($modules.sales.logs.slug) ||
            checkReadPermission($modules.sales.credits.slug) ||
            checkReadPermission($modules.sales.refunds.slug) ||
            checkReadPermission($modules.sales.void.slug)
          "
        >
          <transition name="slide-fade">
            <v-col
              align="center"
              cols="6"
              md="2"
              v-if="checkReadPermission($modules.sales.graph.slug)"
              v-show="btnShow"
            >
              <router-link :to="`/sales`">
                <v-icon color="#066a8c">mdi-finance</v-icon>
                <div class="salesBtn">SALES</div>
              </router-link>
            </v-col>
          </transition>
          <v-col
            align="center"
            cols="6"
            md="2"
            :class="[logClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.logs.slug)"
          >
            <router-link :to="`/logs`">
              <v-icon color="#066a8c">mdi-history</v-icon>
              <div class="salesBtn">LOGS</div>
            </router-link>
          </v-col>
          <v-col
            align="center"
            cols="6"
            md="2"
            :class="[creditClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.credits.slug)"
          >
            <router-link :to="`/credits`">
              <v-icon color="#066a8c">mdi-account-clock</v-icon>
              <div class="salesBtn">CREDITS</div>
            </router-link>
          </v-col>
          <v-col
            align="center"
            cols="6"
            md="2"
            :class="[refundClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.refunds.slug)"
          >
            <router-link :to="`/refunds`">
              <v-icon color="#066a8c">mdi-cash-refund</v-icon>
              <div class="salesBtn">REFUNDS</div>
            </router-link>
          </v-col>
          <v-col
            align="center"
            cols="6"
            md="2"
            :class="[cancellationClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.void.slug)"
          >
            <router-link :to="`/cancellations`">
              <v-icon color="#066a8c">mdi-file-cancel</v-icon>
              <div class="salesBtn">CANCELLATIONS</div>
            </router-link>
          </v-col>
        </v-row>
      </v-col>
      <v-spacer></v-spacer>
    </v-row>

    <div class="app">
      <div class="user-wrapper">
        <div>
          <div class="md-card md-theme-default">
            <div class="md-card-content">
              <v-row>
                <v-col md="3"
                  ><span class="header_title">Log Details</span></v-col
                >
                <v-spacer></v-spacer>
                <v-col md="4">
                  <div style="float: right">
                    <v-btn
                      v-if="exportPermission || checkPermission"
                      @click="salesReportExcelDownload"
                      class="mr-5"
                      dark
                      color="#066a8c"
                    >
                      <v-icon>mdi-download</v-icon>&nbsp;&nbsp;Export
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="2">
                  <v-autocomplete
                    v-model="columns"
                    outlined
                    label="Table Filter"
                    multiple
                    item-value="value"
                    item-text="name"
                    :items="columnData"
                  >
                    <template
                      v-if="columns.length == columnData.length"
                      v-slot:selection="{ index }"
                    >
                      <span v-if="index == 0">All Fields</span>
                    </template>
                    <template v-else v-slot:selection="{ item, index }">
                      <span v-if="index == 0">{{ item.name }}</span>
                      <span v-if="index == 1">, ...</span>
                      <!-- <span v-if="index === 1" class="grey--text caption pl-1">
                        and
                        {{ columns.length - 1 }}
                        other
                      </span> -->
                    </template>

                    <!-- <template v-slot:prepend-item>
                      <v-list-item ripple @click="toggle('columnData', 'columns',0)">
                        <v-list-item-action>
                          <v-icon
                            :color="
                               columns.length > 0
                                  ? 'indigo darken-4'
                                  : ''
                              "
                          >{{ getIcon('columnData', 'columns', 0) }}</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>All</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </template>-->
                  </v-autocomplete>
                </v-col>
                <v-col class="ml-4" cols="2">
                  <v-autocomplete
                    v-model="searchParams.time_intervel"
                    outlined
                    label="Time Period"
                    @change="changeFxn"
                    item-value="name"
                    item-text="title"
                    :items="timeDuration"
                  ></v-autocomplete>
                </v-col>

                <v-col class="ml-1" cols="4" v-if="flag">
                  <v-row>
                    <v-col cols="5" style="padding-right: 0">
                      <v-menu
                        v-model="menu1"
                        :close-on-content-click="false"
                        :nudge-right="40"
                        transition="scale-transition"
                        offset-y
                        min-width="290px"
                      >
                        <template v-slot:activator="{ on }">
                          <v-text-field
                            outlined
                            background-color="#fff"
                            class="text1"
                            v-model="date1Formatted"
                            readonly
                            v-on="on"
                          ></v-text-field>
                        </template>
                        <v-date-picker
                          v-model="searchParams.start_date"
                          @input="menu1 = false"
                        ></v-date-picker>
                      </v-menu>
                    </v-col>
                    <v-col cols="5">
                      <v-menu
                        v-model="menu2"
                        :close-on-content-click="false"
                        :nudge-right="40"
                        transition="scale-transition"
                        offset-y
                        min-width="290px"
                      >
                        <template v-slot:activator="{ on }">
                          <v-text-field
                            class="text2"
                            v-model="date2Formatted"
                            outlined
                            background-color="#fff"
                            readonly
                            v-on="on"
                          ></v-text-field>
                        </template>
                        <v-date-picker
                          v-model="searchParams.end_date"
                          @input="menu2 = false"
                        ></v-date-picker>
                      </v-menu>
                    </v-col>
                    <v-col cols="2">
                      <v-flex xs1 class="ml-1 mt-1">
                        <v-btn
                          color="#00b0af"
                          height="48"
                          dark
                          @click="searchData"
                          >Go</v-btn
                        >
                      </v-flex>
                    </v-col>
                  </v-row>
                </v-col>

                <v-spacer></v-spacer>
                <v-col md="2" class="mr-3">
                  <div style="width: 120px; float: right">
                    <v-select
                      outlined
                      v-model="perPage"
                      label="Per Page"
                      :items="[10, 15, 25, 50]"
                      @change="searchData"
                      :menu-props="{ bottom: true, offsetY: true }"
                    ></v-select>
                  </div>
                </v-col>
              </v-row>
            </div>

            <div class="md-card-content">
              <div>
                <div
                  class="md-content md-table md-theme-default table_borders"
                  table-header-color="orange"
                  value
                >
                  <div
                    class="
                      md-content md-table-content md-scrollbar md-theme-default
                    "
                  >
                    <table class="logTable">
                      <thead class="md-card-header">
                        <tr>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('timestamp')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div
                                class="md-table-head-label cursor-pointer"
                                @click="sortColumn('id')"
                              >
                                Timestamp
                                <v-icon class="ml-2" color="#fff" small>
                                  mdi-sort{{
                                    orderBy == "id"
                                      ? "-" +
                                        (orderDir == "ASC"
                                          ? "ascending"
                                          : "descending")
                                      : ""
                                  }}
                                </v-icon>
                              </div>
                              <div class="search_column mt-1">
                                <date-menu
                                  v-model="searchParams.timestamp"
                                  @change="getFilterData"
                                  :range="true"
                                >
                                </date-menu>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('invoice_no')"
                          >
                            <div
                              style="width: 200px"
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Invoice No</div>
                              <div class="search_column">
                                <v-text-field
                                  solo
                                  filled
                                  label="Invoice No"
                                  clearable
                                  v-model="searchParams.invoice_no"
                                  @change="getFilterData"
                                ></v-text-field>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('bill_no')"
                          >
                            <div
                              style="width: 200px"
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Transaction No
                              </div>
                              <div class="search_column">
                                <v-text-field
                                  solo
                                  filled
                                  label="Transaction No"
                                  clearable
                                  v-model="searchParams.bill_no"
                                  @change="getFilterData"
                                ></v-text-field>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="
                              checkTableColumnVisibility('original_trans_no')
                            "
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Original Transaction No
                              </div>
                              <div class="search_column">
                                <v-text-field
                                  solo
                                  filled
                                  label="Original transaction No"
                                  clearable
                                  v-model="searchParams.original_trans_no"
                                  @change="getFilterData"
                                ></v-text-field>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('cashier_name')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Cashier Name
                              </div>
                              <div class="search_column">
                                <v-autocomplete
                                  solo
                                  filled
                                  label="Name"
                                  v-model="searchParams.cashier_name"
                                  item-value="name"
                                  item-text="name"
                                  :items="cashierNames"
                                  :loading="isLoading"
                                  :search-input.sync="cashierName"
                                  @change="getFilterData"
                                ></v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('date')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div
                                class="md-table-head-label cursor-pointer"
                                @click="sortColumn('order_date')"
                              >
                                Due Date
                                <v-icon class="ml-2" color="#fff" small>
                                  mdi-sort{{
                                    orderBy == "order_date"
                                      ? "-" +
                                        (orderDir == "ASC"
                                          ? "ascending"
                                          : "descending")
                                      : ""
                                  }}
                                </v-icon>
                              </div>
                              <div class="search_column mt-1">
                                <date-menu
                                  v-model="searchParams.date"
                                  @change="getFilterData"
                                >
                                </date-menu>
                              </div>
                            </div>
                          </th>

                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('reason')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Refund Reason
                              </div>
                              <div class="search_column"></div>
                            </div>
                          </th>

                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('customer_type')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Customer Type
                              </div>
                              <div class="search_column">
                                <v-autocomplete
                                  label="Customer Type"
                                  :items="[
                                    { name: 'All', id: null },
                                    { name: 'Normal', id: 'normal' },
                                    { name: 'Corporate', id: 'corporate' },
                                    { name: 'Member', id: 'member' },
                                  ]"
                                  v-model="searchParams.customer_type"
                                  item-text="name"
                                  item-value="id"
                                  @change="searchData"
                                  solo
                                  filled
                                >
                                </v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('name')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Customer Name
                              </div>
                              <div class="search_column">
                                <v-autocomplete
                                  solo
                                  filled
                                  label="Name"
                                  v-model="searchParams.name"
                                  item-value="name"
                                  item-text="name"
                                  :items="names"
                                  :loading="isLoading"
                                  :search-input.sync="name"
                                  @change="getFilterData"
                                ></v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('mobile')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Mobile</div>
                              <div class="search_column">
                                <v-autocomplete
                                  solo
                                  filled
                                  label="Mobile"
                                  v-model="searchParams.mobile"
                                  item-value="mobile"
                                  item-text="mobile"
                                  :items="phones"
                                  :loading="isLoading"
                                  :search-input.sync="phone"
                                  @change="getFilterData"
                                ></v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('email')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Email</div>
                              <div class="search_column">
                                <v-autocomplete
                                  solo
                                  filled
                                  label="Email"
                                  v-model="searchParams.email"
                                  item-value="email"
                                  item-text="email"
                                  :items="emails"
                                  :loading="isLoading"
                                  :search-input.sync="email"
                                  @change="getFilterData"
                                ></v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            style="position: relative"
                            v-if="checkTableColumnVisibility('age')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Age</div>
                              <div class="search_column">
                                <v-select
                                  v-model="searchParams.age"
                                  :items="AgeRange"
                                  @change="ageChange"
                                  label="Age"
                                  ref="ageRef"
                                  solo
                                >
                                  <template v-slot:append-item>
                                    <v-divider class="mb-2"></v-divider>
                                    <div style="width: 150px">
                                      <v-list-item>
                                        <v-text-field
                                          label="Start Age"
                                          dense
                                          v-model="fromAge"
                                          append-icon="mdi-keyboard_arrow_down"
                                        ></v-text-field>
                                      </v-list-item>
                                      <v-list-item>
                                        <v-text-field
                                          label="End Age"
                                          dense
                                          v-model="toAge"
                                          append-icon="mdi-keyboard_arrow_down"
                                        ></v-text-field>
                                      </v-list-item>
                                      <v-list-item>
                                        <v-btn
                                          small
                                          color="primary"
                                          @click="ageOkClick"
                                          >Ok</v-btn
                                        >
                                      </v-list-item>
                                    </div>
                                  </template>
                                </v-select>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('gender')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Gender</div>
                              <div class="search_column">
                                <v-autocomplete
                                  v-model="searchParams.gender"
                                  solo
                                  filled
                                  label="Gender"
                                  :items="['All', 'Male', 'Female']"
                                  @change="getFilterData"
                                ></v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('country')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Country</div>
                              <div class="search_column">
                                <v-autocomplete
                                  solo
                                  filled
                                  label="Country"
                                  v-model="searchParams.country_id"
                                  item-value="id"
                                  item-text="name"
                                  :items="['All', ...countries]"
                                  @change="getFilterData"
                                ></v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            style="position: relative"
                            v-if="
                              checkTableColumnVisibility('type') &&
                              global == true
                            "
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Type</div>
                              <div class="search_column">
                                <v-select
                                  v-model="searchParams.product_type_id"
                                  label="Type"
                                  :items="[
                                    { id: null, name: 'All' },
                                    ...productTypes,
                                  ]"
                                  item-text="name"
                                  item-value="id"
                                  solo
                                  filled
                                  @change="searchData"
                                ></v-select>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('service')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Service</div>
                              <div class="search_column">
                                <v-select
                                  v-model="searchParams.services"
                                  label="Service"
                                  :items="venueServices"
                                  item-text="name"
                                  item-value="venue_service_id"
                                  multiple
                                  solo
                                  filled
                                  @change="searchData"
                                >
                                  <template
                                    v-if="
                                      searchParams.services.length ==
                                      venueServices.length
                                    "
                                    v-slot:selection="{ index }"
                                  >
                                    <span
                                      v-if="index == 0"
                                      class="ellipsis-small"
                                      >All Services</span
                                    >
                                  </template>
                                  <template
                                    v-else
                                    v-slot:selection="{ item, index }"
                                  >
                                    <span v-if="index == 0">
                                      {{ item.name }}
                                    </span>
                                    <span v-if="index == 1"> , ... </span>
                                  </template>

                                  <template v-slot:prepend-item>
                                    <v-list-item
                                      ripple
                                      @click="
                                        toggle('venueServices', 'services', 1)
                                      "
                                    >
                                      <v-list-item-action>
                                        <v-icon
                                          :color="
                                            searchParams.services.length > 0
                                              ? 'indigo darken-4'
                                              : ''
                                          "
                                          >{{
                                            getIcon(
                                              "venueServices",
                                              "services",
                                              1
                                            )
                                          }}</v-icon
                                        >
                                      </v-list-item-action>
                                      <v-list-item-content>
                                        <v-list-item-title
                                          >All</v-list-item-title
                                        >
                                      </v-list-item-content>
                                    </v-list-item>
                                  </template>
                                </v-select>
                              </div>
                            </div>
                          </th>

                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('title')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                {{
                                  searchParams.type != null
                                    ? searchParams.type
                                    : "Activity"
                                }}
                                <br />Name
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('product_name')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Product Name
                              </div>
                              <div class="search_column">
                                <v-autocomplete
                                  solo
                                  filled
                                  label="Product Name"
                                  v-model="searchParams.product_id"
                                  item-value="product_id"
                                  item-text="product_name"
                                  :items="product_names"
                                  :loading="isLoading"
                                  :search-input.sync="product_name"
                                  @change="getFilterData"
                                ></v-autocomplete>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('price')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Price</div>
                              <div class="search_column">
                                <v-menu
                                  v-model="priceMenu"
                                  :close-on-content-click="false"
                                  :nudge-width="50"
                                  max-width="150"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      label="Price"
                                      solo
                                      v-bind="attrs"
                                      v-on="on"
                                    ></v-text-field>
                                  </template>
                                  <v-card width="150">
                                    <v-card-text>
                                      <v-text-field
                                        dense
                                        v-model="searchParams.from_amount"
                                        label="Price From"
                                      ></v-text-field>
                                      <v-text-field
                                        dense
                                        v-model="searchParams.to_amount"
                                        label="Price To"
                                      ></v-text-field>
                                    </v-card-text>
                                    <v-card-actions>
                                      <v-spacer></v-spacer>
                                      <v-btn
                                        text
                                        @click="
                                          () => {
                                            searchParams.from_amount = null;
                                            searchParams.to_amount = null;
                                            priceMenu = false;
                                            searchData();
                                          }
                                        "
                                        >Clear</v-btn
                                      >
                                      <v-btn
                                        color="primary"
                                        @click="
                                          () => {
                                            priceMenu = false;
                                            searchData();
                                          }
                                        "
                                        >Filter</v-btn
                                      >
                                    </v-card-actions>
                                  </v-card>
                                </v-menu>
                              </div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('method')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Payment Method
                              </div>
                              <!-- <div class="search_column">
                                <v-select
                                  v-model="searchParams.payment_method_id"
                                  label="Payment Methods"
                                  :items="paymentMethods"
                                 item-text="name"
                                item-value="id"
                                  multiple
                                  solo
                                  filled
                                  @change="searchData"
                                >
                                 
                                </v-select>
                              </div> -->
                              <div class="search_column">
                                <v-select
                                  v-model="searchParams.payment_method"
                                  label="Payment Methods"
                                  :items="
                                    paymentMethods.concat(
                                      paymentMethodFromOnline
                                    )
                                  "
                                  item-text="name"
                                  item-value="id"
                                  multiple
                                  solo
                                  filled
                                  @change="searchData"
                                >
                                  <template
                                    v-if="
                                      searchParams.payment_method.length ==
                                      paymentMethods.length
                                    "
                                    v-slot:selection="{ index }"
                                  >
                                    <span v-if="index == 0" class="ellipsis"
                                      >All Payment Methods</span
                                    >
                                  </template>
                                  <template
                                    v-else
                                    v-slot:selection="{ item, index }"
                                  >
                                    <span v-if="index == 0">
                                      {{ item.name }}
                                    </span>
                                    <span v-if="index == 1"> , ... </span>
                                    <!-- <span
                                      v-if="index === 1"
                                      class="grey--text caption pl-1"
                                    >
                                      and
                                      {{
                                        searchParams.payment_method.length - 1
                                      }}
                                      other
                                    </span> -->
                                  </template>

                                  <template v-slot:prepend-item>
                                    <v-list-item
                                      ripple
                                      @click="
                                        toggle(
                                          'paymentMethods',
                                          'payment_method',
                                          1
                                        )
                                      "
                                    >
                                      <v-list-item-action>
                                        <v-icon
                                          :color="
                                            searchParams.payment_method.length >
                                            0
                                              ? 'indigo darken-4'
                                              : ''
                                          "
                                          >{{
                                            getIcon(
                                              "paymentMethods",
                                              "payment_method",
                                              1
                                            )
                                          }}</v-icon
                                        >
                                      </v-list-item-action>
                                      <v-list-item-content>
                                        <v-list-item-title
                                          >All</v-list-item-title
                                        >
                                      </v-list-item-content>
                                    </v-list-item>
                                  </template>
                                </v-select>
                              </div>
                            </div>
                          </th>
                          <!-- Transaction status -->
                          <th
                            class="md-table-head"
                            style="position: relative; min-width: 155px"
                            v-if="checkTableColumnVisibility('transaction')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">
                                Transaction Status
                              </div>

                              <div class="search_column" v-if="logClass">
                                <v-select
                                  v-model="searchParams.status_id"
                                  label="Status"
                                  :items="statuses"
                                  item-text="name"
                                  item-value="id"
                                  multiple
                                  solo
                                  filled
                                  @change="searchData"
                                >
                                  <template
                                    v-if="
                                      searchParams.status_id != null &&
                                      searchParams.status_id.length ==
                                        statuses.length
                                    "
                                    v-slot:selection="{ index }"
                                  >
                                    <span
                                      v-if="index == 0"
                                      class="ellipsis-small"
                                      >All Status</span
                                    >
                                  </template>
                                  <template
                                    v-else
                                    v-slot:selection="{ item, index }"
                                  >
                                    <span v-if="index == 0">
                                      {{ item.name }}
                                    </span>
                                    <span v-if="index == 1"> , ... </span>
                                  </template>

                                  <template v-slot:prepend-item>
                                    <v-list-item
                                      ripple
                                      @click="
                                        toggle('statuses', 'status_id', 1)
                                      "
                                    >
                                      <v-list-item-action>
                                        <v-icon
                                          :color="
                                            searchParams.status_id != null &&
                                            searchParams.status_id.length > 0
                                              ? 'indigo darken-4'
                                              : ''
                                          "
                                          >{{
                                            getIcon("statuses", "status_id", 1)
                                          }}</v-icon
                                        >
                                      </v-list-item-action>
                                      <v-list-item-content>
                                        <v-list-item-title
                                          >All</v-list-item-title
                                        >
                                      </v-list-item-content>
                                    </v-list-item>
                                  </template>
                                </v-select>
                              </div>
                            </div>
                          </th>
                          <!-- order status -->
                          <th
                            class="md-table-head"
                            style="position: relative; min-width: 155px"
                            v-if="checkTableColumnVisibility('transaction')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Status</div>

                              <div class="search_column" v-if="logClass">
                                <v-select
                                  v-model="searchParams.order_status_id"
                                  label="Status"
                                  :items="orderStatuses"
                                  item-text="name"
                                  item-value="id"
                                  multiple
                                  solo
                                  filled
                                  @change="searchData"
                                >
                                  <template
                                    v-if="
                                      searchParams.order_status_id != null &&
                                      searchParams.order_status_id.length ==
                                        statuses.length
                                    "
                                    v-slot:selection="{ index }"
                                  >
                                    <span
                                      v-if="index == 0"
                                      class="ellipsis-small"
                                      >All Status</span
                                    >
                                  </template>
                                  <template
                                    v-else
                                    v-slot:selection="{ item, index }"
                                  >
                                    <span v-if="index == 0">
                                      {{ item.name }}
                                    </span>
                                    <span v-if="index == 1"> , ... </span>
                                  </template>

                                  <template v-slot:prepend-item>
                                    <v-list-item
                                      ripple
                                      @click="
                                        toggle(
                                          'orderStatuses',
                                          'order_status_id',
                                          1
                                        )
                                      "
                                    >
                                      <v-list-item-action>
                                        <v-icon
                                          :color="
                                            searchParams.order_status_id !=
                                              null &&
                                            searchParams.order_status_id
                                              .length > 0
                                              ? 'indigo darken-4'
                                              : ''
                                          "
                                          >{{
                                            getIcon(
                                              "orderStatuses",
                                              "order_status_id",
                                              1
                                            )
                                          }}</v-icon
                                        >
                                      </v-list-item-action>
                                      <v-list-item-content>
                                        <v-list-item-title
                                          >All</v-list-item-title
                                        >
                                      </v-list-item-content>
                                    </v-list-item>
                                  </template>
                                </v-select>
                              </div>
                            </div>
                          </th>
                          <th class="md-table-head">
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Attendance</div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('reciept')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Bill</div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('ticket')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Ticket</div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="
                              checkTableColumnVisibility('schedule') && !eventId
                            "
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Details</div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="
                              creditClass &&
                              checkTableColumnVisibility('schedule')
                            "
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Action</div>
                            </div>
                          </th>
                          <th
                            class="md-table-head"
                            v-if="checkTableColumnVisibility('order_notes')"
                          >
                            <div
                              class="
                                md-table-head-container md-ripple md-disabled
                              "
                            >
                              <div class="md-table-head-label">Notes</div>
                            </div>
                          </th>
                        </tr>
                      </thead>

                      <tbody>
                        <tr
                          class="md-table-row"
                          v-for="(data, index) in logDatas"
                          :key="data.id"
                        >
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('timestamp')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.timestamp | timeStamp }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('invoice_no')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.invoice_seq_no }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('bill_no')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.order_seq_no }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="
                              checkTableColumnVisibility('original_trans_no')
                            "
                          >
                            <div class="md-table-cell-container">
                              {{ data.original_trans_no }}
                            </div>
                          </td>

                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('cashier_name')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.cashier_name }}
                            </div>
                          </td>

                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('date')"
                          >
                            <div class="md-table-cell-container">
                              <div class="user_date">
                                {{ data.order_date | dayFormat }}
                              </div>

                              <!-- <div class="book_time_log">
                                {{ data.start_time.substring(0, 5) }} -
                                {{ data.end_time.substr(0, 5) }}
                              </div>-->
                            </div>
                          </td>

                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('reason')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.reason || "--" }}
                            </div>
                          </td>

                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('customer_type')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.customer_type || "NA" }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('name')"
                          >
                            <div class="md-table-cell-container">
                              <router-link
                                v-if="
                                  checkReadPermission(
                                    $modules.clients.customers.id
                                  ) && data.customer_id
                                "
                                :to="`/clients/customers/` + data.customer_id"
                                >{{ data.first_name }}
                                {{ data.last_name }}</router-link
                              >

                              <v-btn
                                v-else-if="data.customer_id"
                                small
                                color="#00b0af"
                                dark
                                @click="showUserModel(data.customer_id)"
                                >{{ data.first_name }}
                                {{ data.last_name || "" }}</v-btn
                              >
                              <span v-else>Anonymous</span>
                            </div>
                          </td>

                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('mobile')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.mobile || "NA" }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('email')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.email || "NA" }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('age')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.age || "NA" }}
                            </div>
                          </td>

                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('country')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.country || "NA" }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('type') && global"
                          >
                            <div class="md-table-cell-container">
                              {{ data.product_type || "--" }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('service')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.service_name || "--" }}
                            </div>
                          </td>

                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('title')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.title || "NA" }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('product_name')"
                          >
                            <div
                              v-if="data.product_name"
                              class="md-table-cell-container product-names-div"
                            >
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <span v-bind="attrs" v-on="on">
                                    {{
                                      getSingleProductName(data.product_name)
                                    }}
                                  </span>
                                </template>
                                <span
                                  class="d-block product-name"
                                  v-for="(
                                    product_name, index
                                  ) in data.product_name.split(',')"
                                  :key="index"
                                  >{{ product_name }}</span
                                >
                              </v-tooltip>
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('price')"
                          >
                            <div
                              class="md-table-cell-container text-capitalize"
                            >
                              {{ Number(Math.abs(data.total)) | toCurrency }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('method')"
                          >
                            <div
                              class="md-table-cell-container text-capitalize"
                            >
                              {{
                                data.status != "Refund"
                                  ? data.payment_method_name
                                  : data.refund_payment_method_name || "NA"
                              }}
                              {{
                                data.card_number
                                  ? "- (" +
                                    data.card_type +
                                    " " +
                                    data.card_number.slice(-4) +
                                    ")"
                                  : ""
                              }}
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('transaction')"
                          >
                            <div
                              class="md-table-cell-container text-capitalize"
                            >
                              {{ data.status || "NA" }}
                              <!-- <span
                                v-if="
                                  data.status == 'Refund' &&
                                  data.refund_payment_method_name
                                "
                                >({{ data.refund_payment_method_name }})</span
                              > -->
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('transaction')"
                          >
                            <div
                              class="md-table-cell-container text-capitalize"
                            >
                              {{ data.order_status_name || "NA" }}
                            </div>
                          </td>
                          <td class="md-table-cell" v-if="!cancellationClass">
                            <div class="md-table-cell-container">
                              <v-btn
                                v-if="
                                  data.facility_booking_id != null &&
                                  data.is_capacity == 1
                                "
                                normal
                                small
                                @click="openParticipants(data)"
                              >
                                <span>View</span>
                              </v-btn>
                              <span v-else>NA</span>
                            </div>
                          </td>
                          <td class="md-table-cell" v-if="!cancellationClass">
                            <div class="md-table-cell-container">
                              <v-btn
                                normal
                                small
                                @click="getOrderDetails(data)"
                              >
                                <span
                                  v-if="
                                    data.status == 'Unpaid' ||
                                    (data.paid_at == null &&
                                      !['Rescheduled'].includes(
                                        data.order_status_name
                                      ))
                                  "
                                  >Invoice</span
                                >
                                <span v-else-if="data.status == 'Refund'"
                                  >Refund Bill</span
                                >
                                <span v-else>Receipt</span>
                              </v-btn>
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('ticket')"
                          >
                            <div class="md-table-cell-container">
                              <v-btn normal small @click="viewTicket(index)"
                                >Ticket</v-btn
                              >
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="
                              checkTableColumnVisibility('schedule') && !eventId
                            "
                          >
                            <div class="md-table-cell-container">
                              <v-btn
                                normal
                                small
                                @click="gotoDetails(index)"
                                v-if="
                                  data.status != 'Refund' &&
                                  data.status != 'Cancelled' &&
                                  data.status != 'Void' &&
                                  data.order_status_name !=
                                    'Reservation Cancel' &&
                                  data.order_status_name != 'Cancelled' &&
                                  data.order_status_name != 'Rescheduled' &&
                                  data.product_type != 'Credit Settlement' &&
                                  data.order_status_name != 'Refund' &&
                                  data.order_status_name != 'Cancelled'
                                "
                                >Details</v-btn
                              >
                              <span v-else>NA</span>
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="
                              creditClass &&
                              checkTableColumnVisibility('schedule')
                            "
                          >
                            <div class="md-table-cell-container">
                              <v-btn
                                normal
                                small
                                v-if="data.status != 'Refund'"
                                @click="creditSettle(data.order_id)"
                                >Settle</v-btn
                              >
                              <span v-if="data.status == 'Refund'">NA</span>
                            </div>
                          </td>
                          <td
                            class="md-table-cell"
                            v-if="checkTableColumnVisibility('order_notes')"
                          >
                            <div class="md-table-cell-container">
                              {{ data.order_notes || "--" }}
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <v-pagination
                  v-model="page"
                  :length="totalPages"
                ></v-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <order-details
      :id="orderId"
      :ids="orderIds"
      :log="true"
      @close="((orderId = null), (orderIds = null)), searchData()"
    ></order-details>
    <credit-order-payment
      :id="creditOrderId"
      @close="creditOrderId = null"
      @payed="(creditOrderId = null), searchData()"
      @cancel="(creditOrderId = null), searchData()"
    ></credit-order-payment>
    <attendance
      @open-booking="openBooking"
      v-bind="participant"
      @close="participant.showParticipants = false"
      :refresh="refresh"
    ></attendance>
    <ticket-model v-bind="ticketData" @close="ticketData = { id: null }" />
    <customer-model v-bind="userModel" @close="userModel.userID = null" />
  </v-container>
</template>

<script>
import moment from "moment";
import { mapGetters } from "vuex";
import OrderDetails from "../../components/Order/OrderDetails.vue";
import CreditOrderPayment from "../../components/Order/CreditOrderPayment.vue";
import TicketModel from "../../components/Ticket/TicketModel.vue";
import CustomerModel from "./../Clients/Customer/CustomerModel";
import Attendance from "./Attendance";

export default {
  props: {
    global: {
      type: Boolean,
      default: true,
    },
    eventId: {
      type: Number,
      default: null,
    },
    promotionId: {
      type: Number,
      default: null,
    },
    exportPermission: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    OrderDetails,
    TicketModel,
    CustomerModel,
    CreditOrderPayment,
    Attendance,
  },
  data() {
    return {
      items: [],
      sports: [],
      datas: [],
      logDatas: [],
      searchFlag: false,
      date1: moment().subtract(30, "days").format("YYYY-MM-DD"),
      date2: moment().format("YYYY-MM-DD"),
      searchParams: {
        services: [],
        payment_method: [],
        timestamp: [],
        status_id: null,
        start_date: moment().subtract(30, "days").format("YYYY-MM-DD"),
        end_date: moment().format("YYYY-MM-DD"),
      },
      menu1: false,
      menu2: false,
      flag: false,
      getGroundbyID: [],
      timeDuration: [
        { name: "All", title: "All" },
        { name: "week", title: "This Week" },
        { name: "year", title: "This Year" },
        { name: "month", title: "This Month" },
        { name: "custom", title: "Custom Duration" },
      ],
      orderBy: "id",
      orderDir: "DESC",
      status_id: null,
      logClass: false,
      creditClass: false,
      refundClass: false,
      cancellationClass: false,
      page: 1,
      totalPages: 1,
      perPage: null,
      fieldData: {},
      btnShow: false,
      receiptData: { id: null },
      ticketData: { id: null },
      product_names: [],
      product_name: null,
      names: [],
      name: null,
      cashierNames: [],
      cashierName: null,
      country_id: null,
      payment_method_id: null,
      phones: [],
      phone: null,
      emails: [],
      email: null,
      isLoading: false,
      datemenu: false,
      duemenu: false,
      AgeRange: ["All", "18-25", "26-30", "31-35"],
      Age: [],
      toAge: null,
      fromAge: null,
      columnData: [],
      columnData_list: [
        { name: "Timestamp", value: "timestamp" },
        { name: "Transaction No", value: "bill_no" },
        { name: "Invoice No", value: "invoice_no" },
        { name: "Original Transaction No", value: "original_trans_no" },
        { name: "Cashier name", value: "cashier_name" },
        { name: "Due Date", value: "date" },
        { name: "Customer Type", value: "customer_type" },
        { name: "Customer Name", value: "name" },
        { name: "Customer Mobile", value: "mobile" },
        { name: "Customer Email", value: "email" },
        { name: "Booking Type", value: "type" },
        { name: "Service", value: "service" },
        { name: "Product Name", value: "product_name" },

        // { name: "Event/Class Name", value: "title" },
        // { name: 'Location', value: 'location' },
        { name: "Price", value: "price" },
        { name: "Payment Method", value: "method" },
        { name: "Transation Status", value: "transaction" },
        { name: "Receipt", value: "reciept" },
        { name: "Ticket", value: "ticket" },
        { name: "Schedule Details", value: "schedule" },
        { name: "Notes", value: "order_notes" },
      ],
      columns: [],
      columns_list: [
        "timestamp",
        "bill_no",
        "invoice_no",
        "original_trans_no",
        "cashier_name",
        "date",
        "name",
        "type",
        "service",
        "product_name",
        "price",
        "method",
        "reciept",
        "ticket",
        "schedule",
        "status",
        "transaction",
      ],
      //  paymentMethod: ["Cash", "Card", "Voucher", "Credit", "Complementary"],
      booking_details: {},
      priceMenu: false,
      orderId: null,
      creditOrderId: null,
      checkPermission: false,
      userModel: { userID: null, type: "details" },
      showParticipants: false,
      participant: {},
      refresh: false,
      paymentMethodFromOnline: [
        {
          id: 15,
          name: "Online",
          status_id: 1,
          type: "online",
        },
        {
          id: 16,
          name: "Krews",
          status_id: 1,
          type: "online",
        },
      ],
      orderIds: null,
    };
  },
  watch: {
    page() {
      this.searchData();
    },
    name(val) {
      if (val == "" || val == null) return;
      if (this.isLoading) return;
      this.filterSearch("name", val);
    },
    cashierName(val) {
      if (val == "" || val == null) return;
      if (this.isLoading) return;
      this.filterSearch("cashier_name", val);
    },
    phone(val) {
      if (val == "" || val == null) return;
      if (this.isLoading) return;
      this.filterSearch("mobile", val);
    },
    email(val) {
      if (val == "" || val == null) return;
      if (this.isLoading) return;
      this.filterSearch("email", val);
    },
    product_name(val) {
      if (val == "" || val == null) return;
      if (this.isLoading) return;
      this.productFilterSearch("product_name", val);
    },

    $route(to) {
      if (to.name == "Credits") {
        this.logClass = false;
        this.creditClass = true;
        this.refundClass = false;
        this.cancellationClass = false;
        this.status_id = 7;
        this.checkPermission = this.checkExportPermission(
          this.$modules.sales.credits.slug
        );
        this.searchData();
        this.columns = this.columns_list.filter((e) => e !== "reason");
        this.columns.push("schedule");
        this.columnData = this.columnData_list.filter(
          (e) => e.value !== "reason"
        );
      } else if (to.name == "Refunds") {
        this.logClass = false;
        this.creditClass = false;
        this.refundClass = true;
        this.cancellationClass = false;
        this.status_id = 8;
        this.searchData();
        this.checkPermission = this.checkExportPermission(
          this.$modules.sales.refunds.slug
        );
        this.columns = this.columns_list.filter(
          (e) => e !== "schedule" && e !== "invoice_no"
        );
        this.columns.push("reason");
        this.columnData = this.columnData_list;
      } else if (to.name == "Cancellations") {
        this.logClass = false;
        this.creditClass = false;
        this.refundClass = false;
        this.cancellationClass = true;
        this.status_id = 13;
        this.searchData();
        this.checkPermission = this.checkExportPermission(
          this.$modules.sales.logs.slug
        );
        this.columns = [];
        this.columns = this.columns_list.filter(
          (e) =>
            e === "timestamp" ||
            e === "bill_no" ||
            e === "original_trans_no" ||
            e === "cashier_name" ||
            e === "date" ||
            e === "name" ||
            e === "type" ||
            e === "service" ||
            e === "price" ||
            e === "status"
        );
        this.columnData = [];
        this.columnData = this.columnData_list.filter(
          (e) =>
            e.value === "timestamp" ||
            e.value === "bill_no" ||
            e.value === "original_trans_no" ||
            e.value === "cashier_name" ||
            e.value === "date" ||
            e.value === "name" ||
            e.value === "type" ||
            e.value === "service" ||
            e.value === "price" ||
            e === "status"
        );
      } else {
        this.logClass = true;
        this.creditClass = false;
        this.refundClass = false;
        this.cancellationClass = false;
        this.status_id = [4, 5, 8, 17, 21, 22];
        this.searchData();
        this.checkPermission = this.checkExportPermission(
          this.$modules.sales.logs.slug
        );
        this.columns = [];
        this.columns = this.columns_list.filter(
          (e) => e !== "original_trans_no"
        );
        this.columns.push("schedule");
        this.columnData = [];
        this.columnData = this.columnData_list.filter(
          (e) => e.value !== "original_trans_no"
        );
      }
    },
  },
  computed: {
    ...mapGetters({
      getVenueServices: "getVenueServices",
      getSportsCategory: "getSportsCategory",
    }),
    date1Formatted() {
      return moment(this.searchParams.start_date, "YYYY-MM-DD").format(
        "Do MMM YYYY"
      );
    },
    date2Formatted() {
      return moment(this.searchParams.end_date, "YYYY-MM-DD").format(
        "Do MMM YYYY"
      );
    },
    venueServices() {
      return this.$store.getters.getVenueServices.data;
    },
    countries() {
      return this.$store.getters.getCountries.data;
    },
    venueCode() {
      return this.$store.getters.venueInfo.code.toUpperCase();
    },
    productTypes() {
      return this.$store.getters.getProductTypes.data;
    },
    paymentMethods() {
      return this.$store.getters.getPaymentMethods.data;
    },
    statuses() {
      return this.$store.getters.getStatuses.data;
    },
    orderStatuses() {
      return this.$store.getters.getOrderStatuses.data;
    },
  },
  mounted() {
    this.productFilterSearch("product_name", "");
    if (this.$router.currentRoute.name == "Logs") {
      this.status_id = [4, 5, 8, 17, 21, 22];
      this.logClass = true;
      this.creditClass = false;
      this.columns = [];
      this.columns = this.columns_list.filter((e) => e !== "original_trans_no");
      this.columns.push("schedule");
      this.columnData = [];
      this.columnData = this.columnData_list.filter(
        (e) => e.value !== "original_trans_no"
      );

      this.checkPermission = this.checkExportPermission(
        this.$modules.sales.logs.slug
      );
    } else if (this.$router.currentRoute.name == "Credits") {
      this.logClass = false;
      this.creditClass = true;
      this.status_id = 7;
      this.columns = this.columns_list.filter((e) => e !== "reason");
      this.columns.push("schedule");
      this.columnData = this.columnData_list.filter(
        (e) => e.value !== "reason"
      );
      this.checkPermission = this.checkExportPermission(
        this.$modules.sales.credits.slug
      );
    } else if (this.$router.currentRoute.name == "Refunds") {
      this.logClass = false;
      this.creditClass = false;
      this.refundClass = true;
      this.cancellationClass = false;
      this.status_id = 8;
      this.checkPermission = this.checkExportPermission(
        this.$modules.sales.refunds.slug
      );
      this.columns = this.columns_list.filter(
        (e) => e !== "schedule" && e !== "invoice_no"
      );
      this.columns.push("reason");
      this.columnData = this.columnData_list;
    } else if (this.$router.currentRoute.name == "Cancellations") {
      this.logClass = false;
      this.creditClass = false;
      this.refundClass = false;
      this.cancellationClass = true;
      this.status_id = 13;
      this.searchData();
      this.checkPermission = this.checkExportPermission(
        this.$modules.sales.logs.slug
      );
      this.columns = this.columns_list.filter(
        (e) =>
          e === "timestamp" ||
          e === "bill_no" ||
          e === "original_trans_no" ||
          e === "cashier_name" ||
          e === "date" ||
          e === "name" ||
          e === "type" ||
          e === "service" ||
          e === "price" ||
          e === "status"
      );

      this.columnData = this.columnData_list.filter(
        (e) =>
          e.value === "timestamp" ||
          e.value === "bill_no" ||
          e.value === "original_trans_no" ||
          e.value === "cashier_name" ||
          e.value === "date" ||
          e.value === "name" ||
          e.value === "type" ||
          e.value === "service" ||
          e.value === "price" ||
          e === "status"
      );
    } else {
      this.columns = [];
      this.columns = this.columns_list.filter((e) => e !== "original_trans_no");
      this.columns.push("schedule");
      this.columnData = [];
      this.columnData = this.columnData_list.filter(
        (e) => e.value !== "original_trans_no"
      );
    }
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch("loadCountries");
    }
    if (this.$store.getters.getProductTypes.status == false) {
      this.$store.dispatch("loadProductTypes");
    }
    if (this.$store.getters.getPaymentMethods.status == false) {
      this.$store.dispatch("loadPaymentMethods", "normal");
    }
    if (this.$store.getters.getStatuses.status == false) {
      this.$store.dispatch("loadStatuses").then(() => {});
    } else {
      this.searchParams.status_id = [];
      this.statuses.forEach((element) => {
        if (element.id) {
          this.searchParams.status_id.push(element.id);
        }
      });
      // this.searchData();
    }

    if (this.$store.getters.getOrderStatuses.status == false) {
      this.$store.dispatch("loadOrderStatuses").then(() => {
        this.searchParams.order_status_id = [];
        this.searchData();
      });
    } else {
      this.searchParams.order_status_id = [];
      console.log("search data 8");
      this.searchData();
    }

    setTimeout(() => {
      this.btnShow = true;
    }, 10);

    if (this.$store.getters.getCardTypes.status == false) {
      this.$store.dispatch("loadCardTypes").then((response) => {
        response.forEach((el) => {
          this.paymentMethodFromOnline.push({
            id: el.id.toString(),
            name: "Card" + " ( " + el.name + " )",
            status_id: 1,
            type: "online",
          });
        });
      });
    } else {
      this.$store.getters.getCardTypes.data.forEach((el) => {
        this.paymentMethodFromOnline.push({
          id: el.id.toString(),
          name: "Card" + " ( " + el.name + " )",
          status_id: 1,
          type: "online",
        });
      });
    }
  },
  methods: {
    showUserModel(userId) {
      this.userModel.userID = parseInt(userId);
      this.userModel.type = "details";
    },
    toggle(type, field, searchP) {
      this.$nextTick(() => {
        if (searchP) {
          if (
            this.searchParams[field] != null &&
            this.searchParams[field].length == this[type].length
          ) {
            this.searchParams[field] = [];
          } else {
            this.searchParams[field] = this[type].map((x) => x.id);
          }
        } else {
          if (this[field].length == this[type].length) {
            this[field] = [];
          } else {
            this.searchParams[field] = this[type];
          }
        }
        this.searchData();
      });
      this.$forceUpdate();
    },

    getOrderDetails(data) {
      if (data.parent_order_ids) {
        this.orderIds = [data.order_id];
        data.parent_order_ids.split(",").forEach((id) => {
          this.orderIds.push(Number(id));
        });
      } else {
        this.orderId = data.order_id;
      }
    },

    creditSettle(id) {
      this.creditOrderId = id;
    },

    getIcon(type, field, searchP) {
      let icon = "mdi-checkbox-blank-outline";
      if (searchP) {
        if (
          this.searchParams[field] != null &&
          this.searchParams[field].length == this[type].length
        ) {
          icon = "mdi-close-box";
        }
        if (
          this.searchParams[field] != null &&
          this.searchParams[field].length > 0 &&
          this.searchParams[field].length != this[type].length
        )
          icon = "mdi-minus-box";
      } else {
        if (this[field].length == this[type].length) {
          icon = "mdi-close-box";
        }
        if (this[field].length > 0 && this[field].length != this[type].length)
          icon = "mdi-minus-box";
      }
      return icon;
    },
    customerForward(id) {
      this.$router.push(
        {
          name: "CustomersByID",
          params: { id: id },
        },
        () => {}
      );
    },
    ageOkClick() {
      if (this.fromAge != null && this.toAge != null) {
        this.searchParams.from_age = this.fromAge;
        this.searchParams.to_age = this.toAge;
        let customAgeRange = this.fromAge + "-" + this.toAge;
        this.AgeRange.push(customAgeRange);
        this.searchParams.age = customAgeRange;
        this.toAge = null;
        this.fromAge = null;
        this.$refs.ageRef.blur();
        this.searchData();
      }
    },
    ageChange() {
      if (this.searchParams.age != "All") {
        let data = this.searchParams.age.split("-");
        this.searchParams.from_age = data[0];
        this.searchParams.to_age = data[1];
        this.searchData();
      } else {
        this.allAge();
      }
    },
    allAge() {
      delete this.searchParams.from_age;
      delete this.searchParams.to_age;
      delete this.searchParams.age;
      this.searchData();
    },
    filterSearch(type, val) {
      this.isLoading = true;
      this.$http
        .get("venues/customers/filters/?field=" + type + "&search=" + val)
        .then((response) => {
          if (response.status == 200 && response.data.status) {
            this.isLoading = false;
            if (type == "name") {
              this.names = response.data.data;
              this.names.unshift(val);
              this.names.unshift("All");
            } else if (type == "mobile") {
              this.phones = response.data.data;
              this.phones.unshift(val);
              this.phones.unshift("All");
            } else if (type == "email") {
              this.emails = response.data.data;
              this.emails.unshift(val);
              this.emails.unshift("All");
            } else if (type == "cashier_name") {
              this.cashierNames = response.data.data;
              this.cashierNames.unshift(val);
              this.cashierNames.unshift("All");
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    productFilterSearch(type, val) {
      this.isLoading = true;
      this.$http
        .get("venues/products/filters/?field=" + type + "&search=" + val)
        .then((response) => {
          if (response.status == 200 && response.data.status) {
            this.isLoading = false;
            if (type == "product_name") {
              this.product_names = response.data.data;
              this.product_names.unshift(val);
              this.product_names.unshift("All");
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getFilterData() {
      this.page = 1;
      this.searchData();
    },
    searchData() {
      this.showLoader("Loading..");
      let url = "";
      if (
        typeof this.$route.name != "undefined" &&
        this.$route.name == "Refunds"
      ) {
        url += "&status=2";
      }

      if (
        this.searchParams.time_intervel != "All" &&
        this.searchParams.time_intervel != null
      ) {
        // this.searchParams.end_date = moment().format("YYYY-MM-DD");
        if (this.searchParams.time_intervel == "custom") {
          this.flag = true;
        } else if (this.searchParams.time_intervel == "week") {
          this.searchParams.start_date = moment()
            .startOf("week")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "month") {
          this.searchParams.start_date = moment()
            .startOf("month")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "year") {
          this.searchParams.start_date = moment()
            .startOf("year")
            .format("YYYY-MM-DD");
        }
        url += "&end_date=" + this.searchParams.end_date;
        url += "&start_date=" + this.searchParams.start_date;
      }
      if (this.searchParams.product_type_id != null) {
        url += `&product_type_id=${this.searchParams.product_type_id}`;
      }
      if (
        this.searchParams.product_id != null &&
        this.searchParams.product_id != "All"
      ) {
        url += `&product_id=${this.searchParams.product_id}`;
      }
      if (
        typeof this.searchParams.reason != "undefined" &&
        this.searchParams.reason.length > 0
      ) {
        url += `&reason=${this.searchParams.reason}`;
      }
      if (
        this.searchParams.services != "All" &&
        this.searchParams.services != null &&
        this.searchParams.services.length > 0
      ) {
        url += `&venue_service_id=${this.searchParams.services}`;
      }
      if (this.searchParams.date == null) {
        this.searchParams.date = null;
      } else if (typeof this.searchParams.date != "undefined") {
        url += "&date=" + this.searchParams.date;
      }
      if (this.searchParams.timestamp == null) {
        delete this.searchParams.timestamp;
      } else if (typeof this.searchParams.timestamp != "undefined") {
        if (this.searchParams.timestamp.length > 1) {
          this.searchParams.timestamp.sort((a, b) => new Date(a) - new Date(b));
        }
        url += "&timestamp=" + this.searchParams.timestamp;
      }
      if (this.searchParams.name == "All") {
        delete this.searchParams.name;
      } else if (this.searchParams.name) {
        url += "&name=" + this.searchParams.name;
      }

      if (this.searchParams.cashier_name == "All") {
        delete this.searchParams.cashier_name;
      } else if (this.searchParams.cashier_name) {
        url += "&cashier_name=" + this.searchParams.cashier_name;
      }

      if (this.searchParams.email == "All") {
        delete this.searchParams.email;
      } else if (this.searchParams.email) {
        url += "&email=" + this.searchParams.email;
      }
      if (this.searchParams.mobile == "All") {
        delete this.searchParams.mobile;
      } else if (this.searchParams.mobile) {
        url += "&mobile=" + this.searchParams.mobile;
      }
      if (this.searchParams.gender == "All") {
        this.searchParams.gender = null;
      } else if (this.searchParams.gender) {
        url += "&gender=" + this.searchParams.gender;
      }
      if (this.searchParams.customer_type == null) {
        delete this.searchParams.customer_type;
      } else if (this.searchParams.customer_type) {
        url += "&customer_type=" + this.searchParams.customer_type;
      }
      if (this.searchParams.country_id == "All") {
        delete this.searchParams.country_id;
      } else if (this.searchParams.country_id) {
        url += "&country_id=" + this.searchParams.country_id;
      }
      if (
        typeof this.searchParams.from_age != "undefined" &&
        typeof this.searchParams.to_age != "undefined"
      ) {
        url +=
          "&from_age=" +
          this.searchParams.from_age +
          "&to_age=" +
          this.searchParams.to_age;
      }
      if (this.searchParams.from_amount) {
        url += "&from_amount=" + this.searchParams.from_amount;
      }
      if (this.searchParams.to_amount) {
        url += "&to_amount=" + this.searchParams.to_amount;
      }
      if (this.searchParams.bill_no) {
        url += "&bill_no=" + this.searchParams.bill_no;
      }
      if (this.searchParams.invoice_no) {
        url += "&invoice_no=" + this.searchParams.invoice_no;
      }

      if (
        this.searchParams.payment_method != "All" &&
        this.searchParams.payment_method != null &&
        this.searchParams.payment_method.length > 0
      ) {
        let cardTypeId = [];
        let paymentMethod = [];
        this.searchParams.payment_method.forEach((ex) => {
          if (typeof ex == "string") {
            cardTypeId.push(Number(ex));
          } else {
            paymentMethod.push(ex);
            if (ex == 6) {
              paymentMethod.push(11);
            }
          }
        });

        if (this.searchParams.payment_method[0] == 11) {
          this.searchParams.payment_method = [];
          paymentMethod = [];
        }
        if (paymentMethod.length) {
          url += `&payment_method=${paymentMethod}`;
        }

        if (cardTypeId.length) {
          url += `&card_type_id=${cardTypeId}`;
        }
      }

      if (
        this.searchParams.order_status_id != "All" &&
        this.searchParams.order_status_id != null &&
        this.searchParams.order_status_id.length > 0
      ) {
        url += `&order_status_id=${this.searchParams.order_status_id}`;
      } else {
        this.searchParams.order_status_id = null;
      }

      if (this.eventId > 0) {
        url += "&event_id=" + this.eventId;
      }

      if (this.promotionId > 0) {
        url += "&promotion_id=" + this.promotionId;
      }

      if (this.refundClass) {
        url += "&status_id=8&refund=true";
      } else if (this.cancellationClass) {
        url += "&status_id=13&order_status_id=13,23";
      } else if (
        this.searchParams.status_id != null &&
        this.searchParams.status_id.length > 0 &&
        !this.creditClass
      ) {
        url += "&status_id=" + this.searchParams.status_id;
      } else if (this.status_id != null && !this.creditClass) {
        url += "&status_id=" + this.status_id;
      } else if (this.creditClass) {
        url += "&credit";
      }

      this.searchParams.page = this.page;
      this.searchParams.per_page = this.perPage != null ? this.perPage : 10;
      this.searchParams.order_by = this.orderBy;
      this.searchParams.order_by_value = this.orderDir;
      this.$http
        .get(
          "venues/sales?page=" +
            this.page +
            "&per_page=" +
            (this.perPage != null ? this.perPage : 10) +
            "&order_by=" +
            this.orderBy +
            "&sort_order=" +
            this.orderDir +
            url
        )
        .then((response) => {
          if (response.status == 200) {
            this.hideLoader();
            this.logDatas = response.data.data;
            this.totalPages = response.data.total_pages;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getInvoiceItems() {
      this.receiptData.items = [];
      this.receiptData.discount = false;
      this.receiptData.booking_id = this.booking_details.booking_id;
      if (this.booking_details.promotion != null) {
        this.receiptData.discount = true;
        this.receiptData.promotion = true;
        this.receiptData.promotion_name =
          this.booking_details.promotion.promotion_name;
        this.receiptData.promotion_type = `${
          this.booking_details.promotion.benefit_type == "P"
            ? this.booking_details.promotion.benefit_amount + "% Discount"
            : this.booking_details.promotion.benefit_amount +
              this.$store.getters.currencyCode +
              "  OFF"
        }`;
        this.receiptData.original_price =
          this.booking_details.promotion.original_price;
      }
      if (
        this.booking_details.products != null &&
        this.booking_details.products.length > 0
      ) {
        this.receiptData.items = this.booking_details.products.map((item) => {
          let vat = (item.price * 5) / 100;
          let data = {
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            discount: "",
            discount_amount: "",
            vat_rate: "5%",
            vat_amount: vat,
            total_price: vat + parseFloat(item.price),
          };
          if (
            this.booking_details.promotion != null &&
            item.original_price != null &&
            this.booking_details.promotion.benefit_type == "P"
          ) {
            data.discount =
              item.benefit_type == "P" ? item.benefit_amount + "%" : "";
            data.discount_amount = data.price;
            data.price = item.original_price;
          }
          return data;
        });
      } else {
        if (parseFloat(this.booking_details.tax_amount) == 0) {
          this.receiptData.disable_vat = true;
        }
        let title =
          this.receiptData.title +
          (this.receiptData.type == "EVENTBOOKING"
            ? "(" + this.receiptData.ticket_type + ")"
            : "");
        let data = {
          title: title,
          quantity: this.booking_details.total_quanity,
          price: parseFloat(this.booking_details.price),
          discount: 0,
          discount_amount: 0,
          vat_rate: parseFloat(this.booking_details.tax_amount) > 0 ? "5%" : 0,
          vat_amount: parseFloat(this.booking_details.tax_amount),
          total_price: parseFloat(this.booking_details.total_price),
        };
        if (
          this.booking_details.promotion != null &&
          this.booking_details.promotion.benefit_type == "P"
        ) {
          data.discount = `${
            this.booking_details.promotion.benefit_type == "P"
              ? this.booking_details.promotion.benefit_amount + "% Discount"
              : this.booking_details.promotion.benefit_amount +
                this.$store.getters.currencyCode +
                "  OFF"
          }`;
          data.discount_amount = this.booking_details.promotion.promotion_price;
          data.price = this.booking_details.promotion.original_price;
        }
        this.receiptData.items.push(data);
      }
    },
    viewTicket(index) {
      let data = this.logDatas[index];
      this.ticketData = {};
      this.ticketData.timestamp = data.timestamp;
      this.ticketData.order_date = data.order_date;
      this.ticketData.start_time = data.start_time;
      this.ticketData.end_time = data.end_time;
      this.ticketData.type = data.product_type;
      this.ticketData.service = data.service_name;
      this.ticketData.id = String(data.order_id);
    },
    changeFxn() {
      if (this.searchParams.time_intervel == "custom") {
        this.searchParams.start_date = moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD");
        this.searchParams.end_date = moment().format("YYYY-MM-DD");
        this.flag = true;
      } else {
        this.flag = false;
        this.searchData();
      }
    },
    sortColumn(type) {
      if (type == this.orderBy) {
        this.orderDir = this.orderDir == "ASC" ? "DESC" : "ASC";
      } else {
        this.orderDir = "DESC";
      }
      this.orderBy = type;
      this.searchData();
    },
    checkTableColumnVisibility(column) {
      let index = this.columns.findIndex((item) => item == column);
      if (index == -1) {
        return false;
      }
      return true;
    },
    gotoDetails(index) {
      let data = this.logDatas[index];
      let venue_service = this.venueServices.find(
        (x) => x.venue_service_id == data.venue_service_id
      );
      if (data.product_type == "Voucher") {
        this.$router.push({
          name: "VoucherSales",
          params: { data: btoa(data.voucher_id) },
        });
      } else if (data.product_type == "Event") {
        this.$router.push({
          name: "EventScheduleWithParams",
          params: {
            data: btoa(
              JSON.stringify({
                venue_service: venue_service,
                date: data.order_date,
                order_id: data.order_id,
              })
            ),
          },
        });
      } else if (data.product_type == "Trainer") {
        this.$router.push({
          name: "TrainerCustomers",
          params: { data: btoa(data.trainer_id) },
        });
      } else if (data.product_type == "Facility") {
        this.$router.push({
          name: "ScheduleWithParams",
          params: {
            data: btoa(
              JSON.stringify({
                venue_service: venue_service,
                date: data.order_date,
                order_id: data.order_id,
              })
            ),
          },
        });
      } else if (data.product_type == "Membership") {
        this.$router.push({
          name: "MembershipView",
          params: { data: btoa(data.membership_id) },
        });
      } else if (data.product_type == "Academy") {
        this.$router.push({
          name: "WorkshopView",
          params: { data: btoa(JSON.stringify({ id: data.workshop_id })) },
        });
      }
    },
    salesReportExcelDownload() {
      this.showLoader("Downloading report");
      let url = "";
      if (
        typeof this.$route.name != "undefined" &&
        this.$route.name == "Refunds"
      ) {
        url += "&status=2";
      }

      if (
        this.searchParams.time_intervel != "All" &&
        this.searchParams.time_intervel != null
      ) {
        this.searchParams.end_date = moment().format("YYYY-MM-DD");
        if (this.searchParams.time_intervel == "custom") {
          this.searchParams.start_date = moment()
            .subtract(30, "days")
            .format("YYYY-MM-DD");
          this.flag = true;
        } else if (this.searchParams.time_intervel == "week") {
          this.searchParams.start_date = moment()
            .startOf("week")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "month") {
          this.searchParams.start_date = moment()
            .startOf("month")
            .format("YYYY-MM-DD");
        } else if (this.searchParams.time_intervel == "year") {
          this.searchParams.start_date = moment()
            .startOf("year")
            .format("YYYY-MM-DD");
        }
        url += "&end_date=" + this.searchParams.end_date;
        url += "&start_date=" + this.searchParams.start_date;
      }
      if (this.searchParams.product_type_id != null) {
        url += `&product_type_id=${this.searchParams.product_type_id}`;
      }
      if (
        typeof this.searchParams.reason != "undefined" &&
        this.searchParams.reason.length > 0
      ) {
        url += `&reason=${this.searchParams.reason}`;
      }
      if (
        this.searchParams.services != "All" &&
        this.searchParams.services != null &&
        this.searchParams.services.length > 0
      ) {
        url += `&venue_service_id=${this.searchParams.services}`;
      }
      if (this.searchParams.date == null) {
        this.searchParams.date = null;
      } else if (typeof this.searchParams.date != "undefined") {
        url += "&date=" + this.searchParams.date;
      }
      if (this.searchParams.timestamp == null) {
        delete this.searchParams.timestamp;
      } else if (typeof this.searchParams.timestamp != "undefined") {
        url += "&timestamp=" + this.searchParams.timestamp;
      }
      if (this.searchParams.name == "All") {
        this.searchParams.name = null;
      } else if (typeof this.searchParams.name != "undefined") {
        url += "&name=" + this.searchParams.name;
      }
      if (this.searchParams.email == "All") {
        delete this.searchParams.email;
      } else if (typeof this.searchParams.email != "undefined") {
        url += "&email=" + this.searchParams.email;
      }
      if (this.searchParams.mobile == "All") {
        delete this.searchParams.mobile;
      } else if (typeof this.searchParams.mobile != "undefined") {
        url += "&mobile=" + this.searchParams.mobile;
      }
      if (this.searchParams.gender == "All") {
        delete this.searchParams.gender;
      } else if (typeof this.searchParams.gender != "undefined") {
        url += "&gender=" + this.searchParams.gender;
      }
      if (this.searchParams.customer_type == null) {
        delete this.searchParams.customer_type;
      } else if (typeof this.searchParams.customer_type != "undefined") {
        url += "&customer_type=" + this.searchParams.customer_type;
      }
      if (this.searchParams.country_id == "All") {
        delete this.searchParams.country_id;
      } else if (typeof this.searchParams.country_id != "undefined") {
        url += "&country_id=" + this.searchParams.country_id;
      }

      if (this.searchParams.cashier_name == "All") {
        delete this.searchParams.cashier_name;
      } else if (this.searchParams.cashier_name) {
        url += "&cashier_name=" + this.searchParams.cashier_name;
      }

      if (
        typeof this.searchParams.from_age != "undefined" &&
        typeof this.searchParams.to_age != "undefined"
      ) {
        url +=
          "&from_age=" +
          this.searchParams.from_age +
          "&to_age=" +
          this.searchParams.to_age;
      }
      if (this.searchParams.from_amount != null) {
        url += "&from_amount=" + this.searchParams.from_amount;
      }
      if (this.searchParams.to_amount != null) {
        url += "&to_amount=" + this.searchParams.to_amount;
      }

      if (this.searchParams.bill_no) {
        url += "&bill_no=" + this.searchParams.bill_no;
      }
      if (this.searchParams.invoice_no) {
        url += "&invoice_no=" + this.searchParams.invoice_no;
      }

      if (this.eventId > 0) {
        url += "&event_id=" + this.eventId;
      }

      if (this.promotionId > 0) {
        url += "&promotion_id=" + this.promotionId;
      }
      // if (typeof this.searchParams.payment_method_id != "undefined") {
      //   url += "&payment_method=" + this.searchParams.payment_method_id;
      // }

      if (
        this.searchParams.payment_method != "All" &&
        this.searchParams.payment_method != null &&
        this.searchParams.payment_method.length > 0
      ) {
        let cardTypeId = [];
        let paymentMethod = [];
        this.searchParams.payment_method.forEach((ex) => {
          if (typeof ex == "string") {
            cardTypeId.push(Number(ex));
          } else {
            paymentMethod.push(ex);
            if (ex == 6) {
              paymentMethod.push(11);
            }
          }
        });

        if (this.searchParams.payment_method[0] == 11) {
          this.searchParams.payment_method = [];
          paymentMethod = [];
        }
        if (paymentMethod.length) {
          url += `&payment_method=${paymentMethod}`;
        }

        if (cardTypeId.length) {
          url += `&card_type_id=${cardTypeId}`;
        }
      }

      if (
        this.searchParams.order_status_id != "All" &&
        this.searchParams.order_status_id != null &&
        this.searchParams.order_status_id.length > 0
      ) {
        url += `&order_status_id=${this.searchParams.order_status_id}`;
      } else {
        this.searchParams.order_status_id = null;
      }
      if (this.refundClass) {
        url += "&status_id=8&refund=true";
      } else if (this.cancellationClass) {
        url += "&status_id=13";
      } else if (this.searchParams.status_id != null && !this.creditClass) {
        url += "&status_id=" + this.searchParams.status_id;
      } else if (this.status_id != null && !this.creditClass) {
        url += "&status_id=" + this.status_id;
      } else {
        url += "&credit";
      }
      this.searchParams.page = this.page;
      this.searchParams.per_page = this.perPage != null ? this.perPage : 10;
      this.searchParams.order_by = this.orderBy;
      this.searchParams.order_by_value = this.orderDir;
      this.$http
        .get(
          "venues/sales/report/download?page=" +
            this.page +
            "&per_page=" +
            (this.perPage != null ? this.perPage : 10) +
            "&order_by=" +
            this.orderBy +
            "&sort_order=" +
            this.orderDir +
            url,
          {
            responseType: "blob",
          }
        )
        .then((response) => {
          this.hideLoader();
          if (response.status == 200) {
            this.downloadFile(response, "SalesReport_");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    openParticipants(data) {
      this.participant = {
        showParticipants: true,
        order_id: data.order_id,
      };
    },
    openBooking(data) {
      this.openBookingForm(data);
    },
    openBookingForm(data) {
      this.bookingForm = {
        showBookingForm: true,
        start_time: moment(data.facility_booking_start_time, "hh:mm a").format(
          "HH:mm:ss"
        ),
        end_time: moment(data.facility_booking_end_time, "hh:mm a").format(
          "HH:mm:ss"
        ),
        facility_name: data.facility_name,
        facility_id: data.facility_id,
        date: data.facility_booking_date,
        increment: data.increment,
        venue_service_id: data.venue_service_id,
        service: data.service_name,
        id: data.id != null ? data.id : 0,
        order_id: data.order_id,
      };
    },
    getSingleProductName(data) {
      if (data) {
        let product_name = data.split(",");
        if (product_name.length > 1) {
          return product_name[0] + " ...";
        } else {
          return product_name[0];
        }
      } else {
        return "";
      }
    },
  },
};
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
.salesBtn {
  padding: 5px;
  font-size: 14px;
  font-weight: bold;
  color: #066a8c;
}
.btn_bg {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
}
.book_time_log {
  color: #066a8c;
}
.md-card-header tr th:nth-child(1) {
  text-align: left !important;
}
.search_customer .v-input {
  margin: 0 0px;
}
.headline_ticket {
  background: #000;
  color: #fff;
}
.head {
  text-align: center;
  margin: 0 auto;
}
.headTicket {
  color: gray;
  font-weight: bold;
}
.valueTicket {
  color: #fff;
  border-bottom: 1px solid gray;
}
.sch_btn_class,
.sch_btn_event {
  padding: 0 35px !important;
}
.logTable tbody tr:hover {
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -webkit-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  -moz-box-shadow: 0px 0px 5px 2px rgba(13, 84, 139, 0.3) !important;
  z-index: 1;
}
</style>

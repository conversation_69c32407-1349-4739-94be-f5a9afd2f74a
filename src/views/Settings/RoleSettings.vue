<template>
  <v-container>
    <v-row>
      <v-col></v-col>
      <v-spacer></v-spacer>
      <v-col lg="4" sm="4" style="text-align: center">
        <v-btn-toggle
          borderless
          class="button_navigation"
          v-model="status"
          mandatory
          @change="getUserRolesList"
        >
          <v-btn value="1">Active</v-btn>

          <v-btn value="2">Inactive</v-btn>
        </v-btn-toggle>
      </v-col>
      <v-spacer></v-spacer>
      <v-col class="text-lg-right" lg="2" sm="4">

      <v-btn
          v-if="checkWritePermission($modules.settings.roles.slug)"
          class="bg-blue text-white text-capitalize"
          height="48"
          @click="addRole"
      >
        <AddIcon class="mr-2"/>
        Add Role
      </v-btn>
      </v-col>
    </v-row>
    <v-row>
      <template v-for="(role, i) in roles">
        <v-col :key="i" lg="4" md="4">
          <role-card
            v-bind="role"
            @edit="showRoleDetails"
            @delete="deleteRole"
          ></role-card>
        </v-col>
      </template>
    </v-row>
    <v-dialog v-model="role_dialog" max-width="1000" scrollable>
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-title class="border-bottom px-5">
            <div class=" w-full">
              <div class="d-flex justify-space-between align-center">
                <p class="mb-0 font-medium">
                  {{ editFlag ? "Edit" : "Add" }} Role
                </p>
                <v-btn class="shadow-0" fab x-small @click="role_dialog = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </v-card-title>
          <v-card-text class="pt-4">
            <v-row>
              <v-col :md="hasChildVenues ? 6: 12" :lg="hasChildVenues ? 6: 12" sm="12">
                <label>Role Name</label>
                <v-text-field
                  persistent-hint
                  dense
                  outlined
                  class="q-text-field shadow-0"
                  hide-details="auto"
                  v-model="role.title"
                  :readonly="
                    !(typeof role.venue_id == 'undefined') &&
                    role.venue_id == null
                  "
                  background-color="#fff"
                ></v-text-field>
              </v-col>
              <v-col md="6" lg="6" sm="12" v-if="hasChildVenues">
                <label for="">Target Venue(s) </label>
                <v-autocomplete
                    :items="venues"
                    v-model="targetVenueIds"
                    item-value="id"
                    item-text="name"
                    outlined
                    multiple
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    hide-details="auto"
                    dense
                >
                  <template v-slot:prepend-item>
                    <v-list-item ripple @click="toggleVenueSelect()">
                      <v-list-item-action>
                        <v-icon :color="targetVenueIds.length > 0 ? 'teal darken-4' : ''">{{
                            getVenueServiceIcon()
                          }}</v-icon>
                      </v-list-item-action>
                      <v-list-item-content>
                        <v-list-item-title>All</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                  <template
                      v-if="targetVenueIds.length === venues.length"
                      v-slot:selection="{ index }"
                  >
                    <span v-if="index === 0">All Venues</span>
                  </template>
                  <template v-else v-slot:selection="{ item, index }">
                    <span v-if="index === 0">{{ item.name }}</span>
                    <span v-if="index === 1">, ...</span>
                  </template>
                </v-autocomplete>
              </v-col>
              <div class="w-full d-flex justify-space-between align-center px-2">
                <div class="q-titles">Module Selected</div>
                <div>
                  <v-checkbox
                      @change="toggleSelectAll"
                      class="mx-2"
                      label="Select all"
                  ></v-checkbox>
                </div>
              </div>
              <v-col md="12">
                <v-row>
                  <v-col
                    md="3"
                    v-for="(modules, mIndex) in modules"
                    :key="`m_${mIndex}`"
                    style="margin-top: -30px"
                  >
                    <v-checkbox
                      v-model="modulesSelected"
                      :value="modules.module_id"
                      class="mx-2"
                      :readonly="modules.module_name == userProfileModule"
                      :label="`${modules.module_name}`"
                    ></v-checkbox>
                  </v-col>
                </v-row>
              </v-col>
              <div class="q-titles">Role Permissions</div>
              <v-col md="12">
                <v-expansion-panels>
                  <template v-for="(modules, mIndex) in role.permissions">
                    <v-expansion-panel
                      v-if="
                        modules.module_id != null &&
                        checkModuleExist(modules.module_id)
                      "
                      :key="mIndex"
                    >
                      <v-expansion-panel-header>{{
                        modules.module_name
                      }}</v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <div class="px-4">
                          <table class="table border-collapse">
                            <thead>
                            <tr class="opacity-70 tr-neon tr-rounded">
                              <th><b>Module</b></th>
                              <th
                                  class="pointer"
                                  @click="checkAllSubModules('read', mIndex)"
                              >
                                <b>Read</b>
                                <v-icon
                                    :color="
                                    checkSubModulesPermission(
                                      'allow_read',
                                      mIndex
                                    ) == 1
                                      ? 'success'
                                      : checkSubModulesPermission(
                                          'allow_read',
                                          mIndex
                                        ) == 0
                                      ? 'error'
                                      : 'warning'
                                  "
                                    right
                                >
                                  {{
                                    checkSubModulesPermission(
                                        "allow_read",
                                        mIndex
                                    ) == 1
                                        ? "mdi-check"
                                        : checkSubModulesPermission(
                                            "allow_read",
                                            mIndex
                                        ) == 0
                                            ? "mdi-close"
                                            : "mdi-minus"
                                  }}</v-icon
                                >
                              </th>
                              <th
                                  class="pointer"
                                  @click="checkAllSubModules('write', mIndex)"
                              >
                                <b>Write</b>
                                <v-icon
                                    :color="
                                    checkSubModulesPermission(
                                      'allow_write',
                                      mIndex
                                    ) == 1
                                      ? 'success'
                                      : checkSubModulesPermission(
                                          'allow_write',
                                          mIndex
                                        ) == 0
                                      ? 'error'
                                      : 'warning'
                                  "
                                    right
                                >
                                  {{
                                    checkSubModulesPermission(
                                        "allow_write",
                                        mIndex
                                    ) == 1
                                        ? "mdi-check"
                                        : checkSubModulesPermission(
                                            "allow_write",
                                            mIndex
                                        ) == 0
                                            ? "mdi-close"
                                            : "mdi-minus"
                                  }}</v-icon
                                >
                              </th>
                              <th
                                  class="pointer"
                                  @click="checkAllSubModules('delete', mIndex)"
                              >
                                <b>Delete</b>
                                <v-icon
                                    :color="
                                    checkSubModulesPermission(
                                      'allow_delete',
                                      mIndex
                                    ) == 1
                                      ? 'success'
                                      : checkSubModulesPermission(
                                          'allow_delete',
                                          mIndex
                                        ) == 0
                                      ? 'error'
                                      : 'warning'
                                  "
                                    right
                                >
                                  {{
                                    checkSubModulesPermission(
                                        "allow_delete",
                                        mIndex
                                    ) == 1
                                        ? "mdi-check"
                                        : checkSubModulesPermission(
                                            "allow_delete",
                                            mIndex
                                        ) == 0
                                            ? "mdi-close"
                                            : "mdi-minus"
                                  }}</v-icon
                                >
                              </th>
                              <th
                                  class="pointer"
                                  @click="checkAllSubModules('export', mIndex)"
                              >
                                <b>Export</b>
                                <v-icon
                                    :color="
                                    checkSubModulesPermission(
                                      'allow_export',
                                      mIndex
                                    ) == 1
                                      ? 'success'
                                      : checkSubModulesPermission(
                                          'allow_export',
                                          mIndex
                                        ) == 0
                                      ? 'error'
                                      : 'warning'
                                  "
                                    right
                                >
                                  {{
                                    checkSubModulesPermission(
                                        "allow_export",
                                        mIndex
                                    ) == 1
                                        ? "mdi-check"
                                        : checkSubModulesPermission(
                                            "allow_export",
                                            mIndex
                                        ) == 0
                                            ? "mdi-close"
                                            : "mdi-minus"
                                  }}</v-icon
                                >
                              </th>
                              <th
                                  class="pointer"
                                  @click="checkAllSubModules('backfill', mIndex)"
                              >
                                <b>Backfill</b>
                                <v-icon
                                    :color="
                                    checkSubModulesPermission(
                                      'allow_backfill',
                                      mIndex
                                    ) == 1
                                      ? 'success'
                                      : checkSubModulesPermission(
                                          'allow_backfill',
                                          mIndex
                                        ) == 0
                                      ? 'error'
                                      : 'warning'
                                  "
                                    right
                                >
                                  {{
                                    checkSubModulesPermission(
                                        "allow_backfill",
                                        mIndex
                                    ) == 1
                                        ? "mdi-check"
                                        : checkSubModulesPermission(
                                            "allow_backfill",
                                            mIndex
                                        ) == 0
                                            ? "mdi-close"
                                            : "mdi-minus"
                                  }}</v-icon
                                >
                              </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr
                                v-for="(permission, index) in modules.sub_modules"
                                :key="permission.sub_module_id"
                                class="border-bottom"
                            >
                              <td>
                                {{ permission.sub_module_name }}

                                <v-tooltip slot="append" top v-if="permission.description">
                                  <template v-slot:activator="{ on }">
                                    <v-icon color="primary" dark v-on="on">
                                      mdi-information
                                    </v-icon>
                                  </template>
                                  <span
                                  >{{ permission.description }}</span
                                  >
                                </v-tooltip>
                              </td>
                              <td>
                                <v-btn
                                    icon
                                    :color="
                                    permission.allow_read == 1
                                      ? 'success'
                                      : 'error'
                                  "
                                    @click="
                                    changeModulePermission(
                                      'read',
                                      mIndex,
                                      index
                                    )
                                  "
                                >
                                  <v-icon>
                                    {{
                                      permission.allow_read == 1
                                          ? "mdi-check"
                                          : "mdi-close"
                                    }}
                                  </v-icon>
                                </v-btn>
                              </td>
                              <td>
                                <v-btn
                                    icon
                                    :color="
                                    permission.allow_write == 1
                                      ? 'success'
                                      : 'error'
                                  "
                                    @click="
                                    changeModulePermission(
                                      'write',
                                      mIndex,
                                      index
                                    )
                                  "
                                >
                                  <v-icon>
                                    {{
                                      permission.allow_write == 1
                                          ? "mdi-check"
                                          : "mdi-close"
                                    }}
                                  </v-icon>
                                </v-btn>
                              </td>
                              <td>
                                <v-btn
                                    icon
                                    :color="
                                    permission.allow_delete == 1
                                      ? 'success'
                                      : 'error'
                                  "
                                    @click="
                                    changeModulePermission(
                                      'delete',
                                      mIndex,
                                      index
                                    )
                                  "
                                >
                                  <v-icon>
                                    {{
                                      permission.allow_delete == 1
                                          ? "mdi-check"
                                          : "mdi-close"
                                    }}
                                  </v-icon>
                                </v-btn>
                              </td>
                              <td>
                                <v-btn
                                    icon
                                    :color="
                                    permission.allow_export == 1
                                      ? 'success'
                                      : 'error'
                                  "
                                    @click="
                                    changeModulePermission(
                                      'export',
                                      mIndex,
                                      index
                                    )
                                  "
                                >
                                  <v-icon>
                                    {{
                                      permission.allow_export == 1
                                          ? "mdi-check"
                                          : "mdi-close"
                                    }}
                                  </v-icon>
                                </v-btn>
                              </td>
                              <td>
                                <v-btn
                                    icon
                                    :color="
                                    permission.allow_backfill == 1
                                      ? 'success'
                                      : 'error'
                                  "
                                    @click="
                                    changeModulePermission(
                                      'backfill',
                                      mIndex,
                                      index
                                    )
                                  "
                                >
                                  <v-icon>
                                    {{
                                      permission.allow_backfill == 1
                                          ? "mdi-check"
                                          : "mdi-close"
                                    }}
                                  </v-icon>
                                </v-btn>
                              </td>
                            </tr>
                            </tbody>
                          </table>
                        </div>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </template>
                </v-expansion-panels>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="ma-2 text-capitalize" text @click="role_dialog = false">Close</v-btn>
            <v-btn class="ma-2 white--text blue-color shadow-0" @click="addUpdateRole" >{{ editFlag ? "Update" : "Add" }} Role</v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
  </v-container>
</template>

<script>
import RoleCard from "@/components/Settings/RoleCard";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";
import AddIcon from "@/assets/images/misc/plus-icon.svg";
export default {
  components: {
    AddIcon,
    RoleCard,
    ConfirmModel,
  },
  data() {
    return {
      roles: [],
      editFlag: false,
      role_dialog: false,
      modules: [],
      valid: false,
      status: 1,
      role: { permissions: [] },
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      modulesSelected: [],
      targetVenueIds:[],
    };
  },

  computed: {
    hasChildVenues() {
      return this.$store.getters.venueInfo.sub_venues;
    },
    venues(){
      const subVenues = this.$store.getters.getSubVenues?.data || [];
      const currentVenueId = this.$store.getters.userInfo?.venue_id;

      return subVenues.filter(venue => venue.id !== currentVenueId);
    },
    userProfileModule() {
      return process.env.VUE_APP_USER_PROFILE_MODULE;
    },
  },
  mounted() {
    this.getVenueSubscriptions();

    if (this.hasChildVenues && this.$store.getters.getSubVenues.status == false) {
      this.$store.dispatch("loadSubVenues");
    }
  },
  methods: {
    toggleVenueSelect() {
      this.$nextTick(() => {
        if (this.targetVenueIds.length == this.venues.length) {
          this.targetVenueIds = [];
        } else {
          this.targetVenueIds = this.venues.map((item) => item.id);
        }
      });
    },
    getVenueServiceIcon() {
      if (this.targetVenueIds.length == 0) return "mdi-checkbox-blank-outline";
      if (this.venues.length == this.targetVenueIds.length)
        return "mdi-close-box";
      return "mdi-minus-box";
    },
    toggleSelectAll(isChecked) {
      if (isChecked) {
        this.modulesSelected = this.modules.map(e => e.module_id);
      } else {
        this.modulesSelected = this.modules.filter(e => e.module_name == this.userProfileModule).map(e => e.module_id);
      }
    },
    addRole() {
      this.role = {};
      this.editFlag = false;
      this.role.permissions = this.modules;
      this.modulesSelected = this.modules.map((item) => item.module_id);
      this.role_dialog = true;
    },
    checkModuleExist(moduleID) {
      let check = this.modulesSelected.find((item) => item == moduleID);
      return check != null ? true : false;
    },
    getUserRolesList() {
      this.$http
        .get(`venues/roles?status_id=${this.status}`)
        .then((response) => {
          if (response.status == 200) this.roles = response.data.data;
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getVenueSubscriptions() {
      this.$http
        .get("venues/profile/subscriptions")
        .then((response) => {
          if (response.status == 200) {
            this.modules = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    changeModulePermission(type, moduleIndex, subIndex) {
      let subModule = this.role.permissions[moduleIndex].sub_modules[subIndex];

      if (type == "read") {
        if (subModule.module_name == this.userProfileModule) {
          this.showError("Error! User basic read permission cannot be changed");
          return;
        }
        this.role.permissions[moduleIndex].sub_modules[subIndex].allow_read =
          subModule.allow_read == 1 ? 0 : 1;
        if (
          this.role.permissions[moduleIndex].sub_modules[subIndex].allow_read ==
          0
        ) {
          if (subModule.allow_write == 1) {
            this.role.permissions[moduleIndex].sub_modules[
              subIndex
            ].allow_write = 0;
          }
          if (subModule.allow_delete == 1) {
            this.role.permissions[moduleIndex].sub_modules[
              subIndex
            ].allow_delete = 0;
          }
          if (subModule.allow_export == 1) {
            this.role.permissions[moduleIndex].sub_modules[
              subIndex
            ].allow_export = 0;
          }
          if (subModule.allow_backfill == 1) {
            this.role.permissions[moduleIndex].sub_modules[
              subIndex
            ].allow_backfill = 0;
          }
        }
      } else if (type == "write") {
        this.role.permissions[moduleIndex].sub_modules[subIndex].allow_write =
          subModule.allow_write == 1 ? 0 : 1;
      } else if (type == "delete") {
        this.role.permissions[moduleIndex].sub_modules[subIndex].allow_delete =
          subModule.allow_delete == 1 ? 0 : 1;
      } else if (type == "export") {
        this.role.permissions[moduleIndex].sub_modules[subIndex].allow_export =
          subModule.allow_export == 1 ? 0 : 1;
      } else if (type == "backfill") {
        this.role.permissions[moduleIndex].sub_modules[
          subIndex
        ].allow_backfill = subModule.allow_backfill == 1 ? 0 : 1;
      }
      this.$forceUpdate();
    },
    checkAllSubModules(type, moduleIndex) {
      this.role.permissions[moduleIndex].sub_modules.forEach(
        (subModule, index) => {
          this.changeModulePermission(type, moduleIndex, index);
        }
      );
    },
    checkSubModulesPermission(type, moduleIndex) {
      let checkIndex = this.role.permissions[moduleIndex].sub_modules.findIndex(
        (item) => item[type] == 1
      );
      let crossIndex = this.role.permissions[moduleIndex].sub_modules.findIndex(
        (item) => item[type] == 0
      );
      if (checkIndex != -1 && crossIndex != -1) {
        return 2;
      } else if (checkIndex >= 0) {
        return 1;
      } else if (crossIndex >= 0) {
        return 0;
      }
    },
    showRoleDetails(id) {
      this.$http
        .get("venues/roles/" + id)
        .then((response) => {
          if (response.status == 200) {
            let data = response.data.data;
            this.role.title =
              typeof data.title != "undefined" ? data.title : "";
            this.role.id = typeof data.id != "undefined" ? data.id : "";
            this.role.venue_id =
              typeof data.venue_id != "undefined" ? data.venue_id : "";
            this.modulesSelected = data.permissions.map(
              (item) => item.module_id
            );
            this.role.permissions = data.permissions;
            this.modules.forEach((permission) => {
              if (!this.checkModuleExist(permission.module_id)) {
                this.role.permissions.push(permission);
              }
            });
            this.targetVenueIds = data.target_venue_ids;
            this.role_dialog = true;
            this.editFlag = true;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    addUpdateRole() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      event.preventDefault();
      this.showLoader();
      this.role.role_permissions = [];
      this.role.permissions.forEach((permission) => {
        if (this.checkModuleExist(permission.module_id)) {
          this.role.role_permissions.push(...permission.sub_modules);
        }
      });
      this.role.target_venue_ids = this.targetVenueIds;
      // delete this.role.permissions;
      this.$http({
        method: this.role.id != null ? "put" : "post",
        url: `venues/roles${this.role.id != null ? "/" + this.role.id : ""}`,
        data: this.role,
      })
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.role_dialog = false;
            this.showSuccess(response.data.message);

            this.getUserRolesList();
          }else if(response.status == 200 && response.data.status == false){
            this.role_dialog = false;
            this.confirmModel = {
              id: this.role,
              title: `Role cannot be edited`,
              description:
                  "By clicking <b>Yes</b> you can continue to create a copy of this role.  Do you need to continue your action ?",
              type: "create_copy",
            };
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        }).finally(() => {
        this.hideLoader();
      });
    },
    copyChildRole(obj){
      this.$http({
        method:"post",
        url: `venues/roles/replicate-in-child${this.role.id != null ? "/" + this.role.id : ""}`,
        data: obj,
      })
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess(response.data.message);
            this.getUserRolesList();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        }).finally(() => {
        this.hideLoader();
      });
    },
    deleteRole(data) {
      this.confirmModel = {
        id: data.id,
        title: `Do you want to ${data.status} this role?`,
        description:
          "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "delete",
      };
    },
    confirmActions(data) {
      if (data.type == "delete") {
        this.changeRoleStatus(data.id);
      }
      if(data.type == 'create_copy'){
        this.copyChildRole(data.id);
      }
      this.confirmModel.id = null;
    },
    changeRoleStatus(id) {
      this.$http
        .delete("venues/roles/" + id)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.getUserRolesList();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
  },
};
</script>

<style scoped>
.permission_table {
  width: 100%;
  text-align: center !important;
}
.permission_table th {
  text-align: center !important;
}
.permission_table td,
.permission_table th {
  border: 1px solid #ddd;
  padding: 8px;
}

.permission_table tr {
  background-color: #f2f2f2;
}
.permission_table tbody td:first-child {
  font-weight: bolder;
  font-size: 13px;
}

.permission_table tr:hover {
  background-color: #ddd;
}
.permission_table .header {
  background-color: rgb(209, 209, 209);
}

.permission_table th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #066a8c;
  color: white;
}
</style>
<template>
  <v-container>
    <div class="d-flex justify-space-between">
      <div class="text-lg font-semibold black-text ml-1 mt-2 mb-4">
        Tiers
      </div>
      <v-btn
          class="white--text blue-color"
          text
          v-if="tiers.length > 0"
          @click="openConfigureTiersModal()"
      >Configure
      </v-btn>
    </div>
      <v-divider />
      <v-form ref="tier_form" class="mt-4">
        <v-row class="mt-2" dense>
            <v-col
                v-for="(tier, cIndex) in tiers"
                :key="`index${cIndex}`"
                cols="12"
                lg="4"
                md="6"
                sm="12"
                style="position: relative"
            >
              <div :data-label="`Level: ${cIndex+1}`" class="custom-ribbon">
              <v-card class="shadow-0 bordered">
                <v-container>
                  <v-row>
                    <v-col class="pb-0" cols="12" md="12" sm="12">
                      <label for=""> Tier name </label>
                      <v-text-field
                          v-model="tier.title"
                          :rules="[(v) => !!v || 'Name is required']"
                          background-color="#fff"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          light
                          outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <label for=""> Color Code </label>
                      <v-text-field
                          v-model="tier.color_code"
                          :rules="[(v) => !!v || 'Color code is required']"
                          background-color="#fff"
                          class="q-text-field shadow-0 color-picker"
                          dense
                          hide-details="auto"
                          light
                          outlined
                          required
                      >
                        <template v-slot:append>
                          <v-menu :close-on-content-click="false" nudge-bottom="105" nudge-left="16" top>
                            <template v-slot:activator="{ on }">
                              <div :style="swatchStyle(cIndex)" v-on="on" />
                            </template>
                            <v-card>
                              <v-card-text class="pa-0">
                                <v-color-picker v-model="tier.color_code" :swatches="swatches" flat show-swatches />
                              </v-card-text>
                            </v-card>
                          </v-menu>
                        </template>
                      </v-text-field>
                    </v-col>
                    <v-col class="pl-0" md="4">
                      <v-switch
                          v-model="tier.status_id"
                          :false-value="2"
                          :true-value="1"
                          class="mx-4 mt-7"
                          dense
                          hide-details="auto"
                          label="Active"
                          required
                      ></v-switch>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
              </div>
              <v-tooltip v-if="tiers.length > 1" bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                      v-if="checkDeletePermission($modules.settings.tiers.slug)"
                      absolute
                      color="red"
                      dark
                      elevation="0"
                      fab
                      right
                      style="top: -5px"
                      top
                      v-bind="attrs"
                      x-small
                      @click="deleteTier(cIndex)"
                      v-on="on"
                  >
                    <DeleteIcon />
                  </v-btn>
                </template>
                Delete
              </v-tooltip>
            </v-col>
            <v-col md="3" v-if="checkWritePermission($modules.settings.tiers.slug)">
              <v-card
                  class="pa-4 d-flex align-center justify-center rounded-2 shadow-0 bordered"
                  height="190px"
                  outlined
              >
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                        class="teal-color"
                        dark
                        elevation="0"
                        fab
                        small
                        v-bind="attrs"
                        @click="addTier"
                        v-on="on"
                    >
                      <v-icon>mdi-plus-circle</v-icon>
                    </v-btn>
                  </template>
                  Add
                </v-tooltip>
              </v-card>
            </v-col>
          </v-row>
        <v-row>
          <v-col cols="12">
            <v-btn
                class="white--text blue-color"
                height="45"
                text
                v-if="tiers.length > 0"
                @click="saveTier()"
            >Save
            </v-btn>
          </v-col>
        </v-row>
      </v-form>
      <confirm-model
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="confirmModel.id = null"
    ></confirm-model>
    <!-- Draggable List Dialog -->
    <v-dialog v-model="configureTierModal" max-width="400px">
      <v-card>
        <v-card-text>
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon class="text-2xl font-semibold" text="Tier Levels" style="color: black" >
                </SvgIcon>
                <v-btn  fab x-small class="shadow-0" @click="closeConfigureTiersModal">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <div class="mt-8">
            <!-- Draggable List -->
            <draggable
                v-model="tiers"
                group="list-group"
                animation="200"
                item-key="id"
                draggable=".item-drag"
                ghost-class="ghost"
            >
              <v-card class="ma-2 item-drag shadow-0" v-for="(element,index) in tiers" :key="index">
                <v-row align="center">
                  <v-col cols="12">
                    <div class="d-flex justify-space-between px-2">
                      <div><span>{{ element.title }}</span></div>
                      <div><span class="mdi mdi-drag"></span></div>
                    </div>
                  </v-col>
                </v-row>
              </v-card>
            </draggable>
          </div>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="saveTier">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import DeleteIcon from "@/assets/images/misc/delete-bg-icon.svg";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";
import draggable from 'vuedraggable';
import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  components: {
    SvgIcon,
    ConfirmModel,DeleteIcon,draggable
  },
  data() {
    return {
      tiers: [],
      deleted_tiers:[],
      editFlag: false,
      tier_dialog: false,
      modules: [],
      valid: false,
      status: 1,
      tier: { permissions: [],color_code:"#00AAAAFF" },
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      modulesSelected: [],
      swatches: [
        ["#FF0000", "#AA0000", "#550000"],
        ["#FFFF00", "#AAAA00", "#555500"],
        ["#00FF00", "#00AA00", "#005500"],
        ["#00FFFF", "#00AAAA", "#005555"],
        ["#0000FF", "#0000AA", "#000055"],
      ],
      configureTierModal:false,
    };
  },
  mounted() {
    this.getUserTiersList();
  },

  methods: {
    openConfigureTiersModal(){
      this.configureTierModal = true;
    },
    closeConfigureTiersModal(){
      this.configureTierModal = false;
    },
    swatchStyle(cIndex) {
      const { color_code, menu } = this.tiers[cIndex];
      return {
        backgroundColor: color_code,
        cursor: "pointer",
        height: "30px",
        width: "30px",
        borderRadius: menu ? "50%" : "4px",
        transition: "border-radius 200ms ease-in-out",
      };
    },
    addTier() {
      this.tiers.push({
        id: null,
        title: null,
        status_id: 1,
        position:this.tier.length+1,
        color_code: "#00AAAAFF",
      });
      if (this.$refs.cform) {
        this.$refs.cform.resetValidation();
      }
    },
    getUserTiersList() {
      this.$http
          .get(`venues/tiers?status_id=${this.status}`)
          .then((response) => {
            if (response.status === 200) {
              this.tiers = response.data.data;
            }
          }).catch((error) => {
            this.errorChecker(error);
          });
    },
    deleteTier(cIndex) {
      let t = this.tiers[cIndex];
      if (!t.id) {
        this.tiers.splice(cIndex, 1);
        this.$forceUpdate();
      } else {
        this.confirmModel = {
          id: cIndex,
          title: "Do you want to delete this Tier?",
          description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
          type: "delete",
        };
      }
    },
    confirmActions(data) {
      if (data.type === "delete") {
        let index = data.id;
        if (this.tiers[index].id != null) {
          this.deleted_tiers.push(this.tiers[index].id);
        }
        this.tiers.splice(index, 1);
        this.$forceUpdate();
      }
      this.confirmModel.id = null;
    },
    saveTier(){
      if (!this.$refs.tier_form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      let formData = new FormData();
      if (this.tiers.length > 0) {
        this.tiers.forEach((item, index) => {
          if(item.id) {
            formData.append(`tiers[${index}][id]`, item.id);
          }
          formData.append(`tiers[${index}][position]`, parseInt(index)+1);
          formData.append(`tiers[${index}][title]`, item.title);
          formData.append(`tiers[${index}][status_id]`, item.status_id);
          formData.append(`tiers[${index}][color_code]`, item.color_code);
        });
      }
      if (this.deleted_tiers.length) {
        this.deleted_tiers.forEach((item, index) => {
          formData.append(`deleted_tiers[${index}]`, item);
        });
      }
      this.showLoader();
      this.$http({
        method: "post",
        data: formData,
        url: "venues/tiers",
      }).then((response) => {
        if (response.status === 200) {
            this.hideLoader();
            this.showSuccess(response.data.message);
            this.tiers = [];
            this.deleted_tiers = [];
            this.getUserTiersList();
          }
        }).catch((error) => {
          this.errorChecker(error);
        }).finally(() => {
        this.closeConfigureTiersModal();
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.custom-ribbon {
  //&::before {
  //  position: absolute;
  //  top: 2px;
  //  left: -4px;
  //  content: "";
  //  background: #00b0af;
  //  height: 12px;
  //  width: 12px;
  //  transform: rotate(136deg);
  //}

  &::after {
    position: absolute;
    content: attr(data-label);
    top: 4px;
    left: 4px;
    padding: 2px 10px;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    background: #00b0af;
    font-size: 10px;
    color: white;
    text-align: center;
    border-radius: 4px 0;
  }
}
.ghost {
  opacity: 0.7;
  transform: scale(1.03);
}
.item-drag{
  cursor: move !important;
}
</style>
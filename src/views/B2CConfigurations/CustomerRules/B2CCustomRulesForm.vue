<template>
  <v-container fluid>
    <BackButton :handler="goToRules"/>
    <v-container fluid no-gutters style="max-width: 95% !important;" class="form_container">
      <v-form ref="form" v-model="valid" @submit.prevent="submitRule">
        <div class="d-flex justify-space-between align-center">
          <h3 class="text-base font-semibold black-text">Flagged Users</h3>
          <v-switch
              class="mx-0 my-0"
              v-model="rule.enable"
              label="Enable Rule"
              hide-details="auto"
              :true-value="1"
              :false-value="2"
          ></v-switch>
        </div>
        <v-card class="rounded shadow-0 bordered mt-2 p-4">
          <v-row>
            <v-col md="6">
              <div>
                <label for="title">Title*</label>
                <v-text-field
                    hide-details="auto"
                    outlined
                    class="q-text-field shadow-0 w-full"
                    dense
                    background-color="#fff"
                    v-model="rule.title"
                    :rules="[(v) => !!v || 'Title is required']"
                />
              </div>
            </v-col>
            <v-col>
              <div>
                <label for="description">Notes</label>
                <v-textarea
                    hide-details="auto"
                    outlined
                    class="q-text-field shadow-0 w-full"
                    dense
                    background-color="#fff"
                    v-model="rule.description"
                    rows="1"
                ></v-textarea>
              </div>
            </v-col>
          </v-row>
          <hr class="my-6" style="border-color: rgba(17, 42, 70, 0.10) !important;"/>
          <div class="mt-4">
            <div v-for="(group,groupIndex) in rule.groups" :key="groupIndex">
              <div class="condition-row relative">
                <div v-if="group.conditions.length > 1" class="mb-3">
                  <v-select
                      v-model="group.condition_type"
                      :items="[{value:1,label:'All of below'},{value:2,label:'Any of below'}]"
                      :menu-props="{ bottom: true, offsetY: true }"
                      class="q-autocomplete shadow-0 group-select"
                      dense
                      hide-details="auto"
                      item-text="label"
                      item-value="value"
                      outlined
                      style="max-width: 180px"
                      :rules="[(v) => !!v || 'Select a Condition']"
                  />
                </div>
                <v-btn icon @click="removeGroup(groupIndex)" class="absolute" style="top: -10px;right: -10px;">
                  <DeleteBgIcon/>
                </v-btn>
                <div v-for="(condition,index) in group.conditions" :key="index">
                  <div>
                    <v-row>
                      <v-col md="2">
                        <label for="">Action on</label>
                        <v-select
                            v-model="condition.field"
                            :items="condition.condition_on === 'customer'?customerFields:otherModulesFields"
                            :menu-props="{ bottom: true, offsetY: true }"
                            class="q-autocomplete shadow-0"
                            dense
                            hide-details="auto"
                            item-text="label"
                            item-value="value"
                            outlined
                            :rules="[(v) => !!v || 'Select a Field']"
                        />
                      </v-col>
                      <v-col md="3" v-if="condition.field !== 'flagged'">
                        <label for="">Operator</label>
                        <v-select
                            v-model="condition.operator"
                            :items="operators"
                            :menu-props="{ bottom: true, offsetY: true }"
                            class="q-autocomplete shadow-0"
                            dense
                            hide-details="auto"
                            item-text="label"
                            item-value="value"
                            outlined
                            :rules="[(v) => !!v || 'Select an operator']"
                        />
                      </v-col>
                      <v-col v-if="condition.field !== 'flagged'">
                        <label for="">Value</label>
                        <v-text-field
                            v-model="condition.value"
                            class="q-text-field shadow-0 bg-white"
                            dense
                            hide-details="auto"
                            outlined
                            :rules="[(v) => !!v || 'Required']"
                        />
                      </v-col>

                      <v-col md="1">
                        <v-btn v-if="group.conditions.length > 1" icon @click="removeCondition(index,groupIndex)"
                               class="mt-5">
                          <DeleteIcon/>
                        </v-btn>
                      </v-col>
                    </v-row>

                  </div>
                </div>
                <div class="mt-2">
                  <v-btn
                      v-if="group.conditions && group.conditions.length < 2"
                      @click="addCondition(groupIndex)"
                      style="background-color: rgba(17, 42, 70, 0.1) ; color:#112A46;"
                      class="my-2 text_capitalize font-semibold bg-transparent"
                      height="40"
                      depressed
                  >
                    <AddIcon/>
                    <span class="ml-1">Add Condition</span>
                  </v-btn>
                </div>
              </div>
              <v-select
                  v-if="groupIndex < (rule.groups.length-1)"
                  v-model="group.relation"
                  :items="[{value:1,label:'AND Condition'},{value:2,label:'OR Condition'}]"
                  :menu-props="{ bottom: true, offsetY: true }"
                  class="q-autocomplete shadow-0 condition-select"
                  dense
                  hide-details="auto"
                  item-text="label"
                  item-value="value"
                  outlined
                  style="max-width: 180px"
                  :rules="[(v) => !!v || 'Select a Condition']"
              />
            </div>

            <div class="mt-2">
              <v-btn
                  v-if="rule.groups && rule.groups.length < 5"
                  @click="addGroup()"
                  style="background-color: rgba(17, 42, 70, 0.1) ; color:#112A46;border-color:#112A46 !important "
                  class="ma-2 text_capitalize bordered font-semibold"
                  outlined
                  height="40"
              >
                <AddIcon/>
                <span class="ml-1">Add Group</span>
              </v-btn>
            </div>
          </div>
          <hr class="my-6" style="border-color: rgba(17, 42, 70, 0.10) !important;"/>
          <div>
            <h3>
              Actions
            </h3>
            <div v-for="(action,index) in rule.actions" :key="index" class="action-row">
              <v-row>
                <v-col md="3">
                  <label for="">Action Type</label>
                  <v-select
                      v-model="action.action_type"
                      :items="[{value:'notify',label:'Notify'},{value:'block',label:'Block'}]"
                      :menu-props="{ bottom: true, offsetY: true }"
                      class="q-autocomplete shadow-0"
                      dense
                      hide-details="auto"
                      item-text="label"
                      item-value="value"
                      outlined
                      :rules="[(v) => !!v || 'Select an action type']"
                  />
                </v-col>
                <v-col v-if="action.action_type === 'notify'" md="6">
                  <label for="">Email</label>
                  <v-text-field
                      v-model="action.value"
                      class="q-text-field shadow-0 bg-white"
                      dense
                      hide-details="auto"
                      outlined
                      :rules="[(v) => !!v || 'Enter an email']"
                  />
                </v-col>
                <v-spacer/>
                <v-col md="1">
                  <v-btn v-if="rule.actions.length > 1" icon @click="removeAction(index)" class="mt-5">
                    <DeleteIcon/>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
            <div class="mt-2">
              <v-btn
                  v-if="this.rule.actions && this.rule.actions.length < 5"
                  height="40"
                  outlined
                  @click="addAction()"
                  style="background-color: rgba(17, 42, 70, 0.1) ; color:#112A46;border-color:#112A46 !important "
                  class="ma-2 text_capitalize bordered font-semibold"
              >
                <AddIcon/>
                <span class="ml-1">Add Action</span>
              </v-btn>
            </div>
          </div>
        </v-card>
        <div class="d-flex justify-space-between mt-4">
          <div v-if="checkDeletePermission($modules.b2cconfig.blacklist_management.slug)">
            <v-btn class="ma-2 shadow-0 text-white" color="red" @click="deleteRule"
                   v-if="rule.id">
              Delete
            </v-btn>
          </div>
          <v-btn type="submit" color=" darken-1" class="white--text blue-color px-8" v-if="checkWritePermission($modules.b2cconfig.blacklist_management.slug)">
            Save
          </v-btn>
        </div>
      </v-form>
    </v-container>
    <confirm-model
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="confirmModel.id = null"
    ></confirm-model>

  </v-container>
</template>

<script>
import BackButton from '@/components/Common/BackButton.vue'
import DeleteBgIcon from '@/assets/images/misc/delete-bg-icon.svg'
import DeleteIcon from '@/assets/images/misc/delete-icon.svg'
import AddIcon from '@/assets/images/events/plus-icon.svg'

export default {
  components: {
    AddIcon,
    DeleteBgIcon,
    DeleteIcon,
    BackButton,
  },
  mounted () {
    if (typeof this.$route.params.id != 'undefined' && this.$route.params.id) {
      this.getRule()
    }
  },
  data () {
    return {
      valid: false,
      conditionOn: [
        {
          value: 'customer',
          label: 'Customer'
        },
        // {
        //   value: 'facility',
        //   label: 'Facilities'
        // },
        // {
        //   value: 'event',
        //   label: 'Events'
        // },
        // {
        //   value: 'academy',
        //   label: 'Academy'
        // },
        // {
        //   value: 'membership',
        //   label: 'Memberships'
        // },
        // {
        //   value: 'trainer',
        //   label: 'Trainer'
        // },
        // {
        //   value: 'b2b',
        //   label: 'Partners'
        // },
        // {
        //   value: 'pos',
        //   label: 'Retail'
        // }
      ],
      customerFields: [
        // {
        //   value: 'id',
        //   label: 'ID'
        // },
        {
          value: 'name',
          label: 'Name'
        },
        {
          value: 'country',
          label: 'Country'
        },
        {
          value: 'email',
          label: 'Email'
        },
        {
          value: 'emirate_id',
          label: 'Emirate ID'
        },
        {
          value: 'passport',
          label: 'Passport'
        },
        {
          value: 'religion',
          label: 'Religion'
        },
        {
          value: 'flagged',
          label: 'Flagged'
        }
        // {
        //   value: 'age',
        //   label: 'age'
        // },

      ],
      otherModulesFields: [
        {
          value: 'id',
          label: 'ID'
        },
      ],
      operators: [
        {
          value: 'matches',
          label: 'Matches'
        },
        {
          value: 'contains',
          label: 'Contains'
        },
        {
          value: 'start_with',
          label: 'Start With'
        },
        {
          value: 'end_with',
          label: 'End With'
        },
      ],
      rule: {
        title: '',
        description: '',
        enable: 1,
        groups: [
          {
            conditions: [
              {
                condition_on: 'customer',
                field: 'email',
                operator: 'matches',
                value: null,
              }
            ],
            relation: 1,
            condition_type: 1
          }
        ],
        actions: [
          {
            action_type: null,
            value: null
          }
        ],
      },
      deleted_conditions: [],
      deleted_actions: [],
      deleted_groups: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      }
    }
  },
  methods: {
    getRule () {
      const id = atob(this.$route.params.id)
      this.showLoader('Saving...')
      this.$http.get('venues/b2c/rules/' + id, this.rule)
          .then(res => {
            if (res.status == 200) {
              this.rule = res.data.data
              this.rule.enable = res.data.data.status_id
            }
          })
          .catch(err => {
            this.showError(err)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    submitRule () {
      if (!this.$refs.form.validate()) {
        this.showError('Please fill all required fields')
        return
      }
      this.showLoader('Saving...')
      this.rule.status_id = this.rule.enable
      const data = this.rule
      if (data.id) {
        this.rule.deleted_conditions = this.deleted_conditions
        this.rule.deleted_actions = this.deleted_actions
        this.rule.deleted_groups = this.deleted_groups
      }
      this.$http.request({
        url: 'venues/b2c/rules' + (this.rule.id ? '/' + this.rule.id : ''),
        data: this.rule,
        method: this.rule.id ? 'PUT' : 'POST'
      })
          .then(res => {
            if (res.status == 200) {
              this.showSuccess(`Rule saved successfully`)
              this.goToRules()
            }
          })
          .catch(err => {
            this.showError(err)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    goToRules () {
      this.$router.push({ name: 'B2CCustomRules' })
    },
    removeCondition (index, groupIndex) {
      if (this.rule.groups[groupIndex] && this.rule.groups[groupIndex].conditions[index] && this.rule.groups[groupIndex].conditions[index].id) {
        this.deleted_conditions.push(this.rule.groups[groupIndex].conditions[index].id)
      }
      this.rule.groups[groupIndex].conditions.splice(index, 1)
    },
    addCondition (index) {
      this.rule.groups[index].conditions.push({
        condition_on: 'customer',
        field: 'email',
        operator: 'matches',
        value: null,
      })
    },
    addGroup () {
      this.rule.groups.push({
        conditions: [
          {
            condition_on: 'customer',
            field: 'email',
            operator: 'matches',
            value: null,
          }
        ],
        relation: 1,
        condition_type: 1
      })
    },
    removeGroup (index) {
      if (this.rule.groups[index] && this.rule.groups[index].id) {
        this.deleted_groups.push(this.rule.groups[index].id)
      }
      this.rule.groups.splice(index, 1)
    },
    removeAction (index) {
      if (this.rule.actions[index] && this.rule.actions[index].id) {
        this.deleted_actions.push(this.rule.actions[index].id)
      }
      this.rule.actions.splice(index, 1)
    },
    addAction () {
      this.rule.actions.push({
        action_type: null,
        value: null
      })
    },
    deleteRule() {
      this.confirmModel = {
        id: this.rule.id,
        title: "Delete Rule",
        description: "Are you sure you want to delete this rule?",
        type: 'delete'
      }
    },
    confirmActions(data) {
      if (data.type === 'delete') {
        this.$http
            .delete(`venues/b2c/rules/${data.id}`)
            .then((response) => {
              if (response.status === 200 && response.data.status) {
                this.showSuccess(response.data.message);
                this.$router.push({name: "B2CCustomRules"});
              }
            })
            .catch((error) => {
              this.showError(error)
            })
      }
    }
  }
}
</script>

<style lang="scss">
.condition-row, .action-row {
  border-radius: 6px;
  border: 1px solid #DCDCDC;
  background: #F8FAFB;
  padding: 1rem;
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
}

.condition-select {
  &::before{
    content:' ';
    height:25px;
    width: 2px;
    background: #DCDCDC;
    position: absolute;
    top: -25px;
    transform: translateX(90px);
    z-index: 0;
  }
  &::after{
    content:' ';
    height:25px;
    width: 2px;
    background: #DCDCDC;
    position: absolute;
    bottom: -25px;
    transform: translateX(90px);
    z-index: 0;
  }
  border: none !important;
  margin-top: 25px !important;
  margin-bottom: 25px !important;
  fieldset {
    border-color: #4FAEAF !important;
    border-radius: 0.25rem;
  }

  border-radius: 6px;
  background: #4FAEAF !important;
  color: white !important;

  .v-select__selection, .v-input__icon i {
    color: white !important;
  }
}

.group-select {
  border: none !important;

  fieldset {
    border-color: rgba(79, 174, 175, 0.1) !important;
    border-radius: 0.25rem;
  }

  border-radius: 6px;
  background: rgba(79, 174, 175, 0.1) !important;
  color: #00B0AF !important;

  .v-select__selection, .v-input__icon i {
    color: #00B0AF !important;
  }
}
</style>

<template>
  <v-container fluid no-gutters>
    <B2cTabs/>
    <div
        class="d-flex mt-8"
        v-if="checkWritePermission($modules.b2cconfig.blacklist_management.slug)"
    >
      <v-btn
          color=" darken-1"
          class="white--text blue-color ml-auto"
          text
          height="48"
          @click="goToRule()"
      >
        <AddIcon/>
        <span class="ml-1">Add Rule</span>
      </v-btn>
    </div>
    <v-row class="mt-6">
      <v-col v-for="rule in rules" :key="rule.id" md="4" sm="12" lg="3">
        <v-card class="shadow pointer" style="border-radius: 8px;height: 100%" @click="goToRule(rule)">
          <v-card-text class="p-0 d-flex flex-column flex-grow-1 justify-space-between overflow-hidden" style="height: 100%" >
            <div class="p-4 overflow-hidden">
              <div class="d-flex justify-space-between align-center">
                <p class="font-semibold text-lg black--text line-clamp-1 mb-2 flex-grow-1">{{ rule.title }}</p>
                <LightningBoltIcon :class="rule.status_id === 1 ? '':'fill-red'"
                                   @click.stop="toggleStatus(rule.id)" style="width:20px; height:20px;"/>
              </div>
              <p class="text-sm black--text line-clamp-2 mb-1">{{ rule.description }}</p>
            </div>
            <div class="d-flex justify-space-between align-center gap-x-4 p-4 mt-auto" style="background: #F8FAFB">
              <div class="d-flex justify-space-between align-center gap-x-1">
                <ConditionIcon/>
                Conditions: <strong>{{ rule.conditions_count }}</strong>
              </div>
              <div class="d-flex justify-space-between align-center gap-x-1">
                <ActionIcon/>
                Actions: <strong> {{ rule.actions_count }}</strong>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <br>
    <v-pagination
        v-if="totalPages > 0"
        v-model="page"
        :length="totalPages"
    ></v-pagination>
  </v-container>
</template>
<script>
import B2cTabs from '@/views/B2CConfigurations/B2cTabs.vue'
import LightningBoltIcon from '@/assets/images/facilities/lightning-bolt.svg'
import AddIcon from '@/assets/images/misc/plus-icon.svg'
import ConditionIcon from '@/assets/images/qube/condition.svg'
import ActionIcon from '@/assets/images/qube/actions.svg'

export default {
  components: { AddIcon, B2cTabs, LightningBoltIcon, ConditionIcon, ActionIcon },
  data () {
    return {
      rules: [],
      page: 1,
      totalPages: 1
    }
  },
  mounted () {
    this.loadRules()
  },
  watch:{
    page(){
      this.loadRules()
    }
  },
  methods: {
    goToRule (rule = null) {
      if (!this.checkWritePermission(this.$modules.b2cconfig.blacklist_management.slug)){
        return;
      }

      if (rule) {
        this.$router.push({ name: 'B2CCustomRulesForm', params: { id: btoa(rule.id) } })
      } else {
        this.$router.push({ name: 'B2CCustomRulesForm' })
      }
    },
    loadRules () {
      this.showLoader()

      this.$http
          .get(
              `venues/b2c/rules`,
              {
                params: {
                  page: this.page,
                }
              }
          )
          .then((response) => {
            if (response.status == 200) {
              this.rules = response.data.data.data
              this.page = response.data?.data?.current_page || 1
              this.totalPages = response.data?.data?.last_page || 0
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          }).finally(() => {
        this.hideLoader()
      })
    },
    toggleStatus (ruleId) {
      if (!this.checkWritePermission(this.$modules.b2cconfig.blacklist_management.slug)){
        return;
      }
      this.showLoader('toggling status')
      this.$http.put('venues/b2c/rules/toggle/' + ruleId)
          .then(res => {
            if (res.status === 200) {
              this.showSuccess('Status updated successfully.')
              this.loadRules()
            }
          })
          .catch(err => {
            this.showError(err)
          })
          .finally(() => {
            this.hideLoader()
          })
    }
  }
}
</script>


<style scoped>

.fill-red {
  fill: red !important;
}
</style>

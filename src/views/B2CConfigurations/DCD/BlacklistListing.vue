<template>
  <v-container fluid no-gutters>
    <B2cTabs/>
    <div
        v-if="checkWritePermission($modules.b2cconfig.dcd_blacklist_management.slug)"
        class="d-flex mt-8"
    >
    </div>

    <div class="md-card md-theme-default mt-8 shadow rounded-5">
      <div class="md-card-content md-card-table">
        <div class="mb-4">
          <div class="d-flex justify-space-between align-center">
            <SvgIcon class="text-2xl font-semibold" text="Blacklist"></SvgIcon>
            <v-spacer/>
            <div class="d-flex gap-x-3 align-center">
              <v-switch
                  v-model="showDeleted"
                  class="mt-0"
                  dense
                  hide-details="auto"
                  label="Show Deleted"
                  required
              ></v-switch>
              <v-text-field
                  v-model="searchQuery"
                  class="q-autocomplete shadow-0"
                  clearable
                  dense
                  hide-details="auto"
                  label="Search"
                  outlined
              ></v-text-field>
              <v-btn
                  v-if="checkWritePermission($modules.b2cconfig.dcd_blacklist_management.slug)"
                  class="white--text blue-color ml-auto"
                  color=" darken-1"
                  height="40px"
                  text
                  @click="addOrEditModal = true"
              >
                <AddIcon/>
                <span class="ml-1">Add Entry</span>
              </v-btn>
              <v-btn
                  v-if="selectedIds.length > 0 && checkDeletePermission($modules.b2cconfig.dcd_blacklist_management.slug)"
                  class="white--text red-color ml-auto"
                  color=" darken-1"
                  height="40px"
                  text
                  @click="deleteSelected">
                Delete Selected
              </v-btn>
            </div>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table border-collapse">
            <thead>
            <tr class="opacity-70 tr-neon tr-rounded">
              <th>
                <input type="checkbox" @change="toggleSelectAll($event)"/>
              </th>
              <th>
                Name
              </th>
              <th>
                Emirates ID
              </th>
              <th>
                Passport Number
              </th>
              <th>
                Nationality
              </th>
              <th>
                DOB
              </th>
              <th>
                Gender
              </th>
              <th>
                Religion
              </th>
              <th>
                Actions
              </th>
            </tr>
            </thead>
            <tbody>
            <tr v-if="filteredData.length === 0">
              <td colspan="100%">
                <div class="d-flex align-center justify-center">
                  No matching records found.
                </div>
              </td>
            </tr>
            <tr
                v-for="data in filteredData"
                v-else
                :key="data.id"
                class="border-bottom"
            >
              <td>
                <input
                    v-model="selectedIds"
                    :disabled="data.isDeleted == 'true'"
                    :value="data.id"
                    type="checkbox"
                />
              </td>
              <td>
                <span class="text_ellipse">
                  {{ data.name }}
                </span>
              </td>
              <td>
                <span class="text_ellipse">
                  {{ data.eid }}
                </span>
              </td>
              <td>
                <span class="text_ellipse">
                  {{ data.passportNumber }}
                </span>
              </td>
              <td>
                <span class="text_ellipse">
                  {{ data.nationality }}
                </span>
              </td>
              <td>
                <span v-if="data.dob" class="text_ellipse">
                  {{ data.dob | dateformat }}
                </span>
                <span v-else class="text_ellipse">
                  -
                </span>
              </td>
              <td>
                <span class="text_ellipse">
                  {{ data.gender }}
                </span>
              </td>
              <td>
                <span class="text_ellipse">
                  {{ data.religion }}
                </span>
              </td>
              <td>
                <v-menu offset-y>
                  <template
                      v-if="
                          (checkWritePermission($modules.b2cconfig.dcd_blacklist_management.slug) || checkDeletePermission($modules.b2cconfig.dcd_blacklist_management.slug)) && data.isDeleted != 'true'
                        "
                      v-slot:activator="{ on, attrs }"
                  >
                    <v-btn
                        color="transparent"
                        dark
                        depressed
                        small
                        v-bind="attrs"
                        v-on="on"
                    >
                      <v-icon color="black">mdi-dots-horizontal</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
<!--                    <v-list-item-->
<!--                        v-if="-->
<!--                          checkWritePermission($modules.b2cconfig.dcd_blacklist_management.slug)-->
<!--                        "-->
<!--                        @click="editRow(data)"-->
<!--                    >-->
<!--                      <v-list-item-title>Edit</v-list-item-title>-->
<!--                    </v-list-item>-->
                    <v-list-item v-if="checkDeletePermission($modules.b2cconfig.dcd_blacklist_management.slug)"
                                 @click="deleteRow(data)">
                      <v-list-item-title
                      >Delete
                      </v-list-item-title
                      >
                    </v-list-item>
                  </v-list>
                </v-menu>
                <span v-if="data.isDeleted == 'true'"
                      class="text-red">
                  Deleted
                </span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <v-dialog v-model="addOrEditModal" max-width="800px" persistent scrollable width="60%">
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-title class="border-bottom">
            <div class=" w-full">
              <div class="d-flex justify-space-between align-center">
                <p class="mb-0 font-medium">
                  {{ activeObj.id != null ? 'Update Entry' : 'Add Entry' }}
                </p>
                <v-btn class="shadow-0" fab x-small @click="close">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </v-card-title>
          <v-card-text class="mt-4">
            <v-row>
              <v-col lg="4" md="6" sm="12">
                <label>Name</label>
                <v-text-field
                    v-model="activeObj.name"
                    :disabled="is_icp_validated && !!activeObj.name"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                ></v-text-field>
              </v-col>
              <v-col lg="4" md="6" sm="12">
                <label>Emirates ID <span class="text-red">*</span></label>
                <v-text-field
                    v-model="activeObj.eid"
                    :disabled="is_icp_validated && !!activeObj.eid"
                    :rules="[validateEid]"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                ></v-text-field>
              </v-col>
              <v-col lg="4" md="6" sm="12">
                <label>Passport Number <span class="text-red">*</span></label>
                <v-text-field
                    v-model="activeObj.passportNumber"
                    :disabled="is_icp_validated && !!activeObj.passportNumber"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                ></v-text-field>
              </v-col>
              <v-col lg="4" md="6" sm="12">
                <label>Nationality <span class="text-red">*</span></label>
                <v-autocomplete
                    v-model="activeObj.nationality_id"
                    :disabled="is_icp_validated && !!activeObj.nationality_id"
                    :items="countries"
                    append-icon="mdi-chevron-down"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                    item-text="name"
                    item-value="dcd_country_id"
                    outlined
                >
                </v-autocomplete>
              </v-col>
              <v-col lg="4" md="6" sm="12">
                <label>DOB <span class="text-red">*</span></label>
                <DateOfBirthField
                    v-model="activeObj.dob"
                    :dense="true"
                    :disabled="is_icp_validated && !!activeObj.dob"
                    :outlined="true"
                />
              </v-col>
              <v-col lg="4" md="6" sm="12">
                <label>Gender</label>
                <v-select
                    v-model="activeObj.gender_id"
                    :disabled="is_icp_validated && !!activeObj.gender_id"
                    :items="genders"
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    dense
                    item-text="name"
                    item-value="id"
                    outlined
                ></v-select>
              </v-col>
              <v-col lg="4" md="6" sm="12">
                <label>Religion</label>
                <v-autocomplete
                    v-model="activeObj.religion_id"
                    :items="religions"
                    append-icon="mdi-chevron-down"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                    item-text="name"
                    item-value="icp_lookup_code"
                    outlined
                >
                </v-autocomplete>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="ma-2" text @click="close">Close</v-btn>
            <template v-if="checkWritePermission($modules.b2cconfig.dcd_blacklist_management.slug)">
              <v-btn
                  v-if="is_icp_validated || manual_entry"
                  class="ma-2 white--text teal-color"
                  @click="confirmSaveRow"
              >
                {{ activeObj.id != null ? 'Update' : 'Save' }}
              </v-btn>
              <v-btn
                  v-else
                  class="ma-2 white--text teal-color"
                  @click="validateIcp"
              >
                Validate
              </v-btn>

            </template>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <confirm-model
        v-bind="confirmModel"
        @close="closeConfirm"
        @confirm="confirmActions"
    ></confirm-model>
  </v-container>
</template>
<script>
import AddIcon from '@/assets/images/misc/plus-icon.svg'
import B2cTabs from '@/views/B2CConfigurations/B2cTabs.vue'
import SvgIcon from '@/components/Image/SvgIcon.vue'
import DateOfBirthField from '@/components/Fields/DateOfBirthField.vue'

export default {
  name: 'BlacklistListing',
  components: { DateOfBirthField, SvgIcon, B2cTabs, AddIcon },
  computed: {
    countries () {
      return this.$store.getters.getCountries.data
    },
    religions () {
      return this.$store.getters.getReligions.data
    },
    genders () {
      return [
        {
          'id': 1,
          'name': 'Male'
        },
        {
          'id': 2,
          'name': 'Female'
        },
      ]
    },
    filteredData () {

      let baseData = this.showDeleted
          ? this.fetchedData
          : this.fetchedData.filter(e => e.isDeleted != 'true')

      if (!this.searchQuery) {
        return baseData
      }

      const query = this.searchQuery.toLowerCase()

      return baseData.filter(row =>
          Object.values(row).some(value =>
              String(value).toLowerCase().includes(query)
          )
      )
    }
  },
  mounted () {
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch('loadCountries')
    }
    if (this.$store.getters.getReligions.status == false) {
      this.$store.dispatch('loadReligions')
    }
    this.getData()
  },
  data () {
    return {
      showDeleted: false,
      searchQuery: '',
      selectedIds: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      valid: false,
      activeObj: {
        id: null,
        name: null,
        eid: null,
        passportNumber: null,
        dob: null,
        gender: null,
        gender_id: null,
        nationality: null,
        nationality_id: null,
        religion: null,
        religion_id: null,
      },
      is_icp_validated: null,
      manual_entry: null,
      addOrEditModal: false,
      // filteredData:[],
      fetchedData: [],
      searchField: null,
    }
  },
  methods: {
    validateEid (value) {
      if (!value || value.trim() === '') return true // Allow empty
      if (!/^\d{15}$/.test(value)) return 'Emirates ID must be 15 digits.'
      if (!this.isValidLuhn(value)) return 'Invalid Emirates ID.'
      return true
    },
    isValidLuhn (number) {
      let sum = 0
      let shouldDouble = false

      for (let i = number.length - 1; i >= 0; i--) {
        let digit = parseInt(number[i])
        if (shouldDouble) {
          digit *= 2
          if (digit > 9) digit -= 9
        }
        sum += digit
        shouldDouble = !shouldDouble
      }

      return sum % 10 === 0
    },
    deleteSelected () {
      if (this.selectedIds.length === 0) {
        this.showWarning('Please select at least one item.')
        return
      }
      this.deleteEntries(this.selectedIds)
    },
    toggleSelectAll (event) {
      if (event.target.checked) {
        this.selectedIds = this.filteredData.filter(e => e.isDeleted != 'true').map(d => d.id)
      } else {
        this.selectedIds = []
      }
    },
    editRow (data) {
      this.activeObj = data
      this.addOrEditModal = true
    },
    close () {
      this.activeObj = {
        id: null,
        name: null,
        eid: null,
        passportNumber: null,
        dob: null,
        gender: null,
        gender_id: null,
        nationality: null,
        nationality_id: null,
        religion: null,
        religion_id: null,
      }
      this.is_icp_validated = null
      this.manual_entry = false
      this.addOrEditModal = false
    },
    deleteRow (data) {
      this.confirmModel = {
        id: data.id,
        title: 'Do you want to delete this entry?',
        description:
            'By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?',
        type: 'row',
      }
    },
    validateIcp () {
      if (
          (this.activeObj.eid == null || this.activeObj.eid.trim() === '') &&
          (
              (this.activeObj.passportNumber == null || this.activeObj.passportNumber === '') ||
              (this.activeObj.nationality_id == null || this.activeObj.nationality_id === '') ||
              (this.activeObj.dob == null || this.activeObj.dob === '')
          )
      )
      {
        this.showError('Either Emirates ID or (Passport Number, Nationality and DOB) are required.')
        return
      }

      this.showLoader('Validating...')
      this.$http
          .post('venues/b2c/blacklist/validate', this.activeObj)
          .then((response) => {
            if (response && response.status == 200 && response.data.status == true) {
              const { data } = response.data
              this.is_icp_validated = true
              this.activeObj.eid = data.eid
              this.activeObj.passportNumber = data.passportNumber
              this.activeObj.nationality_id = parseInt(data.dcd_country_id)
              this.activeObj.name = data.name
              this.activeObj.dob = data.dob
              if (data.gender) {
                this.activeObj.gender_id = data.gender.toLowerCase() === 'male' ? 1 : 2
              }
              if (data.religion) {
                this.activeObj.religion_id = this.religions.find(religion => religion.name.toUpperCase() === data.religion.toUpperCase())?.icp_lookup_code
              }
              console.log(this.activeObj)
              this.showSuccess(response.data.message)
            } else {
              this.is_icp_validated = false

              this.confirmModel = {
                id: 88888,
                title: 'Data not found. Do you want to enter manually ?',
                description: '',
                type: 'manual-data',
              }
              this.showError(response.data.message)
            }
          })
          .catch(error => {
            this.is_icp_validated = false
            if (error.response?.data?.message === 'Details Mismatch.') {
              this.confirmModel = {
                id: 88888,
                title: 'Data not found. Do you want to enter manually ?',
                description: '',
                type: 'manual-data',
              }
            }
            this.errorChecker(error)
          }).finally(() => {
        this.hideLoader()
      })
    },
    confirmActions (data) {
      if (data.type == 'row') {
        this.deleteEntries(data.id)
      } else if (data.type == 'save-row') {
        this.saveRow()
      } else if (data.type == 'manual-data') {
        this.manual_entry = true
      }
      this.$forceUpdate()
      this.confirmModel.id = null
    },
    closeConfirm (data) {
      this.confirmModel.id = null
      if (data.type == 'manual-data') {
        this.manual_entry = false
        this.close()
      }
    },
    deleteEntries (input) {

      const selectedIds = Array.isArray(input) ? input : [input]

      if (selectedIds.length === 0) {
        this.showWarning('Please select at least one entry to delete.')
        return
      }

      this.showLoader('Saving...')

      var formData = new FormData()

      selectedIds.forEach(id => {
        formData.append('selected_ids[]', id)
      })

      this.$http
          .post('venues/b2c/blacklist/delete', formData)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let message = response.data.message
              this.showSuccess(message)
              this.close()
              this.getData()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          }).finally(() => {
        this.hideLoader()
      })
    },
    confirmSaveRow () {
      this.confirmModel = {
        id: 9999999,
        title: 'Do you want to add this entry to the black list?',
        description:
            'By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?',
        type: 'save-row',
      }
    },
    saveRow (e) {
      if (e != null) {
        e.preventDefault()
        if (!this.$refs.form.validate()) {
          this.showError('Either Emirates ID or Passport Number, Nationality and DOB are required.')
          return
        }
      }
      if (!this.$refs.form.validate()) {
        this.showError('Either Emirates ID or Passport Number, Nationality and DOB are required.')
        return
      }
      this.showLoader('Saving...')

      this.$http
          .post('venues/b2c/blacklist', this.activeObj)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let message = response.data.message
              this.showSuccess(message)
              this.close()
              this.getData()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          }).finally(() => {
        this.hideLoader()
      })
    },
    getData () {
      this.selectedIds = []
      this.showLoader('Loading...')
      this.$http.get(`venues/b2c/blacklist`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.searchField = null
              this.fetchedData = response.data.data
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          }).finally(() => {
        this.hideLoader()
      })
    }
  }
}
</script>

<style scoped>

</style>

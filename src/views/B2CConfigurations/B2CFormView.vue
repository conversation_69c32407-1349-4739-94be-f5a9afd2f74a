<template>
  <v-container fluid>
    <v-row class="mb-2 mt-2">
      <v-btn class="back-button" elevation="0">
        <router-link :to="{ name: 'B2CForms' }">
          <v-icon size="18">mdi-arrow-left</v-icon>
        </router-link>
      </v-btn>
    </v-row>
    <v-card class="shadow px-8 py-4 rounded-5 mt-10">
      <v-row class="mt-4">
        <div
            class="mb-2 mt-2"
            style="font-size: 25px;font-weight: 600; margin-left:15px"
        >
          Form Submissions
        </div>

        <v-spacer></v-spacer>
        <v-col md="2">
          <div style="width: 110px; float: right">
            <v-select
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
                filled
                dense
                v-model="perPage"
                label="Per Page"
                :items="[10, 15, 25, 50]"
                @change="loadSubmissions"
            ></v-select>
          </div>
        </v-col>
      </v-row>

      <v-simple-table>
        <template v-slot:default>
          <thead>
          <tr class="tr-rounded tr-neon opacity-70">
            <th>
              Created on
            </th>
            <th>
              Country
            </th>

            <th>
              City
            </th>

            <th>
              Timezone
            </th>
            <th>
              Status
            </th>
            <th>
              Actions
            </th>
          </tr>
          </thead>

          <tbody>
          <tr v-for="(submission, i) in submissions" :key="i">
            <td>
              {{ submission.created_at | timeStamp }}
            </td>
            <td>
              {{ submission.country }}
            </td>
            <td>
              {{ submission.city }}
            </td>
            <td>
              {{ submission.timezone }}
            </td>
            <td>
              <v-btn v-if="submission.form_action_list_item" class="mt-2" small block :color="submission.form_action_list_item.color_code" dark>

                <span style="text-transform: none">
                  {{ submission.form_action_list_item.step_name }}
                </span>
              </v-btn>

            </td>
            <td>
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                      v-bind="attrs"
                      depressed
                      v-on="on"
                      dark
                      small
                      color="transparent"
                  >
                    <v-icon color="black">mdi-dots-horizontal</v-icon>
                  </v-btn
                  >
                </template>
                <v-list>
                  <v-list-item @click="viewSubmission(submission)">
                    <v-list-item-title>View</v-list-item-title>
                  </v-list-item>
                  <v-list-item @click="toggleStatus(submission)">
                    <v-list-item-title>Change Status</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </td>
          </tr>
          </tbody>
        </template>
      </v-simple-table>

      <v-pagination
          v-if="totalPages > 1"
          v-model="page"
          :length="totalPages"
      ></v-pagination>

      <confirm-model
          v-bind="confirmModel"
          @confirm="confirmActions"
          @close="confirmModel.id = null"
      ></confirm-model>
      <SubmissionResponseDetails v-if="viewSubmissionId && isResponseModalVisible" :id="viewSubmissionId"
                           :is-modal-visible="isResponseModalVisible"
                           @close="closeResponseModal"/>
      <SubmissionChangeStatusModal v-if="viewSubmissionId && isStatusModalVisible" :id="viewSubmissionId"
                             :is-modal-visible="isStatusModalVisible"
                             @close="closeStatusModal"/>
    </v-card>
  </v-container>
</template>
<script>
import SubmissionChangeStatusModal from "@/views/B2CConfigurations/SubmissionChangeStatusModal.vue";
import SubmissionResponseDetails from "@/views/B2CConfigurations/SubmissionResponseDetails.vue";

export default {
  components: {SubmissionResponseDetails, SubmissionChangeStatusModal},
  data() {
    return {
      submissions: [],
      isLoading: false,
      page: 1,
      id:null,
      totalPages: 1,
      perPage: 10,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      viewSubmissionId: null,
      isResponseModalVisible: false,
      isStatusModalVisible: false
    };
  },
  mounted() {
    if (this.$route.params.data) {
      let data = JSON.parse(atob(this.$route.params.data));
      this.id = data;
      setTimeout(() => {
        this.loadSubmissions(data);
      },200);
    }
  },
  watch: {
    page() {
      this.loadSubmissions();
    },
  },
  methods: {
    closeResponseModal() {
      this.viewSubmissionId = null;
      this.isResponseModalVisible = false
    },
    closeStatusModal(refresh = false) {
      this.viewSubmissionId = null;
      this.isStatusModalVisible = false
      if (refresh) {
        this.loadSubmissions()
      }
    },
    viewSubmission(data) {
      this.viewSubmissionId = data.id;
      this.isResponseModalVisible = true
    },
    toggleStatus(data) {
      this.viewSubmissionId = data.id;
      this.isStatusModalVisible = true
    },
    confirmActions(data) {
      if (data.type == "close_confirm") {
        this.loadSubmissions();
      }
      this.confirmModel.id = null;
    },
    loadSubmissions() {
      this.isLoading = true;
      this.$http.get(`venues/b2c/forms/submissions/${this.id}`, {
        params: {
          per_page: this.perPage,
          page: this.page
        }
      }).then(response => {
        if (response.status == 200) {
          const records = response.data
          this.submissions = records.data;
          this.page = records.current_page ? Number(records.current_page) : 1;
          this.totalPages = records.total_pages
        }
      }).finally(() => {
        this.isLoading = false;
      })
    },
  }
}
</script>


<style scoped>
.back-button {
  background: transparent !important;
  font-weight: 500;

  &,
  * {
    color: #112a46 !important;
  }
}
</style>
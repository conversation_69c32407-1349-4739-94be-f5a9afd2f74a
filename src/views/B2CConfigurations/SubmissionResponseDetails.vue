<template>
  <v-dialog v-model="isModalVisible"  scrollable width="60%" persistent>
    <v-card class="pa-8 overflow-auto" v-if="submission">
      <div class="d-flex justify-space-between align-content-center">
        <h1 class="font-16 mb-6">
          {{ submission.answers.name }}
        </h1>
        <v-btn elevation="0" type="text" color="transparent" @click="close()">
          <v-icon>
            mdi-close
          </v-icon>
        </v-btn>
      </div>
      <p class="font-12">
        {{ submission.answers.description }}
      </p>
      <hr style="border-color: rgba(0,0,0,0.1)">
      <v-row>
        <v-col md="6" sm="12" v-for="(field,index) in submission.answers.fields" :key="field.field_name">
          <p class="ma-2 font-weight-bold">
            {{ index + 1 }}. <span v-html="parseMarkdownText(field.field_name)"></span>
          </p>
          <p class="ma-2" v-if="field.type === 'check_boxes'">
            {{ field.answer && field.answer.length ? field.answer.join(', ') : "N/A" }}
          </p>
          <p class="ma-2" v-else-if="field.type === 'drop_down'">
            {{ field.answer && field.answer.value ? field.answer.value : "N/A" }}
          </p>
          <p class="ma-2" v-else-if="field.type === 'phone_number'">
            {{ field.answer.country_code }}{{field.answer.mobile_number}}
          </p>
          <p class="ma-2" v-else-if="field.type === 'file'">
            <a :href="getFileUrl(field)" target="_blank" referrerpolicy="no-referrer">
              View File
            </a>
          </p>
          <p class="ma-2" v-else>
            {{ field.answer && field.answer ? field.answer : "N/A" }}
          </p>
          <hr style="border-color: rgba(0,0,0,0.1)">
        </v-col>
      </v-row>
    </v-card>
  </v-dialog>

</template>
<script>
export default {
  props: {
    isModalVisible: {type: Boolean},
    id: {type: Number}
  },
  data() {
    return {
      submission: null
    }
  },
  mounted() {
    this.getSubmission()
  },
  watch: {
    id(val) {
      if (val) {
        this.getSubmission();
      }
    },
  },
  methods: {
    getSubmission() {
      this.showLoader("Loading Submission");
      this.$http.get(`venues/b2c/forms/submissions/get/${this.$props.id}`).then(response => {
        if (response.status == 200) {
          this.submission = response.data.data;
        }
      }).catch(err => {
        this.showError(err)
      }).finally(() => {
        this.hideLoader()
      })
    },
    getFileUrl(field){
      return this.s3BucketURL + field.answer
    },
    close(){
      this.$emit('close')
    }
  }
};
</script>

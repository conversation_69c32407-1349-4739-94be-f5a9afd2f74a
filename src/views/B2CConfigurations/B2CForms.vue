<template>
  <v-container fluid no-gutters>
    <B2cTabs/>
    <div class="d-flex w-full justify-end">
      <v-btn
          dark
          @click="openConfigurations"
          style="background-color: #112A46;color: white; margin-right:10px; text-transform: none"
      >
        <v-icon dark>mdi-cog</v-icon>
        Actions List
      </v-btn>
    </div>
    <v-row>
      <v-col v-for="(form,index) in forms" :key="index" lg="3" md="3" xl="3">
        <FormWidget
            :key="index"
            :index="index"
            :id="form.id"
            :name="form.name"
            :status_id="form.status_id"
            :submissions="form.submissions"
            @edit="addOrEditForm"
            @delete="deleteForm"
            @view="viewForm"
            @toggleFormStatus="toggleFormStatus"
        ></FormWidget>
      </v-col>
    </v-row>

    <div class="add_btn mt-8" style="text-align: left">
      <v-btn
          color="#00AAAAFF"
          dark
          @click="addOrEditForm()"
          style="text-transform: none"
      >
        + Add Form
      </v-btn>
    </div>

  </v-container>
</template>
<script>
import B2cTabs from "@/views/B2CConfigurations/B2cTabs.vue";
import FormWidget from "@/components/B2C/FormWidget.vue";

export default {
  components: {
    FormWidget,
    B2cTabs,
  },
  name: "B2CForms",
  data() {
    return {
      forms: [],
    }
  },
  mounted() {
    this.getForms();
  },
  methods: {
    openConfigurations() {
      this.$router.push({
        name: "B2CFormsConfiguration",
      });
    },
    getForms(){
      this.showLoader('Loading all forms..');
      this.$http
          .get(`venues/b2c/forms`)
          .then((response) => {
            if (response.status == 200) {
              this.forms = response.data.data;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(()=> {
            this.hideLoader();
      });
    },
    toggleFormStatus(id){
      this.showLoader('changing status...');
      this.$http
          .get(`venues/b2c/forms/toggle/${id}`)
          .then((response) => {
            if (response.status == 200) {
              this.getForms();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(()=> {
        this.hideLoader();
      });
    },
    addOrEditForm(id = null) {
      if (id) {
        this.$router.push({
          name: "B2CEditFormsWithId",
          params: {data: btoa(id)},
        });
      } else {
        this.$router.push({
          name: "B2CAddForms",
        });
      }
    },
    viewForm(id){
      this.$router.push({
        name: "B2CFormView",
        params: {data: btoa(id)},
      });
    },
    deleteForm(id) {
      this.showLoader('deleting form...');
      this.$http
          .get(`venues/b2c/forms/delete/${id}`)
          .then((response) => {
            if (response.status == 200) {
              this.getForms();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(()=> {
        this.hideLoader();
      });
    }
  }
}
</script>
<style scoped>

</style>

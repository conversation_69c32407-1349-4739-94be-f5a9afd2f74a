<template>
  <v-container fluid>
    <v-row class="mb-2 mt-2">
      <v-btn class="back-button" elevation="0">
        <router-link :to="{ name: 'B2CForms' }">
          <v-icon size="18">mdi-arrow-left</v-icon>
        </router-link>
      </v-btn>
      <div style="font-size: 25px;font-weight: 600; margin-left:15px">
        Configuration
      </div>
      <v-spacer></v-spacer>
      <v-switch
          v-model="enable_arabic"
          :false-value="0"
          :true-value="1"
          class="mx-4 my-0"
          dense
          hide-details="auto"
          label="Arabic"
      ></v-switch>
    </v-row>

    <v-form ref="form" v-model="valid" lazy-validation @submit.prevent="saveForm">
      <div class="d-flex align-start gap-x-8">
        <div style="width: 70%">
          <v-card class="shadow px-8 pt-2 rounded-10 pb-0 rounded-border">
            <div class="d-flex justify-space-between">

              <div style="height: 180px; min-width:180px; max-width: 180px;" class="p-l-4 mt-2 w-full">
                <image-uploader
                    @upload="
                      (data) => {
                        file = data;
                      }
                    "
                    @remove="
                      () => {
                        image_path = null;
                      }
                    "
                    :image_path="image_path"
                    :defaultImage="'ground'"
                    message-text=""
                ></image-uploader>
              </div>

              <div class="w-full">
                <v-row>
                  <v-col md="6" lg="6" sm="12">
                    <div
                        class="mt-2"
                        style="font-size: 14px;font-weight: 400; margin-left:15px"
                    >
                      Name:
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                              v-bind="attrs"
                              v-on="on"
                              small
                              color="primary"
                          >
                            mdi-information-outline
                          </v-icon>
                        </template>
                        <span>This is the name that will be displayed only on QPortal for form identification.</span>
                      </v-tooltip>
                      <v-col cols="12" sm="12" md="12" class="px-0">
                        <v-text-field
                            clearable
                            outlined
                            dense
                            background-color="#fff"
                            v-model="name"
                            required
                            hide-details="auto"
                        ></v-text-field>
                      </v-col>
                    </div>
                  </v-col>
                  <v-col md="6" lg="6" sm="12">
                    <div
                        class="mt-2"
                        style="font-size: 14px;font-weight: 400;"
                    >
                      Slug:
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                              v-bind="attrs"
                              v-on="on"
                              small
                              color="primary"
                          >
                            mdi-information-outline
                          </v-icon>
                        </template>
                        <span>This needs to be unique for each form.</span>
                      </v-tooltip>
                      <v-col cols="12" sm="12" md="12" class="px-0">
                        <v-text-field
                            clearable
                            outlined
                            dense
                            background-color="#fff"
                            v-model="slug"
                            required
                            hide-details="auto"
                        ></v-text-field>
                      </v-col>
                    </div>
                  </v-col>
                </v-row>

                <v-row>
                  <v-col :md="enable_arabic ? 6 : 12" :lg="enable_arabic ? 6 : 12" sm="12">
                    <div
                        class="mb-2"
                        style="font-size: 14px;font-weight: 400; margin-left:15px"
                    >
                      Form Title:
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                              v-bind="attrs"
                              v-on="on"
                              class="mr-2"
                              small
                              color="primary"
                          >
                            mdi-information-outline
                          </v-icon>
                        </template>
                        <span>This is the title that will be displayed only on the website.</span>
                      </v-tooltip>
                      <v-col cols="12" sm="12" md="12" class="px-0">
                        <v-text-field
                            clearable
                            outlined
                            dense
                            background-color="#fff"
                            v-model="title"
                            required
                            hide-details="auto"
                        ></v-text-field>
                      </v-col>
                    </div>
                  </v-col>
                  <v-col md="6" lg="6" sm="12" v-if="enable_arabic">
                    <div
                        class="mb-2"
                        style="font-size: 14px;font-weight: 400;"
                    >
                      Arabic Title:
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                              v-bind="attrs"
                              v-on="on"
                              class="mr-2"
                              small
                              color="primary"
                          >
                            mdi-information-outline
                          </v-icon>
                        </template>
                        <span>This is the title that will be displayed only on the website.</span>
                      </v-tooltip>
                      <v-col cols="12" sm="12" md="12" class="px-0">
                        <v-text-field
                            clearable
                            outlined
                            dense
                            background-color="#fff"
                            v-model="ar_title"
                            required
                            hide-details="auto"
                        ></v-text-field>
                      </v-col>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </div>
            <v-row>
              <v-col :md="enable_arabic ? 6 : 12" :lg="enable_arabic ? 6 : 12" sm="12">
                <div class="mb-2" style="font-size: 14px;font-weight: 400; margin-left:15px">
                  Description:
                  <v-col cols="12" class="px-0">
                    <v-textarea
                        v-model="description"
                        name="description"
                        placeholder="Add description..."
                        rows="3"
                        outlined
                        background-color="#fff"
                    ></v-textarea>
                  </v-col>
                </div>
              </v-col>
              <v-col md="6" lg="6" sm="12" v-if="enable_arabic">
                <div class="mb-2" style="font-size: 14px;font-weight: 400; margin-left:15px">
                  Arabic Description:
                  <v-col cols="12" class="px-0">
                    <v-textarea
                        v-model="ar_description"
                        name="description"
                        placeholder="Add Arabic description..."
                        rows="3"
                        outlined
                        background-color="#fff"
                    ></v-textarea>
                  </v-col>
                </div>
              </v-col>
            </v-row>
          </v-card>

          <div
              class="mt-8"
              style="font-size: 20px;font-weight: 500; margin-left:15px; margin-bottom:4px"
          >
            Fields
          </div>

          <div v-if="fields.length > 0">
            <LeadFormGeneric
                v-for="(lead, key) in fields"
                :key="key"
                :id="key"
                :type="lead.type"
                :arabic_enabled="enable_arabic"
                :options="lead.options"
                :is_required="lead.is_required"
                :field_name="lead.field_name"
                :ar_field_name="lead.ar_field_name"
                @deleteField="deleteField(key)"
                @setFieldName="setFieldName"
                @setArFieldName="setArFieldName"
                @setIsRequired="setIsRequired"
                @moveUp="moveUp(key)"
                @moveDown="moveDown(key)"
                @addOption="addOption(key)"
                @deleteOption="deleteOption"
                :allow-markdown="true"
            />
          </div>
          <div v-else>
            <v-card class="shadow px-8 py-4 rounded-10 mt-1 rounded-border">
              <div
                  class="mb-2 mt-2"
                  style="font-size: 14px;font-weight: 400; display:flex; align-items:center; justify-content:center"
              >
                <span>Please add some fields</span>
              </div>
            </v-card>
          </div>
        </div>
        <div style="position:sticky; top:100px; width: 30%;">
          <v-card
              class="shadow pt-2 rounded-10 pb-0 rounded-border"
              style="position:sticky;"
          >
            <LeadFormActions @addFormItem="addNewFormItem"/>
          </v-card>

          <v-card
              class="shadow px-8 pt-2 mt-10 rounded-10 pb-0 rounded-border"
              style="position:sticky;"
          >
            <div class="mb-2 mt-2" style="font-size: 14px;font-weight: 400; ">
              Choose Sequence
            </div>
            <v-divider horizontal/>
            <v-select
                v-model="b2c_form_actions_list_id"
                :items="actionsList"
                item-value="id"
                outlined
                dense
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
                item-text="name"
                required
            ></v-select>
          </v-card>

          <v-card
              class="shadow px-8 pt-2 mt-10 rounded-10 pb-0 rounded-border"
              style="position:sticky;"
          >
            <div class="mb-2 mt-2" style="font-size: 14px;font-weight: 400; ">
              Operator Emails
            </div>
            <v-divider horizontal/>
            <v-col v-for="(operator_email,index) in operator_emails" :key="index" md="12" sm="12">
              <v-row>
                <v-col md="10">
                  <v-text-field
                      clearable
                      outlined
                      dense
                      background-color="#fff"
                      v-model="operator_emails[index]"
                      required
                      hide-details="auto"
                      :rules="[
                        (v) => !!v || 'Email is required',
                        (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                      ]"
                  ></v-text-field>
                </v-col>
                <v-col md="2">
                  <v-btn
                      small
                      block
                      color="white"
                      style="color:red;"
                      depressed
                      @click="deleteEmail(index)"
                  >
                    <v-icon left dark>
                      mdi-trash-can-outline
                    </v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
            <div
                style=""
                class="add-new-option"
                @click="addEmail"
            >
              + Add Email
            </div>
          </v-card>
          <v-card
              class="shadow px-8 pt-2 pb-4 mt-10 rounded-10 pb-0 rounded-border"
              style="position:sticky;"
          >
            <div class="mb-2 mt-2" style="font-size: 14px;font-weight: 400; ">
              Success Message
            </div>
            <v-divider horizontal/>
            <div>
              <RichEditor
                  v-model="success_message"
                  id="editor"
              />
            </div>
          </v-card>
        </div>
      </div>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn class="ma-2 white--text teal-color" type="submit">Save</v-btn>
      </v-card-actions>
    </v-form>
  </v-container>
</template>

<script>
import LeadFormActions from "@/views/LeadTracking/LeadFormActions.vue";
import LeadFormGeneric from "@/components/Leads/LeadFormGeneric.vue";
import ImageUploader from "@/components/Image/ImageUploader.vue";
import RichEditor from '@/components/Common/RichEditor.vue'

export default {
  name: "B2CAddOrEditForm",
  components: { RichEditor, ImageUploader, LeadFormGeneric, LeadFormActions},
  data() {
    return {
      id: null,
      enable_arabic: 0,
      valid: true,
      fields: [],
      image_path: null,
      form_action: null,
      name: "",
      title: "",
      ar_title: "",
      slug: "",
      description: "",
      ar_description: "",
      file: "",
      success_message:"",
      operator_emails: [],
      b2c_form_actions_list_id: null,
      names: [
        { name: "full_name", value: "Full Name", ar_value: "الاسم الكامل" },
        { name: "phone_number", value: "Phone Number", ar_value: "رقم الهاتف" },
        { name: "email", value: "Email", ar_value: "البريد الإلكتروني" },
        { name: "radio_buttons", value: "Radio Buttons", ar_value: "أزرار الاختيار" },
        { name: "check_boxes", value: "Check Boxes", ar_value: "مربعات الاختيار" },
        { name: "text_box", value: "Text Field", ar_value: "حقل النص" },
        { name: "text_area", value: "Text Area", ar_value: "منطقة النص" },
        { name: "drop_down", value: "Dropdown list", ar_value: "قائمة منسدلة" },
        { name: "file", value: "Attachment", ar_value: "مرفق" },
        {name: "nationality", value: "Nationality/Country",ar_value:"الجنسية / البلد"},
        {name: "date", value: "Date",ar_value:"تاريخ"},
      ]
    }
  },
  computed: {
    actionsList() {
      return this.$store.getters.getB2CActionsList.data;
    },
  },
  mounted() {
    if (this.$route.params.data) {
      let data = JSON.parse(atob(this.$route.params.data));
      this.id = data;
      setTimeout(() => {
        this.getFormData(data);
      }, 200);
    }

    if (this.$store.getters.getB2CActionsList.status == false) {
      this.$store.dispatch("loadB2CActionsList");
    }
  },
  methods: {
    addEmail() {
      this.operator_emails.push(null);
    },
    deleteEmail(index) {
      this.operator_emails.splice(index, 1);
    },
    getFormData(id) {
      this.showLoader("Fetching details...");

      this.$http
          .get(`venues/b2c/forms/get-form/${id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              if (response.data.data) {
                let data = response.data.data;
                this.enable_arabic = data.enable_arabic;
                this.name = data.name;
                this.title = data.title;
                this.ar_title = data.ar_title;
                this.image_path = data.image_path;
                this.slug = data.slug;
                this.description = data.description;
                this.ar_description = data.ar_description;
                this.fields = data.fields;
                this.b2c_form_actions_list_id = data.b2c_form_actions_list_id;
                this.operator_emails = data.operator_emails ?? [];
                this.success_message = data.success_message ?? "";
              }
            }
            this.hideLoader();
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    saveForm(e) {
      if (e != null) {
        e.preventDefault();
        if (!this.$refs.form.validate()) {
          this.showError("Please fill all required fields");
          return;
        }
      }
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      this.showLoader("Saving...");

      const formData = new FormData();

      if (this.id) {
        formData.append("id", this.id);
      }

      if(this.operator_emails && this.operator_emails.length > 0){
        this.operator_emails.forEach((email, index) => {
          formData.append(`operator_emails[${index}]`, email);
        })
      }

      formData.append("enable_arabic", this.enable_arabic);
      formData.append("name", this.name);
      formData.append("title", this.title);
      formData.append("ar_title", this.ar_title);
      formData.append("slug", this.slug);
      formData.append("description", this.utf8ToBase64(this.sanitizeHTML(this.description)));
      formData.append("ar_description", this.utf8ToBase64(this.sanitizeHTML(this.ar_description)));
      formData.append("fields", JSON.stringify(this.fields));
      formData.append("b2c_form_actions_list_id", this.b2c_form_actions_list_id);
      formData.append("success_message", this.success_message);

      if (this.file) {
        formData.append("image", this.file);
      }

      this.$http({
        method: "post",
        data: formData,
        url: `venues/b2c/forms/save-form`,
        headers: {
          "Content-Type": "multipart/form-data; boundary=${form._boundary}",
        },
      }).then((response) => {
        if (response.status == 200 && response.data.status == true) {
          this.showSuccess("Configuration saved successfully.");
          this.$router.push({
            name: "B2CForms",
          });
        }
        this.hideLoader();
      })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    getName(type) {
      let n = this.names.find((ele) => {
        return ele.name === type;
      });
      return n.value;
    },
    getArName(type) {
      let n = this.names.find((ele) => {
        return ele.name === type;
      });
      return n.ar_value;
    },
    addNewFormItem(type) {
      this.fields.push({
        type: type,
        field_name: this.getName(type),
        ar_field_name: this.getArName(type),
        is_required: "0",
        options: [],
      });
      window.scrollTo({ top: document.body.scrollHeight+300, behavior: "smooth" });
    },
    deleteField(key) {
      this.fields.splice(key, 1);
    },
    setFieldName(obj) {
      this.fields[obj.key].field_name = obj.value;
    },
    setArFieldName(obj) {
      this.fields[obj.key].ar_field_name = obj.value;
    },
    setIsRequired(obj) {
      this.fields[obj.key].is_required = obj.value;
    },
    addOption(key) {
      this.fields[key].options.push({value: ""});
    },
    deleteOption(obj) {
      this.fields[obj.key].options.splice(obj.innerKey, 1);
    },

    moveUp(key) {
      if (this.fields.length <= 1) {
        return;
      }
      let fromIndex = key;
      let toIndex = key - 1;
      if (
          fromIndex < 0 ||
          fromIndex >= this.fields.length ||
          toIndex < 0 ||
          toIndex >= this.fields.length
      ) {
        console.error("Invalid index");
        return this.fields;
      }

      const elementToMove = this.fields.splice(fromIndex, 1)[0];
      this.fields.splice(toIndex, 0, elementToMove);
      return this.fields;
    },
    moveDown(key) {
      if (this.fields.length <= 1) {
        return;
      }
      let fromIndex = key;
      let toIndex = key + 1;
      if (
          fromIndex < 0 ||
          fromIndex >= this.fields.length ||
          toIndex < 0 ||
          toIndex >= this.fields.length
      ) {
        console.error("Invalid index");
        return this.fields;
      }

      const elementToMove = this.fields.splice(fromIndex, 1)[0];
      this.fields.splice(toIndex, 0, elementToMove);
      return this.fields;
    },
  }
}
</script>


<style scoped>
.add-new-option {
  color: var(--main-color, #4faeaf);
  font-family: Inter;
  font-size: 16px;
  cursor: pointer;
  padding-bottom: 20px;
  width: max-content;
  font-weight: 600;

  &:hover {
    text-decoration: underline;
    text-underline-offset: 3px;
  }
}

.back-button {
  background: transparent !important;
  font-weight: 500;

  &,
  * {
    color: #112a46 !important;
  }
}
</style>

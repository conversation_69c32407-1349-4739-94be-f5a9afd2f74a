<template>
  <v-container fluid no-gutters>
    <B2cTabs/>
    <div
        class="d-flex justify-end mt-8 gap-x-4"
    >
      <v-btn
          color=" darken-1"
          class="white--text bg-neon"
          text
          height="48"
          @click="viewConfig = true"
      >
        <span class="ml-1">Tags</span>
      </v-btn>
      <v-btn
          color=" darken-1"
          class="white--text blue-color"
          text
          height="48"
          @click="showResource()"
          v-if="checkWritePermission($modules.b2cconfig.mediaLibrary.slug)"
      >
        <AddIcon />
        <span class="ml-1">Add</span>
      </v-btn>
    </div>
    <div class="md-card md-theme-default shadow-2 rounded-2">
      <div class="md-card-content">
        <v-simple-table class="table">
          <template v-slot:default>
            <thead>
            <tr class="rounded-lg tr-rounded tr-neon opacity-70">
              <th class="text-left black-text" style="max-width: 25%">Title</th>
              <th class="text-left black-text">
                Type
              </th>
              <th class="text-left black-text">Tag</th>
              <th class="text-left black-text">
                <div class="cursor-pointer">
                  Upload Date
                </div>
              </th>
              <th class="text-left black-text">Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr v-if="resources.length < 1">
              <td colspan="6" class="text-center mt-4">
                <h3>No Data found</h3>
              </td>
            </tr>
            <tr class="text-left" v-for="(item,key) in resources" :key="key">
              <td width="40%">
                <div class="d-flex align-center gap-x-2">
                  <component
                      :is="icons[item.type]"
                      v-if="icons[item.type]"
                      :component="icons[item.type]"
                      style="min-width: 18px"
                  />
                  <DefaultIcon v-else></DefaultIcon>
                  <FilePreview
                      v-if="item.document"
                      :extension="item.document.original_file_name.split('.').pop()"
                      :path="item.document.file_path"
                      :item="{name:item.title}"
                  />
                  <p v-else class="mb-0 line-clamp-1">
                    {{ item.title }}
                  </p>
                </div>
              </td>
              <td class="text-capitalize">
                {{ item.type }}
              </td>
              <td>
                {{ item.tag.name }}
              </td>
              <td>
                {{ item.created_at |timeStamp }}
              </td>
              <td>
                <v-menu
                    content-class="q-menu"
                    offset-y
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                        class="text_capitalize "
                        elevation="0"
                        style="background-color: transparent; min-width: fit-content !important; height: fit-content !important; padding: 2px !important "
                        v-bind="attrs"
                        v-on="on"
                        :ripple="false"
                    >
                      <DotsIcon/>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                        @click="showResource(item)"
                        v-if="checkWritePermission($modules.b2cconfig.mediaLibrary.slug)"
                    >
                      <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
                        <template #icon>
                          <EditIcon/>
                        </template>
                      </SvgIcon>
                    </v-list-item>
                    <v-list-item
                        @click="deleteResource(item)"
                        v-if="checkDeletePermission($modules.b2cconfig.mediaLibrary.slug)"
                    >
                      <SvgIcon class="font-medium text-sm gap-x-2 red--text svg-stroke-red" text="Delete">
                        <template #icon>
                          <DeleteIcon/>
                        </template>
                      </SvgIcon>
                    </v-list-item>

                  </v-list>
                </v-menu>
              </td>
            </tr>
            </tbody>
          </template>
        </v-simple-table>
      </div>
    </div>

    <br />

    <confirm-model
        v-bind="confirmModel"
        @close="confirmModel.id = null"
        @confirm="confirmActions"
    ></confirm-model>
    <v-pagination
      v-if="totalPages > 0"
      v-model="page"
      :length="totalPages"
    ></v-pagination>
    <MediaLibraryTags :view-config="viewConfig" @close="viewConfig = false"/>
  </v-container>
</template>

<script>
import SimpleImage from "@/components/Image/SimpleImage.vue";
import AddIcon from "@/assets/images/misc/plus-icon.svg";
import B2cTabs from "@/views/B2CConfigurations/B2cTabs.vue";
import MediaLibraryTags from "@/components/B2C/MediaLibraryTags.vue";
import CopyIcon from "@/assets/images/misc/Copy.svg";
import DefaultIcon from "@/assets/images/misc/file-icons/Default.svg";
import ViewIcon from "@/assets/images/misc/file-icons/eye.svg";
import MoveIcon from "@/assets/images/misc/move.svg";
import EditIcon from "@/assets/images/tables/edit.svg";
import ShareIcon from "@/assets/images/misc/share.svg";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
import DotsIcon from "@/assets/images/misc/h-options.svg";
import FilePreview from "@/components/MediaLibrary/FilePreview.vue";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DownloadIcon from "@/assets/images/misc/file-icons/download.svg";
import ImageIcon from "@/assets/images/qube/media-library/image.svg";
import VideoIcon from "@/assets/images/qube/media-library/video.svg";
import DocumentIcon from "@/assets/images/qube/media-library/document.svg";
import AudioIcon from "@/assets/images/qube/media-library/audio.svg";

export default {
  components: {
    DownloadIcon, SvgIcon, FilePreview, DotsIcon, DeleteIcon, ShareIcon, EditIcon, MoveIcon, ViewIcon,
    DefaultIcon, CopyIcon, MediaLibraryTags, B2cTabs, AddIcon, SimpleImage },
  mounted() {
    this.btnShow = true;
    this.getData();
  },
  data() {
    return {
      page: 1,
      totalPages: 0,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      resources: [],
      btnShow: false,
      viewConfig:false,
      icons: {
        'document': DocumentIcon,
        'audio': AudioIcon,
        'image': ImageIcon,
        'video':VideoIcon,
      },
    };
  },
  watch: {
    page() {
      this.getData();
    },
  },
  methods: {
    getData() {
      this.resources = [];
      this.showLoader("Loading");
      this.$http
        .get(`venues/b2c/media-library`, {
          params: {
            page: this.page,
            per_page: 12,
          },
        })
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.resources = response.data?.data?.data || [];
            this.page = response.data?.data?.current_page || 1;
            this.totalPages = response.data?.data?.last_page || 0;
            this.hideLoader();
          }
        })
        .catch((error) => {
          console.log(error);
          this.hideLoader();
        });
    },
    showResource(resource = null) {
      if (resource) {
        this.$router.push({ name: "B2CMediaLibrary", params: { id: btoa(resource.id) } });
      } else {
        this.$router.push({ name: "B2CMediaLibrary" });
      }
    },
    deleteResource(item) {
      this.confirmModel = {
        id: item.id,
        title: "Delete Resource",
        description: "Are you sure you want to delete this resource?",
        type: 'delete'
      }
    },
    confirmActions(data) {
      if (data.type === 'delete') {
        this.$http
            .delete(`venues/b2c/media-library/${data.id}`)
            .then((response) => {
              if (response.status === 200 && response.data.status) {
                this.showSuccess(response.data.message);
                this.getData()
              }
            })
            .catch((error) => {
              this.showError(error)
            })
      }
    }
  },
};
</script>

<style scoped>
.btn_bg {
  background-color: #fff;
  border-bottom: 3px solid #066a8c;
  border-radius: 10px;
  box-shadow: 0 3px 3px -2px black;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.excerpt {
  min-height: 61px;
}
.resource-title {
  min-height: 41px;
}
</style>

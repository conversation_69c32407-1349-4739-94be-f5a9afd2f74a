<template>
  <v-container fluid>
    <B2cTabs/>
    <div class="d-flex justify-end mt-4">
      <v-btn
          v-if="checkWritePermission($modules.b2cconfig.venue_maintenance.slug)"
          class="bg-blue text-white"
          dark
          height="45"
          @click="gotoAddForm"
      >
        <v-icon dark left>mdi-plus-circle</v-icon>
        Add
      </v-btn>
    </div>
    <template v-if="maintenances.length>0">
      <v-row class="mt-4">
        <v-col v-for="maintenance in maintenances" :key="maintenance.id" lg="4" md="6" sm="12" xl="3">
          <v-card class="shadow pointer" style="border-radius: 8px" @click="showMaintanence(maintenance)">
            <v-card-text>
              <p class="font-semibold text-lg black--text line-clamp-2 mt-6 blog-title">{{ maintenance.title }}</p>
              <div>
                <p class="text-sm text-blue line-clamp-3 mb-1">
                  Start Date: {{ moment(maintenance.start_date).format('ddd, DD MMM YYYY') }}
                </p>
                <p class="text-sm text-blue line-clamp-3 mb-1">
                  End Date: {{ moment(maintenance.end_date).format('ddd, DD MMM YYYY') }}
                </p>
                <p class="text-sm text-blue line-clamp-3 mb-1">
                  Target: {{ getTarget(maintenance) }}
                </p>
                <p class="text-sm text-blue mb-1 d-flex align-center gap-x-2">
                  Status:
                  <LightningBoltIcon :class="maintenance.is_public ? '':'fill-red'"/>
                </p>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <br>
      <v-pagination
          v-if="totalPages > 0"
          v-model="page"
          :length="totalPages"
      ></v-pagination>
    </template>
    <p v-else class="text-2xl text-blue font-semibold text-center py-16">
      No Maintenance Found
    </p>
  </v-container>
</template>

<script>
import moment from 'moment'
import LightningBoltIcon from '@/assets/images/facilities/lightning-bolt.svg'
import B2cTabs from '@/views/B2CConfigurations/B2cTabs.vue'

export default {
  computed: {
    moment () {
      return moment
    }
  },
  components: {
    B2cTabs,
    LightningBoltIcon,
  },
  mounted () {
    if (typeof this.$route.params.type != 'undefined') {
      this.type = this.$route.params.type
    }
    this.getData()
  },
  data () {
    return {
      type: 'website_notification',
      maintenances: [],
      page: 1,
      totalPages: 1,
      id: null,
      search: '',
    }
  },
  methods: {
    getTarget (maintenance) {
      const targets = []
      if (maintenance.is_app) {
        targets.push('App')
      }
      if (maintenance.is_qube) {
        targets.push('Website')
      }
      return targets.join(', ')
    },
    gotoAddForm () {
      this.$router.push({
        name: 'AddVenueMaintenance',
      })
    },
    getData () {
      this.$http
          .get('venues/b2c/maintenance', {
            params: {
              page: this.page,
            }
          })
          .then((response) => {
            if (response.status == 200) {
              this.maintenances = response.data.data.data
              this.page = response.data?.data?.current_page || 1
              this.totalPages = response.data?.data?.last_page || 0
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    showMaintanence (maintenance = null) {
      if (maintenance) {
        this.$router.push({ name: 'AddVenueMaintenance', params: { id: btoa(maintenance.id) } })
      } else {
        this.$router.push({ name: 'AddVenueMaintenance' })
      }
    },
  },
}
</script>

<style scoped>


.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.fill-red {
  fill: red !important;
}
</style>

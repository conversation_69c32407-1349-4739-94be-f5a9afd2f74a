<template>
  <v-container fluid>
    <BackButton :handler="gotoMail"/>
    <v-container class="form_container" fluid no-gutters style="max-width: 95% !important;">
      <v-form ref="form" v-model="valid" lazy-validation>
        <div class="d-flex justify-space-between">
          <div class="titles blue-text">Maintenance</div>
        </div>
        <v-card class="pb-4 rounded-5 shadow-0">
          <v-card-text class="pb-0">
            <v-row>
              <v-col cols="12" md="6" sm="6">
                <label for="title">Title*</label>
                <v-text-field
                    v-model="messageForm.title"
                    :rules="[
                  (v) =>
                    !!v ||
                    'Title is required',
                ]"
                    dense
                    hide-details="auto"
                    class="q-text-field shadow-0"
                    outlined
                    id="title"
                    required
                    validate-on-blur
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <label for="">
                  Target
                </label>
                <v-select
                    v-model="messageForm.target"
                    :items="[{value:'app',text:'App'},{value:'website',text:'Website'}]"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    dense
                    item-text="text"
                    item-value="value"
                    multiple
                    hide-details="auto"
                    outlined
                />
              </v-col>
              <v-col cols="12" md="2" sm="2" class="mt-3">
                    <label for="">
                      Enable Online
                    </label>
                    <v-switch
                        v-model="messageForm.is_public"
                        :false-value="0"
                        :true-value="1"
                        class="mx-4 my-0 v-input--reverse"
                        dense
                        hide-details="auto"
                    />
              </v-col>

              <v-col cols="12" md="12" sm="12">
                <label>Message</label>
                <v-textarea
                    v-model="messageForm.message"
                    dense
                    hide-details="auto"
                    outlined
                    class="shadow-0 q-text-field"
                    validate-on-blur
                />
              </v-col>


            </v-row>
          </v-card-text>
        </v-card>

        <div class="titles mt-8 blue-text">Configuration</div>
        <v-card class="rounded-5 shadow-0">
          <v-card-text class="pb-0">
            <v-row>
              <v-col cols="12" md="3" sm="3">
                <label for="">Start Date*</label>
                <date-field
                    v-model="messageForm.start_date"
                    :backFill="
                  checkBackfillPermission($modules.b2cconfig.venue_maintenance.slug)
                "
                    :hide-details="true"
                    :rules="[(v) => !!v || 'Start date is required']"
                    class-name="q-text-field shadow-0"
                    dense
                    label=""
                ></date-field>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <label for="">Start time*</label>
                <v-select
                    v-model="messageForm.start_time"
                    :items="timings"
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Start Time is required']"
                    class="q-text-field shadow-0"
                    dense
                    item-text="text"
                    item-value="value"
                    outlined
                />
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <label for="">End Date*</label>
                <date-field
                    v-model="messageForm.end_date"
                    :backFill="
                  checkBackfillPermission($modules.b2cconfig.venue_maintenance.slug)
                "
                    :hide-details="true"
                    :rules="[(v) => !!v || 'End date is required']"
                    class-name="q-text-field shadow-0"
                    dense
                    label=""
                ></date-field>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <label for="">End time*</label>
                <v-select
                    v-model="messageForm.end_time"
                    :items="timings"
                    :rules="[(v) => !!v || 'End Time is required']"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    item-text="text"
                    item-value="value"
                    outlined
                />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
        <br>
        <v-row class="mt-3">
          <v-btn v-if="id && checkDeletePermission($modules.b2cconfig.venue_maintenance.slug)"
                 class="ma-2 shadow-0 text-white"
                 color="red"
                 @click="deleteMaintenance">
            Delete
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn
              class="ma-2 white--text teal-color"
              color="darken-1"
              text
              @click="saveMaintenance()"
          >Save
          </v-btn
          >
        </v-row>
      </v-form>
      <confirm-model
          v-bind="confirmModel"
          @close="confirmModel.id = null"
          @confirm="confirmActions"
      ></confirm-model>
    </v-container>
  </v-container>
</template>

<script>
import { mapGetters } from 'vuex'
import BackButton from '@/components/Common/BackButton.vue'
import moment from 'moment'

export default {
  data () {
    return {
      id: null,
      messageForm: {
        title: '',
        message: '',
        start_date: null,
        start_time: null,
        end_date: null,
        end_time: null,
        is_public: 0,
        is_app: 0,
        is_qube: 1,
        target: ['website']
      },
      emailEditContent: '',
      valid: true,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      isArabicEnabled: false
    }
  },
  components: {
    BackButton,
  },
  mounted () {
    if (typeof this.$route.params.id != 'undefined') {
      this.id = atob(this.$route.params.id)
      this.getMessageDetails()
    }
  },
  computed: {
    ...mapGetters({
      checkReadPermission: 'checkReadPermission',
    }),
    timings () {
      const startTime = moment().startOf('day')
      const endTime = moment().endOf('day')
      const timings = []
      while (startTime.isSameOrBefore(endTime)) {
        timings.push({
          value: startTime.format('HH:mm:ss'),
          text: startTime.format('hh:mm a'),
        })
        startTime.add(10, 'minutes')
      }
      return timings
    }
  },
  methods: {
    saveMaintenance () {
      if (!this.$refs.form.validate()) {
        this.showError('Please fill all required fields')
        return
      }
      if (this.messageForm.target.length === 0){
        this.showError('Please select at least one target');
        return
      }
      this.showLoader()
      const data = { ...this.messageForm }

      data.is_app = this.messageForm.target.includes('app') ? 1 : 0
      data.is_qube = this.messageForm.target.includes('website') ? 1 : 0

      delete data.target

      const id = this.id
      const config = {
        method: id ? 'put' : 'post',
        url:
            'venues/b2c/maintenance' +
            (id != null ? '/' + id : ''),
        data: data,
      }
      this.$http(config)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader()
              this.showSuccess('Saved successfully')
              this.$router.push({
                name: 'VenueMaintenance',
              })
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    getMessageDetails () {
      this.showLoader('Loading')
      this.$http
          .get('venues/b2c/maintenance/' + this.id)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const { data } = response.data
              const targets = []
              if (data.is_app) {
                targets.push('app')
              }
              if (data.is_qube) {
                targets.push('website')
              }
              this.messageForm = data
              this.messageForm.is_public = data.is_public ? 1 : 0
              this.messageForm.target = targets
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    gotoMail () {
      this.$router.push({
        name: 'VenueMaintenance',
      })
    },

    deleteMaintenance () {
      if (!this.id) {
        return
      }
      this.confirmModel = {
        id: parseInt(this.id),
        title: 'Delete maintenance',
        description: 'Are you sure you want to delete this maintenance?',
        type: 'delete'
      }
    },
    confirmActions (data) {
      if (data.type === 'delete') {
        this.$http
            .delete(`venues/b2c/maintenance/${this.id}`)
            .then((response) => {
              if (response.status === 200 && response.data.status) {
                this.showSuccess(response.data.message)
                this.$router.push({ name: 'VenueMaintenance' })
              }
            })
            .catch((error) => {
              this.showError(error)
            })
      }
    }
  },
}
</script>

<style></style>

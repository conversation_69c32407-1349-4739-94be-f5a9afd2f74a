<template>
  <v-dialog scrollable :value="show" max-width="800">
    <v-card>
      <v-card-actions>
        <p class="text-2xl font-semibold ml-1" style="">
          Additional Fields
        </p>
      </v-card-actions>
      <v-card-text>
        <v-container class="pl-0 pr-0 pt-0 mt-0">
          <div class="table-responsive">
            <table class="table border-collapse">
              <thead>
              <tr class="opacity-70 tr-neon tr-rounded">
                <th>Field</th>
                <th>Value</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(field, index) in additional_fields" :key="index" class="border-bottom">
                <td style="width: 40%">{{ field.name }}</td>
                <td  style="width: 60%" v-if="field.type==='file' || field.type === 'disclaimer_form'">
                  <v-icon color="cyan" @click="openFile(field.value)" >mdi-download-box</v-icon>
                </td>
                <td  style="width: 60%" v-else>{{ Array.isArray(field.value) ? field.value.join(', ') : field.value }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <!-- <v-btn @click="clearNote(id)" class="ma-2 black--text yellow-color" text
        >Clear Alert
    </v-btn> -->
        <v-btn @click="closeModal()"
               color=" "
               class="ma-2  "
               text
        >Close
        </v-btn>
        <v-spacer></v-spacer>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
export default {
  props: {
    show: { type: Boolean, default: false },
    customerId: { type: Number, default: null },
  },
  data(){
    return {
      additional_fields:[]
    }
  },

  mounted() {
    this.getAdditionalFields()
  },
  methods: {
    closeModal() {
      this.$emit("closeAdditionalField");
    },

    getAdditionalFields() {

      let url = `venues/customers/additional-fields?customerId=${this.customerId}`;
      this.showLoader("Loading");
      this.$http
          .get(url)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.additional_fields = response.data.data;
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
  },
};
</script>

<template>
  <v-container>
    <v-tabs
      v-model="tab"
      background-color="transparent"
      centered
      icons-and-text
    >
      <v-tabs-slider></v-tabs-slider>
      <v-tab
        href="#tab-1"
        v-if="checkReadPermission($modules.general.profile.slug)"
      >
        Profile
        <v-icon color="#066a8c">mdi-account-box</v-icon>
      </v-tab>
      <v-tab
        href="#tab-2"
        v-if="checkReadPermission($modules.general.profile.slug)"
      >
        Documents
        <v-icon color="#066a8c">mdi-file-document</v-icon>
      </v-tab>
      <v-tab
        href="#tab-3"
        v-if="checkReadPermission($modules.general.profile.slug)"
      >
        Address
        <v-icon color="#066a8c">mdi-map-marker</v-icon>
      </v-tab>
      <v-tab
        href="#tab-4"
        v-if="enableSMTP && checkReadPermission($modules.general.profile.slug)"
      >
        SMTP Configuration
        <v-icon color="#066a8c">mdi-at</v-icon>
      </v-tab>
      <v-tab href="#tab-5" v-if="enableSSO">
        Azure AD configuration
        <v-icon color="#066a8c">mdi-microsoft-azure</v-icon>
      </v-tab>
      <v-tab href="#tab-6" v-if="checkReadPermission($modules.general.ai_sensy.slug)">
        AI Sensy
        <v-icon color="#066a8c">mdi-whatsapp</v-icon>
      </v-tab>
    </v-tabs>
    <v-tabs-items v-model="tab">
      <v-tab-item value="tab-1">
        <div id="section1" class="section">
          <venue-details
            v-bind="profileForm"
            @updateVenue="editModal"
            v-bind:services="venueServices"
            @deleteService="deleteServiceConfirm"
            @addService="openAddServiceDialog"
          ></venue-details>
        </div>
      </v-tab-item>

      <v-tab-item value="tab-2">
        <div id="section4" class="section">
          <venue-docs></venue-docs>
        </div>
      </v-tab-item>

      <v-tab-item value="tab-3">
        <div id="section6" class="section">
          <venue-address></venue-address>
        </div>
      </v-tab-item>

      <v-tab-item v-if="enableSMTP" value="tab-4">
        <div id="section6" class="section">
          <MailChimpSMTP></MailChimpSMTP>
        </div>
      </v-tab-item>

      <v-tab-item v-if="enableSSO" value="tab-5">
        <div id="section6" class="section">
          <AzureAdSSO></AzureAdSSO>
        </div>
      </v-tab-item>

      <v-tab-item v-if="checkReadPermission($modules.general.ai_sensy.slug)" value="tab-6">
        <div id="section6" class="section">
          <AiSensy></AiSensy>
        </div>
      </v-tab-item>

    </v-tabs-items>

    <v-dialog v-model="profile_edit" scrollable width="50%" persistent>
      <v-form v-on:submit.prevent ref="form" v-model="valid" lazy-validation>
        <v-card>
          <v-card-title class="headline">Update Profile</v-card-title>
          <v-card-text class="form_bg">
            <v-container>
              <v-row class="ma-1">
                <v-col md="12">
                  <v-row no-gutters justify="center">
                    <v-col md="6"
                      ><image-upload
                        @upload="
                          (data) => {
                            profileForm.image = data;
                          }
                        "
                        @remove="
                          () => {
                            profileForm.image = null;
                          }
                        "
                        :image_path="profileForm.profile_image"
                        :height="160"
                        defaultImage="default"
                      ></image-upload
                    ></v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileForm.name"
                    label="Venue Name"
                    required
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Name is required']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <mobile-field
                    v-model="profileForm.phone"
                    label="Phone Number"
                    required
                  ></mobile-field>
                </v-col>
                <v-col cols="12" md="6" class="pt-0">
                  <v-text-field
                    v-model="profileForm.display_address"
                    label="Venue Address"
                    required
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Address is required']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6" class="pt-0">
                  <v-text-field
                    v-model="profileForm.email"
                    label="E-mail"
                    :rules="[
                      (v) => !!v || 'E-mail is required',
                      (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                    ]"
                    required
                    outlined
                    background-color="#fff"
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0">
                  <v-textarea
                    name="description"
                    label="About Venue"
                    v-model="profileForm.description"
                    value
                    outlined
                    background-color="#fff"
                    :rules="[(v) => !!v || 'Description is required']"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              class="ma-2 white--text teal-color"
              text
              @click="profile_edit = false"
              >Close</v-btn
            >
            <v-btn
              class="ma-2 white--text blue-color"
              text
              @click="updateProfile"
              >Update</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>

    <v-dialog v-model="serviceDialog" scrollable max-width="450" persistent>
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-title class="headline">Add Service</v-card-title>
          <v-card-text class="form_bg">
            <v-row class="mt-4">
              <v-col md="12">
                <v-select
                  v-model="serviceType"
                  label="Select Category"
                  required
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  @change="resetCategorySelection"
                  :items="['Sports', 'Social']"
                  :rules="[(v) => !!v || 'Please select the type']"
                ></v-select>
              </v-col>
              <v-col md="12">
                <v-select
                  v-model="service"
                  label="Select Service"
                  item-value="id"
                  item-text="name"
                  :items="serviceByType()"
                  @change="serviceChange"
                  required
                  return-object
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  :rules="[(v) => !!v || 'Categories is required']"
                ></v-select>
              </v-col>
              <v-col
                cols="md"
                v-if="serviceType != null && service.name == 'Custom'"
              >
                <v-text-field
                  v-model="serviceForm.name"
                  label="Service Name"
                  required
                  outlined
                  background-color="#fff"
                  :rules="[(v) => !!v || 'Service Name is required']"
                ></v-text-field>
              </v-col>
              <v-col cols="12" justify="center">
                <v-row
                  v-if="serviceImageUrl"
                  no-gutters
                  justify="center"
                  style="margin-top: -20px"
                >
                  <v-col md="3">
                    <v-card
                      flat
                      color="#edf9ff"
                      tile
                      class="d-flex"
                      elevation="0"
                    >
                      <img
                        :src="serviceImageUrl"
                        height="120"
                        width="120"
                        style="object-fit: contain"
                      />
                    </v-card> </v-col
                ></v-row>
              </v-col>
              <div
                v-if="serviceType != null && service.name == 'Custom'"
                style="min-height: 200px; max-height: 200px"
                class="overflow-y-auto pa-3 overflow-x-hidden"
              >
                <v-row>
                  <v-col
                    md="3"
                    v-for="(image, index) in categoryImages"
                    :key="index"
                  >
                    <v-hover v-slot:default="{ hover }">
                      <v-card
                        color="#edf9ff"
                        tile
                        class="d-flex"
                        :elevation="hover ? 4 : 0"
                        @click="selectImage(image)"
                      >
                        <v-img
                          :src="s3BucketURL + image"
                          contain
                          aspect-ratio="1.1"
                        ></v-img>
                      </v-card>
                    </v-hover>
                  </v-col>
                </v-row>
              </div>
              <v-col
                cols="12"
                v-if="serviceType != null && service.name == 'Custom'"
              >
                <v-row no-gutters justify="center">
                  <v-col md="5">
                    <image-upload
                      @upload="
                        (data) => {
                          serviceForm.image = data;
                        }
                      "
                      @result="
                        (data) => {
                          serviceImageUrl = data;
                        }
                      "
                      text="Click to upload custom icon or image"
                      @remove="
                        (data) => {
                          serviceForm.image = null;
                          serviceForm.map_pin = null;
                          serviceImageUrl = null;
                        }
                      "
                    ></image-upload> </v-col
                ></v-row>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              class="ma-2 white--text blue-color"
              text
              @click="closeServiceForm"
              >Close</v-btn
            >
            <v-btn class="ma-2 white--text teal-color" text @click="addService"
              >Add</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>

    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    ></confirm-model>
  </v-container>
</template>

<script>
import VenueDetails from "./Tabs/VenueDetails";
import VenueDocs from "./Tabs/VenueDocs";
// import KrewsPosts from "./Tabs/KrewsPosts";
import VenueAddress from "./Tabs/VenueAddress";
import MailChimpSMTP from "./Tabs/MailChimpSMTP";
import AiSensy from "./Tabs/AiSensy.vue";
import AzureAdSSO from "./Tabs/AzureAdSSO";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";
export default {
  name: "Profile",
  components: {
    VenueDetails,
    VenueDocs,
    VenueAddress,
    MailChimpSMTP,
    ConfirmModel,
    AiSensy,
    AzureAdSSO,
  },
  data() {
    return {
      enableSMTP: false,
      enableSSO: false,
      enableAiSensy: true,
      tab: null,
      valid: true,
      profile: {},
      venueServices: [],
      profile_edit: false,
      profileForm: {},
      serviceDialog: false,
      serviceForm: {},
      serviceImageUrl: false,
      services: [],
      serviceType: null,
      service: {},
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
    };
  },
  computed: {
    categoryImages() {
      return this.services
        .filter((item) => item.image_path != "" && item.image_path != null)
        .map((item) => item.image_path);
    },
  },
  created() {
    this.getProfileDetails();
    this.getGlobalServies();
  },
  methods: {
    getProfileDetails() {
      this.$store.dispatch("loadVenueProfileDetails").then(() => {
        const data = this.$store.getters.getVenueDetails.data;
        console.log(data);
        this.venueServices = JSON.parse(JSON.stringify(data.services));
        this.profileForm = JSON.parse(JSON.stringify(data.profile));
        this.enableSMTP = data.enable_smtp ? data.enable_smtp : 0;
        this.enableSSO = data.enable_azure_ad_sso
          ? data.enable_azure_ad_sso
          : 0;
      });
    },
    getGlobalServies() {
      this.$http
        .get("services/global")
        .then((response) => {
          if (response.status == 200) {
            this.services = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    serviceChange() {
      this.serviceForm.service_id = this.service.id;
      if (this.serviceForm.service_id != null) {
        this.serviceForm.name = this.service.name;
        this.serviceForm.default_image = this.service.image_path;
        this.serviceImageUrl =
          this.serviceForm.default_image != null &&
          this.serviceForm.default_image != ""
            ? this.s3BucketURL + this.serviceForm.default_image
            : null;
      } else {
        this.serviceForm.name = null;
        this.serviceForm.default_image = null;
        this.serviceImageUrl = null;
      }
    },
    resetCategorySelection() {
      this.service = {};
      this.serviceForm.serviceForm = null;
      this.serviceForm.name = null;
      this.serviceForm.default_image = null;
    },
    selectImage(image) {
      this.serviceForm.default_image = image;
      this.serviceImageUrl = this.s3BucketURL + this.serviceForm.default_image;
    },
    updateProfile() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      this.showLoader();
      event.preventDefault();
      let formData = new FormData();
      Object.keys(this.profileForm).forEach((key) => {
        let value = this.profileForm[key];
        formData.append(key, value);
      });
      this.$http
        .post("venues/profile", formData, {
          headers: {
            "Content-Type": "multipart/form-data; boundary=${form._boundary}",
          },
        })
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.profile_edit = false;
            this.showSuccess("Venue Details Updated Successfully.");
            this.hideLoader();
            this.getProfileDetails();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    editModal() {
      this.profile_edit = true;
    },
    deleteServiceConfirm(data) {
      this.confirmModel = {
        id: data,
        title: "Do you want to delete this service?",
        description:
          "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "delete_service",
      };
    },
    confirmActions(data) {
      if (data.type == "delete_service") {
        this.deleteService(data.id);
      }
      this.confirmModel.id = null;
    },
    deleteService(id) {
      this.showLoader();
      this.$http
        .delete(`venues/services/${id}`)
        .then((response) => {
          if (response.status == 200) {
            this.showSuccess("Service Deleted Successfully.");
            this.hideLoader();
            this.getProfileDetails();
            this.$store.dispatch("loadVenueServices");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    openAddServiceDialog() {
      this.serviceForm = {};
      this.serviceType = null;
      this.service = null;
      this.serviceDialog = true;
      this.serviceImageUrl = null;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },
    addService() {
      if (!this.$refs.form.validate()) return;
      if (this.serviceImageUrl == null) {
        this.showError("Select any image");
        return;
      }
      this.showLoader();
      event.preventDefault();
      let formData = new FormData();
      Object.keys(this.serviceForm).forEach((key) => {
        let value = this.serviceForm[key];
        if (value != null) {
          formData.append(key, value);
        }
      });

      this.$http
        .post("venues/services", formData)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.serviceDialog = false;
            this.showSuccess("Venue Details Updated Successfully.");
            this.hideLoader();
            this.getProfileDetails();
            this.$store.dispatch("loadVenueServices");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    closeServiceForm() {
      this.serviceDialog = false;
      this.$refs.form.resetValidation();
    },
    serviceByType() {
      if (this.serviceType != null) {
        let type = this.serviceType.toLowerCase();
        let data = this.services
          .filter((item) => {
            return item.service_type == type;
          })
          .sort((a, b) => a.order - b.order);
        data = [{ id: null, name: "Custom" }, ...data];
        return data;
      }
      return [];
    },
  },
};
</script>

<style scoped>
.v-btn--active .v-btn__content .v-icon {
  color: #66c8c8 !important;
}
.section {
  margin: 20px 0 50px 0;
  padding: 20px 0 20px;
}
.profile_title {
  padding: 10px;
  text-align: center;
}

.profile_title h1 {
  color: #066a8c;
}

.v-item-group.v-bottom-navigation {
  top: 64px !important;
  z-index: 1;
}

.nav {
  position: relative;
}
.layout.wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-top: 20px;
}
.v-item-group.v-bottom-navigation {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.theme--light.v-bottom-navigation {
  background-color: #edf9ff;
  height: 75px !important;
}
.v-item-group.v-bottom-navigation--grow .v-btn {
  height: 73px;
  padding-top: 10px;
}
.v-bottom-navigation span {
  font-size: 13px;
  padding: 10px;
  text-align: center;
}

.v-btn__content,
.v-btn--fab.v-btn--absolute {
  z-index: 0 !important;
}
.v-btn--fab.v-btn--absolute {
  z-index: 4;
}
.theme--light.v-tabs-items {
  background-color: transparent;
}
.section {
  margin: 20px 0 50px 0;
  padding: 20px 0 20px;
}
.v-tabs__item--active {
  background: #fff;
}
.v-tab {
  min-width: 150px;
}
.v-tab--active {
  background: #fff !important;
  color: rgb(6, 106, 140);
}
</style>

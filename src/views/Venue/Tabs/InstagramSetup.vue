<template>
  <v-layout ml-5 mr-5 row wrap>
    <v-container>
      <v-row>
        <v-col cols="12" lg="6" md="6" sm="6">
          <v-card class="card">
            <v-card-text class="pt-1 pb-5">
              <div class="row pt-1 border-bottom">
                <div class="col-md-12">
                  <div class="d-flex justify-space-between align-center mt-2">
                    <SvgIcon class="text-xl font-semibold" style="color: black" text="Instagram details">
                    </SvgIcon>
                    <v-btn class="shadow-0" fab x-small @click.stop="showSMTPPopup">
                      <v-icon>mdi-pencil</v-icon>
                    </v-btn>
                  </div>
                </div>
              </div>
              <v-layout wrap>
                <v-flex sm12>
                  <div class="account" style="text-align: left">
                    <div class="fleft commercial">
                      <div><strong>Access Token</strong></div>
                      <div><strong>IG UID</strong></div>
                      <div><strong>IG Username</strong></div>
                      <div><strong>Valid</strong></div>
                      <div><strong>Expires at</strong></div>
                    </div>

                    <div class="fleft commercial">
                      <div>***********</div>
                      <div>{{ instagramData.instagram_user_id || 'NA' }}</div>
                      <div>{{ instagramData.username || 'NA' }}</div>
                      <div>{{ instagramData.is_valid ? 'Yes' : 'No' }}</div>
                      <div>{{ instagramData.expires_at || 'NA' }}</div>
                    </div>

                    <div class="clearfix"></div>
                  </div>
                </v-flex>
              </v-layout>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- SMTP Details  Start-->
    <v-dialog v-model="instagram_dailog" max-width="650" persistent scrollable>
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-text class="border-bottom mb-3">
            <div class="row pt-1 border-bottom">
              <div class="col-md-12">
                <div class="d-flex justify-space-between align-center mt-2">
                  <SvgIcon class="text-2xl font-semibold"
                           style="color: black"
                           text="SMTP Details">
                  </SvgIcon>
                  <v-btn class="shadow-0" fab x-small @click="closeInstagramModal">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
            <v-row class="mt-8" dense>
              <v-col cols="12" md="6">
                <label for="">Access Token</label>
                <v-text-field
                    v-model="instagramData.access_token"
                    :rules="[(v) => !!v || 'Access Token is required']"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                    validate-on-blur
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <label for="">Select Status</label>
                <v-select
                    v-model="instagramData.status_id"
                    :items="[{ id:1,name:'Activate'},{id:2,name:'Deactivate'}]"
                    :menu-props="{ bottom: true, offsetY: true }"
                    :rules="[(v) => !!v || 'Please select the status']"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    item-text="name"
                    item-value="id"
                    outlined
                    required
                    validate-on-blur
                ></v-select>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
                class="ma-2 white--text blue-color"
                text
                @click="closeInstagramModal"
            >Close
            </v-btn
            >
            <v-btn
                class="ma-2 white--text teal-color"
                text
                @click="updateSMTPDetail"
            >Update
            </v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <!-- SMTP Details end-->
  </v-layout>
</template>

<script>
import SvgIcon from '@/components/Image/SvgIcon.vue'

export default {
  components: { SvgIcon },
  data () {
    return {
      instagram_dailog: false,
      valid: true,
      document: {},
      instagramData: {},
    }
  },
  mounted () {
    this.fetchInstagramConfig()
  },
  methods: {
    fetchInstagramConfig () {
      this.showLoader()
      this.$http
          .get('venues/profile/instagram-account', this.instagramData)
          .then(res => {
            if (res.status == 200 && res.data.status) {
              this.instagramData = res.data.data
              if (!this.instagramData) {
                this.instagramData = {
                  access_token: '',
                  status_id: 2
                }
              }
            }
          })
          .catch(err => {
            this.errorChecker(err)
          })
          .finally(() => {
            this.hideLoader()
          })
    },
    updateSMTPDetail () {
      if (!this.$refs.form.validate()) {
        this.showError('Please fill all required fields')
        return
      }
      event.preventDefault()
      this.showLoader()
      this.$http
          .post('venues/profile/instagram-account', this.instagramData)
          .then((response) => {
            if (response.status == 200 && response.data.status) {
              this.showSuccess('Instagram account updated')
              this.instagram_dailog = false
              this.fetchInstagramConfig()
              this.hideLoader()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    showSMTPPopup () {
      this.instagram_dailog = true
    },

    closeInstagramModal () {
      this.instagram_dailog = false
      this.fetchInstagramConfig()
      this.$forceUpdate()
      this.$refs.form.resetValidation()
    },
  },
}
</script>

<style scoped>
.form_bg {
  margin-top: 2px;
}

.v-btn--fab.v-btn--fixed,
.v-btn--fab.v-btn--absolute {
  z-index: 0 !important;
}

.documents .v-icon {
  border-radius: 12px;
  background: black;
}

.doc_image {
  margin: 40px;
}

.doc_btn {
  text-align: center;
}

.account {
  padding: 22px 10px;
  text-align: left;
  margin: 0 auto;
  width: 90%;
}

.title_txt {
  color: #fff;
  font-size: 14px;
  padding: 6px;
  background: #066a8c;
  margin: 5px;
  border-radius: 3px;
  text-align: center;
}

.commercial {
  width: 50%;
}

.commercial div {
  padding: 6px;
}

#section4 .card {
  min-height: 230px;
}

.fleft {
  float: left;
}
</style>

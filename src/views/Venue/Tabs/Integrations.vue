<template>
  <v-col md="3" sm="3">
    <v-card class="pa-4 pb-0 shadow-2">
      <div class="d-flex justify-space-between">
        <p>DCD integration status</p>
        <span v-if="dcdStatus" class="green--text text-lg">ON</span>
        <span v-else class="red--text text-lg">OFF</span>
      </div>
      <v-card-actions>
        <v-btn
            class="ma-2 white--text teal-color"
            text
            @click="setDCDStatus"
        >Toggle
        </v-btn
        >
      </v-card-actions>
    </v-card>
  </v-col>
</template>
<script>
export default {
  name: "Integrations",
  props: ["dcdStatus"],
  mounted() {
    console.log(this.dcdStatus, 'dcdStatus');
  },
  methods: {
    setDCDStatus() {
      this.$emit("setDCDStatus");
    }
  }
}
</script>


<style scoped>

</style>
<template>
  <v-container fluid v-if="checkReadPermission($modules.general.venue_outlet_management.slug)">
    <div class="d-flex justify-space-between align-center mb-4">
      <h3 class="font-semibold text-blue">
        Venue Outlets
      </h3>

      <v-text-field
        v-model="searchTerm"
        class="q-text-field shadow-0 bg-white"
        hide-details="auto"
        outlined
        placeholder="Search outlets..."
        @input="searchOutlets"
        @keydown.enter="searchOutlets"
        @click:clear="clearSearch"
        style="max-width: 300px"
        dense
        clearable
      >
        <template #prepend-inner>
          <SearchIcon/>
        </template>
      </v-text-field>
    </div>

    <v-row v-if="venue_outlets.length > 0">
      <v-col
        v-for="(venue_outlet, index) in venue_outlets"
        :key="index"
        cols="12"
        sm="6"
        md="4"
        lg="3"
        xl="2"
        class="mt-4"
      >
        <v-card
          color="white"
          class="rounded-3 shadow-2 outlet-card"
          @click="checkWritePermission($modules.general.venue_outlet_management.slug) ? editVenueOutlet(venue_outlet) : null"
          :style="checkWritePermission($modules.general.venue_outlet_management.slug) ? 'cursor: pointer;' : 'cursor: default;'"
        >
          <v-menu
            content-class="q-menu"
            right
            top
            v-if="checkWritePermission($modules.general.venue_outlet_management.slug) || checkDeletePermission($modules.general.venue_outlet_management.slug)"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                v-on="on"
                fab
                x-small
                absolute
                top
                right
                elevation="0"
                class="border-0"
                @click.stop
              >
                <DotsIcon/>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                @click="editVenueOutlet(venue_outlet)"
                v-if="checkWritePermission($modules.general.venue_outlet_management.slug)"
              >
                <v-list-item-icon>
                  <EditIcon />
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>Edit</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
              <v-list-item
                @click="deleteConfirm(venue_outlet)"
                v-if="checkDeletePermission($modules.general.venue_outlet_management.slug)"
              >
                <v-list-item-icon>
                  <DeleteIcon />
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>Delete</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-card-text class="py-0">
            <div class="d-flex justify-start align-center gap-x-4">
              <div class="outlet-content">
                <div class="outlet_name">
                  {{ venue_outlet.name }}
                </div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="12"
        sm="6"
        md="4"
        lg="3"
        xl="2"
        class="mt-4"
        v-if="checkWritePermission($modules.general.venue_outlet_management.slug)"
      >
        <v-card
          height="100"
          color="white"
          class="d-flex rounded-3 flex-column justify-center align-center add-outlet-btn"
        >
          <v-btn
            color="#ffffff"
            elevation="0"
            style="color:#4FAEAF;"
            fab
            @click.stop="openAddDialog"
          >
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <div v-else class="text-center mt-8 mb-0">
      <span class="text-grey-600">No Venue Outlets found</span>
    </div>

    <v-dialog v-model="modalStatus" max-width="500px" persistent>
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-text class="border-bottom mb-3">
            <div class="row pt-1 border-bottom">
              <div class="col-md-12">
                <div class="d-flex justify-space-between align-center mt-2">
                  <h3 class="text-xl font-semibold">
                    {{ isEdit ? 'Edit' : 'Add' }} Venue Outlet
                  </h3>
                  <v-btn fab x-small class="shadow-0" @click="closeModal">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
            <v-row class="mt-6" dense>
              <v-col cols="12">
                <label>Outlet Name*</label>
                <v-text-field
                  v-model="form.name"
                  required
                  outlined
                  background-color="#fff"
                  :rules="nameRules"
                  validate-on-blur
                  class="q-text-field shadow-0"
                  hide-details="auto"
                  dense
                />
              </v-col>

            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn class="ma-2" text @click="closeModal">
              Cancel
            </v-btn>
            <v-btn
              class="ma-2 white--text teal-color"
              text
              @click="saveVenueOutlet"
              :loading="saving"
              v-if="checkWritePermission($modules.general.venue_outlet_management.slug)"
            >
              {{ isEdit ? 'Update' : 'Save' }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>

    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    />
  </v-container>

  <!-- No Permission Message -->
  <v-container v-else fluid>
    <v-row justify="center">
      <v-col cols="12" md="8" lg="6">
        <v-alert type="warning" outlined class="text-center">
          <v-icon slot="prepend" size="48">mdi-lock</v-icon>
          <h3 class="mb-2">Access Denied</h3>
          <p class="mb-0">You don't have permission to view Venue Outlets. Please contact your administrator for access.</p>
        </v-alert>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import DotsIcon from "@/assets/images/misc/h-options.svg";
import EditIcon from "@/assets/images/tables/edit.svg";
import DeleteIcon from "@/assets/images/retail/delete-bg-icon.svg";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";

export default {
  name: "VenueOutlets",
  components: {
    DotsIcon,
    EditIcon,
    DeleteIcon,
    ConfirmModel
  },
  data() {
    return {
      venue_outlets: [],
      modalStatus: false,
      valid: true,
      saving: false,
      isEdit: false,
      searchTerm: '',
      searchTimeout: null,
      form: {
        name: ''
      },
      confirmModel: {
        id: null,
        title: null,
        description: null,
        type: null
      }
    };
  },
  computed: {
    nameRules() {
      return [
        v => !!v || 'Name is required',
        v => (v && v.length <= 255) || 'Name must be less than 255 characters'
      ];
    }
  },
  mounted() {
    if (this.checkReadPermission(this.$modules.general.venue_outlet_management.slug)) {
      this.loadVenueOutlets();
    }
  },
  methods: {
    loadVenueOutlets() {
      this.showLoader("Loading outlets");
      let url = "venues/venue-outlets";

      if (this.searchTerm && this.searchTerm.trim() !== '') {
        url += `?search=${encodeURIComponent(this.searchTerm.trim())}`;
      }

      this.$http
        .get(url)
        .then((response) => {
          if (response.status === 200 && response.data.status === true) {
            this.venue_outlets = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.hideLoader();
        });
    },

    searchOutlets() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      this.searchTimeout = setTimeout(() => {
        this.loadVenueOutlets();
      }, 500);
    },

    openAddDialog() {
      this.isEdit = false;
      this.form = {
        name: ''
      };
      this.modalStatus = true;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },

    editVenueOutlet(outlet) {
      this.isEdit = true;
      this.form = {
        id: outlet.id,
        name: outlet.name
      };
      this.modalStatus = true;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },

    closeModal() {
      this.modalStatus = false;
      this.isEdit = false;
      this.form = {
        name: ''
      };
    },

    saveVenueOutlet() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }

      this.saving = true;
      const formData = new FormData();
      formData.append('name', this.form.name);

      if (this.isEdit && this.form.id) {
        formData.append('id', this.form.id);
      }

      const url = this.isEdit
        ? `venues/venue-outlets/${this.form.id}`
        : 'venues/venue-outlets';

      this.$http
        .post(url, formData)
        .then((response) => {
          if (response.status === 200 && response.data.status === true) {
            this.showSuccess(
              this.isEdit
                ? "Venue outlet updated successfully"
                : "Venue outlet created successfully"
            );
            this.closeModal();
            this.loadVenueOutlets();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.saving = false;
        });
    },

    deleteConfirm(outlet) {
      this.confirmModel = {
        id: outlet.id,
        title: "Do you want to delete this venue outlet?",
        description: "By clicking <b>Yes</b> you can confirm the operation. Do you need to continue your action?",
        type: "delete"
      };
    },

    confirmActions(data) {
      if (data.type === "delete") {
        this.deleteVenueOutlet(data.id);
      }
      this.confirmModel.id = null;
    },

    deleteVenueOutlet(id) {
      this.showLoader("Deleting outlet");
      this.$http
        .delete(`venues/venue-outlets/${id}`)
        .then((response) => {
          if (response.status === 200 && response.data.status === true) {
            this.showSuccess("Venue outlet deleted successfully");
            this.loadVenueOutlets();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.hideLoader();
        });
    }
  }
}
</script>

<style scoped>
.outlet_name {
  text-align: left;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.outlet-content {
  flex: 1;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.outlet-card {
  height: 100px;
  min-width: 150px;
  max-width: 250px;
  width: 100%;
}

/* Better responsive sizing for outlet cards */
@media (max-width: 600px) {
  .outlet-card {
    min-width: 120px;
  }
}

.menu-btn {
  background: rgba(255,255,255,0.9) !important;
  backdrop-filter: blur(4px);
}

.add-outlet-btn {
  border: 1px solid #4FAEAF !important;
  background: rgba(79, 174, 175, 0.1) !important;
}

.new_heading {
  font-size: 18px;
}

/* Remove extra bottom spacing */
.v-container {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}
</style>
<template>
  <v-container fluid v-if="checkReadPermission($modules.general.venue_outlet_management.slug)">
    <div class="d-flex justify-space-between align-center mb-4">
      <h3 class="font-semibold text-blue">
        Venue Outlets
      </h3>

      <v-text-field
        v-model="searchTerm"
        class="q-text-field shadow-0 bg-white"
        hide-details="auto"
        outlined
        placeholder="Search outlets..."
        @input="searchOutlets"
        @keydown.enter="searchOutlets"
        @click:clear="clearSearch"
        style="max-width: 300px"
        dense
        clearable
      >
        <template #prepend-inner>
          <SearchIcon/>
        </template>
      </v-text-field>
    </div>

    <v-row v-if="venue_outlets.length > 0">
      <v-col
        v-for="(venue_outlet, index) in venue_outlets"
        :key="index"
        cols="auto"
        class="mt-4"
      >
        <v-card
          color="white"
          class="rounded-3 shadow-2 outlet-card"
          @click="checkWritePermission($modules.general.venue_outlet_management.slug) ? editVenueOutlet(venue_outlet) : null"
          :style="checkWritePermission($modules.general.venue_outlet_management.slug) ? 'cursor: pointer;' : 'cursor: default;'"
        >
          <v-menu
            content-class="q-menu"
            offset-y
            v-if="checkWritePermission($modules.general.venue_outlet_management.slug) || checkDeletePermission($modules.general.venue_outlet_management.slug)"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                class="text_capitalize px-0 no-hover-effect"
                elevation="0"
                height="24"
                style="background-color: transparent; min-width: 24px !important; position: absolute; top: 8px; right: 8px;"
                v-bind="attrs"
                v-on="on"
                @click.stop
              >
                <DotsIcon height="24" width="24"/>
              </v-btn>
            </template>
            <v-list class="pointer">
              <v-list-item
                v-if="checkWritePermission($modules.general.venue_outlet_management.slug)"
                @click.stop="editVenueOutlet(venue_outlet)"
              >
                <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
                  <template #icon>
                    <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
                  </template>
                </SvgIcon>
              </v-list-item>
              <v-list-item
                v-if="checkDeletePermission($modules.general.venue_outlet_management.slug)"
                @click.stop="deleteConfirm(venue_outlet)"
              >
                <SvgIcon class="font-medium text-sm gap-x-2 text-red" text="Delete">
                  <template #icon>
                    <DeleteIcon stroke="#E50000"/>
                  </template>
                </SvgIcon>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-card-text class="py-0">
            <div class="d-flex justify-start align-center gap-x-4">
              <div class="outlet-content">
                <div class="outlet_name">
                  {{ venue_outlet.name }}
                </div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col
        cols="auto"
        class="mt-4"
        v-if="checkWritePermission($modules.general.venue_outlet_management.slug)"
      >
        <v-card
          height="100"
          color="grey lighten-4"
          class="d-flex rounded-3 flex-column justify-center align-center add-outlet-btn outlet-card"
          style="border: 2px dashed #ccc; cursor: pointer;"
          @click.stop="openAddDialog"
        >
          <v-icon color="grey darken-1" size="32">mdi-plus</v-icon>
          <span class="text-caption grey--text text--darken-1 mt-1">Add Outlet</span>
        </v-card>
      </v-col>
    </v-row>

    <div v-else class="text-center mt-8 mb-0">
      <span class="text-grey-600">No Venue Outlets found</span>
    </div>

    <v-dialog v-model="modalStatus" max-width="500px" persistent>
      <v-form ref="form" v-model="valid">
        <v-card>
          <v-card-text class="border-bottom mb-3">
            <div class="row pt-1 border-bottom">
              <div class="col-md-12">
                <div class="d-flex justify-space-between align-center mt-2">
                  <SvgIcon class="text-2xl font-semibold" :text="isEdit ? 'Edit Venue Outlet' : 'Add Venue Outlet'" style="color: black">
                  </SvgIcon>
                  <v-btn fab x-small class="shadow-0" @click="closeModal">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
            <v-row class="mt-6" dense>
              <v-col cols="12">
                <label>Outlet Name*</label>
                <v-text-field
                  v-model="form.name"
                  required
                  outlined
                  background-color="#fff"
                  :rules="nameRules"
                  validate-on-blur
                  class="q-text-field shadow-0"
                  hide-details="auto"
                  dense
                />
              </v-col>

            </v-row>
          </v-card-text>
          <v-card-actions class="px-6 pb-6">
            <v-spacer />
            <v-btn
              class="ma-2 text-capitalize"
              outlined
              color="grey darken-1"
              @click="closeModal"
            >
              Close
            </v-btn>
            <v-btn
              class="ma-2 text-capitalize white--text"
              color="primary"
              @click="saveVenueOutlet"
              :loading="saving"
              v-if="checkWritePermission($modules.general.venue_outlet_management.slug)"
            >
              {{ isEdit ? 'Update' : 'Add' }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>

    <confirm-model
      v-bind="confirmModel"
      @confirm="confirmActions"
      @close="confirmModel.id = null"
    />
  </v-container>

  <!-- No Permission Message -->
  <v-container v-else fluid>
    <v-row justify="center">
      <v-col cols="12" md="8" lg="6">
        <v-alert type="warning" outlined class="text-center">
          <v-icon slot="prepend" size="48">mdi-lock</v-icon>
          <h3 class="mb-2">Access Denied</h3>
          <p class="mb-0">You don't have permission to view Venue Outlets. Please contact your administrator for access.</p>
        </v-alert>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import DotsIcon from "@/assets/images/misc/dots.svg";
import EditIcon from "@/assets/images/tables/edit.svg";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
import SearchIcon from "@/assets/images/events/search.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";

export default {
  name: "VenueOutlets",
  components: {
    DotsIcon,
    EditIcon,
    DeleteIcon,
    SearchIcon,
    SvgIcon,
    ConfirmModel
  },
  data() {
    return {
      venue_outlets: [],
      modalStatus: false,
      valid: true,
      saving: false,
      isEdit: false,
      searchTerm: '',
      searchTimeout: null,
      form: {
        name: ''
      },
      confirmModel: {
        id: null,
        title: null,
        description: null,
        type: null
      }
    };
  },
  computed: {
    nameRules() {
      return [
        v => !!v || 'Name is required',
        v => (v && v.length <= 255) || 'Name must be less than 255 characters'
      ];
    }
  },
  mounted() {
    if (this.checkReadPermission(this.$modules.general.venue_outlet_management.slug)) {
      this.loadVenueOutlets();
    }
  },
  methods: {
    loadVenueOutlets() {
      this.showLoader("Loading outlets");
      let url = "venues/venue-outlets";

      if (this.searchTerm && this.searchTerm.trim() !== '') {
        url += `?search=${encodeURIComponent(this.searchTerm.trim())}`;
      }

      this.$http
        .get(url)
        .then((response) => {
          if (response.status === 200 && response.data.status === true) {
            this.venue_outlets = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.hideLoader();
        });
    },

    searchOutlets() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      this.searchTimeout = setTimeout(() => {
        this.loadVenueOutlets();
      }, 500);
    },

    clearSearch() {
      this.searchTerm = '';
      setTimeout(() => {
        this.loadVenueOutlets();
      }, 100);
    },

    openAddDialog() {
      this.isEdit = false;
      this.form = {
        name: ''
      };
      this.modalStatus = true;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },

    editVenueOutlet(outlet) {
      this.isEdit = true;
      this.form = {
        id: outlet.id,
        name: outlet.name
      };
      this.modalStatus = true;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },

    closeModal() {
      this.modalStatus = false;
      this.isEdit = false;
      this.form = {
        name: ''
      };
    },

    saveVenueOutlet() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }

      this.saving = true;
      const formData = new FormData();
      formData.append('name', this.form.name);

      if (this.isEdit && this.form.id) {
        formData.append('id', this.form.id);
      }

      const url = this.isEdit
        ? `venues/venue-outlets/${this.form.id}`
        : 'venues/venue-outlets';

      this.$http
        .post(url, formData)
        .then((response) => {
          if (response.status === 200 && response.data.status === true) {
            this.showSuccess(
              this.isEdit
                ? "Venue outlet updated successfully"
                : "Venue outlet created successfully"
            );
            this.closeModal();
            this.loadVenueOutlets();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.saving = false;
        });
    },

    deleteConfirm(outlet) {
      this.confirmModel = {
        id: outlet.id,
        title: "Do you want to delete this venue outlet?",
        description: "By clicking <b>Yes</b> you can confirm the operation. Do you need to continue your action?",
        type: "delete"
      };
    },

    confirmActions(data) {
      if (data.type === "delete") {
        this.deleteVenueOutlet(data.id);
      }
      this.confirmModel.id = null;
    },

    deleteVenueOutlet(id) {
      this.showLoader("Deleting outlet");
      this.$http
        .delete(`venues/venue-outlets/${id}`)
        .then((response) => {
          if (response.status === 200 && response.data.status === true) {
            this.showSuccess("Venue outlet deleted successfully");
            this.loadVenueOutlets();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.hideLoader();
        });
    }
  }
}
</script>

<style scoped>
.outlet_name {
  text-align: left;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.outlet-content {
  flex: 1;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.outlet-card {
  height: 100px;
  min-width: 120px;
  max-width: 180px;
  width: fit-content !important;
  flex: 0 0 auto !important;
  padding: 12px 25px !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.outlet-card .v-card__text {
  padding: 0 !important;
  text-align: center;
  line-height: 1.2;
  font-size: 14px;
  font-weight: 500;
}

/* Ensure proper spacing between cards */
.v-col[cols="auto"] {
  flex: 0 0 auto !important;
  margin-right: 16px;
}

/* Better responsive sizing for outlet cards */
@media (max-width: 600px) {
  .outlet-card {
    min-width: 100px;
    max-width: 140px;
    padding: 10px 12px !important;
  }

  .outlet-card .v-card__text {
    font-size: 12px;
    line-height: 1.1;
  }

  .v-col[cols="auto"] {
    margin-right: 12px;
  }
}

.menu-btn {
  background: rgba(255,255,255,0.9) !important;
  backdrop-filter: blur(4px);
}

.add-outlet-btn {
  border: 1px solid #4FAEAF !important;
  background: rgba(79, 174, 175, 0.1) !important;
}

.new_heading {
  font-size: 18px;
}

/* Remove extra bottom spacing */
.v-container {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

/* Add outlet button styling */
.add-outlet-btn:hover {
  border-color: #4FAEAF !important;
  background-color: #f8f9fa !important;
}

.add-outlet-btn:hover .v-icon {
  color: #4FAEAF !important;
}

.add-outlet-btn:hover .text-caption {
  color: #4FAEAF !important;
}
</style>
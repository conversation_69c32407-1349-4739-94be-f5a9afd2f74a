<template>
  <v-layout row wrap mr-5 ml-5>
    <v-container class="relete">
      <v-row class="ma-2">
        <v-col
          v-if="checkWritePermission($modules.general.posts.slug)"
          cols="12"
          sm="3"
        >
          <v-card sm="3" md="3" lg="3" class="mx-auto add_button">
            <v-layout align-center justify-center>
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    v-bind="attrs"
                    v-on="on"
                    color="#00b0af"
                    fab
                    dark
                    small
                    @click.stop="addPostsDialoge"
                  >
                    <v-icon>mdi-plus</v-icon>
                  </v-btn>
                </template>
                Add
              </v-tooltip>
            </v-layout>
          </v-card>
        </v-col>
        <v-col
          v-for="(post, index) in krewsPosts"
          :key="index"
          cols="12"
          sm="3"
          md="3"
          lg="3"
        >
          <post-widget
            v-bind="post"
            :index="index"
            @delete="deleteConfirmPost"
            @comment="getPostComments"
            @like="getPostLikes"
          ></post-widget>
        </v-col>
      </v-row>

      <!--  Add Post  Start-->
      <v-dialog v-model="post_dialog" scrollable max-width="550" persistent>
        <v-form ref="form" v-model="valid">
          <v-card>
            <v-card-title class="headline">Add Post</v-card-title>
            <v-card-text class="form_bg">
              <v-col cols="12">
                <image-upload
                  @upload="imageSelected"
                  :height="150"
                  default="user"
                  @remove="
                    () => {
                      imageSelected = null;
                    }
                  "
                ></image-upload>
              </v-col>
              <v-col cols="12">
                <v-text-field
                  v-model="post.title"
                  label="Post Title"
                  required
                  outlined
                  background-color="#fff"
                  :rules="[(v) => !!v || 'Post Title is required']"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="post.description"
                  label="Post Description"
                  required
                  outlined
                  background-color="#fff"
                  :rules="[(v) => !!v || 'Description is required']"
                ></v-textarea>
              </v-col>
            </v-card-text>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                class="ma-2 white--text blue-color"
                text
                @click="closeAddPost"
                >Close</v-btn
              >
              <v-btn class="ma-2 white--text teal-color" text @click="addPost"
                >Add</v-btn
              >
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>

      <!-- Comment -->
      <v-dialog v-model="comment_dialogue" scrollable width="500">
        <v-card>
          <v-card-title class="headline" primary-title>Comments</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item
                v-for="(comment, index) in all_comments"
                :key="index"
              >
                <v-list-item-content>
                  <v-list-item-title>{{ comment.name }}</v-list-item-title>
                  {{ comment.comment }}
                </v-list-item-content>

                <v-list-item-avatar>
                  <v-img :src="s3BucketURL + comment.picture"></v-img>
                </v-list-item-avatar>
              </v-list-item>
            </v-list>
          </v-card-text>
          <v-divider></v-divider>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              class="blue-color"
              dark
              text
              @click="comment_dialogue = false"
              >Close</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Like -->
      <v-dialog v-model="like_dialogue" scrollable width="500">
        <v-card>
          <v-card-title class="headline" primary-title>Likes</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item v-for="(like, index) in all_likes" :key="index">
                <v-list-item-content>
                  <v-list-item-title>{{ like.name }}</v-list-item-title>
                </v-list-item-content>

                <v-list-item-avatar>
                  <v-img
                    v-if="like.picture"
                    :src="s3BucketURL + like.picture"
                  ></v-img>
                </v-list-item-avatar>
              </v-list-item>
            </v-list>
          </v-card-text>

          <v-divider></v-divider>

          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="blue-color" dark @click="like_dialogue = false"
              >Close</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>

      <confirm-model
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="confirmModel.id = null"
      ></confirm-model>
    </v-container>
  </v-layout>
</template>

<script>
import PostWidget from "@/components/Venue/PostWidget";
import ConfirmModel from "@/components/Confirmation/ConfirmModel";
export default {
  components: {
    ConfirmModel,
    PostWidget,
  },
  data() {
    return {
      krewsPosts: [],
      post_dialog: false,
      comment_dialogue: false,
      like_dialogue: false,
      post: {},
      all_comments: [],
      all_likes: [],
      valid: true,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
    };
  },
  mounted() {
    this.getKrewsVenuePosts();
  },

  methods: {
    getKrewsVenuePosts() {
      // this.$http
      //   .get("profile/posts")
      //   .then((response) => {
      //     if (response.status == 200 && response.data.status == true) {
      //       this.krewsPosts = response.data.data;
      //     }
      //   })
      //   .catch((error) => {
      //     this.errorChecker(error);
      //   });
      this.krewsPosts = [
        {
          id: 1394,
          userId: null,
          venue_id: "21",
          title: "Test",
          detail: null,
          date: null,
          postLocation: null,
          status: "1",
          taggedUsers: null,
          categoryId: "0",
          isGame: "0",
          image: "venue/21/post/1622461096.jpg",
          created: "2021-05-31 11:38:16",
          likes_count: "0",
          comments_count: "0",
        },
      ];
    },
    getPostLikes(id) {
      this.like_dialogue = true;
      this.$http
        .get("profile/likes/" + id)
        .then((response) => {
          this.all_likes = response.data.data;
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getPostComments(id) {
      this.comment_dialogue = true;
      this.$http
        .get("profile/comments/" + id)
        .then((response) => {
          if (response.status == 200) {
            this.all_comments = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    deleteConfirmPost(data) {
      this.confirmModel = {
        id: data.id(),
        title: "Do you want to delete this post?",
        description:
          "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "delete_post",
        data: { index: data.index },
      };
    },
    confirmActions(data) {
      if (data.type == "delete_post") {
        this.deletePost(data.id, data.data.index);
      }
      this.confirmModel.id = null;
    },
    deletePost(id, index) {
      this.showLoader();
      this.$http
        .delete("profile/delete/post/" + id)
        .then((response) => {
          if (response.status == 200) {
            this.hideLoader();
            this.showSuccess("Post Deleted Successfully.");
            this.krewsPosts.splice(index, 1);
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    addPostsDialoge() {
      this.post_dialog = true;
      this.post = {};
    },
    imageSelected(data) {
      this.post.image = data;
    },
    closeAddPost() {
      this.post_dialog = false;
      this.$refs.form.resetValidation();
    },
    addPost() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      event.preventDefault();
      this.showLoader();
      let formData = new FormData();
      Object.keys(this.post).forEach((key) => {
        let value = this.post[key];
        formData.append(key, value);
      });
      this.$http
        .post("profile/post", formData, {
          headers: {
            "Content-Type": "multipart/form-data; boundary=${form._boundary}",
          },
        })
        .then((response) => {
          if (response.status == 200) {
            this.post_dialog = false;
            this.showSuccess("Post Added Successfully.");
            this.hideLoader();
            this.getKrewsVenuePosts();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
  },
};
</script>

<style scoped>
.add_button {
  padding: 109px;
}
.relete {
  position: relative;
}
/*.v-list{
overflow-x: hidden;
    overflow-y: auto;
    padding: 15px;
    max-height: 200px;
    width: 100%;}*/
</style>

<template>
  <v-card
    class="mx-auto bg_card"
    color="#26c6da"
    dark
    rounded
    @click="gotoSocialPosts"
  >
    <v-card-text class="font-weight-bold">
      <v-row align="center" justify="center">
        <v-col md="3">
          <v-img
            v-if="type == 'instagram'"
            src="@/assets/images/social_media/instagram.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'facebook'"
            src="@/assets/images/social_media/facebook.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'twitter'"
            src="@/assets/images/social_media/twitter.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'youtube'"
            src="@/assets/images/social_media/youtube.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'snapchat'"
            src="@/assets/images/social_media/snapchat.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'krews'"
            src="@/assets/images/social_media/krews.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'sms'"
            src="@/assets/images/social_media/sms.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'email'"
            src="@/assets/images/social_media/email.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'newsletter'"
            src="@/assets/images/social_media/newsletter.png"
            height="70"
            contain
          ></v-img>

          <v-img
            v-else-if="mail_config.includes(type)"
            src="@/assets/images/social_media/settings.png"
            height="70"
            contain
          ></v-img>
          <v-img
            v-else-if="type == 'announcement'"
            src="@/assets/images/social_media/notification.png"
            height="70"
            contain
          ></v-img>
        </v-col>
        <v-col md="5">
          <div class="title font-weight-light soc_title">{{ name }}</div>
          <div v-if="type == 'sms'" class="balance_number">
            {{ remaining }} remaining
          </div>
          <div v-else-if="type == 'email'" class="balance_number">
            {{ remaining }} / {{ total }} remaining
          </div>
        </v-col>
        <v-col md="4" style="text-align: center; float: right">
          <div v-if="!mail_config.includes(type)" class="mr-2 count">
            {{ post_count }}
          </div>
          <div v-if="type == 'sms' || type == 'email'" class="post">
            Message
          </div>
          <div v-if="type == 'announcement'" class="post">
            Notifications
          </div>
          <div v-else-if="mail_config.includes(type)" class="post">Setup</div>
          <div v-else-if="type == 'newsletter'" class="post">Subscriptions</div>
          <div v-else class="post">Posts</div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  props: {
    id: { type: Number },
    name: { type: String, default: "" },
    type: { type: String },
    post_count: { type: Number },
    url: { type: String, default: null },
    remaining: { type: Number, default: null },
    total: { type: Number, default: null },
  },
  data() {
    return {
      mail_config: [
        "order_confirmation",
        "order_cancel",
        "order_change",
        "welcome_mail",
        "new_member_mail",
        "renew_member_mail",
        "survey_mail",
        "approval_mail",
        "reminder_mail",
        "e_invoice_mail",
        "order_reservation",
        "order_refund",
        "promo_mail",
        "membership_purchase",
      ],
    };
  },
  methods: {
    gotoSocialPosts() {
      if (this.type == "email" || this.type == "sms") {
        this.$router.push({
          name: "MailSMS",
          params: { type: this.type },
        });
      } else if (this.type == "newsletter") {
        this.$router.push({
          name: "Newsletter",
          params: { type: this.type },
        });
      } else if (this.mail_config.includes(this.type)) {
        if (this.url) {
          this.$router.push({
            name: this.url,
            params: { type: this.type },
          });
        } else {
          this.$router.push({
            name: "OrderConfirmationMail",
            params: { type: this.type },
          });
        }
      } else if (this.type == "announcement") {
        this.$router.push({
          name: "AnnouncementDetails",
          params: { type: this.type },
        });
      } else {
        this.$router.push({
          name: "SocialPosts",
          params: { type: this.type },
        });
      }
    },
  },
};
</script>

<style scoped>
.soc_title {
  color: #40a6a7;
  font-weight: normal;
}
.balance_number {
  color: #40a6a7;
  font-weight: bold !important;
}
.bg_card {
  background: rgba(255, 255, 255, 1);
  background: -moz-linear-gradient(
    top,
    rgba(255, 255, 255, 1) 0%,
    rgba(246, 246, 246, 1) 47%,
    rgba(237, 237, 237, 1) 100%
  );
  background: -webkit-gradient(
    left top,
    left bottom,
    color-stop(0%, rgba(255, 255, 255, 1)),
    color-stop(47%, rgba(246, 246, 246, 1)),
    color-stop(100%, rgba(237, 237, 237, 1))
  );
  background: -webkit-linear-gradient(
    top,
    rgba(255, 255, 255, 1) 0%,
    rgba(246, 246, 246, 1) 47%,
    rgba(237, 237, 237, 1) 100%
  );
  background: -o-linear-gradient(
    top,
    rgba(255, 255, 255, 1) 0%,
    rgba(246, 246, 246, 1) 47%,
    rgba(237, 237, 237, 1) 100%
  );
  background: -ms-linear-gradient(
    top,
    rgba(255, 255, 255, 1) 0%,
    rgba(246, 246, 246, 1) 47%,
    rgba(237, 237, 237, 1) 100%
  );
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 1) 0%,
    rgba(246, 246, 246, 1) 47%,
    rgba(237, 237, 237, 1) 100%
  );
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ededed', GradientType=0 );
  border-radius: 20px !important;
}
.post {
  color: #066a8c;
  font-weight: 400;
}
.count {
  color: #066a8c;
  font-weight: 500;
  font-size: 36px;
  margin-bottom: 10px;
}
</style>

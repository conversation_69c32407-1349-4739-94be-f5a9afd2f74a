<template>
  <v-dialog v-model="openModal" max-width="900" persistent>
    <v-card>
      <v-card-title class="ml-8"> Additional Field </v-card-title>
      <v-card-text>
        <v-container fluid>
          <v-row dense>
            <v-col>
              <label for="">Field Type</label>
              <v-select
                v-model="selectedControl"
                :items="controls"
                item-text="value"
                item-value="name"
                background-color="#fff"
                class="q-autocomplete shadow-0"
                dense
                hide-details="auto"
                hint="Select field type"
                outlined
                required
                :rules="[(v) => !!v || 'Field type is required']"
                @change="updateType"
              />
            </v-col>
          </v-row>

          <v-row dense>
            <v-col cols="6">
              <label for="">Field English</label>
              <v-text-field
                v-model="name"
                clearable
                outlined
                dense
                required
                :rules="[(v) => !!v || 'Field is required']"
                background-color="#fff"
                class="q-text-field shadow-0"
              >
                <template v-if="field_config.allowMarkdown" #append>
                  <v-tooltip top>
                    <template #activator="{ on }">
                      <v-icon color="primary" v-on="on">
                        mdi-information
                      </v-icon>
                    </template>
                    <span>
                      To turn any word into a clickable hyperlink, use square
                      brackets as illustrated here, and include the desired text
                      within parentheses: [https://example.com] (text)
                    </span>
                  </v-tooltip>
                </template>
              </v-text-field>
            </v-col>
            <v-col>
              <label for="">Field Arabic</label>
              <v-text-field
                v-model="name_ar"
                clearable
                outlined
                dense
                :required="field_config.arabic_enabled"
                :rules="
                  field_config.arabic_enabled
                    ? [(v) => !!v || 'Field is required']
                    : []
                "
                background-color="#fff"
                class="q-text-field shadow-0"
              >
                <template v-if="field_config.allowMarkdown" #append>
                  <v-tooltip top>
                    <template #activator="{ on }">
                      <v-icon color="primary" v-on="on">
                        mdi-information
                      </v-icon>
                    </template>
                    <span>
                      To turn any word into a clickable hyperlink, use square
                      brackets as illustrated here, and include the desired text
                      within parentheses: [https://example.com] (text)
                    </span>
                  </v-tooltip>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
          <v-row dense v-if="selectedControl == 'disclaimer_form'">
            <div class="ml-4" style="margin-top: -10px">
              <span class="overline">Placeholders</span>
              <v-tooltip top max-width="250">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon color="grey lighten-1">mdi-information</v-icon>
                  </v-btn>
                </template>
                <span class="text-center">
                  Placeholder is used to add dynamic data in you message.
                  Placeholder will be replaced with actual data representing
                  each of them. Please click to copy and paste it on your
                  message
                </span>
              </v-tooltip>
              <template v-for="item in placeholders">
                <v-chip
                  :key="item.id"
                  class="mr-2 pa-4"
                  @click="copyPlaceholder(item)"
                >
                  {{ item.name }}
                  <v-icon right small>mdi-content-copy</v-icon>
                </v-chip>
              </template>
            </div>
            <v-col cols="12" class="text-right">
              <v-switch
                v-model="isEnableArabic"
                :false-value="0"
                :true-value="1"
                class="my-0"
                dense
                hide-details="auto"
                label="Arabic"
                @change="checkArabicTranslation"
              ></v-switch>
            </v-col>
            <v-col cols="12">
              <div>
                <label for="">Content</label>
                <RichEditor v-model="disclaimer_content" />
              </div>
            </v-col>
            <v-col cols="12" v-if="isEnableArabic">
              <div class="mt-2">
                <label for=""> Content (AR) </label>
                <RichEditor
                  v-model="ara_disclaimer_content"
                  contents-lang-direction="rtl"
                  language="ar"
                  key="ar_editor"
                />
              </div>
            </v-col>
          </v-row>
          <v-row v-if="enableOptions.includes(selectedControl)" dense>
            <v-col
              cols="12"
              lg="12"
              v-for="(option, index) in options"
              :key="index"
            >
              <div
                class="d-flex align-center justify-space-between gap-x-2 bordered p-4"
              >
                <div>
                  <label for="">Option</label>
                  <v-text-field
                    v-model="option.value"
                    clearable
                    outlined
                    dense
                    required
                    :rules="[(v) => !!v || 'Option is required']"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                  />
                </div>
                <div>
                  <label>Option Arabic</label>
                  <v-text-field
                    v-model="option.value_ar"
                    clearable
                    outlined
                    dense
                    :required="field_config.arabic_enabled"
                    :rules="
                      field_config.arabic_enabled
                        ? [(v) => !!v || 'Option (AR) is required']
                        : []
                    "
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                  />
                </div>
                <v-btn
                  small
                  color="white"
                  depressed
                  @click="deleteOption(index)"
                >
                  <v-icon dark>mdi-trash-can-outline</v-icon>
                </v-btn>
              </div>
            </v-col>
          </v-row>
          <v-row dense v-if="enableOptions.includes(selectedControl)">
            <v-col>
              <div
                class="add-new-option"
                style="cursor: pointer; color: #4faeaf; margin-top: 10px"
                @click="addOption"
              >
                + Add Option
              </div>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col
                cols="12"
                md="6"
                sm="6"
            >
              <label for="">
                Attach any helping material
              </label>
              <v-file-input
                  v-model="document"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  outlined
                  prepend-icon=""
                  validate-on-blur
              />
              <template v-if="helping_document">
                <v-btn  :ripple="false" class="no-hover-effect px-0" text x-small
                       @click="openFile(helping_document)">
                  Download
                </v-btn>
                <v-btn  :ripple="false" class="no-hover-effect px-0" text x-small @click="deleteDocument">
                  <SvgIcon class="font-medium text-sm gap-x-2" >
                    <template #icon>
                      <DeleteIcon opacity="1" stroke="red" />
                    </template>
                  </SvgIcon>
                </v-btn>
              </template>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions class="" style="border-top: 1px solid rgba(0, 0, 0, 0.1)">
        <v-spacer />
        <div class="">
          <v-btn class="ma-2" text @click="closeModal"> Close </v-btn>
          <v-btn
            elevation="0"
            class="ma-2 white--text blue-color"
            :disabled="!isFormValid"
            @click="addFormItem"
          >
            Save
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import RichEditor from "@/components/Common/RichEditor.vue";
import DeleteIcon from '@/assets/images/misc/delete-icon.svg'
import SvgIcon from '@/components/Image/SvgIcon.vue'
export default {
  components: {
    SvgIcon, DeleteIcon,
    RichEditor,
  },
  props: {
    openModal: { type: Boolean, default: false },
    field_config: {
      type: Object,
      default: () => ({
        is_required: 0,
        arabic_enabled: 0,
        is_visible: 0,
        allowMarkdown: 0,
        type: null,
        name: null,
        name_ar: null,
        disclaimer_content: null,
        ara_disclaimer_content: null,
        slug: null,
        id: null,
        uId: null,
        is_additional: 1,
        options: [],
        helping_document:null
      }),
    },
  },
  data() {
    return {
      selectedControl: null,
      name: null,
      name_ar: null,
      disclaimer_content: null,
      ara_disclaimer_content: null,
      slug: null,
      id: null,
      uId: null,
      isRequired: 0,
      isVisible: 0,
      options: [],
      controls: [
        { name: "text_box", value: "Text Field" },
        { name: "radio_buttons", value: "Radio Buttons" },
        { name: "check_boxes", value: "Check Boxes" },
        { name: "text_area", value: "Text Area" },
        { name: "drop_down", value: "Dropdown list" },
        { name: "date", value: "Date" },
        { name: "phone_number", value: "Phone Number" },
        { name: "email", value: "Email" },
        { name: "file", value: "Attachment" },
        { name: "disclaimer_form", value: "Disclaimer Form" },
        { name: "emirates_id", value: "Emirates ID" },
      ],
      enableOptions: ["radio_buttons", "check_boxes", "drop_down"],
      isEnableArabic: false,
      placeholders: [],
      helping_document:null,
      document:null
    };
  },
  computed: {
    isFormValid() {
      const isFieldNameValid = !!this.name;

      let isFieldDisclaimerValid = true;
      let isArabicDisclaimerValid = true;
      if (this.selectedControl == "disclaimer_form") {
        isFieldDisclaimerValid = !!this.disclaimer_content;

        isArabicDisclaimerValid =
          !this.isEnableArabic || !!this.ara_disclaimer_content;
      }

      const isArabicNameValid =
        !this.field_config.arabic_enabled || !!this.name_ar;

      const areOptionsValid =
        !this.enableOptions.includes(this.selectedControl) ||
        (this.options.length > 0 &&
          this.options.every(
            (option) =>
              !!option.value &&
              (!this.field_config.arabic_enabled || !!option.value_ar)
          ));

      return (
        !!this.selectedControl &&
        isFieldNameValid &&
        isArabicNameValid &&
        isFieldDisclaimerValid &&
        isArabicDisclaimerValid &&
        areOptionsValid
      );
    },
  },
  methods: {
    copyPlaceholder(data) {
      const placeholderValue = data?.placeholder ? data.placeholder : "";
      if (!placeholderValue) {
        console.warn("Invalid placeholder data", data);
        return;
      }
      navigator.clipboard
        .writeText(placeholderValue)
        .then(() => {
          this.showInfo(
            `Placeholder for "${data.name}" copied to clipboard`,
            2000
          );
        })
        .catch((err) => {
          console.error("Failed to copy: ", err);
          this.showInfo("Failed to copy placeholder", 2000);
        });
    },
    getPlaceholders() {
      this.$http
        .get("venues/marketing/messages/placeholders?type=configuration")
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.placeholders = data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    initializeForm() {
      this.selectedControl = this.field_config.type || null;
      this.name = this.field_config.name || null;
      this.name_ar = this.field_config.name_ar || null;
      this.disclaimer_content = this.field_config.content || null;
      this.ara_disclaimer_content = this.field_config.ar_content || null;
      this.isEnableArabic = this.ara_disclaimer_content ? 1 : 0;
      this.slug = this.field_config.slug || null;
      this.id = this.field_config.id || null;
      this.isRequired = this.field_config.is_required ? 1 : 0;
      this.isVisible = this.field_config.is_visible ? 1 : 1;
      this.helping_document = this.field_config.helping_document || null;
      this.uId = this.field_config.uId
        ? this.field_config.uId
        : new Date().getTime();
      this.options =
        this.field_config.options && Array.isArray(this.field_config.options)
          ? this.field_config.options.map((opt) => ({
              value: opt.value || "",
              value_ar: opt.value_ar || "",
            }))
          : [];
    },
    closeModal() {
      this.resetForm();
      this.$emit("closeModal");
    },
    addFormItem() {
      const fieldData = {
        type: this.selectedControl,
        name: this.name,
        name_ar: this.name_ar,
        content: this.disclaimer_content,
        ar_content: this.ara_disclaimer_content,
        id: this.id,
        slug: this.slug && this.id ? this.slug : this.slugify(this.name),
        is_additional: 1,
        is_required: this.isRequired === 1 ? 1 : 0,
        is_visible: this.isVisible === 1 ? 1 : 0,
        allow_markdown: this.field_config.allowMarkdown,
        uId: this.field_config.uId
          ? this.field_config.uId
          : new Date().getTime(),
        options: this.enableOptions.includes(this.selectedControl)
          ? this.options
          : [],
        document: this.document,
        helping_document: this.helping_document,
      };
      this.$emit("addFormItem", fieldData);
      this.resetForm();
    },
    updateType() {
      if (this.enableOptions.includes(this.selectedControl)) {
        this.options = this.options.length
          ? this.options
          : [{ value: "", value_ar: "" }];
      } else {
        this.options = [];
      }
      this.isRequired = 0;
      this.isVisible = 0;
    },
    resetForm() {
      this.selectedControl = null;
      this.name = null;
      this.name_ar = null;
      // this.disclaimer_content = null;
      // this.ara_disclaimer_content = null;
      this.slug = null;
      this.id = null;
      this.uId = null;
      this.isRequired = 0;
      this.isVisible = 0;
      this.options = [];
    },
    slugify(text) {
      if (!text) return "";
      return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/[\s\W-]+/g, "-") // Replace spaces and non-word chars with -
        .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
    },
    addOption() {
      this.options.push({ value: "", value_ar: "" });
    },
    deleteOption(index) {
      this.options.splice(index, 1);
    },
    checkArabicTranslation() {
      if (!this.isEnableArabic) this.ara_disclaimer_content = "";
    },
    deleteDocument(){
      this.helping_document = null;
    }
  },
  watch: {
    field_config: {
      handler() {
        this.initializeForm();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.getPlaceholders();
  },
};
</script>

<style lang="scss" scoped>
.form-item-list {
  border-radius: 4px;
  display: flex;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  padding-left: 10px;
  padding-top: 10px;
  padding-bottom: 10px;
  gap: 8px;

  .add-button {
    display: none;
    font-weight: 500;
    font-size: 16px;
    padding-right: 10px;
  }
  &:hover {
    .add-button {
      display: block;
    }
    color: rgba(79, 174, 175, 1);
    background: rgba(79, 174, 175, 0.1);
    .theme--light.v-icon {
      color: rgba(79, 174, 175, 1) !important;
    }
  }
}

.add-new-option {
  cursor: pointer;
  color: #4faeaf;
  margin-top: 10px;
  font-weight: 500;
}

.remove-button {
  min-width: 36px !important;
}
</style>

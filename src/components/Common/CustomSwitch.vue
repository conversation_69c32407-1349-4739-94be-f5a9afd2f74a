<script>
export default {
  name: 'CustomSwitch',
  props: {
    modelValue: {
      required: true,
    },
    showLabel: {
      type: Boolean,
      default: true,
    },
    label: {
      type: [String, null, undefined],
      required: false,
    },
    className: {
      type: String,
      default: '',
    },
    trueValue: {
      type: [String, Boolean, Number],
      default: true,
    },
    falseValue: {
      type: [String, Boolean, Number],
      default: false,
    }
  },
  methods: {
    emitValue ($event) {
      this.$emit('update:modelValue', $event)
    }
  },
  computed: {
    state: {
      get () {
        return this.modelValue
      },
      set (val) {
        this.$emit('update:modelValue', val)
      }
    }
  }
}
</script>

<template>
  <div :class="className">
    <p v-if="showLabel" class="mb-0" @click="state = state === trueValue?falseValue:trueValue">
      {{ label }}
    </p>
    <v-switch
        v-model="state"
        :false-value="falseValue"
        :ripple="false"
        :true-value="trueValue"
        class="custom-switch"
        density="compact"
        hide-details="auto"
        v-bind="$attrs"
        @change="emitValue"
    />
  </div>
</template>

<style lang="scss">
.custom-switch {
  margin-top: 0 !important;
  width: 40px !important;

  .v-input--switch__track {
    top: calc(50% - 13.5px) !important;
    height: 16px !important;
    width: 30px !important;
    background-color: #112A46 !important;
    opacity: 1 !important;
  }

  .v-input--switch__thumb {
    &:not(.primary--text) {
      left: 6px !important;
    }

    height: 8px !important;
    width: 8px !important;
    top: calc(50% - 9px) !important;
    color: white !important;
  }

  .v-input--switch__track.primary--text {
    opacity: 1;
    background-color: #4FAEAF !important;
  }
}
</style>

<script>
export default {
  name: "<PERSON><PERSON><PERSON>",
  props: {
    ageData: Object,
  },
  data() {
    return {
      chartOptions: {
        plugins: {
          datalabels: {
            color: '#fff'
          }
        }
      }
    }
  },
}
</script>

<template>
  <column-chart
      :colors="['rgba(17, 42, 70, 1)','rgba(79, 174, 175, 1)','rgba(187, 226, 226, 1)']"
      :data="ageData.chartData"
      :library="chartOptions"
      :stacked="true"
      legend="bottom"/>
</template>

<style scoped>

</style>
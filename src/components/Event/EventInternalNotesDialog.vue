<template>
  <v-dialog v-model="isShowInternalNotesPopup" width="500px" scrollable persistent>
    <v-card color="lighten-4" min-width="350px" flat class="pt-0" v-if="eventId">
      <v-card-title >
        <div class="row pt-1 border-bottom">
          <div class="col-md-12">
            <div class="d-flex justify-space-between align-center mt-2">
              <SvgIcon
                  class="text-xl font-semibold"
                  text="Internal Notes"
                  style="color: black"
              >
              </SvgIcon>
              <v-btn fab x-small class="shadow-0" @click="close">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
      </v-card-title>
      <v-card-text class="pt-0">
        <v-row class="mt-0">
          <v-col md="12">
            <v-textarea
                name="Internal Notes"
                v-model="notes"
                outlined
                background-color="#fff"
                class="q-text-field shadow-0"
                hide-details="auto"
                dense
                :rules="[(v) => !!v || 'Notes are required']"
            ></v-textarea>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn class="ma-2 white--text blue-color" text @click="saveNotes">Save</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  props: {
    isShowInternalNotesPopup: {
      type: Boolean,
      default: false,
    },
    eventId: { type: Number,default: null },
    internalNotes: { type: String, default: null },
  },
  components: {
    SvgIcon,
  },
  mounted(){
    if(this.internalNotes && this.eventId){
      this.notes = this.internalNotes;
    }
  },
  data(){
    return {
      notes: "",
    }
  },
  methods: {
    close(){
      this.$emit('close');
    },
    saveNotes() {
      if(!this.eventId || !this.notes){
        this.showError("Notes required");
        return;
      }
      this.showLoader("Loading...");
      var formData = new FormData();
      formData.append("internal_notes", this.notes);
      this.$http
          .post(`venues/events/${this.eventId}/internal-notes`, formData)
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              this.hideLoader();
              this.$emit("saved",this.notes);
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
  },
}

</script>
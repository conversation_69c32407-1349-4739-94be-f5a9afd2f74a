<template>
  <div>
  <v-tooltip right :disabled="!showPreview">
    <template v-slot:activator="{ on, attrs }">
      <span class="breadcrumb-link pointer"
            v-if="item.name"
            v-bind="attrs"
            v-on="on"
            @click="handleCLick">{{ item.name  }}</span>
    </template>
    <v-card v-if="showPreview" flat>
      <v-img v-if="isImage" :src="getPath(path)" :max-height="height" :max-width="width"></v-img>
      <v-responsive v-else-if="isPdf" :max-height="height" :max-width="width">
        <embed :src="getPath(path)" type="application/pdf" style="width: 100%; height: 100%;" />
      </v-responsive>
      <v-card-text v-else>Preview not available</v-card-text>
    </v-card>
  </v-tooltip>
  </div>
</template>

<script>

export default {
  props: {
    extension: String,
    path: String,
    item:Object,
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 400
    },
  },
  methods: {
    getPath(path){
      return this.s3BucketURL + path;
    },
    handleCLick(){
      this.$emit('handleCLick', this.item);
    }
  },
  computed: {
    isImage() {
      return ['jpg', 'jpeg', 'png', 'gif','svg'].includes(this.extension.toLowerCase());
    },
    isPdf() {
      return this.extension.toLowerCase() === 'pdf';
    },
    showPreview() {
      return this.isImage || this.isPdf;
    },
  },
};
</script>

<style scoped>
/* Additional styling if needed */
</style>

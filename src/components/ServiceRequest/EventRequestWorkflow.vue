<template>
  <v-container class="pa-0" fluid>
    <div class="d-flex justify-space-between align-center mb-2 pr-4" v-if="checkWritePermission($modules.serviceRequest.management.slug)">
      <SvgIcon class="text-xl font-semibold" text="Event request workflow">
      </SvgIcon>
      <v-btn
          class="white--text blue-color"
          text
          @click="openWorkFlowSettings()"
      >Configure
      </v-btn>
    </div>

    <div class="d-flex flex-wrap mb-8">
      <div v-for="(tier, index) in tiers" :key="index" class="d-flex align-center mr-3">
        <tierCard :name="tier.title" class="tier-card"/>
        <div v-if="index !== tiers.length - 1" class="arrow-right"></div>
      </div>
    </div>

    <ServiceRequestConfigurationModal
      :src-modal="workFlowSettingsModal"
      :all-tiers="allTiers"
      :selected-tiers="tiers"
      :product-type-id="productTypeId"
      @saveTier="saveTier"
      @closeConfigureTiersModal="closeConfigureTiersModal"
    />

  </v-container>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
import tierCard from "@/components/ServiceRequest/tierCard.vue";
import ServiceRequestConfigurationModal from "@/components/ServiceRequest/ServiceRequestConfigurationModal.vue";

export default {
  name: "EventRequestWorkflow",
  components: {ServiceRequestConfigurationModal, SvgIcon, tierCard},
  props:{
    allTiers: Array,
  },
  data() {
    return {
      productTypeId: 3,
      workFlowSettingsModal: false,
      tiers: []
    }
  },
  mounted() {
    this.getWorkflows();
  },
  methods: {
    saveTier(data){
      const sendData = {
        tiers:data.selectedTier
      };
      this.showLoader("Loading...");
      this.$http.post(`venues/service-request/configurations/set/${this.productTypeId}`,sendData)
          .then((response) => {
            if (response.status == 200 && response.data.status) {
              this.closeConfigureTiersModal();
              this.getWorkflows();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(() => {
            this.hideLoader();
      });
    },
    closeConfigureTiersModal(){
      this.workFlowSettingsModal = false;
    },
    openWorkFlowSettings() {
      this.workFlowSettingsModal = true
    },
    getWorkflows() {
      this.showLoader("Loading Event workflows");
      this.$http.get(`venues/service-request/configurations/get/${this.productTypeId}`).then((response) => {
        this.hideLoader();
        if (response.status == 200 && response.data.status == true) {
          if(response.data.data && response.data.data.work_flow_tiers){
            this.tiers = response.data.data.work_flow_tiers;
          }
        }
      }).catch((error) => {
        this.errorChecker(error);
      }).finally(() => {
        this.hideLoader();
      });
    }
  }
}
</script>

<style scoped>
.arrow-right {
  width: 50px;
  height: 2px;
  background-color: #000;
  position: relative;
}

.arrow-right {
  background-color: #00b0af; /* Blue */
}

.arrow-right {
  height: 4px; /* Change thickness */
}


.arrow-right::after {
  content: '';
  position: absolute;
  top: -5px;
  right: -10px;
  width: 0;
  height: 4px;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 12px solid #00b0af;
}

</style>
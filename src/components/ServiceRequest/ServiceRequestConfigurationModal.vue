<template>
  <v-dialog v-model="srcModal" max-width="400px" persistent @input="closeConfigureTiersModal">
    <v-card>
      <v-card-text>
        <div class="row pt-1 border-bottom">
          <div class="col-md-12">
            <div class="d-flex justify-space-between align-center mt-2">
              <SvgIcon class="text-2xl font-semibold" text="Applicable Tiers" style="color: black" >
              </SvgIcon>
              <v-btn  fab x-small class="shadow-0" @click="closeConfigureTiersModal">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
        <div class="mt-8">
          <v-form ref="form">
            <v-select
                placeholder="Tiers"
                v-model="localTiers"
                item-value="id"
                item-text="title"
                :menu-props="{ bottom: true, offsetY: true }"
                :rules="[(v) => !!v || 'Select a Tier']"
                :items="allTiers"
                outlined
                background-color="#fff"
                dense
                multiple
                class="q-autocomplete shadow-0"
                required
                hide-details="auto"
            ></v-select>
          </v-form>
        </div>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="saveTier" v-if="checkWritePermission($modules.serviceRequest.management.slug)">Save</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";

export default {
  name: "ServiceRequestConfigurationModal" ,
  components: {SvgIcon},
  props: {
    srcModal:{ type:Boolean, default: false },
    productTypeId: { type:Number, default:null },
    allTiers:{type:Array, default:() => []},
    selectedTiers:{type:Array, default:() => []},
  },
  watch:{
    srcModal() {
        this.localTiers = this.selectedTiers;
    },
  },
  data(){
    return{
      localTiers:[],
    }
  },
  methods: {
    saveTier(){
      if (!this.$refs.form.validate()) {
        this.showError("Please select a Tier");
        return;
      }
      this.$emit('saveTier', {selectedTier:this.localTiers, productTypeId:this.productTypeId});
    },
    closeConfigureTiersModal(){
      this.$emit('closeConfigureTiersModal');
    }
  }
}
</script>


<style scoped>

</style>
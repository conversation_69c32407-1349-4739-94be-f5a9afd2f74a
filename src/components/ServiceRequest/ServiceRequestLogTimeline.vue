<template>
  <div style="
    max-width: 600px;
    max-height: 1000px;
    overflow-y: auto;
    overflow-x: clip;"
    class="timeline">
    <v-timeline dense clipped  align-top  v-if="logs">
      <v-timeline-item
          v-for="(log,index) in logs"
          :key="index"
          :class="getClass(log)"
          icon-color="grey lighten-2"
          small
      >
        <v-row justify="space-between">
          <v-col cols="12" md="9" lg="9">
            <span class="d-block font-weight-bold">{{  log.modified_by }}</span>
            <span class="d-block font-semibold font-12 revision-requested">{{ log.title }}</span>
            <span class="font-12">{{ log.timestamp | timeStamp }}</span>
            <span class="font-12 d-block" v-if="log.assign_to"><b>Assign To:</b> {{ log.assign_to }}</span>
          </v-col>
          <v-col cols="12" md="3" lg="3" v-if="log.comment" @click="toggleComment(index)"><v-icon>mdi-comment-outline</v-icon></v-col>
          <v-col cols="12" md="12" v-show="visibleComments[index]" class="comment-div">
            <span class="comment font-12 d-block"  v-if="log.comment">{{ log.comment }}</span>
            <span class="documents font-12"  v-if="log.documents">
              <span class="btn" v-for="(doc,i) in log.documents" :key="i">
                <v-icon color="cyan" :title="doc.file_name" v-if="doc.file_path" @click="openFile(doc.file_path)" >mdi-download-box</v-icon>
              </span>
            </span>
          </v-col>
        </v-row>
      </v-timeline-item>
    </v-timeline>
  </div>
</template>
<script>
export default {
  props: {
    logs: { type: Array, default: () => [] },
  },
  computed: {
    timeline () {
      return this.events.slice().reverse()
    },
  },
  data(){
    return {
      visibleComments: {},
    }
  },
  methods: {
    getClass(log){
        if(log) {
          if (log.status_id === 11) {
            return "pending";
          }else if(log.status_id === 13){
            return "cancelled";
          }else if(log.status_id === 31){
            return "amendment";
          }else if(log.status_id === 32){
            return "rejected";
          }else if (log.status_id === 33) {
            return "approved";
          }else if(log.status_id === 35) {
            return "cancellation requested";
          }
        }
        return "";
    },
    toggleComment(index) {
      console.log(index);
      this.$set(this.visibleComments, index, !this.visibleComments[index]);
    },
  },
}
</script>
<style scoped>
.timeline ::v-deep .pending .v-timeline-item__inner-dot {
  background-color: #FFCDB3 !important;
}
.timeline ::v-deep .rejected .v-timeline-item__inner-dot {
  background-color: #FFBAB6 !important;
}
.timeline ::v-deep .amendment .v-timeline-item__inner-dot {
  background-color: dimgrey !important;
}
.timeline ::v-deep .approved .v-timeline-item__inner-dot {
  background-color: #B6F0B5 !important;
}
.timeline ::v-deep .cancellation .v-timeline-item__inner-dot {
  background-color: pink !important;
}
</style>

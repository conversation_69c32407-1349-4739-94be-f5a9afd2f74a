<template>
  <div class="qpoints-tabs">
    <v-row dense >
      <v-col cols="12" xl="12" lg="12" md="12">
        <div class="d-flex justify-space-between flex-wrap" >
          <div class="d-flex justify-start align-center mb-6" >
            <div class="d-flex p-2  bg-white bordered w-fit tabs" style="height:46px">
              <template v-for="(tab) in tabs">
                <div v-if="checkReadPermission(tab.permission)" v-bind:key="tab.name" class="nv_item d-flex pointer tab" @click="filterByRequestTypeTab(tab.value)">
                  <SvgIcon
                      :class="{ 'qp-tab-nav-is-active': JSON.stringify(tab.value) == JSON.stringify(activeTab), 'qp-tab-nav': tab.path && !tab.path.includes($route.path) }"
                      :text="tab.name"
                      class="text-thin-gray">
                    <template v-slot:icon>
                      <component :is="tab.icon" :component="tab.icon" />
                    </template>
                  </SvgIcon>
                </div>
              </template>
                <div v-if="checkWritePermission($modules.serviceRequest.management.slug)" class="nv_item d-flex pointer tab" @click="goToConfigurations()">
                  <SvgIcon
                      :class="{ 'qp-tab-nav-is-active': JSON.stringify(['configurations']) == JSON.stringify(activeTab) }"
                      text="Configurations"
                      class="text-thin-gray qp-tab-nav"
                  >
                    <template v-slot:icon>
                      <ConfigIconGrey />
                    </template>
                  </SvgIcon>
                </div>
            </div>
          </div>
            <div class="d-flex qp-tab-nav gap-x-2">
              <div class="nv_item" v-if="checkWritePermission($modules.serviceRequest.management.slug)">
                <v-text-field
                    v-model="nameFilter"
                    class="q-text-field shadow-0 bg-white"
                    hide-details="auto"
                    outlined
                    placeholder="Search Partner"
                    @keydown.enter="searchByName"
                    @click:clear="clearSearch"
                    style="max-width: 300px"
                    :height="46"
                    dense
                >
                  <template #prepend-inner>
                    <SearchIcon/>
                  </template>
                </v-text-field>
              </div>
              <div class="nv_item">
                <v-autocomplete
                    v-model="selectedStatus"
                    :hide-details="true"
                    :items="statuses"
                    item-text="name"
                    item-value="id"
                    outlined
                    multiple
                    placeholder="Status"
                    style="max-width: 180px"
                    @change="filterByStatus"
                    class="q-autocomplete shadow-0 nv_item_select q-tab-autocomplete"
                    :height="46"
                    dense
                    color="#4FAEAF"
                    item-color="#4FAEAF"
                >
                  <template v-if="selectedStatus.length === statuses.length" v-slot:selection="{ index }">
                    <span v-if="index === 0">All Request</span>
                  </template>
                  <template v-else v-slot:selection="{ item, index }">
                    <span v-if="index === 0">{{ item.name }}</span>
                    <span v-if="index === 1">, {{ item.name }}</span>
                    <span v-if="index === 2">, ...</span>
                  </template>
                  <template v-slot:prepend-item>
                    <v-list-item ripple @click="toggle('status')">
                      <v-list-item-action>
                        <v-icon :color="selectedStatus.length > 0 ? 'indigo darken-4' : ''">{{ icon() }}</v-icon>
                      </v-list-item-action>
                      <v-list-item-content>
                        <v-list-item-title>Select All</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                    <v-divider class="mt-2"></v-divider>
                  </template>
                </v-autocomplete>
              </div>
              <div class="nv_item" v-if="checkWritePermission($modules.serviceRequest.event.slug) || checkWritePermission($modules.serviceRequest.academy.slug)">
                <v-menu offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn v-bind="attrs" v-on="on" height="46" class="white--text blue-color ml-1 conf" color=" darken-1" text>
                      <ConfigIcon />
                      <span class="ml-1"> New Request</span>
                      <v-icon right dark>mdi-menu-down</v-icon></v-btn>
                  </template>
                  <v-list>
                    <v-list-item @click="gotoPage('/service-request/event')" v-if="checkWritePermission($modules.serviceRequest.event.slug)">
                      <v-list-item-title>Create Event</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="gotoPage('/service-request/academy')" v-if="checkWritePermission($modules.serviceRequest.academy.slug)">
                      <v-list-item-title>Create Academy</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
        </div>
      </v-col>
    </v-row>
  </div>
</template>
<script>
import ConfigIconGrey from "@/assets/images/memberships/cog_icon.svg";
import ConfigIcon from "@/assets/images/misc/cog_icon.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DashboardIcon from "@/assets/images/partners/dashboard.svg";
import EventsIcon from "@/assets/images/events/black-events.svg";
import AcademyIcon from "@/assets/images/workshops/black-academy.svg";
import SearchIcon from "@/assets/images/events/search.svg";
export default {
  props: {
    activeTab: { type: Array, default:() => [3,4]}
  },
  components: {SvgIcon, ConfigIcon,ConfigIconGrey, SearchIcon},
  data() {
    return {
      tabs: [
        {
          name: "All",
          icon: DashboardIcon,
          value:[3,4],
          permission: this.$modules.serviceRequest.management.slug
        },
        {
          name: "Events",
          icon: EventsIcon,
          value:[3],
          permission: this.$modules.serviceRequest.event.slug
        },
        {
          name: "Academies",
          icon: AcademyIcon,
          value:[4],
          permission: this.$modules.serviceRequest.academy.slug
        },
      ],
      selectedRequestType: [],
      requestTypes:[{ id:3, name: 'Event' }, {id: 4, name: 'Academy'}],
      statuses:[{id: 33, name: 'Approved'}, {id: 11, name: 'Pending'}, {id: 31, name: 'Amendment'}, {id: 32, name: 'Rejected'}],
      selectedStatus:[],
      perPage: 10,
      nameFilter: null,
    };
  },
  methods: {
    goToConfigurations(){
      this.$emit("setActiveTab",['configurations']);
    },
    toggle(type) {
      if(type === "request_type") {
        this.$nextTick(() => {
          if (this.selectedRequestType.length === this.requestTypes.length) {
            this.selectedRequestType = [];
          } else {
            this.selectedRequestType = this.requestTypes.map((item) => item.id);
          }
        });
        setTimeout(() => {
         this.filterByRequestType();
        });
      }else if(type === 'status'){
        this.$nextTick(() => {
          if (this.selectedStatus.length === this.statuses.length) {
            this.selectedStatus = [];
          } else {
            this.selectedStatus = this.statuses.map((item) => item.id);
          }
        });
        setTimeout(() => {
         this.filterByStatus()
        });
      }
    },
    icon() {
      if (this.selectedRequestType.length === this.selectedRequestType)
        return "mdi-close-box";
      if (this.selectedRequestType.length === 0) return "mdi-checkbox-blank-outline";
        return "mdi-minus-box";
    },
    gotoPage(route){
      if (this.$route.path !== route) {
        this.$router.push(route);
      }
    },
    filterByRequestType(){
      this.$emit("filterByRequestType", this.selectedRequestType);
    },
    filterByRequestTypeTab(val){
      this.$emit("filterByRequestTypeTab", val);
    },
    filterByStatus(){
      this.$emit('filterByStatus',this.selectedStatus);
    },
    changePerPage(){
      this.$emit("setPerPage", this.perPage);
    },
    clearSearch(){
      console.log("clear search")
    },
    searchByName() {
      if (typeof this.nameFilter !== "string") {
        this.showError("Invalid data: Input must be a string.");
        return false;
      }
      const invalidCharacters = this.nameFilter.match(/[^a-zA-Z0-9\s]/g);
      if (invalidCharacters) {
        this.showError("Invalid data: Special characters are not allowed.");
        return false;
      }
      const sanitized =  this.nameFilter.replace(/[^\w\s]/gi, "").trim();
      if (sanitized.length > 255) {
        this.showError("Invalid data: Input exceeds 255 characters.");
        return false;
      }
      this.$emit("filterByPartnerName",sanitized);
    },
  }
}
</script>
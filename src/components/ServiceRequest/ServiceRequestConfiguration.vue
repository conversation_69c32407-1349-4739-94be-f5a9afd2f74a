<template>
<v-container>
  <EventRequestWorkflow :allTiers="tiers" />
  <v-divider class="mt-8 mb-4"/>
  <AcademyRequestWorkflow class="mt-4" :allTiers="tiers"/>
</v-container>
</template>

<script>
import AcademyRequestWorkflow from "@/components/ServiceRequest/AcademyRequestWorkflow.vue";
import EventRequestWorkflow from "@/components/ServiceRequest/EventRequestWorkflow.vue";

export default {
  name: "ServiceRequestConfiguration",
  components:{
    AcademyRequestWorkflow,
    EventRequestWorkflow
  },
  data(){
    return{
      tiers:[],
    }
  },
  mounted() {
    this.getUserTiersList();
  },
  methods:{
    getUserTiersList() {
      this.$http
          .get(`venues/tiers?status_id=${this.status}`)
          .then((response) => {
            if (response.status === 200) {
              this.tiers = response.data.data;
            }
          }).catch((error) => {
        this.errorChecker(error);
      });
    },
  }
}
</script>
<style scoped>

</style>
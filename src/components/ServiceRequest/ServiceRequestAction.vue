<template>
  <div>
    <v-row class="ml-2">
      <v-col cols="12" md="2"><v-btn class="btn text-capitalize v-btn--outlined"  text large style="background: #F7E4E6; color: #EC2028" @click="rejectRequest()" v-if="isShowRejectBtn">Reject</v-btn></v-col>
      <v-col cols="12" md="10" class="text-right pr-5">
        <v-btn class="btn-sm mr-2  text-capitalize v-btn--outlined"  text large style="background: #E7F2F3; color: #4FAEAF" @click="editRequest()" v-if="isShowEditBtn">Edit</v-btn>
        <v-btn class="btn-sm mr-2  text-capitalize v-btn--outlined"  text large style="background: #E7F2F3; color: #4FAEAF" @click="amendRequest()" v-if="isShowAmendmentBtn">Request Amendment</v-btn>
        <v-btn class="btn-sm ml-2 text--white  text-capitalize v-btn--outlined"  large  text style="background: #112A46; color: #fff" @click="approveRequest()" v-if="isShowApproveBtn">Approve</v-btn>

        <v-btn  class="ma-2  text_capitalize"  color="red" large outlined @click="submitCancellationRequest()" v-if="isShowCancelRequestBtn">Request Cancellation</v-btn>
        <v-btn  class="ma-2  text_capitalize" large  outlined @click="rejectCancellationRequest()" v-if="isShowRejectCancellationBtn">Reject Cancellation Request</v-btn>
        <v-btn  class="ma-2  text_capitalize"  color="red"  large outlined @click="cancelRequest()" v-if="isShowCancelBtn">Confirm Cancel</v-btn>

      </v-col>
    </v-row>
    <ServiceRequestActionConfirmationModal
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="closeModal"
    ></ServiceRequestActionConfirmationModal>
  </div>
</template>
<script>
import ServiceRequestActionConfirmationModal from "@/components/ServiceRequest/ServiceRequestActionConfirmationModal.vue";
export default {
  components: {ServiceRequestActionConfirmationModal},
  props:{
    requestData: { type: Object, default: null },
  },
  data(){
    return {
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      userList: [],
    }
  },
  computed: {
    isShowRejectBtn(){
      const userInfo = this.$store.getters.userInfo;
      if(this.requestData && userInfo){
        if(this.requestData.status_id === 11 && this.requestData.assign_to === userInfo.user_id){
          return true;
        }
      }
      return false;
    },
    isShowEditBtn(){
      const userInfo = this.$store.getters.userInfo;
      if(this.requestData && userInfo){
        if(this.requestData.status_id === 34 && this.requestData.requested_by === userInfo.user_id){
          return true;
        }else if(this.requestData.status_id === 31 && this.requestData.assign_to === userInfo.user_id){
          return true;
        }
      }
      return false;
    },
    isShowAmendmentBtn(){
      const userInfo = this.$store.getters.userInfo;
      if(this.requestData && userInfo && this.requestData.status_id === 11 && this.requestData.assign_to === userInfo.user_id){
        return true;
      }
      return false;
    },
    isShowApproveBtn(){
      const userInfo = this.$store.getters.userInfo;
      if(this.requestData && userInfo && this.requestData.status_id === 11 && this.requestData.assign_to === userInfo.user_id){
        return true;
      }
      return false;
    },
    isShowCancelRequestBtn(){
      const userInfo = this.$store.getters.userInfo;
      return this.requestData && this.requestData.status_id === 33 && this.requestData.requested_by === userInfo.user_id;
    },
    isShowCancelBtn(){
      if(this.requestData && this.requestData.status_id === 35){
        if(this.requestData.request_type_id === 3){
          return !!this.checkDeletePermission(this.$modules.events.management.slug);
        }else if(this.requestData.request_type_id === 4){
          return !!this.checkDeletePermission(this.$modules.workshops.management.slug);
        }
        return false;
      }
      return false;
    },
    isShowRejectCancellationBtn(){
      if(this.requestData && this.requestData.status_id === 35){
        if(this.requestData.request_type_id === 3){
          return !!this.checkWritePermission(this.$modules.events.management.slug);
        }else if(this.requestData.request_type_id === 4){
          return !!this.checkWritePermission(this.$modules.workshops.management.slug);
        }
        return false;
      }
      return false;
    }
  },
  methods: {
    confirmActions(data) {
      const formData = new FormData();
      let url = "";
      if (data && data.type === "reject") {
        url = "venues/service-request/reject";
      }else if (data && data.type === "approve") {
        url = "venues/service-request/approve";
      }else if (data && data.type === "amendment") {
        url = "venues/service-request/amendment";
        if(data.assign_to) {
          formData.append('assign_to', data.assign_to);
        }
      }else if (data && data.type === "request_cancellation") {
        url = "venues/service-request/cancellation";
      }else if (data && data.type === "reject_cancellation") {
        url = "venues/service-request/reject-cancellation";
      }else if (data && data.type === "cancel") {
        url = "venues/service-request/cancel";
      }
      if(!url){
        this.showError("Invalid URL");
        return;
      }
      formData.append('id',this.requestData.id);
      formData.append('comment',this.sanitizeInput(data.comment));
      if(data.documents && data.documents.length > 0){
        data.documents.forEach((document,index) => {
          if(document.file != null && document.file != "" && document.file != "null") {
            formData.append(`documents[${index}][name]`, document.name);
            formData.append(`documents[${index}][file]`, document.file);
          }
        });
      }
      this.showLoader("wait");
      this.$http({
          method: "post",
          data: formData,
          url: url,
          headers: {
            "Content-Type": "multipart/form-data; boundary=${form._boundary}",
          },
        }).then((response) => {
          this.hideLoader();
          if (response.status === 200 && response.data.status === true) {
            this.showSuccess(response.data.message);
            this.$emit('reload');
          }
        }).catch((error) => {
          this.errorChecker(error);
        });
      this.confirmModel.id = null;
    },
    editRequest() {
      if(this.requestData.request_type_id === 3){
        this.$router.push({
          name: "EventRequest",
          query: { reqId:  btoa(this.requestData.id) },
        });
      }else if(this.requestData.request_type_id === 4){
        this.$router.push({
          name: "AcademyRequest",
          query: { reqId: btoa(this.requestData.id) },
        });
      }else{
        this.showError("Request type not supported");
      }
      console.log("edit", this.requestData.id);
    },
    amendRequest(){
      const userInfo = this.$store.getters.userInfo;
      if(this.requestData.partner_id !==  userInfo.user_id){
        this.userList = [
          {id: this.requestData.partner_id, name: this.requestData.partner_name+' (partner)'},
        ];
        if(this.requestData.back_tier && this.requestData.back_tier.users) {
          this.requestData.back_tier.users.forEach((u) => {
            let extra = "";
            if (u.id === this.requestData.sender_id) {
              extra = "*";
            }
            let ob = {
              id: u.id,
              name: u.name + ' (' + this.requestData.back_tier.title + extra + ')'
            }
            this.userList.push(ob);
          })
        }
      }
      this.confirmModel = {
        id: this.requestData.id,
        title: `Do you want to amend this request?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "amendment",
        userList: this.userList,
      };
    },
    rejectRequest() {
      this.confirmModel = {
        id: this.requestData.id,
        title: `Do you want to reject this request?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "reject",
      };
    },
    approveRequest(){
      this.confirmModel = {
        id: this.requestData.id,
        title: `Do you want to approve this request?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "approve",
      };
    },
    cancelRequest(){
      this.confirmModel = {
        id: this.requestData.id,
        title: `Do you want to cancel this request?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "cancel",
      }
    },
    submitCancellationRequest(){
      this.confirmModel = {
        id: this.requestData.id,
        title: `Do you want to submit cancellation request?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "request_cancellation",
      }
    },
    rejectCancellationRequest(){
      this.confirmModel = {
        id: this.requestData.id,
        title: `Do you want to reject cancellation request?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "reject_cancellation",
      }
    },
    sanitizeInput(input) {
      if (typeof input !== "string") {
        throw new Error("Invalid data: Input must be a string.");
      }
      const sanitized =  input.replace(/[^\w\s]/gi, "").trim();
      if (sanitized.length > 255) {
        throw new Error("Invalid data: Input exceeds 255 characters.");
      }
      return sanitized;
    },
    closeModal(){
      this.confirmModel.id = null
      this.userList = [];
    }
  }
}
</script>
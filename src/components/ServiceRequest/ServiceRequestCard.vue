<template>
  <div class="shadow rounded-lg p-4 pointer" style="position: relative" @click="viewServiceRequest">
    <div :class="`status-badge ${getStatusText(serviceRequest.status_id)}`">{{ getStatusText(serviceRequest.status_id) }}</div>
    <v-row>
      <v-col class="px-3 mt-3" cols="4">
        <v-img :aspect-ratio="1" :src="imageSrc" class="rounded-2"></v-img>
      </v-col>
      <v-col class="px-2" cols="8">
        <div class="d-flex align-center justify-space-between mt-3">
          <span class="text-lg mb-0 font-semibold">{{ name }}</span>
        </div>
        <div class="d-flex align-center justify-space-between mt-1">
          <span class="font-12 mb-0">Date </span>
          <span class="font-12 mb-0 font-semibold">{{getScheduleDate() }} </span>
        </div>
        <div class="d-flex align-center mt-1 justify-space-between">
          <span class="font-12 mb-0">Partner</span>
          <span class="font-12 mb-0 font-semibold">{{ serviceRequest.partner }} </span>
        </div>
        <div class="d-flex align-center mt-1 justify-space-between">
          <span class="font-12 mb-0">Timestamp</span>
          <span class="font-12 mb-0 font-semibold">{{ serviceRequest.timestamp | timeStamp }} </span>
        </div>
      </v-col>
    </v-row>
  </div>
</template>
<script>
export default {
  name: "ServiceRequestCard",
  props: {
    serviceRequest: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },
  computed:{
    getCurrentVenueId(){
      return this.$store.getters.svid;
    },
    imageSrc(){
      return this.serviceRequest?.request_body?.image_path ? this.s3BucketURL + this.serviceRequest?.request_body?.image_path : require('@/assets/images/default_images/event_default.jpg');
    },
    name(){
      return this.serviceRequest?.request_body?.name;
    },
  },
  methods:{
    getStatusText(status_id){
      if(status_id === 11){
        return "pending";
      }else if(status_id === 31){
        return "amendment";
      }else if(status_id === 32){
        return "rejected";
      }else if(status_id === 33){
        return "approved";
      }else if(status_id === 34){
        return "draft";
      }else if(status_id === 35){
        return "cancellation requested";
      }else if(status_id === 13){
        return "cancelled";
      }
    },
    getScheduleDate(){
      const rb = this.serviceRequest?.request_body;
      if(rb && this.serviceRequest.request_type_id === 3 && rb.event_schedules){
        const result = rb.event_schedules.reduce(
            (acc, schedule) => {
              if (!acc.smallestStartDate || schedule.start_date < acc.smallestStartDate) {
                acc.smallestStartDate = schedule.start_date;
              }
              if (!acc.largestEndDate || schedule.end_date > acc.largestEndDate) {
                acc.largestEndDate = schedule.end_date;
              }
              return acc;
            },
            { smallestStartDate: null, largestEndDate: null }
        );
        // Format dates to 'DD MMM'
        const formatDate = (dateString) => {
          const date = new Date(dateString);
          const options = { day: 'numeric', month: 'short' };
          return date.toLocaleDateString('en-GB', options);
        };
        let displayDate = "";
        if (result.smallestStartDate === result.largestEndDate) {
          displayDate = formatDate(result.smallestStartDate);
        } else {
          const start = formatDate(result.smallestStartDate);
          const end = formatDate(result.largestEndDate);
          displayDate = `${start} - ${end}`;
        }
        // Console Output
        return displayDate;
      }
      else if(rb && this.serviceRequest.request_type_id === 4){
        const formatDate = (dateString) => {
          const date = new Date(dateString);
          const options = { day: 'numeric', month: 'short' };
          return date.toLocaleDateString('en-GB', options);
        };
        let displayDate = "";
        const start = formatDate(rb.start_date);
        const end = formatDate(rb.end_date);
        displayDate = `${start} - ${end}`;
        return displayDate;
      }
      return "";
    },
    viewServiceRequest(){
      if(this.serviceRequest.request_type_id === 3){
        this.$router.push({
          name: "EventRequestDetails",
          params: {data: btoa(JSON.stringify({id: this.serviceRequest.id}))},
        });
      }else if(this.serviceRequest.request_type_id === 4){
        this.$router.push({
          name: "AcademyRequestDetails",
          params: {data: btoa(JSON.stringify({id: this.serviceRequest.id}))},
        });
      }else{
        this.showError("Request type not supported");
      }
    }
  }
}
</script>

<style scoped>
.status-badge {
  position: absolute;
  right: 0;
  top: 0;
  padding: 5px 15px;
  font-size: 10px;
  color: #fff;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
  text-transform: capitalize;
  font-weight: 600;
}
.status-badge.approved{
  background: #B6F0B5;
  color: #113206;
}
.status-badge.pending{
  background: #FFCDB3;
  color: #471E08;
}
.status-badge.rejected{
  background: #FFBAB6;
  color: #4C1008;
}
.status-badge.amendment {
  background: dimgrey;
}
.status-badge.draft{
  background: #D2D2D2;
  color: #000000;
}
.status-badge.cancellation{
  background: pink;
  color: #000000;
}
.status-badge.cancelled{
   background: black;
   color: #fff;
 }
</style>
<template>
  <v-card style="border-radius: 8px" class="shadow package_card pa-0">
    <v-card-text class="d-flex align-center justify-center">
      {{name}}
    </v-card-text>
  </v-card>
</template>
<script>
export default {
  name: "tierCard",
  props: {
    name: String,
  }
}
</script>


<style lang="scss">

.package_card {
  min-width: 80px;
  min-height: 50px;
  top: 225px;
  left: 507px;
  gap: 0px;
  border-radius: 4px 0px 0px 0px;
  position: unset;
}

</style>
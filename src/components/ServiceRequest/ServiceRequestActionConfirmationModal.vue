<template>
  <v-dialog v-model="dialoge" persistent max-width="580">
    <v-card>
      <v-form ref="srform">
        <v-card-title class="headline1">{{ title ? title : "Do you sure?" }}</v-card-title>
        <v-divider />
        <v-card-text class="pa-5">
          <span v-html=" description ? description : 'Do you need to continue your action ?'"></span>
          <slot name="content" ></slot>
          <v-row>
            <v-col cols="12" class="pb-0  mt-3" v-if="type === 'amendment' && userList && userList.length > 1" >
              <v-select
                  style="max-width: 280px;"
                  v-model="assignTo"
                  item-value="id"
                  item-text="name"
                  :menu-props="{ bottom: true, offsetY: true }"
                  :rules="[(v) => !!v || 'Select user']"
                  :items="userList"
                  outlined
                  background-color="#fff"
                  dense
                  class="q-autocomplete shadow-0"
                  required
                  hide-details="auto"
                  label="Assign To"
                  placeholder="Assign To"
                  rows="3"
              ></v-select>
            </v-col>
            <v-col cols="12" class="pb-0 mt-2">
              <v-textarea
                  outlined
                  background-color="#fff"
                  v-model="comment"
                  hide-details="auto"
                  label="Comments *"
                  placeholder="Write your comments"
                  rows="3"
                  :rules="[(v) => !!v || 'comments required']"
              ></v-textarea>
            </v-col>
            <v-col cols="12" class="pb-0" v-if="documents && documents.length" >
              <v-row v-for="(document,index) in documents" :key="index" style="position: relative">
                <v-col cols="12" md="6" class="pb-0 ">
                  <v-text-field
                      class="q-text-field shadow-0"
                      outlined
                      dense
                      background-color="#fff"
                      v-model="document.name"
                      hide-details="auto"
                      label="Document Name"
                      placeholder="Document Name"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="5" class="pb-0pr-2">
                  <v-file-input
                      placeholder="Document"
                      v-model="document.file"
                      outlined
                      background-color="#fff"
                      prepend-inner-icon="mdi-paperclip"
                      prepend-icon
                      hide-details="auto"
                      class="q-text-field shadow-0"
                      dense
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                  >
                  </v-file-input>
                </v-col>
                <v-btn :ripple="false" class="no-hover-effect delete-btn" text @click="deleteDocuments(d)"><DeleteIcon/></v-btn>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <span class="mt-10 ml-6 btn-sm text-capitalize font-12 pointer text-underline teal--text" @click="addDocuments" v-if="documents && documents.length < 5">Add Document</span>
        <v-divider class="mt-1"/>
        <v-card-actions>

          <v-spacer></v-spacer>
          <v-btn class="btn v-btn--outlined" color="darken-1" text @click="cancel">No</v-btn>
          <v-btn class="btn-sm ml-2 text--white  text-capitalize v-btn--outlined" text style="background: #112A46; color: #fff" @click="confirm" :disabled="disableConfirm">Yes</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script>
import DeleteIcon from "@/assets/images/misc/delete-bg-icon.svg";

export default {
  components: { DeleteIcon },
  props: {
    id: { type: Number, default: null },
    type: { type: String, default: "delete" },
    title: { type: String, default: "Are you sure?" },
    description: {
      type: String,
      default: "By clicking <b>Yes</b> you can confirm operation. Do you need to continue your action ???",
    },
    userList: { type: Array, default: () => []},
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    disableConfirm: { type: Boolean, default: false }
  },

  watch: {
    id(val) {
      if (val != null) {
        this.dialoge = true;
        console.log("dada");
      }
    },
  },
  data() {
    return {
      dialoge: false,
      assignTo:null,
      comment: null,
      documents: [
        {
          name: null,
          file: null,
        }
      ],
    };
  },
  methods: {
    confirm() {
      try {
        if (!this.$refs.srform.validate()) {
          this.showError("Comments are required");
          return;
        }
        this.dialoge = false;
        let c = this.comment;
        if(c) {
          c = this.sanitizeInput(c);
        }
        this.comment = null;
        this.$emit("confirm", { id: this.id, type: this.type, comment: c,documents:this.documents,assign_to: this.assignTo });
      } catch (error) {
        this.showError(error.message);
        this.dialoge = false;
      }
    },
    cancel() {
      this.comment = null;
      this.dialoge = false;
      this.documents = [{name: null, file: null,}];
      this.$emit("close", { id: this.id, type: this.type, comment: this.comment });
    },
    addDocuments() {
      this.documents.push({
        name: "",
        file: null,
      });
    },
    deleteDocuments(index) {
      if (this.documents.length > 1) {
        this.documents.splice(index, 1);
      }
    },
    sanitizeInput(input) {
      if (typeof input !== "string") {
        throw new Error("Invalid data: Input must be a string.");
      }
      const sanitized =  input.replace(/[^\w\s]/gi, "").trim();
      if (sanitized.length > 255) {
        throw new Error("Invalid data: Input exceeds 255 characters.");
      }
      return sanitized;
    }
  },
};
</script>
<style scoped>
.delete-btn{
  position: absolute;
  right: 0px;
  top: 16px;
}
</style>

<template>
  <div>
    <v-dialog
      v-model="orderDialog"
      max-width="70%"
      @input="closeOrderDetails"
      scrollable
    >
      <v-card tile>
        <v-card-text>
          <v-row class="pt-6">
            <v-col md="8">
              <v-card outlined>
                <v-card-title
                  ><span class="text_line">SELECT PAYMENT METHOD</span>
                  <v-spacer></v-spacer>
                  <v-btn outlined
                    >Total {{ paymentTotal | toCurrency }} /
                    {{ orderDetails.credit_amount }}</v-btn
                  >
                </v-card-title>
                <v-card-text>
                  <v-form ref="form">
                    <v-row>
                      <v-col cols="12" md="12">
                        <template v-for="(method, index) in payments">
                          <payment-method
                            v-bind="method"
                            :index="index"
                            :key="`p_${index}`"
                            :updateCommitType="'updateCreditPaymentMethod'"
                            @remove="removePaymentMethod"
                          ></payment-method>
                        </template>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-card-text>
                <v-card-actions v-if="isEnabledMultiPayment()">
                  <v-spacer></v-spacer>
                  <v-btn outlined color="primary" @click="addNewPaymentMethod">
                    Add New Method <v-icon small right>mdi-plus</v-icon>
                  </v-btn>
                  <v-spacer></v-spacer>
                </v-card-actions>
              </v-card>
              <v-btn
                dark
                class="yellow-color"
                absolute
                bottom
                @click="$emit('close'), $emit('reload')"
                >Settle later</v-btn
              >
              <v-btn
                dark
                class="red"
                outlined
                absolute
                bottom
                @click="cancel()"
                style="margin-left: 115px"
                >Cancel Reservation</v-btn
              >
            </v-col>
            <v-col md="4">
              <v-card outlined>
                <v-card-title
                  ><span class="text_line">ORDER SUMMARY</span></v-card-title
                >
                <v-card-text>
                  <v-list
                    three-line
                    style="min-height: 150px; max-height: 250px"
                    class="overflow-y-auto"
                  >
                    <v-list-item
                      v-for="(product, i) in products"
                      :key="i"
                      ripple
                      @click="() => {}"
                    >
                      <v-list-item-avatar title height="64" width="64">
                        <view-image :image="product.image"></view-image>
                      </v-list-item-avatar>

                      <v-list-item-content>
                        <v-list-item-title class="text-body">{{
                          product.name
                        }}</v-list-item-title>
                        <v-list-item-subtitle>
                          <div
                            class="d-flex justify-space-between text-caption"
                          >
                            <div class="font-weight-bold">
                              {{ product.product_price }}
                              x {{ product.quantity }}
                            </div>
                            <div class="font-weight-bold">
                              Tax
                              {{
                                (product.discount
                                  ? product.actual_tax
                                  : product.tax) | toCurrency
                              }}
                            </div>
                            <div class="font-weight-bold">
                              {{ currencyCode }}
                              {{
                                product.discount
                                  ? product.actual_total
                                  : product.total
                              }}
                            </div>
                          </div>
                        </v-list-item-subtitle>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list>
                  <v-divider></v-divider>
                  <div class="d-flex justify-space-between subtitle-2">
                    <div class="">Subtotal</div>
                    <div class="">
                      {{
                        (orderDetails.discount
                          ? orderDetails.actual_price
                          : orderDetails.price) | toCurrency
                      }}
                    </div>
                  </div>
                  <div class="d-flex justify-space-between subtitle-2">
                    <div class="">Tax</div>
                    <div class="">
                      {{
                        (orderDetails.discount
                          ? orderDetails.actual_tax
                          : orderDetails.tax) | toCurrency
                      }}
                    </div>
                  </div>
                  <div class="d-flex justify-space-between subtitle-2">
                    <div class="">Paid Amount</div>
                    <div class="">
                      -{{
                        (orderDetails.credit_amount
                          ? orderDetails.total - orderDetails.credit_amount
                          : 0) | toCurrency
                      }}
                    </div>
                  </div>
                  <div
                    class="d-flex justify-space-between subtitle-2 green--text"
                    v-if="orderDetails.discount"
                  >
                    <div class="">
                      Discount - {{ orderDetails.promotion_name }}
                    </div>
                    <div class="">
                      {{
                        (orderDetails.actual_price - orderDetails.price)
                          | toCurrency
                      }}
                      +
                      {{
                        (orderDetails.actual_tax - orderDetails.tax)
                          | toCurrency
                      }}
                      =
                      {{
                        (orderDetails.actual_total - orderDetails.total)
                          | toCurrency
                      }}
                    </div>
                  </div>
                  <v-divider></v-divider>
                  <div
                    class="
                      d-flex
                      justify-space-between
                      font-weight-bold
                      subtitle-1
                      black--text
                    "
                  >
                    <div>Balance</div>

                    <div>
                      <span
                        class="text-decoration-line-through pt-1"
                        v-if="orderDetails.discount"
                      >
                        {{ orderDetails.actual_total | toCurrency }}
                      </span>
                      {{ orderDetails.credit_amount | toCurrency }}
                    </div>
                  </div>
                </v-card-text>
                <v-divider></v-divider>
                <div class="pa-2">
                  <v-textarea
                    v-model="paymentNote"
                    rows="3"
                    label="Payment note"
                    outlined
                  ></v-textarea>
                </div>

                <v-divider></v-divider>
                <v-card-actions>
                  <v-btn
                    block
                    large
                    color="rgb(0, 176, 175)"
                    dark
                    @click="settleCredit"
                    >Pay now</v-btn
                  >
                </v-card-actions>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
        <confirm-model
          v-bind="confirmModel"
          @confirm="confirmActions"
          @close="confirmModel.id = null"
        ></confirm-model>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import PaymentMethod from "./PaymentMethod.vue";
export default {
  components: {
    "payment-method": PaymentMethod,
  },
  props: {
    id: { type: Number, default: null },
  },
  data() {
    return {
      balance: 0,
      confirmModel: {},
      paymentNote: null,
      discountAmount: null,
      prevdiscountAmount: null,
      discountPercentage: null,
      orderDialog: false,
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        if (val == "" || val == null) {
          this.orderId = null;
          this.orderDialog = false;
          return;
        }
        this.orderId = val;
        this.getOrderDetails();
      },
    },
  },
  mounted() {
    if (!this.$store.getters.getSalesConfig) {
      this.$store.dispatch("loadVenueSalesConfig");
    }
  },
  computed: {
    products() {
      return this.$store.getters.getOrderItems;
    },
    payments() {
      return this.$store.getters.getCreditOrderPayments;
    },
    orderDetails() {
      return this.$store.getters.getOrderDetails;
    },
    paymentTotal() {
      return this.$store.getters.getCreditPaymentTotal;
    },
    salesConfig() {
      return this.$store.getters.getSalesConfig;
    },
  },
  methods: {
    getOrderDetails() {
      this.showLoader("Loading..");
      this.$store
        .dispatch("loadOrderDetails", this.orderId)
        .then((response) => {
          if (response.status == 200) {
            this.orderDialog = true;
            this.hideLoader();
          }
        });
    },

    addNewPaymentMethod() {
      this.$store
        .dispatch("addNewCreditPaymentMethod")
        .then(() => {})
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    removePaymentMethod(data) {
      this.confirmModel = {
        id: data.index,
        title: "Do you want to remove this Payment Method?",
        description:
          "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "remove_payment_method",
        data: { id: data.id, index: data.index },
      };
    },
    cancel() {
      this.confirmModel = {
        id: this.orderId,
        title: "Do you want to cancel this order?",
        description:
          "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "cancel",
      };
    },
    confirmActions(data) {
      if (data.type == "remove_payment_method") {
        setTimeout(() => {
          this.$store.commit("removeCreditPaymentMethod", data.data.index);
        });
      } else if (data.type == "cancel") {
        this.cancelOrder();
      }
      this.confirmModel.id = null;
    },
    settleCredit() {
      let data = {
        order_id: this.orderDetails.id,
        payments: this.payments,
      };
      if (this.paymentNote) {
        data.notes = this.paymentNote;
      }
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all fields");
        return;
      }
      this.showLoader("Payment processing");

      this.$http
        .post("venues/orders/settle-credit", data)
        .then((response) => {
          this.hideLoader();
          if (response.status == 200) {
            this.showSuccess("Payment success");
            if (this.orderDetails.id) {
              this.$store.dispatch("loadOrderDetails", this.orderDetails.id);
            }
            this.$emit("payed");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    cancelOrder() {
      this.showLoader("Wait");
      this.$http
        .delete(`venues/orders/${this.orderId}`)
        .then((response) => {
          if (response.status == 200) {
            this.hideLoader();
            this.showSuccess("Cancel success");
            this.$emit("cancel");
          }
        })
        .catch((error) => {
          this.hideLoader();
          this.errorChecker(error);
        });
    },
    closeOrderDetails() {
      this.$emit("close");
    },
    isEnabledMultiPayment() {
      if (this.salesConfig) {
        if (this.salesConfig.enable_multi_payment == 1) {
          return true;
        } else {
          return false;
        }
      }
      return true;
    }
  },
};
</script>

<style scoped>
.text_line {
  border-bottom: 3px solid rgb(0, 176, 175);
}
</style>

<template>
  <v-card
    color="#edf9ff"
    style="border: 1px #ccc solid"
    class="pa-5 mt-5 mb-6"
    outlined
  >
    <template>
      <v-row
        v-for="(attendanceCustomer, index) in attandanceForm.customers"
        :key="index"
        class="mt-0.5"
        style="position: relative"
      >
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              fab
              x-small
              absolute
              top
              right
              @click="removeCustomer(index)"
            >
              <v-icon color="red">mdi-delete</v-icon>
            </v-btn>
          </template>
          Delete
        </v-tooltip>
        <v-col sm="3" md="4">
          <div>
            <v-mobile-search
              label="Mobile No*"
              outlined
              :selected="attendanceCustomer.mobile"
              v-model="attendanceCustomer.search"
              @updateCustomer="setCustomerData(index, $event)"
            ></v-mobile-search>
          </div>
        </v-col>

        <v-col sm="3" md="4">
          <div>
            <v-name-search
              label="Name*"
              outlined
              :mobile="attendanceCustomer.mobile"
              :email="attendanceCustomer.email"
              :selected="attendanceCustomer.name"
              v-model="attendanceCustomer.nameSearch"
              @updateCustomer="setCustomerData(index, $event)"
              required
            ></v-name-search>
          </div>
        </v-col>

        <v-col sm="3" md="4">
          <div>
            <v-text-field
              outlined
              label="Email*"
              background-color="#fff"
              v-model="attendanceCustomer.email"
              :rules="[
                (v) => !!v || 'E-mail is required',
                (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
              ]"
              @updateCustomer="setCustomerData(index, $event)"
            ></v-text-field>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col sm="5" md="12" v-if="addAttandanceBtn">
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="blue-color"
                  fab
                  x-small
                  dark
                  @click="addAttandance"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add
            </v-tooltip>
          </div>
        </v-col>
      </v-row>
    </template>
  </v-card>
</template>

<script>
export default {
  props: {
    totalBookedCapacity: { type: Number, default: 0 },
    totalAttendies: { type: Number },
    totalCapacity: { type: Number },
    attendanceCustomers: { type: Array },
    editFlag: { type: Boolean, default: false },
    pastTime: { type: Boolean, default: false },
  },
  data() {
    return {
      //attandanceForm: { customers: [{}] },
      attendanceCustomer: null,
      addAttandanceBtn: true,
      bookedCapacity: 2,
    };
  },
  watch: {},
  mounted() {
    if (this.editFlag == true) {
      this.bookedCapacity = this.totalBookedCapacity;
      if (this.attendanceCustomers != "") {
        this.attandanceForm.customers = this.attendanceCustomers;
        this.$forceUpdate();
      } else {
        this.attandanceForm.customers = [{}];
        this.bookedCapacity = this.bookedCapacity + 1;
      }
    } else {
      this.bookedCapacity = this.totalBookedCapacity + 2;
    }

    this.capacityStatus();
  },
  computed: {
    attandanceForm() {
      if (this.attendanceCustomers != "") {
        return { customers: this.attendanceCustomers };
      } else {
        return { customers: [{}] };
      }
    },
  },
  methods: {
    addAttandance() {
      this.attandanceForm.customers.push({});
      this.bookedCapacity = this.bookedCapacity + 1;
      this.capacityStatus();
    },
    setCustomerData(index, data) {
      if (!data.customer_id) {
        this.$set(this.attandanceForm.customers[index], "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(
          this.attandanceForm.customers[index],
          "name",
          data.first_name
        );
      }

      if (
        this.attandanceForm.customers[index].customer_id &&
        !data.customer_id &&
        this.attandanceForm.customers[index].name != data.name &&
        this.attandanceForm.customers[index].mobile != data.mobile
      ) {
        this.$set(this.attandanceForm.customers[index], "mobile", null);
        this.attandanceForm.customers[index].search = null;
        this.attandanceForm.customers[index].nameSearch = null;
        this.$set(this.attandanceForm.customers[index], "email", null);
        this.$set(this.attandanceForm.customers[index], "gender", null);
        this.$set(this.attandanceForm.customers[index], "name", null);
        this.$set(this.attandanceForm.customers[index], "customer_id", null);
        this.$set(this.attandanceForm.customers[index], "first_name", null);
        this.$set(this.attandanceForm.customers[index], "image_path", null);
        this.$set(this.attandanceForm.customers[index], "dob", null);
        this.$set(this.attandanceForm.customers[index], "country_id", null);
        this.$set(this.attandanceForm.customers[index], "last_name", null);
        this.$forceUpdate();
      }

      if (data.mobile)
        this.$set(this.attandanceForm.customers[index], "mobile", data.mobile);
      if (data.email)
        this.$set(this.attandanceForm.customers[index], "email", data.email);
      if (data.country_id) {
        this.$set(
          this.attandanceForm.customers[index],
          "country_id",
          data.country_id
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "country_id", null);
      }
      if (data.gender) {
        this.$set(this.attandanceForm.customers[index], "gender", data.gender);
      } else {
        this.$set(this.attandanceForm.customers[index], "gender", null);
      }
      if (data.dob) {
        this.$set(this.attandanceForm.customers[index], "dob", data.dob);
      } else {
        this.$set(this.attandanceForm.customers[index], "dob", null);
      }
      if (data.name) {
        this.$set(this.attandanceForm.customers[index], "name", data.name);
        this.$set(this.attandanceForm.customers[index], "nameSearch", {
          name: data.name,
        });
      }
      if (data.last_name) {
        this.$set(
          this.attandanceForm.customers[index],
          "last_name",
          data.last_name
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "last_name", null);
      }
      if (data.first_name)
        this.$set(
          this.attandanceForm.customers[index],
          "first_name",
          data.first_name
        );
      if (data.customer_id)
        this.$set(
          this.attandanceForm.customers[index],
          "customer_id",
          data.customer_id
        );
      if (data.image_path) {
        this.$set(
          this.attandanceForm.customers[index],
          "image_path",
          data.image_path
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "image_path", null);
      }
      this.$forceUpdate();
      this.$emit("setAttendanceData", this.attandanceForm);
    },
    removeCustomer(index) {
      this.attandanceForm.customers.splice(index, 1);
      this.bookedCapacity = this.bookedCapacity - 1;
      this.capacityStatus();
    },
    capacityStatus() {
      this.$emit("setAttendanceData", this.attandanceForm);
      if (this.bookedCapacity >= this.totalCapacity) {
        this.addAttandanceBtn = false;
      } else {
        this.addAttandanceBtn = true;
      }
      this.$forceUpdate();
    },
  },
};
</script>

<style></style>

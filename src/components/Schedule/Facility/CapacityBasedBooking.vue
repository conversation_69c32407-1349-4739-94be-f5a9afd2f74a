<template>
  <v-card
    color="#edf9ff"
    style="border: 1px #ccc solid"
    class="pa-5 mt-5 mb-6"
    outlined
  >
    <template>
      <v-row
        v-for="(attendanceCustomer, index) in attandanceForm.customers"
        :key="index"
        class="mt-0.5"
        style="position: relative"
      >
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              fab
              x-small
              absolute
              top
              right
              @click="removeCustomer(index)"
            >
              <v-icon color="red">mdi-delete</v-icon>
            </v-btn>
          </template>
          Delete
        </v-tooltip>
        <v-col sm="12" md="12">
          <div class="row mt-1 mb-2">
            <v-col md="4"
              ><div class="pa-2 titles">Customer Details</div></v-col
            >
            <v-col md="4"
              ><div class="pa-2 titles text-center">
                Price
                {{ attendanceCustomer.total_price | toCurrency }}
                <span
                  v-if="
                    attendanceCustomer.discount != null &&
                    attendanceCustomer.price !=
                      attendanceCustomer.discount.actual_price
                  "
                  class="text-decoration-line-through"
                >
                  {{
                    attendanceCustomer.discount.actual_total | toCurrency
                  }}</span
                >
              </div></v-col
            >
            <v-spacer></v-spacer>
            <div
              md="4"
              v-if="promotions.length > 0"
              style="margin-bottom: -20px"
            >
              <v-autocomplete
                v-if="attendanceCustomer.card_number == null"
                :items="[{ name: 'None', promotion_code: null }, ...promotions]"
                item-text="name"
                height="50"
                item-value="promotion_code"
                v-model="attendanceCustomer.promotion_code"
                background-color="rgb(206, 168, 0)"
                outlined
                @change="verifyBenefitParticipants('promotion', index)"
                label="Promotions"
                :readonly="disablePromotion"
              >
              </v-autocomplete>
            </div>
          </div>
          <v-divider></v-divider>
          <v-row no-gutters>
            <v-col md="3">
              <v-radio-group
                v-model="attendanceCustomer.customer_type"
                class="d-flex"
                row
                @change="customerTypeChange($event, index)"
                mandatory
                :readonly="id > 0"
              >
                <v-radio label="Normal" color="cyan" value="normal"></v-radio>
                <v-radio
                  label="Corporate"
                  color="cyan"
                  value="corporate"
                ></v-radio>
                <v-radio
                  v-if="
                    checkWritePermission(
                      $modules.memberships.management.slug
                    ) ||
                    checkWritePermission($modules.memberships.dashboard.slug)
                  "
                  label="Member"
                  color="cyan"
                  value="member"
                ></v-radio>
              </v-radio-group>
            </v-col>
            <v-col md="3" v-if="attendanceCustomer.customer_type == 'member'">
              <v-member-search
                v-model="attendanceCustomer.member"
                @clear="clearBenefitParticipant"
                :selected="attendanceCustomer.card_number"
                @updateCustomer="(data) => setMemberData(data, index)"
                class="mt-4"
              >
              </v-member-search>
            </v-col>
            <v-col
              md="3"
              v-if="attendanceCustomer.customer_type == 'corporate'"
            >
              <v-autocomplete
                class="mt-4"
                label="Company Name"
                :items="companies"
                v-model="attendanceCustomer.company_id"
                item-text="name"
                item-value="id"
                outlined
                background-color="#fff"
                dense
              >
              </v-autocomplete>
            </v-col>
            <v-col
              md="2"
              v-if="attendanceCustomer.customer_type == 'corporate'"
            >
              <v-autocomplete
                :disabled="attendanceCustomer.company_id == null"
                :items="getCompanySales(index)"
                label="Sale Order ID"
                item-text="sale_seq_no"
                item-value="id"
                class="mt-4 ml-2"
                v-model="attendanceCustomer.company_sale_id"
                outlined
                background-color="#fff"
                dense
              >
              </v-autocomplete>
            </v-col>
            <v-spacer></v-spacer>
            <v-col md="3">
              <v-switch
                style="float: right"
                v-model="attendanceCustomer.opt_marketing"
                label="Opt In Marketing"
              ></v-switch>
            </v-col>
            <v-col md="1" class="text-right" v-if="!order_id">
              <card-data-button
                class="mt-2"
                label="Emirates ID"
                @data="
                  (data) => {
                    setCardData(data, index);
                  }
                "
              ></card-data-button>
            </v-col>
          </v-row>
        </v-col>
        <v-col sm="3" md="4">
          <div>
            <v-mobile-search
              label="Mobile No*"
              outlined
              :selected="attendanceCustomer.mobile"
              v-model="attendanceCustomer.search"
              @updateCustomer="setCustomerData(index, $event)"
            ></v-mobile-search>
          </div>
        </v-col>

        <v-col sm="3" md="4">
          <div>
            <v-name-search
              label="Name*"
              outlined
              :mobile="attendanceCustomer.mobile"
              :email="attendanceCustomer.email"
              :selected="attendanceCustomer.name"
              v-model="attendanceCustomer.nameSearch"
              @updateCustomer="setCustomerData(index, $event)"
              required
            ></v-name-search>
          </div>
        </v-col>

        <v-col sm="3" md="4">
          <div>
            <v-text-field
              outlined
              label="Email*"
              background-color="#fff"
              v-model="attendanceCustomer.email"
              :rules="[
                (v) => !!v || 'E-mail is required',
                (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
              ]"
              @updateCustomer="setCustomerData(index, $event)"
            ></v-text-field>
          </div>
        </v-col>
        <v-col sm="4" md="4" v-if="field.gender.is_visible">
          <v-select
            :items="['Male', 'Female']"
            :placeholder="`Gender${field.gender.is_required ? '*' : ''}`"
            :label="`Gender${field.gender.is_required ? '*' : ''}`"
            outlined
            :menu-props="{ bottom: true, offsetY: true }"
            v-model="attendanceCustomer.gender"
            :rules="genderRule"
            background-color="#fff"
          ></v-select>
        </v-col>
        <v-col sm="4" md="4" v-if="field.dob.is_visible">
          <date-of-birth
            :placeholder="`Date of Birth${field.dob.is_required ? '*' : ''}`"
            :label="`Date of Birth${field.dob.is_required ? '*' : ''}`"
            :rules="dobRule()"
            v-model="attendanceCustomer.dob"
          >
          </date-of-birth>
        </v-col>
        <v-col sm="4" md="4" v-if="field.nationality.is_visible">
          <v-autocomplete
            :items="countries"
            :hint="`Nationality${field.nationality.is_required ? '*' : ''}`"
            :label="`Nationality${field.nationality.is_required ? '*' : ''}`"
            :rules="nationalityRule"
            item-value="id"
            item-text="name"
            outlined
            v-model="attendanceCustomer.country_id"
            background-color="#fff"
          ></v-autocomplete>
        </v-col>
        <v-col sm="4" md="4" v-if="field.idProof.is_visible">
          <v-select
            :hint="`ID Type${field.idProof.is_required ? '*' : ''}`"
            :label="`ID Type${field.idProof.is_required ? '*' : ''}`"
            :rules="idTypeRule"
            outlined
            :menu-props="{ bottom: true, offsetY: true }"
            item-value="id"
            item-text="name"
            :items="idProofTypes"
            v-model="attendanceCustomer.id_proof_type_id"
            background-color="#fff"
          ></v-select>
        </v-col>
        <v-col sm="4" md="4" v-if="field.idProof.is_visible">
          <v-row no-gutters>
            <v-col md="7">
              <v-text-field
                :hint="`ID Number${field.idProof.is_required ? '*' : ''}`"
                :label="`ID Number${field.idProof.is_required ? '*' : ''}`"
                :rules="idTypeRule"
                class="text_field1"
                outlined
                v-model="attendanceCustomer.id_proof_number"
                background-color="#fff"
              ></v-text-field>
            </v-col>
            <v-col md="5">
              <v-file-input
                v-model="attendanceCustomer.id_proof"
                class="text_field2"
                :placeholder="`${
                  attendanceCustomer.id_proof_path ? 'Change' : 'Select'
                }${field.idProof.is_required ? '*' : ''}`"
                :label="`${
                  attendanceCustomer.id_proof_path ? 'Download' : 'ID Proof'
                } ${field.idProof.is_required ? '*' : ''}`"
                :rules="idProofRule"
                prepend-icon=""
                background-color="#fff"
                outlined
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-icon
                        color="cyan"
                        v-if="attendanceCustomer.id_proof_path"
                        @click="openFile(attendanceCustomer.id_proof_path)"
                        v-on="on"
                      >
                        mdi-download-box
                      </v-icon>
                      <v-icon v-else v-on="on">
                        mdi-card-account-details
                      </v-icon>
                    </template>
                    <span v-if="attendanceCustomer.id_proof_path"
                      >Download uploaded file</span
                    >
                    <span v-else>Upload ID Proof</span>
                  </v-tooltip>
                </template>
                <template v-slot:selection="{ index, text }">
                  <v-chip
                    v-if="index == 0"
                    color="cyan accent-4"
                    dark
                    label
                    small
                  >
                    <span style="width: 38px" class="text-truncate">{{
                      text
                    }}</span>
                  </v-chip>
                </template>
              </v-file-input>
            </v-col>
          </v-row>
          <!-- <div style="margin-top: -110px"></div> -->
        </v-col>
        <v-col sm="4" md="4" v-if="field.image.is_visible">
          <v-row no-gutters>
            <v-col md="8">
              <v-file-input
                v-model="attendanceCustomer.profile_image"
                class="text_field1"
                prepend-icon=""
                :placeholder="`${
                  attendanceCustomer.id_proof_path ? 'Change' : 'Select'
                }${field.image.is_required ? '*' : ''}`"
                :label="`${
                  attendanceCustomer.id_proof_path
                    ? 'Change image'
                    : 'Upload Image'
                } ${field.image.is_required ? '*' : ''}`"
                :rules="imageRule"
                background-color="#fff"
                outlined
                show-size
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-icon
                        color="cyan"
                        v-if="attendanceCustomer.image_path"
                        @click="openFile(attendanceCustomer.image_path)"
                        v-on="on"
                      >
                        mdi-download-box
                      </v-icon>
                      <v-icon v-else v-on="on">mdi-image</v-icon>
                    </template>
                    <span v-if="attendanceCustomer.image_path">
                      Download image</span
                    >
                    <span v-else>Upload Image</span>
                  </v-tooltip>
                </template>
                <template v-slot:selection="{ index, text }">
                  <v-chip
                    v-if="index == 0"
                    color="cyan accent-4"
                    dark
                    label
                    small
                  >
                    <span style="width: 120px" class="text-truncate">{{
                      text
                    }}</span>
                  </v-chip>
                </template>
              </v-file-input>
            </v-col>
            <v-col md="4">
              <v-btn
                large
                block
                style="background-color: #fff"
                outlined
                height="56"
                color="blue-grey"
                class="white--text text_field2"
                @click="setWebCamDialog(index)"
              >
                <v-icon dark>mdi-camera</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
        <v-col md="12">
          <ProductSelection
            :key="index"
            :products="attendanceCustomer.products"
            :categories="categories"
            :categoriesList="categoriesList"
            :taxTypes="taxTypes"
            :productCatId="attendanceCustomer.productCategoryId"
            :venueServiceId="venueServiceId"
            @setCustomerProduct="(data) => setCustomerProduct(index, data)"
        /></v-col>
      </v-row>

      <v-row>
        <v-col sm="5" md="12" v-if="addAttandanceBtn">
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="blue-color"
                  fab
                  x-small
                  dark
                  @click="addAttandance"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add
            </v-tooltip>
          </div>
        </v-col>
      </v-row>
      <capture-image
        :open="webcamDialog"
        @close="webcamDialog = false"
        @confirm="confirmImageCapture"
      />
    </template>
  </v-card>
</template>

<script>
import moment from "moment";
import VMemberSearch from "@/components/Customer/VMemberSearch";
import CaptureImage from "@/components/Image/CaptureImage";
import bookingFields from "@/mixins/bookingFieldValidation";
import ProductSelection from "@/components/Schedule/Facility/ProductSelection";
export default {
  props: {
    repeatId: { type: Number, default: null },
    venueServiceId: { type: Number, default: null },
    totalBookedCapacity: { type: Number, default: 0 },
    totalAttendies: { type: Number },
    totalCapacity: { type: Number },
    attendanceCustomers: { type: Array },
    editFlag: { type: Boolean, default: false },
    pastTime: { type: Boolean, default: false },
    id: { type: Number, default: 0 },
    order_id: { type: Number, default: null },
    countries: { type: Array },
    idProofTypes: { type: Array },
    categoriesList: { type: Array },
    categories: { type: Array },
    taxTypes: { type: Array },
    promotions: { type: Array },
    disablePromotion: { type: Boolean, default: false },
    companies: { type: Array },
  },
  mixins: [bookingFields],
  components: {
    VMemberSearch,
    CaptureImage,
    ProductSelection,
  },
  data() {
    return {
      //attandanceForm: { customers: [{}] },
      attendanceCustomer: null,
      addAttandanceBtn: true,
      bookedCapacity: 2,
      webcamDialog: false,
      currentCustomerIndex: 0,
      // attendanceForm: {
      //   customers: [
      //     {
      //       opt_marketing: false,
      //       productCategoryId: null,
      //       quantity: 1,
      //       price: 0,
      //       total_price: 0,
      //       selectedProduct: { quantity: 1, price: 0 },
      //       products: new Array(),
      //       discount: null,
      //       promotion_code: null,
      //     },
      //   ],
      // },
    };
  },
  watch: {},
  mounted() {
    if (this.editFlag == true) {
      this.bookedCapacity = this.totalBookedCapacity;
      if (this.attendanceCustomers != "") {
        this.attandanceForm.customers = this.attendanceCustomers;
        this.$forceUpdate();
      } else {
        this.attandanceForm.customers = [
          {
            opt_marketing: false,
            productCategoryId: null,
            quantity: 1,
            price: 0,
            total_price: 0,
            selectedProduct: { quantity: 1, price: 0 },
            products: new Array(),
            discount: null,
            promotion_code: null,
          },
        ];
        this.bookedCapacity = this.bookedCapacity + 1;
      }
    } else {
      this.bookedCapacity = this.totalBookedCapacity + 2;
    }

    this.capacityStatus();
  },
  computed: {
    attandanceForm() {
      // console.log("computed", this.attendanceCustomers);
      if (this.attendanceCustomers != "") {
        return { customers: this.attendanceCustomers };
      } else {
        // console.log("computed customers");
        return { customers: [{}] };
      }
    },
  },
  methods: {
    setWebCamDialog(index) {
      this.currentCustomerIndex = index;
      this.webcamDialog = true;
    },
    addAttandance() {
      this.attandanceForm.customers.push({
        opt_marketing: false,
        productCategoryId: null,
        quantity: 1,
        price: 0,
        total_price: 0,
        selectedProduct: { quantity: 1, price: 0 },
        products: new Array(),
        discount: null,
        promotion_code: null,
      });
      this.bookedCapacity = this.bookedCapacity + 1;
      this.capacityStatus();
    },
    setCardData(data, index = null) {
      this.setCustomerData(index, data);
      this.$forceUpdate();
    },
    setCustomerData(index, data) {
      if (data.mobile && data.first_name && data.customer_id) {
        this.searchMember(
          data.mobile,
          data.customer_id,
          data.first_name,
          data.last_name,
          index
        );
      } else {
        this.clearCardAndBenefits(index);
      }
      if (!data.customer_id) {
        this.$set(this.attandanceForm.customers[index], "customer_id", null);
        this.$set(this.attandanceForm.customers[index], "card_number", null);
        this.$set(this.attandanceForm.customers[index], "member", null);
      }

      if (!data.name && data.first_name) {
        this.$set(
          this.attandanceForm.customers[index],
          "name",
          data.first_name
        );
      }

      if (
        this.attandanceForm.customers[index].customer_id &&
        !data.customer_id &&
        this.attandanceForm.customers[index].name != data.name &&
        this.attandanceForm.customers[index].mobile != data.mobile
      ) {
        this.$set(this.attandanceForm.customers[index], "card_number", null);
        this.$set(this.attandanceForm.customers[index], "mobile", null);
        this.attandanceForm.customers[index].search = null;
        this.attandanceForm.customers[index].nameSearch = null;
        this.$set(this.attandanceForm.customers[index], "email", null);
        this.$set(this.attandanceForm.customers[index], "gender", null);
        this.$set(this.attandanceForm.customers[index], "name", null);
        this.$set(this.attandanceForm.customers[index], "customer_id", null);
        this.$set(this.attandanceForm.customers[index], "first_name", null);
        this.$set(this.attandanceForm.customers[index], "image_path", null);
        this.$set(this.attandanceForm.customers[index], "dob", null);
        this.$set(this.attandanceForm.customers[index], "country_id", null);
        this.$set(this.attandanceForm.customers[index], "last_name", null);
        this.$forceUpdate();
      }
      if (data.customer_type) {
        this.$set(
          this.attandanceForm.customers[index],
          "customer_type",
          data.customer_type
        );
      } else {
        this.$set(
          this.attandanceForm.customers[index],
          "customer_type",
          "normal"
        );
      }
      if (data.card_number) {
        this.$set(
          this.attandanceForm.customers[index],
          "card_number",
          data.card_number
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "card_number", null);
      }
      if (data.mobile) {
        this.$set(this.attandanceForm.customers[index], "mobile", data.mobile);
      }
      if (data.email) {
        this.$set(this.attandanceForm.customers[index], "email", data.email);
      }
      if (data.country_id) {
        this.$set(
          this.attandanceForm.customers[index],
          "country_id",
          data.country_id
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "country_id", null);
      }
      if (data.gender) {
        this.$set(this.attandanceForm.customers[index], "gender", data.gender);
      } else {
        this.$set(this.attandanceForm.customers[index], "gender", null);
      }
      if (data.dob) {
        this.$set(this.attandanceForm.customers[index], "dob", data.dob);
      } else {
        this.$set(this.attandanceForm.customers[index], "dob", null);
      }
      if (data.name) {
        this.$set(this.attandanceForm.customers[index], "name", data.name);
        this.$set(this.attandanceForm.customers[index], "nameSearch", {
          name: data.name,
        });
      }
      if (data.last_name) {
        this.$set(
          this.attandanceForm.customers[index],
          "last_name",
          data.last_name
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "last_name", null);
      }
      if (data.first_name) {
        this.$set(
          this.attandanceForm.customers[index],
          "first_name",
          data.first_name
        );
      }
      if (data.customer_id) {
        this.$set(
          this.attandanceForm.customers[index],
          "customer_id",
          data.customer_id
        );
      }
      if (data.image_path) {
        this.$set(
          this.attandanceForm.customers[index],
          "image_path",
          data.image_path
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "image_path", null);
      }
      this.$forceUpdate();
      this.$emit("setAttendanceData", this.attandanceForm);
    },
    removeCustomer(index) {
      this.attandanceForm.customers.splice(index, 1);
      this.bookedCapacity = this.bookedCapacity - 1;
      this.capacityStatus();
    },
    capacityStatus() {
      this.$emit("setAttendanceData", this.attandanceForm);
      if (this.bookedCapacity >= this.totalCapacity) {
        this.addAttandanceBtn = false;
      } else {
        this.addAttandanceBtn = true;
      }
      this.$forceUpdate();
    },
    customerTypeChange(e, index = null) {
      this.$emit("customerTypeChange", index);
    },
    getCompanySales(index) {
      return this.attandanceForm.customers[index].company_id != null &&
        this.companies.length
        ? this.companies.find(
            (item) => item.id == this.attandanceForm.customers[index].company_id
          ).company_sale
        : [];
    },
    verifyBenefitParticipants(type, index = null) {
      this.$emit("verifyBenefitParticipants", type, index);
    },
    clearBenefitParticipant(index = null) {
      this.$emit("clearBenefitParticipant", index);
    },
    setMemberData(data, index = null) {
      this.setCustomerData(index, data);
      this.$set(
        this.attandanceForm.customers[index],
        "card_number",
        data.card_number
      );
      if (data.card_number) {
        this.$set(
          this.attandanceForm.customers[index],
          "customer_type",
          "member"
        );
      }
      this.$emit("setAttendanceData", this.attandanceForm);
      this.verifyBenefitParticipants("membership", index);
    },
    searchMember(mobile, id, first_name, last_name, attCustomerIndex = null) {
      console.log(id, first_name, last_name, mobile);
      this.isSearchLoading = true;
      let query = "";
      query = `field=id&search=${id}`;
      this.$http
        .get(`venues/memberships/members/filters?${query}`)
        .then((response) => {
          if (response.status == 200) {
            let data = response.data.data;
            if (data.length > 0) {
              // console.log("search member", data);
              this.$set(
                this.attandanceForm.customers[attCustomerIndex],
                "customer_type",
                "member"
              );
              this.$set(
                this.attandanceForm.customers[attCustomerIndex],
                "card_number",
                data[0].card_number
              );
              if (
                this.attandanceForm.customers[attCustomerIndex] &&
                this.attandanceForm.customers[attCustomerIndex].products
                  .length > 0
              ) {
                this.verifyBenefitParticipants("membership", attCustomerIndex);
              }
            } else {
              if (this.attandanceForm) {
                if (
                  this.attandanceForm.customers[attCustomerIndex]
                    .customer_type == "member"
                ) {
                  this.$set(
                    this.attandanceForm.customers[attCustomerIndex],
                    "customer_type",
                    "normal"
                  );
                }
                this.clearCardAndBenefits(attCustomerIndex);
              }
            }
            // this.$emit("setAttendanceData", this.attandanceForm);
            this.$forceUpdate();
          } else {
            this.clearCardAndBenefits(attCustomerIndex);
          }
        })
        .catch((error) => {
          this.errorChecker(error);
          this.clearCardAndBenefits(attCustomerIndex);
        });
    },
    clearCardAndBenefits(index) {
      this.$emit("clearCardAndBenefits", index);
    },
    confirmImageCapture(image) {
      image.name = this.attandanceForm.customers[this.currentCustomerIndex].name
        ? this.attandanceForm.customers[this.currentCustomerIndex].name +
          "_" +
          moment().format("YYYYMMDDHHSS")
        : "user_image_" + moment().format("YYYYMMDDHHSS");
      this.attandanceForm.customers[this.currentCustomerIndex].profile_image =
        image;
      this.webcamDialog = false;
    },
    setCustomerProduct(index, products) {
      if (this.attandanceForm.customers[index].promotion_code != null) {
        this.$emit("verifyBenefitParticipants", "promotion", index);
      }
      if (this.attandanceForm.customers[index].card_number != null) {
        this.$emit("verifyBenefitParticipants", "membership", index);
      }
      this.attandanceForm.customers[index].products = products;
      this.attandanceForm.customers[index].total_price = products.reduce(
        (a, b) => a + parseFloat(b.total_price),
        0
      );
      this.$emit("setAttendanceData", this.attandanceForm);
    },
  },
};
</script>
<style></style>
<!--
 <capacity-based-booking
                  v-if="bookingForm.attendance && !order_id"
                  :repeatId="repeatId"
                  :venueServiceId="venue_service_id"
                  :totalAttendies="attendies"
                  :totalCapacity="capacity"
                  :totalBookedCapacity="bookedCapacity"
                  :attendanceCustomers="attendanceCustomers"
                  :countries="countries"
                  :idProofTypes="idProofTypes"
                  :editFlag="editFlag"
                  :pastTime="pastTime"
                  :categoriesList="categoriesList"
                  :categories="categories"
                  :companies="companies"
                  :taxTypes="taxTypes"
                  :promotions="promotions"
                  :disablePromotion="disablePromotion"
                  @customerTypeChange="(data) => customerTypeChange(null, data)"
                  @setAttendanceData="setAttendanceData"
                  @verifyBenefitParticipants="verifyBenefitParticipants"
                  @clearBenefitParticipant="clearBenefitParticipant"
                  @clearCardAndBenefits="clearCardAndBenefits"
                ></capacity-based-booking> -->
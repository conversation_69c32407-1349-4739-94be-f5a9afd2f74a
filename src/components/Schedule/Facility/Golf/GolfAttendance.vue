<template>
  <v-card
    color="#edf9ff"
    style="border: 1px #ccc solid"
    class="pa-5 mt-5 mb-6"
    outlined
  >
    <template>
      <v-row
        v-for="(attendanceCustomer, index) in attandanceForm.customers"
        :key="index"
        class="mt-0.5"
        style="position: relative"
      >
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              v-on="on"
              fab
              x-small
              absolute
              top
              right
              @click="removeCustomer(index)"
            >
              <v-icon color="red">mdi-delete</v-icon>
            </v-btn>
          </template>
          Delete
        </v-tooltip>
        <v-col sm="3" md="4">
          <div>
            <v-mobile-search
              label="Mobile No*"
              outlined
              :selected="attendanceCustomer.mobile"
              v-model="attendanceCustomer.search"
              @updateCustomer="setCustomerData(index, $event)"
            ></v-mobile-search>
          </div>
        </v-col>

        <v-col sm="3" md="4">
          <div>
            <v-name-search
              label="Name*"
              outlined
              :mobile="attendanceCustomer.mobile"
              :email="attendanceCustomer.email"
              :selected="attendanceCustomer.name"
              v-model="attendanceCustomer.nameSearch"
              @updateCustomer="setCustomerData(index, $event)"
              required
            ></v-name-search>
          </div>
        </v-col>

        <v-col sm="3" md="4">
          <div>
            <v-text-field
              outlined
              label="Email*"
              background-color="#fff"
              v-model="attendanceCustomer.email"
              :rules="[
                (v) => !!v || 'E-mail is required',
                (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
              ]"
              @updateCustomer="setCustomerData(index, $event)"
            ></v-text-field>
          </div>
        </v-col>
        <v-col sm="12" md="12">
          <div class="titles d-flex justify-space-between">
            <div class="titles">Product Details</div>
          </div>
          <v-row>
            <v-divider></v-divider>
            <v-col md="12">
              <v-chip
                label
                color="cyan"
                dark
                class="ml-2 mb-2"
                v-for="(product, index) in bookingForm.products"
                :key="index"
                :close="repeatId && product.rental ? false : true"
                @click:close="removeProduct(index)"
              >
                <v-avatar left>
                  <view-image :image="product.image_path"></view-image>
                </v-avatar>
                {{ product.name }} x
                {{ product.quantity | numberFormatter(3) }}
                -
                {{
                  (product.discount != null && product.discount
                    ? product.total
                    : product.total_price) | toCurrency
                }}
                <span
                  v-if="product.discount != null"
                  class="text-decoration-line-through pl-1"
                >
                  {{ product.discount.actual_total | toCurrency }}</span
                >
              </v-chip>
            </v-col>
            <v-col :md="productCategoryId == -1 ? 2 : 3">
              <v-autocomplete
                :items="categoriesList"
                v-model="productCategoryId"
                label="Select Category"
                required
                item-value="id"
                item-text="name"
                @change="categoryChange"
                outlined
                background-color="#fff"
              ></v-autocomplete>
              <!-- { name: 'Open Item', id: -1 }, -->
            </v-col>
            <v-col md="3" v-if="productCategoryId == -1">
              <v-text-field
                v-model="selectedProduct.title"
                label="Product name"
                required
                outlined
                background-color="#fff"
              >
                <template v-slot:append>
                  <v-menu
                    top
                    nudge-bottom="105"
                    nudge-left="16"
                    :close-on-content-click="true"
                  >
                    <template v-slot:activator="{ on }">
                      <div v-on="on" class="d-flex align-center open-product">
                        {{ selectedProduct.rental == true ? "Base" : "Addon" }}
                        <v-icon small color="#fff">mdi-chevron-down</v-icon>
                      </div>
                    </template>
                    <v-card>
                      <v-card-text>
                        <v-radio-group v-model="selectedProduct.rental" column>
                          <v-radio
                            :disabled="repeatId"
                            label="Base Product"
                            :value="true"
                          ></v-radio>
                          <v-radio
                            label="Addon Product"
                            :value="false"
                          ></v-radio>
                        </v-radio-group>
                      </v-card-text>
                    </v-card>
                  </v-menu>
                </template>
              </v-text-field>
            </v-col>
            <v-col md="4" v-else>
              <v-autocomplete
                v-model="selectedProduct"
                label="Select Product"
                required
                return-object
                :items="getProducts()"
                item-value="id"
                item-text="name"
                outlined
                background-color="#fff"
              ></v-autocomplete>
            </v-col>
            <v-col :md="productCategoryId == -1 ? 1 : 2">
              <v-text-field
                label="Quantity"
                outlined
                background-color="#fff"
                type="number"
                min="1"
                v-model="selectedProduct.quantity"
              ></v-text-field>
            </v-col>
            <v-col md="1" v-if="productCategoryId == -1">
              <v-select
                label="Tax*"
                v-model="selectedProduct.tax_type_id"
                item-value="value"
                item-text="text"
                hint="Required tax"
                :menu-props="{ bottom: true, offsetY: true }"
                :items="taxTypes"
                outlined
                @change="taxChange()"
                background-color="#fff"
              ></v-select>
            </v-col>
            <v-col md="2" v-if="productCategoryId == -1">
              <v-text-field
                label="Price (Pre Tax)*"
                outlined
                rows="2"
                :prefix="currencyCode"
                background-color="#fff"
                required
                v-model="selectedProduct.price"
                @change="calculateTaxVariation($event, 'pre')"
              ></v-text-field>
            </v-col>
            <v-col md="2" v-if="productCategoryId == -1">
              <v-text-field
                label="Price (Post Tax)*"
                outlined
                rows="2"
                :prefix="currencyCode"
                background-color="#fff"
                required
                @change="calculateTaxVariation($event, 'post')"
                v-model="selectedProduct.total_price"
              ></v-text-field>
            </v-col>
            <v-col md="2" v-else>
              <v-text-field
                label="Price"
                :readonly="productCategoryId != -1"
                outlined
                background-color="#fff"
                v-model="selectedProduct.price"
                :suffix="currencyCode"
              ></v-text-field>
            </v-col>
            <v-col md="1">
              <v-btn
                class="white--text blue-color"
                height="56"
                block
                @click="addProduct"
                >Add</v-btn
              >
            </v-col>
          </v-row>
        </v-col>
      </v-row>

      <v-row>
        <v-col sm="5" md="12" v-if="addAttandanceBtn">
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="blue-color"
                  fab
                  x-small
                  dark
                  @click="addAttandance"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add
            </v-tooltip>
          </div>
        </v-col>
      </v-row>
    </template>
  </v-card>
</template>

<script>
export default {
  props: {
    totalBookedCapacity: { type: Number, default: 0 },
    totalAttendies: { type: Number },
    totalCapacity: { type: Number },
    attendanceCustomers: { type: Array },
    editFlag: { type: Boolean, default: false },
    pastTime: { type: Boolean, default: false },
    bookingForm: { type: Object },
    categoriesList: { type: Array },
  },
  data() {
    return {
      //attandanceForm: { customers: [{}] },
      attendanceCustomer: null,
      addAttandanceBtn: true,
      bookedCapacity: 2,
      selectedProduct: {},
      productCategoryId: null,
      categories: [],
      repeatId: null,
    };
  },
  mounted() {
    if (this.editFlag == true) {
      this.bookedCapacity = this.totalBookedCapacity;
      if (this.attendanceCustomers != "") {
        this.attandanceForm.customers = this.attendanceCustomers;
        this.$forceUpdate();
      } else {
        this.attandanceForm.customers = [{}];
        this.bookedCapacity = this.bookedCapacity + 1;
      }
    } else {
      this.bookedCapacity = this.totalBookedCapacity + 2;
    }
    if (this.$store.getters.getTaxTypes.status == false) {
      this.$store.dispatch("loadTaxTypes");
    }
    this.capacityStatus();
  },
  computed: {
    attandanceForm() {
      if (this.attendanceCustomers != "") {
        return { customers: this.attendanceCustomers };
      } else {
        return { customers: [{}] };
      }
    },
    taxTypes() {
      return this.$store.getters.getTaxTypes.data;
    },
  },
  methods: {
    addAttandance() {
      this.attandanceForm.customers.push({});
      this.bookedCapacity = this.bookedCapacity + 1;
      this.capacityStatus();
    },
    setCustomerData(index, data) {
      if (!data.customer_id) {
        this.$set(this.attandanceForm.customers[index], "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(
          this.attandanceForm.customers[index],
          "name",
          data.first_name
        );
      }

      if (
        this.attandanceForm.customers[index].customer_id &&
        !data.customer_id &&
        this.attandanceForm.customers[index].name != data.name &&
        this.attandanceForm.customers[index].mobile != data.mobile
      ) {
        this.$set(this.attandanceForm.customers[index], "mobile", null);
        this.attandanceForm.customers[index].search = null;
        this.attandanceForm.customers[index].nameSearch = null;
        this.$set(this.attandanceForm.customers[index], "email", null);
        this.$set(this.attandanceForm.customers[index], "gender", null);
        this.$set(this.attandanceForm.customers[index], "name", null);
        this.$set(this.attandanceForm.customers[index], "customer_id", null);
        this.$set(this.attandanceForm.customers[index], "first_name", null);
        this.$set(this.attandanceForm.customers[index], "image_path", null);
        this.$set(this.attandanceForm.customers[index], "dob", null);
        this.$set(this.attandanceForm.customers[index], "country_id", null);
        this.$set(this.attandanceForm.customers[index], "last_name", null);
        this.$forceUpdate();
      }

      if (data.mobile)
        this.$set(this.attandanceForm.customers[index], "mobile", data.mobile);
      if (data.email)
        this.$set(this.attandanceForm.customers[index], "email", data.email);
      if (data.country_id) {
        this.$set(
          this.attandanceForm.customers[index],
          "country_id",
          data.country_id
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "country_id", null);
      }
      if (data.gender) {
        this.$set(this.attandanceForm.customers[index], "gender", data.gender);
      } else {
        this.$set(this.attandanceForm.customers[index], "gender", null);
      }
      if (data.dob) {
        this.$set(this.attandanceForm.customers[index], "dob", data.dob);
      } else {
        this.$set(this.attandanceForm.customers[index], "dob", null);
      }
      if (data.name) {
        this.$set(this.attandanceForm.customers[index], "name", data.name);
        this.$set(this.attandanceForm.customers[index], "nameSearch", {
          name: data.name,
        });
      }
      if (data.last_name) {
        this.$set(
          this.attandanceForm.customers[index],
          "last_name",
          data.last_name
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "last_name", null);
      }
      if (data.first_name)
        this.$set(
          this.attandanceForm.customers[index],
          "first_name",
          data.first_name
        );
      if (data.customer_id)
        this.$set(
          this.attandanceForm.customers[index],
          "customer_id",
          data.customer_id
        );
      if (data.image_path) {
        this.$set(
          this.attandanceForm.customers[index],
          "image_path",
          data.image_path
        );
      } else {
        this.$set(this.attandanceForm.customers[index], "image_path", null);
      }
      this.$forceUpdate();
      this.$emit("setAttendanceData", this.attandanceForm);
    },
    removeCustomer(index) {
      this.attandanceForm.customers.splice(index, 1);
      this.bookedCapacity = this.bookedCapacity - 1;
      this.capacityStatus();
    },
    capacityStatus() {
      this.$emit("setAttendanceData", this.attandanceForm);
      if (this.bookedCapacity >= this.totalCapacity) {
        this.addAttandanceBtn = false;
      } else {
        this.addAttandanceBtn = true;
      }
      this.$forceUpdate();
    },
    categoryChange(e) {
      if (e == -1) {
        this.selectedProduct.rental = false;
        this.selectedProduct.product_type_id = 6;
      }
    },
    getProducts() {
      if (this.productCategoryId != null) {
        return this.categories.find((item) => item.id == this.productCategoryId)
          .products;
      }
      if (this.productCategoryId == null) {
        let products = [];
        this.categories.forEach((category) => {
          category.products.forEach((product) => {
            product.category_id = category.id;
            products.push(product);
          });
        });
        return products;
      }
      return [];
    },
    addProduct() {
      let quantity = this.selectedProduct.quantity
        ? this.selectedProduct.quantity
        : 1;
      if (
        this.selectedProduct.id == null &&
        this.selectedProduct.title == null
      ) {
        this.showError("Please add product");
        return;
      }

      let price = parseFloat(this.selectedProduct.price) * parseFloat(quantity);

      if (this.selectedProduct.title != null) {
        if (this.selectedProduct.tax_type_id == null) {
          this.showError("Please select tax");
          return;
        }
        if (this.selectedProduct.price == null) {
          this.showError("Please add price");
          return;
        }

        this.selectedProduct.id = null;
        this.selectedProduct.name = this.selectedProduct.title;
        if (this.selectedProduct.tax_type_id == 1) {
          this.selectedProduct.tax_amount =
            this.selectedProduct.total_price - this.selectedProduct.price;
        }
      }

      let pIndex = this.bookingForm.products.findIndex((item) =>
        item.product_id ? item.product_id == this.selectedProduct.id : ""
      );

      if (pIndex == -1) {
        var obj = {
          product_id: this.selectedProduct.id ? this.selectedProduct.id : 0,
          price: price,
          name: this.selectedProduct.name,
          tax:
            (this.selectedProduct.tax_amount
              ? this.selectedProduct.tax_amount
              : 0) * quantity,
          quantity: parseFloat(quantity),
          product_type_id: this.selectedProduct.product_type_id,
          venue_service_id: this.venue_service_id,
          category_id: this.selectedProduct.category_id,
          rental: this.selectedProduct.rental == true ? true : false,
          total_price:
            price +
            parseFloat(
              this.selectedProduct.tax_amount
                ? this.selectedProduct.tax_amount
                : 0
            ) *
              quantity,
        };
        this.bookingForm.products.push(obj);
        if (this.repeatId) {
          this.currentOrderProducts.push(obj);
        }
      } else {
        let newQuantity =
          parseFloat(this.bookingForm.products[pIndex].quantity) +
          parseFloat(quantity);
        this.bookingForm.products[pIndex].quantity = newQuantity;
        this.bookingForm.products[pIndex].price =
          this.selectedProduct.price * newQuantity;
        this.bookingForm.products[pIndex].total_price =
          this.bookingForm.products[pIndex].price +
          this.bookingForm.products[pIndex].tax * newQuantity;
      }

      this.bookingForm.price += price;
      this.bookingForm.total_price = this.bookingForm.products.reduce(
        (a, b) => a + parseFloat(b.total_price),
        0
      );

      this.selectedProduct = {};
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit("promotion");
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit("membership");
      }
      this.$forceUpdate();
    },
  },
};
</script>

<style></style>

<template>
  <v-row>
    <v-col sm="12" md="12">
      <v-card
        color="#edf9ff"
        style="border: 1px #ccc solid"
        class="pa-4 mt-2"
        outlined
      >
        <div>
          <div class="titles">Product Details</div>
        </div>
        <v-row>
          <v-col md="12">
            <v-chip
              label
              color="cyan"
              dark
              class="ml-2 mb-2"
              v-for="(product, index) in products"
              :key="index"
              :close="repeatId && product.rental ? false : true"
              @click:close="removeProduct(index)"
            >
              <v-avatar left>
                <view-image :image="product.image_path"></view-image>
              </v-avatar>
              {{ product.name }} x
              {{ product.quantity | numberFormatter(3) }}
              -
              {{
                (product.discount != null && product.discount
                  ? product.total
                  : product.total_price) | toCurrency
              }}
              <span
                v-if="product.discount != null"
                class="text-decoration-line-through pl-1"
              >
                {{ product.discount.actual_total | toCurrency }}</span
              >
            </v-chip>
          </v-col>
          <v-col :md="productCategoryId == -1 ? 2 : 3">
            <v-autocomplete
              :items="categoriesList"
              v-model="productCategoryId"
              label="Select Category"
              required
              item-value="id"
              item-text="name"
              @change="categoryChangeCustomer($event)"
              outlined
              background-color="#fff"
            >
            </v-autocomplete>
            <!-- { name: 'Open Item', id: -1 }, -->
          </v-col>
          <v-col md="3" v-if="productCategoryId == -1">
            <v-text-field
              v-model="selectedProduct.title"
              label="Product name"
              required
              outlined
              background-color="#fff"
            >
              <template v-slot:append>
                <v-menu
                  top
                  nudge-bottom="105"
                  nudge-left="16"
                  :close-on-content-click="true"
                >
                  <template v-slot:activator="{ on }">
                    <div v-on="on" class="d-flex align-center open-product">
                      {{ selectedProduct.rental == true ? "Base" : "Addon" }}
                      <v-icon small color="#fff">mdi-chevron-down</v-icon>
                    </div>
                  </template>
                  <v-card>
                    <v-card-text>
                      <v-radio-group v-model="selectedProduct.rental" column>
                        <v-radio
                          :disabled="repeatId"
                          label="Base Product"
                          :value="true"
                        ></v-radio>
                        <v-radio label="Addon Product" :value="false"></v-radio>
                      </v-radio-group>
                    </v-card-text>
                  </v-card>
                </v-menu>
              </template>
            </v-text-field>
          </v-col>
          <v-col md="4" v-else>
            <v-autocomplete
              v-model="selectedProduct"
              label="Select Product"
              required
              return-object
              :items="getProductsCustomer()"
              item-value="id"
              item-text="name"
              outlined
              background-color="#fff"
            ></v-autocomplete>
          </v-col>
          <v-col :md="productCategoryId == -1 ? 1 : 2">
            <v-text-field
              label="Quantity"
              outlined
              background-color="#fff"
              type="number"
              min="1"
              v-model="selectedProduct.quantity"
            ></v-text-field>
          </v-col>
          <v-col md="1" v-if="productCategoryId == -1">
            <v-select
              label="Tax*"
              v-model="selectedProduct.tax_type_id"
              item-value="value"
              item-text="text"
              hint="Required tax"
              :menu-props="{ bottom: true, offsetY: true }"
              :items="taxTypes"
              outlined
              @change="taxChange(selectedProduct)"
              background-color="#fff"
            ></v-select>
          </v-col>
          <v-col md="2" v-if="productCategoryId == -1">
            <v-text-field
              label="Price (Pre Tax)*"
              outlined
              rows="2"
              :prefix="currencyCode"
              background-color="#fff"
              required
              v-model="price"
              @change="calculateTaxVariation($event, 'pre', selectedProduct)"
            ></v-text-field>
          </v-col>
          <v-col md="2" v-if="productCategoryId == -1">
            <v-text-field
              label="Price (Post Tax)*"
              outlined
              rows="2"
              :prefix="currencyCode"
              background-color="#fff"
              required
              @change="calculateTaxVariation($event, 'post', selectedProduct)"
              v-model="selectedProduct.total_price"
            ></v-text-field>
          </v-col>
          <v-col md="2" v-else>
            <v-text-field
              label="Price"
              :readonly="productCategoryId != -1"
              outlined
              background-color="#fff"
              v-model="selectedProduct.price"
              :suffix="currencyCode"
            >
            </v-text-field>
          </v-col>
          <v-col md="1">
            <v-btn
              class="white--text blue-color"
              height="56"
              block
              @click="addProductCustomer(selectedProduct)"
              >Add</v-btn
            >
          </v-col>
        </v-row>
      </v-card>
    </v-col>
  </v-row>
</template>
<script>
export default {
  name: "ProductSelection",
  props: {
    venueServiceId: { type: Number, default: 0 },
    categories: { type: Array },
    categoriesList: { type: Array },
    promotions: { type: Array },
    disablePromotion: { type: Boolean, default: false },
    products: { type: Array },
    productCatId: { type: Number, default: null },
    repeatId: { type: Number, default: null },
    taxTypes: { type: Array },
  },
  data() {
    return {
      selectedProduct: { quantity: 1, price: 0 },
      customer: null,
      productCategoryId: null,
      price: 0,
      total_price: 0,
    };
  },
  watch: {},
  mounted() {},

  methods: {
    getProductsCustomer() {
      if (this.productCategoryId != null) {
        return this.categories.find((item) => item.id == this.productCategoryId)
          .products;
      }
      if (this.productCategoryId == null) {
        let products = [];
        this.categories.forEach((category) => {
          category.products.forEach((product) => {
            product.category_id = category.id;
            products.push(product);
          });
        });
        return products;
      }
      return [];
    },
    categoryChangeCustomer(e) {
      if (e == -1) {
        this.selectedProduct.rental = false;
        this.selectedProduct.product_type_id = 6;
      }
    },
    addProductCustomer(selectedProduct) {
      // let quantity = this.quantity ? this.quantity : 1
      let quantity =
        this.selectedProduct.quantity && this.selectedProduct.quantity > 0
          ? this.selectedProduct.quantity
          : 1;

      if (this.selectedProduct.inventory_enable) {
        let sales = this.selectedProduct.sales ? this.selectedProduct.sales : 0;
        let totalQty = sales + parseFloat(quantity);
        parseFloat(this.selectedProduct.sales) + parseFloat(quantity) >=
          parseFloat(this.selectedProduct.actual_qty);
        if (parseFloat(this.selectedProduct.actual_qty) < totalQty) {
          if (
            this.selectedProduct.actual_qty - this.selectedProduct.sales ==
            0
          ) {
            this.showError("Product not available!");
          } else {
            this.showError(
              `Only ${
                this.selectedProduct.actual_qty - this.selectedProduct.sales
              } product available!`
            );
          }

          return;
        }
      }
      if (selectedProduct.id == null && selectedProduct.title == null) {
        this.showError("Please add product");
        return;
      }

      if (this.selectedProduct.inventory_enable) {
        this.selectedProduct.sales += parseFloat(quantity);
      }
      let price = parseFloat(selectedProduct.price) * parseFloat(quantity);

      if (selectedProduct.title != null) {
        if (selectedProduct.tax_type_id == null) {
          this.showError("Please select tax");
          return;
        }
        if (selectedProduct.price == null) {
          this.showError("Please add price");
          return;
        }

        selectedProduct.id = null;
        selectedProduct.name = selectedProduct.title;

        if (selectedProduct.tax_type_id == 1) {
          selectedProduct.tax_amount =
            selectedProduct.total_price - selectedProduct.price;
        }
      }

      let pIndex = this.products.findIndex((item) =>
        item.product_id ? item.product_id == selectedProduct.id : ""
      );
      if (pIndex == -1) {
        var obj = {
          product_id: selectedProduct.id ? selectedProduct.id : 0,
          price: price,
          name: selectedProduct.name,
          tax:
            (selectedProduct.tax_amount ? selectedProduct.tax_amount : 0) *
            quantity,
          quantity: parseFloat(quantity),
          product_type_id: selectedProduct.product_type_id,
          inventory_enable: selectedProduct.inventory_enable,
          venue_service_id: this.venueServiceId,
          category_id: selectedProduct.category_id,
          rental: selectedProduct.rental == true ? true : false,
          total_price:
            price +
            parseFloat(
              selectedProduct.tax_amount ? selectedProduct.tax_amount : 0
            ) *
              quantity,
        };
        this.products.push(obj);
        this.$emit("setCustomerProduct", this.products);
      } else {
        let newQuantity =
          parseFloat(this.products[pIndex].quantity) + parseFloat(quantity);
        this.products[pIndex].quantity = newQuantity;
        this.products[pIndex].price = selectedProduct.price * newQuantity;
        this.products[pIndex].total_price =
          this.products[pIndex].price + this.products[pIndex].tax * newQuantity;
      }
      this.$emit("setCustomerProduct", this.products);
      this.selectedProduct = {};
      this.$forceUpdate();
    },

    taxChange(attendanceCustomerSelectedProduct = null) {
      if (attendanceCustomerSelectedProduct.price) {
        this.calculateTaxVariation(
          attendanceCustomerSelectedProduct.price,
          "pre",
          attendanceCustomerSelectedProduct
        );
      } else if (attendanceCustomerSelectedProduct.total_price) {
        this.calculateTaxVariation(
          attendanceCustomerSelectedProduct.total_price,
          "post",
          attendanceCustomerSelectedProduct
        );
      }
    },
    calculateTaxVariation(
      amount,
      type,
      attendanceCustomerSelectedProduct = null
    ) {
      if (attendanceCustomerSelectedProduct === null) {
        let taxTypeId = this.selectedProduct.tax_type_id;
        let taxPercentage = 0;
        if (taxTypeId) {
          taxPercentage = this.taxTypes.find(
            (item) => item.value == taxTypeId
          ).percentage;
        }
        const priceData = this.getTaxVariation(type, taxPercentage, amount);
        this.selectedProduct.price = priceData.price;
        this.selectedProduct.total_price = priceData.total_price;
      } else {
        let taxTypeId = attendanceCustomerSelectedProduct.tax_type_id;
        let taxPercentage = 0;
        if (taxTypeId) {
          taxPercentage = this.taxTypes.find(
            (item) => item.value == taxTypeId
          ).percentage;
        }
        const priceData = this.getTaxVariation(type, taxPercentage, amount);
        attendanceCustomerSelectedProduct.price = priceData.price;
        attendanceCustomerSelectedProduct.total_price = priceData.total_price;
      }
      this.$forceUpdate();
    },
    removeProduct(productIndex) {
      this.products.splice(productIndex, 1);
      this.$emit("setCustomerProduct", this.products);
    },
  },
};
</script>
<template>
  <v-col cols="12" :md="col.md" v-if="fieldConfig.is_visible">
    <!-- Label -->
    <label class="field-label">
      {{ fieldConfig.name }}<span v-if="fieldConfig.is_required" class="required">*</span>
    </label>

    <!-- Text Input -->
    <v-text-field
        v-if="fieldConfig.type === 'text_box'"
        v-model="localValue"
        :rules="validationRules"
        background-color="#fff"
        class="q-text-field shadow-0 text_field1"
        dense
        outlined
        validate-on-blur
        hide-details="auto"
        clearable
    />

    <!-- Textarea -->
    <v-textarea
        v-else-if="fieldConfig.type === 'text_area'"
        v-model="localValue"
        :rules="validationRules"
        class="q-text-field shadow-0 text_field1"
        dense
        rows="3"
        outlined
        validate-on-blur
        hide-details="auto"
    />

    <!-- Checkboxes -->
    <div v-else-if="fieldConfig.type === 'check_boxes'" class="d-flex gap-x-2">
      <v-checkbox
          v-for="option in fieldConfig.options"
          :key="option.value"
          :label="option.value"
          :value="option.value"
          v-model="selectedOptions"
          multiple
          hide-details
          @change="handleCheckboxChange"
      />
    </div>

    <!-- Dropdown -->
    <v-select
        v-else-if="fieldConfig.type === 'drop_down'"
        v-model="localValue"
        :rules="validationRules"
        :items="fieldConfig.options"
        :multiple="fieldConfig.multiple"
        item-text="value"
        item-value="value"
        background-color="#fff"
        class="q-autocomplete shadow-0"
        dense
        outlined
        validate-on-blur
        hide-details="auto"
    />

    <!-- Radio Buttons -->
    <v-radio-group
        v-else-if="fieldConfig.type === 'radio_buttons'"
        v-model="localValue"
        :rules="validationRules"
        row
        class="d-flex gap-x-2"
    >
      <v-radio
          class="r_btn"
          v-for="option in fieldConfig.options"
          :key="option.value"
          :label="option.value"
          :value="option.value"
      />
    </v-radio-group>

    <date-input-field
        v-else-if="fieldConfig.type === 'date'"
        v-model="localValue"
        :rules="validationRules"
        :dense="true"
        label=""
        background-color="#fff"
        hide-details
        class="q-autocomplete shadow-0"
    />
    <!-- Date Picker -->

    <mobile-number-field
        v-else-if="fieldConfig.type === 'phone_number'"
        v-model="localValue"
        :rules="validationRules"
        :dense="true"
        :outlined="true"
        :variant="1"
        :required="!!fieldConfig.is_required"
    />
    <!-- File Input -->

    <v-file-input
        v-else-if="fieldConfig.type === 'file'"
        v-model="safeFileInputValue"
        background-color="#fff"
        class="q-text-field shadow-0"
        dense
        hide-details="auto"
        outlined
        prepend-icon
        prepend-inner-icon="mdi-paperclip"
        :rules="validationRules"
    >
      <template v-slot:prepend-inner v-if="localValue">
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <v-icon
                color="cyan"
                v-if="typeof localValue === 'string'"
                @click="openFile(localValue)"
                v-on="on"
            >
              mdi-download-box
            </v-icon>
            <v-icon v-else v-on="on">mdi-card-account-details</v-icon>
          </template>
          <span v-if="typeof localValue === 'string'">Download file</span>
          <span v-else>Upload File</span>
        </v-tooltip>
      </template>
    </v-file-input>



    <!-- Email Input -->
    <v-text-field
        v-else-if="fieldConfig.type === 'email'"
        v-model="localValue"
        :rules="validationRules"
        :placeholder="`Email${fieldConfig.is_required ? ' *' : ''}`"
        background-color="#fff"
        class="q-text-field shadow-0"
        dense
        label=""
        outlined
        validate-on-blur
        hide-details="auto"
    />
  </v-col>
</template>

<script>
import MobileNumberField from "@/components/Fields/MobileNumberField.vue";
import DateInputField from "@/components/Fields/DateInputField.vue";

export default {
  components:{DateInputField, MobileNumberField},
  name: 'DynamicField',
  props: {
    value: {
      type: Object,
      default: () => ({ value: null })
    },
    modelValue: {
      type: Object,
      default: () => ({ value: null })
    },
    fieldConfig: {
      type: Object,
      required: true,
      default: () => ({
        id: null,
        slug: '',
        is_required: false,
        is_visible: true,
        type: null,
        name: '',
        options: [],
        multiple: false
      })
    },
    col:{
      md:6,
      sm:12
    }
  },
  emits: ['input', 'update:modelValue'],
  data() {
    return {
      selectedOptions: []
    };
  },
  computed: {
    localValue: {
      get() {
        if (this.fieldConfig.type === 'check_boxes') {
          return this.selectedOptions;
        }
        return this.modelValue?.value ?? this.value.value;
      },
      set(val) {
        if (this.fieldConfig.type === 'check_boxes') {
          this.selectedOptions = Array.isArray(val) ? [...val] : [];
          this.emitUpdate();
        } else {
          this.emitUpdate(val);
        }
      }
    },
    validationRules() {
      const rules = [];
      if (this.fieldConfig.is_required) {
        rules.push(value => {
          if (
              this.fieldConfig.type === 'file' &&
              typeof this.localValue === 'string' &&
              this.localValue !== ''
          ) {
            return true;
          }
          if (Array.isArray(value)) {
            return value.length > 0 || `${this.fieldConfig.name} is required`;
          }
          return (!!value || value === 0) || `${this.fieldConfig.name} is required`;
        });
      }
      if (this.fieldConfig.type === 'email') {
        rules.push(v => !v || /.+@.+\..+/.test(v) || 'E-mail must be valid');
      }
      return rules;
    },

    safeFileInputValue: {
      get() {
        return typeof this.localValue === 'string' ? null : this.localValue;
      },
      set(val) {
        this.localValue = val;
      }
    }
  },
  watch: {
    value(newVal) {
      if (this.fieldConfig.type === 'check_boxes') {
        this.selectedOptions = Array.isArray(newVal?.value) ? [...newVal.value] : [];
      }
    },
    modelValue(newVal) {
      if (this.fieldConfig.type === 'check_boxes') {
        this.selectedOptions = Array.isArray(newVal?.value) ? [...newVal.value] : [];
      }
    }
  },
  mounted() {
    this.initSelectedOptions();
  },
  methods: {
    emitUpdate(newValue = this.selectedOptions) {
      const payload = this.fieldConfig;
      payload.value = newValue;
      this.$emit('input', payload);
      this.$emit('update:modelValue', payload);
    },
    initSelectedOptions() {
      if (this.fieldConfig.type === 'check_boxes') {
        const initial = this.modelValue?.value || this.value?.value || [];
        this.selectedOptions = Array.isArray(initial) ? [...initial] : [];
      }
    },
    handleCheckboxChange() {
      this.emitUpdate();
    },
  }
};
</script>

<style scoped lang="scss">
.field-label {
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.required {
  color: red;
  margin-left: 2px;
}


::v-deep .r_btn .v-label {
  white-space: normal !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
}
</style>

<template>
  <v-container class="pa-2" fluid>
    <div class="d-flex justify-space-between align-center mb-2">
      <p class="mb-0 text-black text-xl font-semibold">
        {{ headingLabel }}
      </p>

      <div class="d-flex gap-x-5">


        <v-select
            v-model="perPage"
            :items="[5,10, 15, 25, 50,100,200]"
            :menu-props="{ bottom: true, offsetY: true }"
            class="q-autocomplete shadow-0  mt-2"
            hide-details
            outlined
            @change="getFilterData"
            style="max-width: 120px"
            placeholder="Per Page"
            dense
        ></v-select>

        <v-select
            v-model="status_id"
            :items="statusItems"
            :menu-props="{ bottom: true, offsetY: true }"
            class="q-autocomplete shadow-0  mt-2"
            hide-details
            item-text="name"
            item-value="value"
            outlined
            label="Status"
            @change="getFilterData"
            style="max-width: 150px"
            placeholder="Per Page"
            dense
        ></v-select>
        <v-btn
            class="export-button mt-2"
            elevation="0"
            height="40"
            v-if="checkWritePermission($modules.approvals.management.slug) && this.selectedBookings.length > 0 && status_id == 0"
            @click="bulkApprove"
        >
          <SvgIcon text="Approve selected" >
            <template v-slot:icon>
              <AcceptMultipleIcon/>
            </template>
          </SvgIcon>
        </v-btn>
        <v-btn
            class="export-button mt-2"
            elevation="0"
            height="40"
            v-if="checkDeletePermission($modules.approvals.management.slug) && this.selectedBookings.length > 0 && status_id == 0"
            @click="bulkReject"
        >
          <SvgIcon text="Reject selected" >
            <template v-slot:icon>
              <RejectMultipleIcon/>
            </template>
          </SvgIcon>
        </v-btn>
        <v-btn
            class="export-button mt-2"
            elevation="0"
            height="40"
            v-if="checkWritePermission($modules.approvals.management.slug) && this.selectedBookings.length == 0 && bookingsList.length > 0 && status_id == 0"
            @click="bulkApproveAll"
        >
          <SvgIcon text="Approve All" >
            <template v-slot:icon>
              <AcceptAllIcon/>
            </template>
          </SvgIcon>
        </v-btn>
        <v-btn
            class="export-button mt-2"
            elevation="0"
            height="40"
            v-if="checkDeletePermission($modules.approvals.management.slug) && this.selectedBookings.length == 0 && bookingsList.length > 0 && status_id == 0"
            @click="bulkRejectAllConfirmation"
        >
          <SvgIcon text="Reject All" >
            <template v-slot:icon>
              <RejectAllIcon/>
            </template>
          </SvgIcon>
        </v-btn>
      </div>
    </div>
    <div class="table-responsive">
      <table class="table border-collapse ">
        <thead >
        <tr class="opacity-70 tr-neon tr-rounded ">
          <th>
            <div class="">Select</div>
          </th><th>
          <div class="" style="max-width: 10px">Timestamp</div>
        </th>
          <th>
            <div class="">Due Date</div>
          </th>
          <th>
            <div class="">Membership</div>
            <v-text-field
                v-model="searchParams.search_by_membership"
                append-inner-icon="mdi-magnify"
                @keyup.enter="searchData"
                @click:append="searchData"
                clearable
                @click:clear="clearSearch('search_by_membership')"
                outlined
                background-color="#fff"
                required
                hide-details
                dense
                class="q-autocomplete shadow-0 mt-2"
            ></v-text-field>
          </th>
          <th>
            <div class="">Name</div>
            <v-text-field
                v-model="searchParams.search_by_name"
                append-inner-icon="mdi-magnify"
                @keyup.enter="searchData"
                @click:append="searchData"
                clearable
                @click:clear="clearSearch('search_by_name')"
                outlined
                background-color="#fff"
                required
                hide-details
                dense
                class="q-autocomplete shadow-0 mt-2"
            ></v-text-field>
          </th>
          <th>
            Email

            <v-text-field
                v-model="searchParams.search_by_email"
                append-inner-icon="mdi-magnify"
                @keyup.enter="searchData"
                @click:append="searchData"
                clearable
                @click:clear="clearSearch('search_by_email')"
                outlined
                background-color="#fff"
                required
                hide-details
                dense
                class="q-autocomplete shadow-0 mt-2"
            ></v-text-field>
          </th>
          <th>
            Mobile

            <v-text-field
                v-model="searchParams.search_by_mobile"
                append-inner-icon="mdi-magnify"
                @keyup.enter="searchData"
                @click:append="searchData"
                clearable
                @click:clear="clearSearch('search_by_mobile')"
                outlined
                background-color="#fff"
                required
                hide-details
                dense
                class="q-autocomplete shadow-0 mt-2"
            ></v-text-field>
          </th>
          <th>
            Nationality
          </th>
          <th>
            Status
          </th>
          <th v-if="checkWritePermission($modules.approvals.management.slug) || checkDeletePermission($modules.approvals.management.slug)">
            Actions
          </th>
        </tr>
        </thead>

        <tbody  v-if="bookingsList.length > 0">
        <tr
            v-for="booking in bookingsList"
            :key="booking.id"
        >

          <td>
            <input
                :disabled="booking.status_id != 11"
                type="checkbox"
                :value="booking"
                :checked="isSelected(booking)"
                @change="toggleBooking(booking)"
            />
          </td>

          <td>
            <div>
              {{ booking.created_at | timeStamp }}
            </div>
          </td>
          <td>
            <div>
              {{ booking.date | dateformat }}
            </div>
          </td>
          <td>
            <div>
              {{ booking.membership_name }}
            </div>
          </td>
          <td>
            <div>
                <span class="text_ellipse text-neon font-bold  pointer "  @click="showUserModel(booking.customer_id)">
                {{ booking.name }}
                </span>
            </div>
          </td>
          <td>
            <div>
              {{ booking.email }}
            </div>
          </td>
          <td>
            <div>
              {{ booking.mobile }}
            </div>
          </td>
          <td>
            <div>
              {{ booking.nationality }}
            </div>
          </td>
          <td>
            <div v-if="booking.status_id == 2">
              Rejected
            </div>
            <div v-else-if="booking.status_id == 11">
              Pending
            </div>
            <div v-else-if="booking.status_id == 1">
              Approved
            </div>
          </td>
          <td v-if="checkWritePermission($modules.approvals.management.slug) || checkDeletePermission($modules.approvals.management.slug)">
            <v-icon
                v-if="checkWritePermission($modules.approvals.management.slug) && booking.status_id == 11"
                @click="approveBooking(booking.id)"
                class="pl-2"
                color="#66c8c8">
              mdi-check
            </v-icon>
            <v-icon
                v-if="checkDeletePermission($modules.approvals.management.slug) && booking.status_id == 11"
                @click="confirmReject(booking.id)"
                class="pl-2"
                color="#961a04">
              mdi-close
            </v-icon>
          </td>
        </tr>
        </tbody>
        <tbody v-else>
        <tr>
          <td colspan="11" >
            <p class="text-center">No records found</p>
          </td>
        </tr>
      </tbody>
      </table>
    </div>
    <v-row>
      <v-col cols="4"></v-col>
      <v-col cols="4">
        <v-pagination v-model="page" :length="totalPages" class="new-pagination" total-visible="7"></v-pagination>
      </v-col>
      <v-col class="d-flex align-center justify-end" cols="4">
      </v-col>
    </v-row>
    <v-dialog
        v-model="showRejectionModal"
        width="400px"
        scrollable
        @input="closeRejectionModal"
    >
      <v-card>
        <v-card-title class="headline">
          Please add reason for rejection
        </v-card-title
        >
        <v-card-text class="pa-8">
          <v-form
              ref="form"
              v-model="valid"
              @submit.prevent="submitRejectionReason"
              lazy-validation
          >
            <v-select
                hide-details="auto"
                v-model="rejectionReasonType"
                :items="[
                'Conflicting Schedule',
                'Unavailability of Tour Guide',
                'Weather Conditions',
                'Health and Safety Concerns',
                'Private Event Conflicts',
                'Resource Constraints',
                'Cancellation Requested by Customer',
                'Other (Please Specify)',
              ]"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                :rules="[(v) => !!v || 'Rejection Reason is required']"
                class="q-autocomplete shadow-0 mb-2"
                dense
                validate-on-blur
            >
            </v-select>
            <v-textarea
                name="rejection_reason"
                label="Rejection reason"
                v-model="rejectionReason"
                v-if="rejectionReasonType === 'Other (Please Specify)'"
                outlined
                background-color="#fff"
                :rules="[(v) => !!v || 'Rejection reason is required']"
            ></v-textarea>
            <div class="d-flex justify-end">
              <v-btn class="red-color" dark text type="submit"> Reject</v-btn>
            </div>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
    <customer-model v-bind="userModel" @close="userModel.userID = null" />
    <confirm-model
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="confirmModel.id = null"
    ></confirm-model>
  </v-container>
</template>

<script>
import {mapGetters} from "vuex";
import CustomerModel from "@/views/Clients/Customer/CustomerModel.vue";
import AcceptMultipleIcon from "@/assets/images/misc/ApproveMultiple.svg";
import AcceptAllIcon from "@/assets/images/misc/ApproveAll.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import RejectMultipleIcon from "@/assets/images/misc/RejectMultiple.svg";
import RejectAllIcon from "@/assets/images/misc/RejectAll.svg";

export default {
  name: "FacilityApprovals",
  components: {RejectAllIcon, RejectMultipleIcon, SvgIcon, AcceptAllIcon, AcceptMultipleIcon, CustomerModel},
  computed: {
    ...mapGetters({
      checkReadPermission: "checkReadPermission",
    }),
    headingLabel(){
      if(this.status_id >= 0){
        const status =  this.statusItems.find(st => parseInt(st.value) === parseInt(this.status_id))
        if (status){
          return status.name + " Bookings";
        }
      }
      return "Bookings";
    }
  },
  watch: {
    page() {
      this.getPendingBookings();
    },
  },
  data() {
    return {
      status_id:0,
      pending_with:0,
      statusItems:[
        {
          'name': 'Pending',
          'value': 0
        },
        {
          'name': 'Approved',
          'value': 1
        },
        {
          'name': 'Rejected',
          'value': 2
        },
      ],
      valid:false,
      perPage: 10,
      btnShow: false,
      bookingsList: [],
      isLoading: false,
      page: 1,
      searchParams: {
        memberships: [],
        search_by_membership:'',
        search_by_name:'',
        search_by_mobile:'',
        search_by_email:'',
      },
      totalPages: 1,
      userModel: { userID: null, type: "details" },
      showRejectionModal: false,
      rejectionReason: "",
      rejectionReasonType: null,
      venueService: {},
      reject_id:null,
      selectedBookings: [],
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
    };
  },
  mounted() {
    this.getPendingBookings();
  },
  methods:{
    searchData(){
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = setTimeout(() => {
        this.getPendingBookings();
      }, 300);
    },
    clearSearch(name){
      this.searchParams[name] = '';
      this.getPendingBookings();
    },
    confirmActions(data) {
      if (data.type == "reject_multiple") {
        this.bulkHandle(data.id);
      }
      if (data.type == "reject_all") {
        this.bulkRejectAll();
      }
      this.confirmModel.id = null;
    },
    bulkHandle(data){
      this.showLoader("Loading");
      this.$http
          .post(
              `venues/memberships/members/approvals/pending-bookings/multiple`,data
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.showSuccess("Bookings successfully updated.");
              this.getPendingBookings();
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    bulkApprove(){
      let data = {
        toggle_status:1,
        selected_bookings:this.selectedBookings
      }
      this.bulkHandle(data);
    },
    bulkReject(){
      let data = {
        toggle_status:0,
        selected_bookings:this.selectedBookings
      }

      this.confirmModel = {
        id: data,
        title: "Do you want to reject the selected bookings?",
        description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "reject_multiple",
      };

    },
    bulkRejectAllConfirmation(){
      this.confirmModel = {
        id: 0,
        title: "Do you want to reject all bookings?",
        description:
            "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
        type: "reject_all",
      };

    },
    bulkApproveAll(){
      this.showLoader("Loading");
      this.$http
          .post(
              `venues/memberships/members/approvals/pending-bookings/all`,{
                toggle_status:1
              }
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.showSuccess("All Bookings successfully approved.");
              this.getPendingBookings();
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    bulkRejectAll(){
      this.showLoader("Loading");
      this.$http
          .post(
              `venues/memberships/members/approvals/pending-bookings/all`,{
                toggle_status:0
              }
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.showSuccess("All Bookings successfully rejected.");
              this.getPendingBookings();
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    toggleBooking(booking) {
      const index = this.selectedBookings.findIndex(
          (selected) => selected.id === booking.id
      );

      if (index > -1) {
        // If already selected, remove it
        this.selectedBookings.splice(index, 1);
      } else {
        // If not selected, add it
        this.selectedBookings.push({
          id: booking.id,
          order_id: booking.order_id
        });
      }
    },
    isSelected(booking) {
      // Check if the booking is already selected
      return this.selectedBookings.some(
          (selected) => selected.id === booking.id
      );
    },
    getFilterData() {
      this.page = 1;
      this.getPendingBookings();
    },
    confirmReject(id) {
      this.reject_id = id;
      this.showRejectionModal = true;
    },
    closeRejectionModal() {
      this.showRejectionModal = false;
      this.rejectionReason = null;
      this.rejectionReasonType = null;
      this.reject_id = null;
    },
    getPendingBookings(){
      this.selectedBookings = [];
      const searchByName = this.searchParams.search_by_name?this.searchParams.search_by_name:''
      const searchByMobile = this.searchParams.search_by_mobile?this.searchParams.search_by_mobile:''
      const searchByEmail = this.searchParams.search_by_email?this.searchParams.search_by_email:''
      const searchByMembership = this.searchParams.search_by_membership?this.searchParams.search_by_membership:''
      let url = `&booking_approval_status=${this.status_id}`;
      this.$http
          .get(
              `venues/memberships/members/approvals/pending-bookings?page=${this.page}&per_page=${
                  this.perPage != null ? this.perPage : 10
              }${url}`,
              {
                params: {
                  search_by_name: searchByName,
                  search_by_mobile: searchByMobile,
                  search_by_email: searchByEmail,
                  search_by_membership: searchByMembership,
                }
              }
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.bookingsList = response.data.data;
              this.totalPages = response.data.total_pages;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    showUserModel(userId) {
      this.userModel.userID = parseInt(userId);
      this.userModel.type = "details";
    },
    approveBooking(id) {
      this.showLoader("Loading");
      this.$http
          .get(
              `venues/memberships/members/approvals/pending-bookings/active/${id}`
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.showSuccess("Booking successfully Approved.");
              this.getPendingBookings();
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    submitRejectionReason() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      this.showLoader("Loading");
      this.$http
          .get(`venues/memberships/members/approvals/pending-bookings/deactivate/${this.reject_id}`, {
            data: {
              rejection_reason: this.rejectionReason,
              rejection_reason_type: this.rejectionReasonType,
              type: "rejection",
            },
          })
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.showSuccess("Booking successfully rejected.");
              this.closeRejectionModal()
              this.getPendingBookings();
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
  }
}
</script>

<style scoped>
tbody {
  * {
    font-size:1rem !important;
  }
}
</style>

<template>
  <v-row no-gutters wrap>
    <v-col md="4">
      <v-select
          v-model="type"
          :items="[
          { text: 'Name', value: 'name' },
          { text: 'Mobile', value: 'mobile' },
          { text: 'Email', value: 'email' },
        ]"
          label="Search by"
          outlined
          dense
          :menu-props="{ bottom: true, offsetY: true }"
          background-color="#fff"
          hide-details="auto"
          class="q-autocomplete shadow-0"
      />
    </v-col>
    <v-col md="8">
      <v-autocomplete
          :value="value"
          :items="entries"
          :loading="isSearchLoading"
          :search-input.sync="search"
          :item-text="itemText"
          item-value="trainer_booking_id"
          label="Search*"
          placeholder="Search..."
          @change="changeCustomerData"
          auto-select-first
          validate-on-blur
          return-object
          outlined
          dense
          hide-details="auto"
          autocomplete="cc-mob"
          class="q-autocomplete shadow-0"
          background-color="#fff"
      >
        <template v-slot:item="{ item }">
          <v-list-item-avatar
              v-if="item.profile_image"
              rounded
              color="teal"
              class="text-h5 font-weight-light white--text"
          >
            <view-image :image="item.profile_image" :contain="false" />
          </v-list-item-avatar>
          <v-list-item-avatar
              v-else-if="item.first_name"
              rounded
              color="teal"
              class="text-h5 font-weight-light white--text"
          >
            {{ item.first_name.charAt(0) }}
          </v-list-item-avatar>
          <v-list-item-content>
            <v-list-item-title>{{ item.name || 'N/A' }}</v-list-item-title>
            <v-list-item-subtitle v-if="type === 'email'">{{ item.email || 'N/A' }}</v-list-item-subtitle>
            <v-list-item-subtitle v-else>{{ item.mobile || 'N/A' }}</v-list-item-subtitle>
            <v-list-item-subtitle>Remaining Session: {{ item.remaining_sessions || 0 }}</v-list-item-subtitle>
          </v-list-item-content>
        </template>
      </v-autocomplete>
    </v-col>
  </v-row>
</template>

<script>
import debounce from "lodash.debounce";

export default {
  inheritAttrs: false,
  props: {
    value: { type: Object, default: () => ({}) },
    trainerId: { type: Number, default: null},
  },
  data() {
    return {
      isSearchLoading: false,
      entries: [],
      search: null,
      type: "name",
    };
  },
  computed: {
    itemText() {
      return this.type; // Dynamically set item-text based on type (name, mobile, email)
    },
  },
  watch: {
    search(val) {
      this.debouncedSearch(val);
    },
    type() {
      // Clear entries and search when type changes
      this.entries = [];
      this.search = null;
    },
  },
  created() {
    this.debouncedSearch = debounce(this.searchHelper, 400);
  },
  methods: {
    searchHelper(val) {
      if (!val || val.length < 3) {
        this.entries = [];
        return;
      }

      // Skip search if the input matches the current selected value
      if (
          this.value &&
          ((this.type === "mobile" && this.value.mobile === val) ||
              (this.type === "email" && this.value.email === val) ||
              (this.type === "name" && this.value.name === val))
      ) {
        return;
      }

      if (this.isSearchLoading) return;

      this.searchCustomer(val);
    },
    searchCustomer(search) {
      this.isSearchLoading = true;
      const url = `venues/trainers/customers/search/info?trainer_id=${this.trainerId}&field=${this.type}&search=${encodeURIComponent(search)}`;
      this.$http
          .get(url)
          .then((response) => {
            if (response.status === 200) {
              // Ensure the response data has the expected fields
              this.entries = response.data.data.map((item) => ({
                ...item,
                name: item.name || "N/A",
                mobile: item.mobile || "N/A",
                email: item.email || "N/A",
                remaining_sessions: item.remaining_sessions || 0,
              }));
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          })
          .finally(() => {
            this.isSearchLoading = false;
          });
    },
    changeCustomerData(data) {
      if (!data.trainer_booking_id) {
        this.showError("Error: Please select a customer, Booking not found");
        return;
      }
      this.$emit("input", data);
    },
  },
};
</script>
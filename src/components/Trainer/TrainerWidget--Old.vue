<template>

  <v-card color="#3e4c55" class="mt-2 mr-5 ml-2" v-if="1==2">
    <v-card-text>
      <v-row>
        <v-col md="5">
          <v-avatar class="profile" color="grey" size="120" tile>
            <view-image :image="image" defaultImage="user"></view-image>
          </v-avatar>
        </v-col>
        <v-col md="7">
          <v-list-item style="margin-top: -15px" color="rgba(0, 0, 0, .4)" dark>
            <v-list-item-content>
              <v-list-item-title class="title"
                >{{ first_name }} {{ last_name || "" }}</v-list-item-title
              >
              <v-list-item-subtitle>{{ designation }}</v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>
          <v-card-subtitle
            style="margin-top: -20px"
            class="orange--text text--lighten-5"
          >
            <span class="caption">Email:- {{ email }}</span
            ><br />
            <span class="caption"> Phone: {{ mobile }}</span>
          </v-card-subtitle>
          <div
            style="margin-top: -12px"
            v-if="services && services.split(',').length"
          >
            <v-chip
              v-for="(service, index) in services.split(',')"
              :key="index"
              x-small
              class="mr-2"
              >{{ service }}</v-chip
            >
          </div>
        </v-col>
      </v-row>
      <div class="mt-1">
        <v-row>
          <v-col :cols="getWidth()" class="button-style">
            <v-btn width="100%" class="ma-1" small color="#008483" dark>
              <div>
                <span style="font-weight:700; display:block">Students </span>
                <span>{{ Number(students) | numberFormatter }}</span>
              </div>
            </v-btn>
          </v-col>
          <v-col :cols="getWidth()" class="button-style"  v-if="checkReadPermission($modules.trainers.sales.slug)">
            <v-btn width="100%" small class="ma-1" color="#008483" dark
              ><div>
                <span style="font-weight:700; display:block;">Sales</span>
                <span>{{ Number(total_sales) | toCurrency }}</span>
              </div>
            </v-btn>
          </v-col>
          <v-col :cols="getWidth()" class="button-style" v-if="checkReadPermission($modules.trainers.revenue.slug)">
            <v-btn width="100%" class="ma-1" small color="#008483" dark>
              <div>
                <span style="font-weight:700; display:block;"
                  >Earned Revenue</span
                >
                <span>
                  {{ Number(sales) | toCurrency }}
                </span>
              </div>
            </v-btn>
          </v-col>
          <v-col
            v-if="checkReadPermission($modules.trainers.commission.slug)"
            :cols="getWidth()"
            class="button-style"
          >
            <v-btn width="100%" small class="ma-1" color="#008483" dark
              ><div>
                <span style="font-weight:700; display:block;">Commission</span>
                <span>{{ Number(commission) | toCurrency }}</span>
              </div>
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </v-card-text>
    <v-bottom-navigation>
      <v-spacer
        v-if="checkWritePermission($modules.trainers.dashboard.slug)"
      ></v-spacer>
      <v-btn
        v-if="checkWritePermission($modules.trainers.dashboard.slug)"
        text
        @click="$emit('edit', id)"
      >
        <span>Edit</span>
        <v-icon medium>mdi-pencil</v-icon>
      </v-btn>
      <v-spacer
        v-if="
          checkWritePermission($modules.trainers.dashboard.slug) ||
            checkReadPermission($modules.trainers.management.slug)
        "
      ></v-spacer>
      <v-btn
        v-if="checkReadPermission($modules.trainers.management.slug)"
        @click="trainerCustomers"
      >
        <span>View</span>
        <v-icon> mdi-eye </v-icon>
      </v-btn>
      <v-spacer
        v-if="
          checkReadPermission($modules.trainers.management.slug) ||
            checkDeletePermission($modules.trainers.dashboard.slug)
        "
      ></v-spacer>
      <v-btn
        v-if="checkDeletePermission($modules.trainers.dashboard.slug)"
        @click="$emit('delete', { id: id, status_id: status_id })"
      >
        <span>{{
          status_id == 1 || status_id == 16 ? "Delete" : "Reactivate"
        }}</span>
        <v-icon>
          mdi-{{ status_id == 1 || status_id == 16 ? "delete" : "refresh" }}
        </v-icon>
      </v-btn>
      <v-spacer
        v-if="checkDeletePermission($modules.trainers.dashboard.slug)"
      ></v-spacer>
    </v-bottom-navigation>
  </v-card>
</template>
<script>
export default {
  props: {
    id: { type: Number, default: 0 },
    first_name: { type: String, default: "" },
    last_name: { type: String, default: "" },
    designation: { type: String, default: "" },
    email: { type: String, default: "" },
    mobile: { type: String, default: "" },
    sales: { type: Number, default: 0 },
    total_sales: { type: Number, default: 0 },
    commission: { type: Number, default: 0 },
    students: { type: Number, default: 0 },
    status_id: { type: Number, default: 1 },
    image: {
      type: String,
      default: null,
    },
    services: { type: String, default: "" },
  },
  methods: {
    trainerCustomers() {
      if (this.services) {
        this.$router.push({
          name: "TrainerCustomers",
          params: { data: btoa(this.id) },
        });
      } else {
        this.showError("Please assign service to trainer to proceed");
      }
    },
    getWidth() {
      return this.checkReadPermission(this.$modules.trainers.commission.slug)
        ? 6
        : 4;
    },
  },
};
</script>

<style scoped>
.button-style {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 1%;
  padding-right: 2%;
}
</style>

<template>
  <div>
    <!-- Main div for the report card -->
    <div
      class="d-flex justify-center p-4 report_card"
      style="
        border: 1px solid #dcdcdc;
        height: 70px;
        background-color: #ffffff;
        cursor: pointer;
      "
      @click="gotoReports"
    >
      <div class="d-flex gap-x-4 align-center">
        <p class="font-bold text-base blue-text p-0 m-0">{{ name }}</p>
      </div>
    </div>

    <!-- Dialog Popup for JV Report -->
    <!-- <v-dialog persistent v-model="dialog" max-width="500px"> -->
    <v-dialog persistent v-model="dialog" width="40%">
      <v-form ref="product_form" autocomplete="off">
        <v-card>
          <v-card-text class="border-bottom mb-3">
            <div class="row pt-1 border-bottom">
              <div class="col-md-12">
                <div class="d-flex justify-space-between align-center mt-2">
                  <SvgIcon
                    class="text-2xl font-semibold"
                    text="Sun System JV Report"
                    style="color: black"
                  >
                  </SvgIcon>
                  <v-btn fab x-small class="shadow-0" @click="dialog = false">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>

            <v-row>
              <v-col cols="6" style="padding-right: 0">
                <v-menu
                  v-model="menu1"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="290px"
                >
                  <template v-slot:activator="{ on }">
                    <v-text-field
                      outlined
                      v-model="date1Formatted"
                      readonly
                      v-on="on"
                      class="q-text-field shadow-0"
                      hide-details
                      dense
                      style="background-color: #ffffff"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="searchParams.from_date"
                    @input="menu1 = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>

              <v-col cols="2">
                <v-flex xs1 class="ml-1">
                  <v-btn
                    class="export-button"
                    elevation="0"
                    height="40"
                    @click="jvReportDownload"
                  >
                    <SvgIcon text="Export Report">
                      <template v-slot:icon>
                        <ExportIcon />
                      </template>
                    </SvgIcon>
                  </v-btn>
                </v-flex>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-btn text @click="dialog = false" class="ma-2">Close</v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
  </div>
</template>

<script>
import moment from "moment";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import ExportIcon from "@/assets/images/misc/export-icon.svg";
export default {
  components: { ExportIcon, SvgIcon },
  data() {
    return {
      dialog: false, // Controls the visibility of the popup
      menu1: false,
      menu2: false,
      searchParams: {
        from_date: moment().format("YYYY-MM-DD"),
      },
    };
  },
  props: {
    id: { type: Number },
    route: { type: String, default: "" },
    name: { type: String, default: "" },
    type: { type: String },
    color: { type: String },
    url: { type: String },
  },
  computed: {
    date1Formatted() {
      return moment(this.searchParams.from_date, "YYYY-MM-DD").format(
        "Do MMM YYYY"
      );
    },
  },
  methods: {
    gotoReports() {
      if (this.type == "jv_report") {
        this.dialog = true;
      } else {
        this.$router.push({
          name: this.route,
        });
      }
    },
    jvReportDownload() {
      let url = this.getFilterUrlData();
      if (!url) return;
      this.showLoader("Downloading report");
      this.$http
        .get(`venues/reports/jv-report-export/download${url}`, {
          responseType: "blob",
        })
        .then((response) => {
          this.hideLoader();
          if (response.status == 200) {
            this.downloadReport(response, this.fileName, "SUN");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    getFilterUrlData() {
      let url = `?from_date=${this.searchParams.from_date}&to_date=${this.searchParams.from_date}`;
      this.fileName = this.searchParams.from_date;
      if (this.fileName != null) this.fileName = "JV-Export-" + this.fileName;
      else this.fileName = "JV-Export";
      return url;
    },
  },
};
</script>

<style scoped>
.card {
  font-weight: normal;
  height: 100px;
}

.report_card:hover {
  border: 1px solid rgba(17, 42, 70, 1);
  box-shadow: 0 8px 24px 0 rgba(70, 76, 136, 0.2) !important;
}
</style>

<template>
  <div class="pa-3">
    <table class="new_reciept_details_item_table">
      <tr>
        <th>Order ID</th>
        <th>Items</th>
        <th>Price</th>
        <th>VAT Amount</th>
        <th>Total Price</th>
        <th>Action</th>
      </tr>
      <tr v-for="(order, index) in orders" :key="index">
        <td>
          {{ order.id }}
        </td>
        <td>{{ order.items.length }}</td>
        <td>
          <span>
            {{ Math.abs(Number(order.price)) | toCurrency }}
          </span>
        </td>
        <td>
          {{ order.tax | toCurrency }}
        </td>
        <td>
          {{ Math.abs(order.total) | toCurrency }}
        </td>
        <td>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-icon small class="white--text red-color text"  @click="removeOrderFromInvoice(order.id)"   v-bind="attrs"
          v-on="on">mdi-delete</v-icon>
            </template>
            <span>Remove order from invoice.</span>
          </v-tooltip>
          
        </td>
        
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    orders: { type: Array, default: () => [] },
  },
  data() {
    return {
      orderId: null,
      selectedOrders: [],
    };
  },
  methods: {
    getOrderDetails(id) {
      this.$emit("openOrderDetails", id);
    },
     removeOrderFromInvoice(orderId){
      let selectedOrd = [];
      this.selectedOrders = [];
      this.orders.map((item) => {
        if (item.order_id == orderId) {
          /** to fixed  */
          selectedOrd.push(orderId);
        }
      });
      this.selectedOrders = orderId;
      this.$emit("removeOrderFromInvoice",orderId);
      console.log(this.selectedOrders);
    }
  },
};
</script>

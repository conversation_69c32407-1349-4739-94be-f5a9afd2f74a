<template>
  <div>
    <v-dialog
      v-model="invoiceDialogue"
      :max-width="700"
      @input="closeOrderListingModel"
      scrollable
      persistent
    >
      <v-card ref="recieptPrint">
        <v-card-text class="pa-0 ma-0">
          <div class="receipt_details">
            <v-row class="ma-0">
              <v-col class="text-left pt-5 pb-0" cols="7" md="7">
                <div class="invoice-title">
                  <v-btn icon class="inv-sm-circle">
                    <v-icon>mdi-cart-outline</v-icon>
                  </v-btn>
                  Order Listing
                  <span class="display-block"
                    >{{ olData.invoice_seq_no }}
                  </span>
                </div>
              </v-col>
              <v-col class="text-right pb-0" cols="5" md="5">
                <v-text-field
                  v-model="orderNumber"
                  :loading="loading"
                  dense
                  filled
                  label="Search Order Number"
                  prepend-inner-icon="mdi-magnify"
                  @input="searchOrderNumber"
                  @click:append-inner="onClick"
                ></v-text-field>
              </v-col>
            </v-row>
          </div>
          <v-divider></v-divider>
          <div class="receipt_details_body pa-3">
            <table class="pa-3 venue-info-table">
              <tr>
                <td colspan="4">
                  <div class="v-title vl">Select Order</div>
                </td>
              </tr>
            </table>

            <!--
            <edit-order-table
              @openOrderDetails="openOrderDetails"
              :orders="olData.orders"
              @removeOrderFromInvoice="removeOrderFromInvoice"
            ></edit-order-table> -->
            <InvoiceOrderTable
              @openOrderDetails="openOrderDetails"
              :orders="orders"
              :isCheckbox="true"
              @addOrderToInvoice="addOrderToInvoice"
              :pageNumber="pageNumber"
            ></InvoiceOrderTable>
          </div>
          <v-divider></v-divider>
          <div class="pagi-div">
            <v-pagination v-model="page" :length="totalPages"></v-pagination>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-btn class="ma-2 close-btn" text @click="closeOrderListingModel"
            >Close</v-btn
          >
          <v-spacer></v-spacer>
          <v-btn class="ma-2 white--text blue-color" text @click="updateInvoice"
            >UPDATE</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>

    <confirm-model
      v-bind="confirmInputModel"
      @confirm="confirmActions"
      @close="confirmInputModel.id = null"
    >
    </confirm-model>
  </div>
</template>

<script>
// import EditOrderTable from "@/components/Invoice/EditOrderTable.vue";
import InvoiceOrderTable from "@/components/Invoice/InvoiceOrderTable.vue";

export default {
  components: {
    InvoiceOrderTable,
  },
  props: {
    invoiceId: { type: Number, default: null },
    olData: { type: Array, default: null },
  },
  data() {
    return {
      invoiceDialogue: false,
      invoice: {},
      confirmInputModel: {},
      voidNote: "",
      orderId: null,
      removeOrders: [],
      addOrders: [],
      orders: [],
      loaded: false,
      loading: false,
      page: 1,
      totalPages: 1,
      orderNumber: "",
      pageNumber: 1,
    };
  },
  computed: {
    orderDetails() {
      return this.$store.getters.getOrderDetails;
    },
  },
  watch: {
    invoiceId: {
      immediate: true,
      handler(val) {
        console.log("ol model watch inv id: " + val);
        if (val == "" || val == null) {
          this.invoiceDialogue = false;
        } else {
          this.invoiceDialogue = true;
          this.getPendingOrders();
        }
      },
    },
    page() {
      this.getPendingOrders();
    },
  },
  methods: {
    onClick() {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
        this.loaded = true;
        console.log("click");
      }, 2000);
    },
    confirmActions(data) {
      if (data.type == "remove") {
        console.log("remove order from invoice");
        this.olData.orders = this.olData.orders.filter(
          (order) => order.id !== this.orderId
        );
        this.removeOrders.push(this.orderId);
      }
      this.confirmInputModel.id = null;
    },
    addOrderToInvoice(selectedOrders) {
      this.addOrders = selectedOrders;
    },
    getInvoiceDetails() {
      this.invoice = {
        discount: 0,
        promotion_name: "",
        promotion_code: "",
        price: 450,
        tax_amount: 15,
        cashier: "Qasim",
        total: 465,
      };
    },

    closeOrderListingModel() {
      this.$emit("close");
    },
    openOrderDetails(id) {
      this.$emit("openMainOrderDetails", id);
      // this.closeOrderListingModel();
    },
    updateInvoice() {
      this.showLoader("Wait..");
      var formData = new FormData();
      this.addOrders.forEach((id, index) => {
        formData.append(`order_ids[${index}]`, id);
      });
      formData.append(`invoice_id`, this.invoiceId);
      formData.append(`action_type`, "add");
      this.$http({
        method: "post",
        data: formData,
        url: `venues/invoices/update/${this.invoiceId}`,
      })
        .then((response) => {
          this.hideLoader();
          if (response.status == 200) {
            this.showSuccess("Invoice Updated");
            if (response.data.data && response.data.data.orders.length > 0) {
              this.closeOrderListingModel();
              this.$emit("invoiceReload", this.invoiceId);
            } else {
              this.closeOrderListingModel();
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
          this.hideLoader();
        });
    },
    addNewOrderPopUp() {
      console.log("clicked");
    },
    getPendingOrders(order_number = null) {
      let url = "&per_page=10";
      if (order_number) {
        url += "&order_number=" + order_number;
      }
      this.showLoader("Loading..");
      this.$http
        .get("venues/invoices/pending-orders?page=" + this.page + url)
        .then((response) => {
          if (response.status == 200) {
            this.hideLoader();
            let orders = response.data.data;
            if (this.addOrders && this.orders.length) {
              orders.map((order) => {
                if (this.addOrders.includes(order.id)) {
                  order.isSettle = true;
                }
              });
              this.orders = orders;
            } else {
              this.orders = orders;
            }
            this.totalPages = response.data.total_pages;
            this.pageNumber = this.page;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    searchOrderNumber() {
      if (this.orderNumber.length == 0 || this.orderNumber.length > 3) {
        this.getPendingOrders(this.orderNumber);
      }
    },
  },
};
</script>

<style scoped>
.v-sheet.v-card {
  border-radius: 10px;
}
span.display-block {
  display: block;
  font-size: 14px;
  font-weight: 400;
  margin-left: 40px;
}
.invoice-title {
  font-size: 20px;
  font-weight: 600;
  color: #000;
}

.venue-info-table {
  width: 100%;
  margin: 0px auto;
}
.receipt_details_table {
  width: 99%;
  margin: 20px auto;
}
.receipt_details_table {
  width: 99%;
  margin: 20px auto;
}

.receipt_details_table tr td {
  width: 50%;
  text-align: left;
  color: #000;
  padding: 3px;
}

.reciept_right {
  text-align: right;
}

.reciept_left {
  text-align: left;
}

.reciept_title {
  font-size: 12px;
  color: #002a89;
}

.receipt_details_item_table {
  width: 100%;
  margin: 20px auto;
}

.receipt_details_item_table,
.receipt_details_item_table th,
.receipt_details_item_table td {
  border: 1px solid rgba(163, 163, 163, 0.534) !important;
  border-collapse: collapse;
}

.receipt_details_item_table tr td {
  font-size: 12px;
  text-align: center;
  padding: 3px;
}

.receipt_details_item_table tr th {
  text-align: center;
  padding: 3px;
}

.receipt_details_total_table {
  text-align: right;
  width: 100%;
}

.receipt_details_total_table tr td {
  padding: 6px;
}

.receipt_details_total_table .space {
  width: 186px;
}
.receipt_details_body {
  background: #fff;
  padding: 8px;
}
.receipt_details_total_table th {
  color: #112a45;
}

.v-title {
  font-size: 14px;
  font-weight: 600;
  padding: 10px;
  color: #000;
  margin: 0px;
  padding: 0px;
}

.v-name {
  font-size: 14px;
  padding: 10px;
  font-weight: 400;
  color: #000;
  margin: 0px;
  padding: 0px;
}
.venue-info-table tr:first-child td {
  padding-bottom: 10px;
}
.inv-sm-circle {
  color: #112a45;
  background: rgba(17, 42, 69, 0.1);
}
.text-black {
  color: #000;
}
</style>

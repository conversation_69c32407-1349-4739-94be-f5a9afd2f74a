<template>
  <div>
    <v-dialog
      v-model="invoiceDialogue"
      :max-width="700"
      @input="closeOrderDetails"
      scrollable
      persistent
      style="box-shadow: none !important;"
    >
      <v-card ref="recieptPrint">
        <slot name="carosal"></slot>
        <v-card-text class="pa-0 ma-0">
          <div class="receipt_details">
            <div class="text-left pt-5 pl-6" col="10" md="10">
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon color="white" v-bind="attrs" v-on="on">
                    <v-icon>mdi-printer</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item @click="downloadPdf('a4')">
                    <v-list-item-title>Print A4</v-list-item-title>
                  </v-list-item>
                  <v-list-item @click="downloadPdf('pos')">
                    <v-list-item-title>Print POS</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
              <v-btn icon color="white" @click="downloadPdf('email')"
                ><v-icon>mdi-email</v-icon></v-btn
              >
            </div>

            <div class="head text-center pt-8">Customer Details</div>
            <table class="reciept_table">
              <tr>
                <td>Name</td>
                <td>
                  Qasim
                </td>
              </tr>
              <tr>
                <td>Mobile No.</td>
                <td>
                  0589727469
                </td>
              </tr>
              <tr>
                <td>Email:</td>
                <td>
                  <EMAIL>
                </td>
              </tr>
            </table>

            <v-divider></v-divider>
          </div>
          <div class="reciept_details_body">
            <div class="head pt-4">
              <span>Tax Invoice</span>
            </div>
            <table class="reciept_details_table pa-3">
              <tr>
                <td>
                  <div class="reciept_title">Venue</div>
                </td>
                <td>
                  <div class="reciept_right reciept_title">
                    <span>Invoice</span> generated at
                  </div>
                </td>
              </tr>
              <tr>
                <td>{{ $store.getters.venueInfo.name }}</td>
                <td>
                  <div class="reciept_right">
                    3rd Nov 2022 06:00 pm
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="reciept_title"></div>
                </td>
                <td>
                  <div class="reciept_right reciept_title">
                    Booking Date
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <span> </span>
                </td>
                <td>
                  <div class="reciept_right">
                    <span>
                      3rd Nov 2022
                    </span>
                  </div>
                </td>
              </tr>
              <tr v-if="payments && payments.length > 0">
                <td>
                  <div class="reciept_title">
                    <span> Visa ***1111</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="reciept_title">
                    <span>Invoice Number :</span>
                  </div>
                </td>
                <td>
                  <div class="reciept_right reciept_title">Facility</div>
                </td>
              </tr>
              <tr>
                <td>
                  I-101010111111
                </td>
                <td>
                  <div class="reciept_right">
                    NA
                  </div>
                </td>
              </tr>
            </table>

            <v-divider></v-divider>
            <orders-table :orders="orders"></orders-table>
            <v-divider></v-divider>

            <table class="reciept_details_total_table pa-3">
              <tr v-if="invoice.discount">
                <td class="space"></td>
                <td class="space"></td>

                <td>Promotion:</td>
                <td>
                  {{ invoice.promotion_name }}
                  {{
                    invoice.promotion_code
                      ? "(" + invoice.promotion_code + ")"
                      : ""
                  }}
                </td>
              </tr>
              <tr v-if="invoice.tax_amount">
                <td class="space"></td>
                <td class="space"></td>
                <td>Sub Total Exclusive TAX</td>
                <td>
                  {{
                    Number(parseFloat(Math.abs(invoice.price)).toFixed(4))
                      | toCurrency
                  }}
                </td>
              </tr>
              <slot name="special_field"></slot>
              <tr>
                <td>
                  <div class="reciept_left reciept_title">Cashier</div>
                </td>
                <td class="space"></td>
                <td v-if="invoice.tax_amount">Tax Amount</td>
                <td v-if="invoice.tax_amount">
                  {{
                    Number(parseFloat(Math.abs(invoice.tax_amount)).toFixed(4))
                      | toCurrency
                  }}
                </td>
              </tr>
              <tr>
                <td>
                  <div class="reciept_left">
                    {{ invoice.cashier ? invoice.cashier : "NA" }}
                  </div>
                </td>
                <td class="space"></td>
                <th>Grand Total</th>
                <th>
                  {{
                    Number(parseFloat(Math.abs(invoice.total)).toFixed(4))
                      | toCurrency
                  }}
                </th>
              </tr>
            </table>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-btn
            class="ma-2 white--text blue-color"
            text
            @click="closeOrderDetails"
            >Close</v-btn
          ></v-card-actions
        >
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import OrdersTable from "@/components/Invoice/OrdersTable.vue";

export default {
  components: {
    OrdersTable,
  },
  props: {
    id: { type: Number, default: null },
  },
  data() {
    return {
      invoiceDialogue: false,
      orders: [
        {
          order_id: 1,
          items: [1, 2, 3, 4],
          price: 100,
          tax_type: "5%",
          tax_amount: 5,
        },
        {
          order_id: 2,
          items: [1, 2],
          price: 200,
          tax_type: "5%",
          tax_amount: 10,
        },
        {
          order_id: 3,
          items: [1, 2, 3],
          price: 150,
          tax_type: "Tax free",
          tax_amount: 0,
        },
      ],
      invoice: {},
    };
  },
  computed: {
    orderDetails() {
      return this.$store.getters.getOrderDetails;
    },
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        if (val == "" || val == null) {
          this.invoiceDialogue = false;
        } else {
          this.invoiceDialogue = true;
          this.getInvoiceDetails();
        }
      },
    },
  },
  methods: {
    getInvoiceDetails() {
      this.invoice = {
        discount: 0,
        promotion_name: "",
        promotion_code: "",
        price: 450,
        tax_amount: 15,
        cashier: "Qasim",
        total: 465,
      };
    },
    closeOrderDetails() {
      this.$emit("closeInvoiceDetails");
    },
    downloadPdf(type) {
      this.showLoader("Generating..");
      this.$http
        .get(`venues/orders/pdf/${this.order.id}?type=${type}`, {
          responseType: "blob",
        })
        .then((response) => {
          if (response.status == 200) {
            this.printFile(response);
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
  },
};
</script>

<style scoped>
.receipt_details {
  background: rgb(230, 92, 0);
  background: linear-gradient(0deg, rgb(230, 92, 0) 0%, rgb(119, 57, 3) 100%);
}

.receipt_details_body {
  background: #f0f0f0;
}

.receipt_details_body .head {
  text-align: center;
  color: black;
}

.receipt_details .head {
  text-align: center;
}

.reciept_table {
  width: 70%;
  margin: 20px auto;
}

.reciept_table tr td {
  width: 50%;
  text-align: left;
  color: #fff;
  padding: 3px;
}

.receipt_details_table {
  width: 99%;
  margin: 20px auto;
}

.receipt_details_table tr td {
  width: 50%;
  text-align: left;
  color: #000;
  padding: 3px;
}

.reciept_right {
  text-align: right;
}

.reciept_left {
  text-align: left;
}

.reciept_title {
  font-size: 12px;
  color: #002a89;
}

.receipt_details_item_table {
  width: 99%;
  margin: 20px auto;
}

.receipt_details_item_table,
.receipt_details_item_table th,
.receipt_details_item_table td {
  border: 1px solid rgba(163, 163, 163, 0.534) !important;
  border-collapse: collapse;
}

.receipt_details_item_table tr td {
  font-size: 12px;
  text-align: center;
  padding: 3px;
}

.receipt_details_item_table tr th {
  text-align: center;
  padding: 3px;
}

.receipt_details_total_table {
  text-align: right;
  width: 100%;
}

.receipt_details_total_table tr td {
  padding: 6px;
}

.receipt_details_total_table .space {
  width: 186px;
}
</style>

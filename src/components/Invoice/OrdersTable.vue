<template>
  <div class="pa-3">
    <table class="reciept_details_item_table">
      <tr>
        <th>Order ID</th>
        <th>Items</th>
        <th>Price</th>
        <th>VAT Amount</th>
        <th>Total Price</th>
      </tr>
      <tr v-for="(order, index) in orders" :key="index">
        <td @click="getOrderDetails(order.id)">
          <!-- <v-btn class="white--text blue-color">
            {{ order.id }}
          </v-btn> -->
          <span class="text-decoration-underline" style="cursor:pointer;">
            {{ order.id }}
          </span>
        </td>
        <td>{{ order.items.length }}</td>
        <td>
          <span>
            {{ Math.abs(Number(order.price)) | toCurrency }}
          </span>
        </td>
        <td>
          {{ order.tax | toCurrency }}
        </td>
        <td>
          {{ Math.abs(order.total) | toCurrency }}
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    orders: { type: Array, default: () => [] },
  },
  data() {
    return {
      orderId: null,
    };
  },
  methods: {
    getOrderDetails(id) {
      this.$emit("openOrderDetails", id);
    },
  },
};
</script>

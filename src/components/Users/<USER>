<template>
  <v-card>
    <v-card-text class="pt-2 relative">
      <div class="d-flex justify-end mb-1">
        <v-menu
            content-class="q-menu"
            offset-y
        >
          <template v-slot:activator="{ on, attrs }">
            <v-btn
                class="text_capitalize px-0 no-hover-effect"
                elevation="0"
                height="24"
                style="background-color: transparent; min-width: 24px !important; position: absolute; top:10px; right: 10px; "
                v-bind="attrs"
                v-on="on"
            >
              <DotsIcon height="24" width="24"/>
            </v-btn>
          </template>
          <v-list class="pointer">
            <v-list-item v-if="checkWritePermission($modules.settings.users.slug)"
                         @click.stop="editUser">
              <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
                <template #icon>
                  <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
                </template>
              </SvgIcon>
            </v-list-item>
            <v-list-item v-if="checkDeletePermission($modules.settings.users.slug) && id != $store.getters.userInfo.user_id"
                         @click.stop="deleteUser">
              <SvgIcon class="font-medium text-sm gap-x-2 text-red" :text="'Delete User'">
                <template #icon>
                  <DeleteIcon stroke="#E50000"/>
                </template>
              </SvgIcon>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
      <v-row>
        <v-col md="4">
          <view-image :aspect-ratio="'1'" :image="profile_image" defaultImage="user" style="border-radius: 0.5rem;" :height="150"></view-image>
        </v-col>
        <v-col md="8">
          <div class="mt-0">

              <p class="m-b-0 font-semibold text-lg black--text text-elepsis line-clamp-1">
                {{first_name+' '+ last_name}}
              </p>


            <p class="text-xs mb-2 line-clamp-1">{{ roles.map((role) => role.title).join(", ") }}</p>
            <SvgIcon  class="gap-x-4 m-b-3 font-medium">
              <template v-slot:icon>
                <SMSIcon style="min-width: 24px"/>
              </template>
              <div>
                <span class="m-b-0 font-medium wrap-text line-clamp-1">
                  {{email}}
                </span>
              </div>
            </SvgIcon>
            <SvgIcon class="gap-x-4 m-b-3"
            >
              <template v-slot:icon>
                <CallIcon style="min-width: 24px"/>
              </template>
              <div>
                <p class="m-b-0 font-medium">
                  {{mobile}}
                </p>
              </div>
            </SvgIcon>
            <SvgIcon class="gap-x-4 m-b-3"
            >
              <template v-slot:icon>
                <CalendarIcon style="min-width: 24px"/>
              </template>
              <div>
                <p class="m-b-0 font-medium">
                  {{getCreatedAt()}}
                </p>
              </div>
            </SvgIcon>
          </div>
        </v-col>
      </v-row>

    </v-card-text>
  </v-card>
</template>
<script>
import SMSIcon from "@/assets/images/misc/sms.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import CallIcon from "@/assets/images/misc/call.svg";
import EditIcon from "@/assets/images/tables/edit.svg";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
import DotsIcon from "@/assets/images/misc/dots.svg";
import moment from "moment";
import CalendarIcon from '@/assets/images/Users/<USER>';

export default {
  name: "UsersWidget",
  components: {DotsIcon, DeleteIcon, CalendarIcon,EditIcon, CallIcon, SvgIcon, SMSIcon},
  props: {
    id: { type: Number, default: 0 },
    first_name: { type: String, default: "" },
    last_name: { type: String, default: "" },
    email: { type: String, default: "" },
    mobile: { type: String, default: "" },
    status_id: { type: Number, default: 1 },
    profile_image: {
      type: String,
      default: null,
    },
    roles:{
      type: Array,
      default: () => [],
    },
    created_at: { type: String, default: null },
  },
  methods: {
    getCreatedAt() {
      if (this.created_at) {
        return moment(this.created_at).format("Do MMM, YYYY");
      }else{
        return 'N/A';
      }
    },
    editUser(id){
      this.$emit("edit", id);
    },
    deleteUser(id){
      this.$emit("delete", id);
    }
  }
}
</script>

<style scoped>

</style>
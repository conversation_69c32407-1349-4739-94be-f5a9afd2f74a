
<template>
  <v-dialog
    persistent
    v-model="dialogVisible"
    scrollable
    @input="close"
    width="600px"
  >
    <v-form ref="category_form" autocomplete="off">
      <v-card>
        <v-card-text class="border-bottom">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon
                  class="text-2xl font-semibold"
                  text="Members Check In"
                  style="color: black"
                ></SvgIcon>
                <v-btn fab x-small class="shadow-0" @click="close"
                  ><v-icon>mdi-close</v-icon></v-btn
                >
              </div>
            </div>
          </div>

          <v-row class="d-flex justify-center">
            <v-col md="4">
              <v-select
                v-model="type"
                :items="[
                  { text: 'Name', value: 'name' },
                  { text: 'Mobile', value: 'mobile' },
                  { text: 'Email', value: 'email' },
                  { text: 'Member Id', value: 'card_number' },
                ]"
                label="Search by"
                outlined
                dense
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
                hide-details="auto"
                class="q-autocomplete shadow-0"
              />
            </v-col>
            <v-col md="8">
              <v-autocomplete
                :v-model="memberId"
                :value="value"
                :items="entries"
                :loading="isSearchLoading"
                :search-input.sync="search"
                item-text="name"
                item-value="id"
                label="Search*"
                placeholder="Search..."
                auto-select-first
                validate-on-blur
                return-object
                outlined
                dense
                hide-details="auto"
                autocomplete="cc-mob"
                class="q-autocomplete shadow-0"
                background-color="#fff"
                :filter="() => true"
                @change="changeCustomerData"
              >
                <template v-slot:item="{ item }">
                  <v-list-item-avatar
                    v-if="item.profile_image"
                    rounded
                    color="teal"
                    class="text-h5 font-weight-light white--text"
                  >
                    <view-image :image="item.profile_image" :contain="false" />
                  </v-list-item-avatar>
                  <v-list-item-avatar
                    v-else-if="item.first_name"
                    rounded
                    color="teal"
                    class="text-h5 font-weight-light white--text"
                  >
                    {{ item.first_name.charAt(0) }}
                  </v-list-item-avatar>
                  <v-list-item-content>
                    <v-list-item-title>{{ item.name }}</v-list-item-title>
                    <v-list-item-subtitle v-if="type === 'email'">{{
                      item.email
                    }}</v-list-item-subtitle>
                    <v-list-item-subtitle v-else>{{
                      item.mobile
                    }}</v-list-item-subtitle>
                    <v-list-item-title>{{
                      "Member Id: " + item.card_number
                    }}</v-list-item-title>
                  </v-list-item-content>
                </template>
              </v-autocomplete>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="close()" class="ma-2">Close</v-btn>
          <v-btn text class="white--text blue-color" @click="checkInMember">{{
            checkInButtonText
          }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
import debounce from "lodash.debounce";
import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  components: {
    SvgIcon,
  },
  inheritAttrs: false,
  props: {
    value: { type: Boolean, default: false },
    showSearchMembersPopUp: { type: Boolean, default: false },
  },
  data() {
    return {
      isSearchLoading: false,
      entries: [],
      search: null,
      type: "name",
      showCheckIn: false,
      memberId: null,
      checkInButtonText: "Check In",
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val); // emits back to parent to sync
      },
    },
  },
  watch: {
    // showSearchMembersPopUp: {
    //   immediate: true,
    // },
    search(val) {
      this.debouncedSearch(val);
    },
  },
  created() {
    this.debouncedSearch = debounce(this.searchHelper, 400);
  },
  methods: {
    changeCustomerData(data) {
      this.memberId = data.id;
      const url = `venues/memberships/check-in-status?member_id=${this.memberId}`;
      this.$http
        .get(url)
        .then((response) => {
          if (response.status === 200) {
            let checkInStatus = response.data.data;
            if (checkInStatus) this.checkInButtonText = "Check Out";
            else this.checkInButtonText = "Check In";
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.isSearchLoading = false;
        });
    },
    checkInMember() {
      this.showLoader("Loading");
      let data = {
        member_id: this.memberId,
      };
      this.$http
        .post("venues/memberships/check-in-member", data)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.$emit("refresh-schedules");
            this.dialogVisible = false;
          }
          this.hideLoader();
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    close() {
      this.dialogVisible = false;
      this.$emit("close");
    },
    searchHelper(val) {
      if (!val || val.length < 3) {
        this.entries = [];
        return;
      }

      if (
        this.value &&
        (this.value.mobile === val ||
          this.value.email === val ||
          this.value.name === val)
      ) {
        return;
      }
      if (this.isSearchLoading) return;
      this.searchCustomer(val);
    },
    searchCustomer(search) {
      this.isSearchLoading = true;
      const url = `venues/memberships/search-members?field=${this.type}&search=${search}`;
      this.$http
        .get(url)
        .then((response) => {
          if (response.status === 200) {
            this.entries = response.data.data;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.isSearchLoading = false;
        });
    },
  },
};
</script>

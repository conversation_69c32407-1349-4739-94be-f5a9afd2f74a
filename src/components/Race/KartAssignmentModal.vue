<template>
  <v-dialog v-model="dialog" max-width="900px" persistent>
    <v-card>
      <v-card-title class="justify-space-between">
        <span class="modal-heading">Assign Karts</span>
      </v-card-title>
      <div class="px-2 d-flex gap-x-2 justify-center align-center h-full" style="min-height: 300px">
        <div class="bg-light-neon rounded-2 w-50 scrollable-box">
          <table class="table text-center">
            <thead>
            <tr class="tr-rounded text-center font-bold black-text pa-2">
              <th class="text-left pb-1">Users List</th>
              <th class="text-center pb-1">Kart</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(participant, index) in assignParticipantsList" :key="index" style="height: 57px;">
              <td class="text-left">
                <div class="d-flex gap-x-2 align-center">
                  <input
                      :disabled="participant.vehicle_name"
                      type="checkbox"
                      class="custom-checkbox"
                      :value="participant"
                      :checked="isSelected(participant,'participant')"
                      @change="toggleBooking(participant,'participant')"
                  />
                  {{ participant.participant_name }}
                </div>
              </td>
              <td>
                <div v-if="participant.vehicle_name">
                  <v-chip
                      class="ma-2"
                      close
                      outlined
                      small
                      @click:close="removeAssignment(participant.race_participant_id,participant.vehicle_id)"
                  >
                    {{ participant.vehicle_name }}
                  </v-chip>
                </div>
                <span v-else>-</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="w-10">
          <v-btn
              :disabled="assignParticipantsList.length === 0 || assignKartsList.length === 0"
              class="bg-neon pa-1"
              @click="assignKarts()"
          >
            <AssignIcon></AssignIcon>
            <span class="white--text text-xs">Assign</span>
          </v-btn>


        </div>
        <div class="bg-light-neon rounded-2 w-30 scrollable-box">
          <table class="table text-center">
            <thead>
            <tr class="tr-rounded text-center font-bold black-text">
              <th class="text-left">Available Karts</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(kart, index) in assignKartsList" :key="index">
              <td class="text-left">
                <div class="d-flex gap-x-2 align-center">
                  <input
                      :disabled="kart.vehicle_name"
                      type="checkbox"
                      class="custom-checkbox"
                      :value="kart"
                      :checked="isSelected(kart,'kart')"
                      @change="toggleBooking(kart,'kart')"
                  />
                  {{ kart.name }}
                </div>
              </td>
            </tr>
            </tbody>
          </table>

        </div>
      </div>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn class="ma-2 text-capitalize" text @click="closeModal">Close</v-btn>
        <v-btn color="primary" class="text-capitalize" @click="saveParticipants">Confirm</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
import AssignIcon from "@/assets/images/race/Assign.svg";

export default {
  name: "KartAssignmentModal",
  components: {AssignIcon},
  props: {
    dialog: {type: Boolean, default: false},
    race_id: {type: Number, default: null},
    assignParticipantsList: {type: Array, default: () => []},
    assignKartsList: {type: Array, default: () => []},
  },
  data() {
    return {
      selectedParticipants: [],
      selectedKarts: [],
      kartAssignmentList: [],
    }
  },
  watch: {
    dialog(val) {
      if (!val) {
        this.kartAssignmentList = [];
      } else {
        this.assignParticipantsList.filter(i => i.vehicle_id).forEach(i => {
          this.selectedParticipants.push({
            race_participant_id: i.race_participant_id
          })

          this.selectedKarts.push({
            id: i.vehicle_id,
            name: i.vehicle_name,
          })

          this.kartAssignmentList.push({
            participant_id: i.race_participant_id,
            kart_id: i.vehicle_id,
            kart_name: i.vehicle_name,
          });

        })
      }
    }
  },
  methods: {
    toggleBooking(obj, type) {
      if (type === 'kart') {
        const index = this.selectedKarts.findIndex(
            (selected) => selected.id === obj.id
        );

        if (index > -1) {
          // If already selected, remove it
          this.selectedKarts.splice(index, 1);
        } else {
          // If not selected, add it
          this.selectedKarts.push({
            id: obj.id,
            name: obj.name,
          });
        }
      } else {
        const index = this.selectedParticipants.findIndex(
            (selected) => selected.race_participant_id === obj.race_participant_id
        );
        if (index > -1) {
          // If already selected, remove it
          this.selectedParticipants.splice(index, 1);
        } else {
          // If not selected, add it
          this.selectedParticipants.push({
            race_participant_id: obj.race_participant_id,
          });
        }
      }
    },
    isSelected(obj, type) {
      if (type === 'kart') {
        return this.selectedKarts.some(
            (selected) => selected.id === obj.id
        );
      } else {
        return this.selectedParticipants.some(
            (selected) => selected.race_participant_id === obj.race_participant_id
        );
      }
    },
    closeModal() {
      this.selectedParticipants = [];
      this.selectedKarts = [];
      this.$emit('closeModal');
    },
    async saveParticipants() {
      this.showLoader('Saving assignment');
      await this.$http.post(
          `venues/facilities/bookings/race/kart-assignment/set/${this.race_id}`,
          {
            data: this.kartAssignmentList
          })
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.closeModal();
            }
          }).catch((error) => {
            this.errorChecker(error);
          }).finally(() => {
            this.hideLoader();
          });
    },
    updateKarts() {
      this.$emit('updateKarts', this.kartAssignmentList);
    },
    assignKarts() {
      // Find the minimum length between selectedParticipants and selectedKarts
      const minLength = Math.min(this.selectedParticipants.length, this.selectedKarts.length);

      // Clear existing assignments
      this.kartAssignmentList = [];

      // Edge Case: If either array is empty, don't proceed
      if (minLength === 0) {
        console.warn("No assignments possible: Either selectedParticipants or selectedKarts is empty.");
        this.showError('Assignment not possible, please select Karts and Participants');
        return;
      }

      // Assign karts to participants
      for (let i = 0; i < minLength; i++) {
        this.kartAssignmentList.push({
          participant_id: this.selectedParticipants[i].race_participant_id,
          kart_id: this.selectedKarts[i].id,
          kart_name: this.selectedKarts[i].name,
        });
      }

      this.updateKarts();
    },
    removeAssignment(p_id,v_id) {
      this.toggleBooking({id: v_id},'kart');
      this.toggleBooking({race_participant_id:p_id},'participant');
      this.$emit('removeAssignment', p_id);
      this.assignKarts();
    }
  }

}
</script>


<style scoped>
.w-50 {
  width: 50% !important;
  height: 100% !important;
  min-height: 100% !important;
}

.w-10 {
  width: 10% !important;
}

.w-30 {
  width: 30% !important;
  min-height: 100% !important;
  height: 100% !important;
}

/* Hide default checkbox */
.custom-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #4FAEAF; /* Checkbox border color */
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  background-color: white;
}

/* When checkbox is checked */
.custom-checkbox:checked {
  background-color: #4FAEAF; /* Checkbox fill color */
  border-color: #4FAEAF;
}

/* Add checkmark */
.custom-checkbox:checked::after {
  content: "✔";
  color: white;
  font-size: 14px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Disabled state */
.custom-checkbox:disabled {
  opacity: 0.1;
  cursor: not-allowed;
}

.scrollable-box{
  height: 500px !important;
  overflow-y: auto;
}
</style>

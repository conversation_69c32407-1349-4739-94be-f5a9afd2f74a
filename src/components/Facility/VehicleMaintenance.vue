<template>
  <v-dialog persistent v-model="maintenanceDialog" width="60%">
      <v-card>
        <v-card-text class="border-bottom mb-3">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon
                    class="text-2xl font-semibold"
                    text="Maintenance Log"
                    style="color: black"
                >
                </SvgIcon>
                <v-btn fab x-small class="shadow-0" @click="close">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <v-row class="mt-3">
            <div class="col-md-12">
              <table class="table border-collapse text-center">
                <thead>
                <tr class="opacity-70 tr-neon tr-rounded text-center">
                  <th>Name</th>
                  <th>Start Date</th>
                  <th>End Date</th>
                  <th>Start time</th>
                  <th>End Time</th>
                  <th>Author</th>
                  <th>Actions</th>
                </tr>
                </thead>
                <tbody v-if="logs && logs.length > 0">
                <tr
                    v-for="(log, index) in logs"
                    :key="index"
                    class="text-left"
                >
                  <td>
                    {{ log.name }}
                  </td>
                  <td>
                    {{ log.start_date | dateformat }}
                  </td>
                  <td>
                    {{ log.end_date | dateformat }}
                  </td>
                  <td>
                    {{ log.start_time | timeFormat }}
                  </td>
                  <td>
                    {{ log.end_time | timeFormat }}
                  </td>
                  <td>
                    {{ log.created_by? log.created_by.first_name+' '+log.created_by.last_name : 'N/A' }}
                  </td>
                  <td>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon
                            v-bind="attrs"
                            v-on="on"
                            @click="toggleMaintenance(log.id)"
                            :color="
                                `${log.status_id == 1 ? 'error' : 'success'}`
                              "
                        >mdi-{{
                            log.status_id == 1 ? "delete" : "refresh"
                          }}</v-icon
                        >
                      </template>
                      <span>{{
                          log.status_id == 1
                              ? "Deactivate Maintenance"
                              : "Activate Maintenance"
                        }}</span>
                    </v-tooltip>
                  </td>
                </tr>
                </tbody>
                <tbody v-else>
                <tr>
                  <td colspan="7" class="text-center">No records</td>
                </tr>
                </tbody>
              </table>
            </div>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text class="ma-2 white--text blue-color" @click="addMaintenance"
          >Add Maintenance</v-btn
          >
        </v-card-actions>
      </v-card>
    <v-dialog persistent v-model="addMaintenanceModal" width="30%">
      <v-form ref="form" autocomplete="off">
        <v-card>
          <v-card-text class="border-bottom mb-3">
            <div class="row pt-1 border-bottom">
              <div class="col-md-12">
                <div class="d-flex justify-space-between align-center mt-2">
                  <SvgIcon
                      class="text-2xl font-semibold"
                      text="Add Maintenance"
                      style="color: black"
                  >
                  </SvgIcon>
                  <v-btn fab x-small class="shadow-0" @click="closeAddMaintenance">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
            <v-row>
              <v-col lg="12" sm="12" md="12">
                <label for="">
                  Name
                </label>
                <v-text-field
                    v-model="maintenanceData.name"
                    :rules="[(v) => !!v || 'Name is required']"
                    background-color="#fff"
                    outlined
                    dense
                    hide-details="auto"
                    class="q-text-field shadow-0"
                    required
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <label for="">Description</label>
                <v-textarea
                    v-model="maintenanceData.description"
                    :rules="[(v) => !!v || 'Description is required']"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                    rows="4"
                    validate-on-blur
                ></v-textarea>
              </v-col>
              <v-col lg="6" sm="12" md="12">
                <label for="">
                  Start Date
                </label>
                <date-field
                    v-model="maintenanceData.start_date"
                    class-name="q-text-field shadow-0"
                    :dense="true"
                    :hide-details="true"
                    label=""
                    :rules="[(v) => !!v || 'Start date is required']"
                    :backFill="false"
                >
                </date-field>
              </v-col>
              <v-col lg="6" sm="12" md="12">
                <label for="">
                  End Date
                </label>
                <date-field
                    v-model="maintenanceData.end_date"
                    class-name="q-text-field shadow-0"
                    :dense="true"
                    :hide-details="true"
                    label=""
                    :rules="[(v) => !!v || 'End date is required']"
                    :backFill="false"
                >
                </date-field>
              </v-col>
              <v-col lg="6" sm="12" md="12">
                <label for="">
                  Start Time
                </label>
                <v-select
                    :items="totalTimes"
                    label="From*"
                    item-text="text"
                    item-value="value"
                    v-model="maintenanceData.start_time"
                    required
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    :rules="[(v) => !!v || 'From time is required']"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details
                ></v-select>
              </v-col>
              <v-col lg="6" sm="12" md="12">
                <label for="">
                  End Time
                </label>
                <v-select
                    :items="totalTimes"
                    label="To*"
                    item-text="text"
                    item-value="value"
                    v-model="maintenanceData.end_time"
                    required
                    outlined
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    :rules="[(v) => !!v || 'End time is required']"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details
                ></v-select>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn text class="ma-2 white--text blue-color" @click="save"
            >Save</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
  </v-dialog>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
import moment from "moment/moment";

export default {
  name: "VehicleMaintenance",
  components: {SvgIcon},
  props: ["maintenanceVehicleId", "maintenanceDialog"],
  watch: {
    maintenanceDialog: {
      immediate: true,
      handler(val) {
        if (val) {
          if (val) {
            this.getMaintenanceLog();
          }
        }
      },
    },
  },
  data() {
    return {
      logs:[],
      addMaintenanceModal: false,
      maintenanceData:{
        id:null,
        vehicle_id:null,
        name:'',
        description:'',
        start_date:moment().format("YYYY-MM-DD"),
        end_date:moment().add(1,'day').format("YYYY-MM-DD"),
        start_time:null,
        end_time:null,
      },
      totalTimes: this.generateTimeOptions(),
    }
  },
  methods: {
    generateTimeOptions() {
      const times = [];
      const interval = 15; // every 15 minutes

      for (let h = 0; h < 24; h++) {
        for (let m = 0; m < 60; m += interval) {
          const hour24 = h.toString().padStart(2, '0');
          const minute = m.toString().padStart(2, '0');
          const value = `${hour24}:${minute}:00`; // full 24-hour format with seconds

          // Convert to 12-hour display format
          let hour12 = h % 12 || 12; // 12 instead of 0
          const ampm = h < 12 ? 'AM' : 'PM';
          const text = `${hour12}:${minute.padStart(2, '0')} ${ampm}`;

          times.push({ text, value });
        }
      }

      return times;
    },
    close(){
      this.$emit("close");
    },
    addMaintenance(){
      this.addMaintenanceModal = true;
    },
    closeAddMaintenance(){
      this.$refs.form.resetValidation();
      this.maintenanceData = {
            id:null,
            vehicle_id:null,
            name:'',
            description:'',
            start_date:moment().format("YYYY-MM-DD"),
            end_date:moment().add(1,'days').format("YYYY-MM-DD"),
            start_time:moment().format("YYYY-MM-DD"),
            end_time:'',
      };
      this.addMaintenanceModal = false;
    },
    save(){
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      this.showLoader('Saving...');
      this.maintenanceData.vehicle_id = this.maintenanceVehicleId;
      this.$http.post(`venues/mylaps/vehicles/maintenance`,this.maintenanceData)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.getMaintenanceLog();
              this.closeAddMaintenance();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(() => {
        this.hideLoader();
      })
    },
    toggleMaintenance(id){
      this.showLoader('Changing...');
      this.$http.post(`venues/mylaps/vehicles/maintenance/toggle/${id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.getMaintenanceLog();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(() => {
        this.hideLoader();
      })
    },
    getMaintenanceLog(){
      this.showLoader('Loading logs');
      this.$http.get(`venues/mylaps/vehicles/maintenance/${this.maintenanceVehicleId}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.logs = response.data.data;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(() => {
            this.hideLoader();
      })
    }
  }
}
</script>


<style scoped>

</style>
<template>
  <v-dialog
    persistent
    v-model="isEnableProductCategory"
    scrollable
    @input="close"
    width="600px"
  >
    <v-row class="mt-8">
      <v-col cols="12" lg="12" md="12">
        <v-form ref="cform">
          <div class="titles">Tags</div>
          <v-row>
            <v-col lg="4" md="6" sm="12" cols="12" style="position: relative">
              <v-card class="shadow-2">
                <v-container>
                  <v-row>
                    <v-col cols="12" md="12" sm="12" class="pb-0">
                      <label for=""> Tag name </label>
                      <v-text-field
                        outlined
                        background-color="#fff"
                        light
                        v-model="transponder.name"
                        dense
                        hide-details="auto"
                        class="q-text-field shadow-0"
                        required
                        :rules="[(v) => !!v || 'Tag name is required']"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <label for=""> Category </label>
                      <v-select
                        v-model="transponder.category_id"
                        :items="transponderCategories"
                        item-text="name"
                        item-value="id"
                        :menu-props="{ bottom: true, offsetY: true }"
                        background-color="#fff"
                        outlined
                        dense
                        hide-details="auto"
                        class="q-text-field shadow-0"
                        required
                      ></v-select>
                    </v-col>

                    <v-col cols="12" md="6" sm="12">
                      <label for=""> Color Code </label>
                      <v-text-field
                        outlined
                        v-model="transponder.color_code"
                        background-color="#fff"
                        light
                        dense
                        hide-details="auto"
                        class="q-text-field shadow-0 color-picker"
                        required
                        :rules="[(v) => !!v || 'Color code is required']"
                      >
                        <template v-slot:append>
                          <v-menu
                            top
                            nudge-bottom="105"
                            nudge-left="16"
                            :close-on-content-click="false"
                          >
                            <template v-slot:activator="{ on }">
                              <div :style="swatchStyle(cIndex)" v-on="on" />
                            </template>
                            <v-card>
                              <v-card-text class="pa-0">
                                <v-color-picker
                                  v-model="transponder.color_code"
                                  flat
                                  :swatches="swatches"
                                  show-swatches
                                />
                              </v-card-text>
                            </v-card>
                          </v-menu>
                        </template>
                      </v-text-field>
                    </v-col>
                    <v-col md="4" class="pl-0">
                      <v-switch
                        class="mx-4 my-0"
                        dense
                        hide-details="auto"
                        :false-value="14"
                        :true-value="1"
                        label="Active"
                        required
                        v-model="code.status_id"
                      ></v-switch>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>

              <v-tooltip bottom v-if="colorCodes.length > 1">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    color="red"
                    dark
                    elevation="0"
                    fab
                    v-bind="attrs"
                    x-small
                    absolute
                    top
                    style="top: -5px"
                    right
                    @click="deleteCode(cIndex)"
                    v-on="on"
                  >
                    <DeleteIcon />
                  </v-btn>
                </template>
                Delete
              </v-tooltip>
            </v-col>
          </v-row>
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="blue-color"
                  fab
                  x-small
                  dark
                  @click="addColorCode()"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add new code
            </v-tooltip>
          </div>
          <div style="float: right">
            <v-btn
              class="white--text blue-color"
              @click="saveColorCodes($event)"
              >Update
            </v-btn>
          </div>
        </v-form>
      </v-col>
      <v-spacer></v-spacer>
    </v-row>

    <v-dialog v-model="categoriesModal" persistent max-width="600">
      <v-card class="">
        <v-card-title class="headline">
          <h3 class="success-heading">Tag Categories</h3>
        </v-card-title>
        <v-card-text>
          <v-row class="ma-0">
            <v-col
              cols="12"
              v-for="(category, index) in categories"
              :key="index"
            >
              <v-row class="ma-0">
                <v-col cols="10">
                  <v-text-field
                    outlined
                    background-color="#fff"
                    light
                    hide-details
                    v-model="category.name"
                    label="Category name*"
                    :rules="[(v) => !!v || 'Category name is required']"
                  ></v-text-field>
                </v-col>
                <v-col cols="2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        class="del_btn"
                        color="red"
                        @click="deleteCategory(index)"
                        x-small
                        dark
                        fab
                      >
                        <v-icon small>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                    Delete
                  </v-tooltip>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <div class="add_btn">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  color="blue-color"
                  fab
                  x-small
                  dark
                  @click="addCategory()"
                >
                  <v-icon>mdi-plus-circle</v-icon>
                </v-btn>
              </template>
              Add new tag
            </v-tooltip>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="darken-1"
            class="ma-2 white--text blue-color"
            text
            @click="closeCategoriesModal"
            >Close
          </v-btn>

          <v-btn
            color=" darken-1"
            class="ma-2 white--text teal-color"
            @click="saveCategories"
            text
            >Save
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-dialog>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";

export default {
  components: { SvgIcon },
  props: ["isEnableProductCategory", "venueServiceId", "productCategories"],
  data() {
    return {
      deleted_categories: [],
      transponder: {},
      transponderCategories: [],
      colorCodes: [
        {
          name: null,
          status_id: 1,
          category_id: null,
          color_code: "#00AAAAFF",
        },
      ],
      swatches: [
        ["#FF0000", "#AA0000", "#550000"],
        ["#FFFF00", "#AAAA00", "#555500"],
        ["#00FF00", "#00AA00", "#005500"],
        ["#00FFFF", "#00AAAA", "#005555"],
        ["#0000FF", "#0000AA", "#000055"],
      ],
    };
  },
  computed: {},
  mounted() {},
  methods: {
    close() {
      this.$emit("close");
    },
    save() {
      this.$emit("save");
    },
    saveProductCategory() {
      if (!this.$refs.category_form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (!this.venueServiceId) {
        this.showError("Please add new service");
        return;
      }
      let categories = this.productCategories.map((x) => {
        let obj = {};
        obj.name = x.name;
        x.id ? (obj.id = x.id) : "";
        return obj;
      });
      this.$http
        .post(`venues/facilities/categories/${this.venueServiceId}`, {
          categories: categories,
          deleted_categories: this.deleted_categories,
        })
        .then((response) => {
          if (response.status == 200) {
            this.$store.dispatch(
              "loadProductCategoriesByVenueServiceId",
              this.venueServiceId
            );
            this.save();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    swatchStyle(cIndex) {
      const { color_code, menu } = this.colorCodes[cIndex];
      return {
        backgroundColor: color_code,
        cursor: "pointer",
        height: "30px",
        width: "30px",
        borderRadius: menu ? "50%" : "4px",
        transition: "border-radius 200ms ease-in-out",
      };
    },
    addNewProductCategory() {
      this.productCategories.push({ name: null });
    },
    deleteProductCategory(index) {
      if (this.productCategories[index] && this.productCategories[index].id) {
        this.deleted_categories.push(this.productCategories[index].id);
      }
      this.$emit("delete", index);
    },
  },
};
</script>
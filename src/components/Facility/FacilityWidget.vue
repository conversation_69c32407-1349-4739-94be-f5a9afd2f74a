<template>
  <v-card class="shadow-0  bordered facility_widget"  @click="viewFacility(id)" >
    <v-card-text >
      <v-row justify="center" class="border-bottom " dense>
        <v-col class="text-center d-flex justify-space-between">
          <span v-if="capacity>=1"/>
          <TimeIcon v-else/>
          <p class="text-xl black-text font-bold">{{ name }}</p>
          <div class="d-flex justify-space-between gap-x-2">


            <LightningBoltIcon  v-if="isConfigurationEnableBooking == 1" :class="is_public ? '':'fill-red'"/>
<!--            <v-tooltip bottom>-->
<!--              <template v-slot:activator="{ on }">-->
<!--                <v-btn class="ml-3" x-small @click.stop="duplicateFacility(id)" icon  v-on="on">-->
<!--                  <CopyIcon/>-->
<!--                </v-btn>-->
<!--              </template>-->
<!--              <span>Duplicate this facility</span>-->
<!--            </v-tooltip>-->

            <v-menu
                content-class="q-menu"
                offset-y
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                    class="text_capitalize px-0 no-hover-effect"
                    elevation="0"
                    height="24"
                    style="background-color: transparent; min-width: 24px !important; "
                    v-bind="attrs"
                    v-on="on"
                >
                  <DotsIcon height="24" width="24"/>
                </v-btn>
              </template>
              <v-list class="pointer">
                <v-list-item v-if="checkWritePermission($modules.facility.management.slug)"
                             @click.stop="edit(id)">
                  <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
                    <template #icon>
                      <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
                    </template>
                  </SvgIcon>
                </v-list-item>
                <v-list-item
                    v-if="checkWritePermission($modules.facility.management.slug)"
                    @click.stop="duplicateFacility(id)">
                  <SvgIcon class="font-medium text-sm gap-x-2" text="Duplicate">
                    <template #icon>
                      <CopyIcon stroke="#141B34"/>
                                    </template>
                  </SvgIcon>
                </v-list-item>
                <v-list-item
                    v-if="hasChildVenues && checkWritePermission($modules.facility.child_facility.slug)"
                    @click.stop="duplicateToChildFacility(id)">
                  <SvgIcon class="font-medium text-sm gap-x-2" text="Copy to venue">
                    <template #icon>
                      <CopyToChildIcon stroke="#141B34"/>
                    </template>
                  </SvgIcon>
                </v-list-item>
                <v-list-item v-if="checkWritePermission($modules.facility.management.slug)"
                             @click.stop="deleteGround(id)">
                  <SvgIcon class="font-medium text-sm gap-x-2 text-red" text="Delete Facility">
                    <template #icon>
                      <DeleteIcon stroke="#E50000"/>
                    </template>
                  </SvgIcon>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>


        </v-col>
      </v-row>

      <v-row  dense class="mt-4 align-center">
        <v-col md="5">
          <simple-image
              :image="image"
              :defaultImage="'ground'"
              style="border-radius: 8px"
          ></simple-image>
        </v-col>

        <v-col  md="7">
          <div class="member_details pl-2">
            <div class="d-flex justify-space-between  stats_col">
              <p class="text-base font-normal text-dark-gray ma-0 pa-0">Surface:</p>
              <p class="text-base font-normal text-dark-gray ma-0 pa-0">Configuration:</p>

            </div>
            <div class="d-flex justify-space-between stats_col ">
              <p class="text-base black-text font-medium"> {{ getGameFormation() || "--" }}</p>
              <p class="text-base blue-text font-medium ">  {{ facility_surface != null ? facility_surface.name : "--" }}</p>
            </div>

<!--            <div class="d-flex justify-space-between stats_col ">-->
<!--              <p class="text-base font-normal text-dark-gray">Booking Type:</p>-->
<!--              <p class="text-base black-text font-medium"> {{ capacity>=1 ? "Capacity " :"Time " }}</p>-->
<!--            </div>-->

<!--            <div class="d-flex justify-space-between stats_col ">-->
<!--              <p class="text-base font-normal text-dark-gray">Is Public:</p>-->
<!--              <p class="text-base black-text font-medium"> {{ is_public ? "Yes " :"No " }}</p>-->
<!--            </div>-->


          </div>
          <v-simple-table
              dense
              class=" dependencies "
              height="100"
              fixed-header

          >
            <template v-slot:default>
              <thead class="" >
                <tr class="opacity-70 tr-neon ">
                  <th class="" style="font-size: 10px !important;">Dependency</th>
                  <th class="" style="font-size: 10px !important;">Service</th>
                </tr>
              </thead>
              <tbody               v-if="child_facilities.length || parent_facilities.length"
              >
              <tr v-for="item in child_facilities" :key="`c_${item.id}`">
                <td class="text-xxs">{{ item.name }}</td>
                <td class="text-xxs">
                  {{ item.facility_services[0].venue_service.service.name }}
                </td>
              </tr>
              <tr v-for="item in parent_facilities" :key="`p_${item.id}`">
                <td class="text-xxs"> {{ item.name }}</td>
                <td class="text-xxs">
                  {{ item.facility_services[0].venue_service.service.name }}
                </td>
              </tr>
              </tbody>
              <tbody v-else>
                <tr >
                  <td colspan="2" rowspan="2" class="text-center nodepend">No Dependencies</td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-col>

      </v-row>
    </v-card-text>

    <v-dialog
        v-model="copyChildDialogue"
        persistent
        max-width="500"
        @input="closeCopyChildDialogue"
    >
      <v-card>
        <v-card-title class="border-bottom px-5">
          <div class=" w-full">
            <div class="d-flex justify-space-between align-center">
              <p class="mb-0 font-medium">
                Copying '{{ name }}'
              </p>
              <v-btn class="shadow-0" fab x-small @click="closeCopyChildDialogue">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </v-card-title>
        <v-card-text class="pa-5 border-bottom">
          <label for="">Select Target Venue(s) </label>
          <v-autocomplete
              :items="venues"
              v-model="targetVenueIds"
              item-value="id"
              item-text="name"
              outlined
              multiple
              :rules="[(v) => v.length > 0 || 'Venue is required']"
              background-color="#fff"
              class="q-autocomplete shadow-0"
              hide-details="auto"
              dense
          >
            <template v-slot:prepend-item>
              <v-list-item ripple @click="toggleVenueSelect()">
                <v-list-item-action>
                  <v-icon :color="targetVenueIds.length > 0 ? 'teal darken-4' : ''">{{
                      getVenueServiceIcon()
                    }}</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                  <v-list-item-title>All</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
            <template
                v-if="targetVenueIds.length === venues.length"
                v-slot:selection="{ index }"
            >
              <span v-if="index === 0">All Venues</span>
            </template>
            <template v-else v-slot:selection="{ item, index }">
              <span v-if="index === 0">{{ item.name }}</span>
              <span v-if="index === 1">, ...</span>
            </template>
          </v-autocomplete>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2 text-capitalize" text @click="closeCopyChildDialogue">No</v-btn>
          <v-btn class="ma-2 white--text blue-color shadow-0" @click="confirmCopyChildDialogue">Yes</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-card>
</template>

<script>
import SimpleImage from "@/components/Image/SimpleImage.vue";
import LightningBoltIcon from "@/assets/images/facilities/lightning-bolt.svg";
import TimeIcon from "@/assets/images/facilities/time.svg";
import CopyIcon from '@/assets/images/misc/copy-icon.svg'
import CopyToChildIcon from '@/assets/images/misc/copy-child.svg'
import SvgIcon from "@/components/Image/SvgIcon.vue";
import EditIcon from "@/assets/images/tables/edit.svg";
import DotsIcon from "@/assets/images/misc/dots.svg";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";

export default {
  components: {DeleteIcon, DotsIcon, EditIcon, SvgIcon, SimpleImage,LightningBoltIcon,CopyIcon,CopyToChildIcon,TimeIcon},
  props: {
    name: { type: String, default: "" },
    type: { type: String, default: "" },
    id: { type: Number },
    image: { type: String },
    facility_surface: { type: Object, default: () => {} },
    game_formations: { type: Array, default: () => [] },
    capacity: { type: Number, default: 0 },
    venueServiceId: { type: Number },
    child_facilities: { type: Array, default: () => [] },
    parent_facilities: { type: Array, default: () => [] },
    is_public: { type: Number, default: 0 },
    isConfigurationEnableBooking: { type: Number, default: 0 },
    isGameFormationEnabled: { type: Number, default: 0 },
    isSurfaceEnabled:{type: Number, default: 0},
  },
  computed: {
    hasChildVenues() {
      return this.$store.getters.venueInfo.sub_venues;
    },
    venues(){
      const subVenues = this.$store.getters.getSubVenues?.data || [];
      const currentVenueId = this.$store.getters.userInfo?.venue_id;

      return subVenues.filter(venue => venue.id !== currentVenueId);
    },
  },
  data(){
    return {
      copyChildDialogue: false,
      parentFacilityId: null,
      parentVenueServiceId: null,
      targetVenueIds:[],
      // venue:[],
    }
  },
  methods: {
    toggleVenueSelect() {
      this.$nextTick(() => {
        if (this.targetVenueIds.length == this.venues.length) {
          this.targetVenueIds = [];
        } else {
          this.targetVenueIds = this.venues.map((item) => item.id);
        }
      });
    },
    getVenueServiceIcon() {
      if (this.targetVenueIds.length == 0) return "mdi-checkbox-blank-outline";
      if (this.venues.length == this.targetVenueIds.length)
        return "mdi-close-box";
      return "mdi-minus-box";
    },
    closeCopyChildDialogue(){
      this.copyChildDialogue = false;
      this.parentFacilityId = null;
      this.parentVenueServiceId = null;
      this.targetVenueIds = [];
    },
    confirmCopyChildDialogue(){
      let data = {
        parent_facility_id:this.parentFacilityId,
        parent_venue_service_id:this.parentVenueServiceId,
        target_venue_ids:this.targetVenueIds,
      };

      this.showLoader("Copying...")
      this.$http
          .post(`venues/facilities/copy-to-child`, data)
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.showSuccess(response.data.message);
              this.$emit('reload');
              this.closeCopyChildDialogue();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          })
          .finally(() => {
            this.hideLoader()
          });
    },
    // deleteGround(id) {
    //   this.confirmModel = {
    //     id: id,
    //     title: `Do you want to delete this facility?`,
    //     description:
    //         "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
    //     type: "delete_facility",
    //   };
    // },
    edit() {
      this.$router.push({
        name: "FacilityForm",
        params: {
          data: btoa(
            JSON.stringify({
              venue_service_id: this.venueServiceId,
              facility_id: this.id,
            })
          ),
        },
      });
    },
    duplicateToChildFacility(id){
      this.copyChildDialogue = true;
      this.parentFacilityId = id;
      this.parentVenueServiceId = this.venueServiceId;
      this.targetVenueIds = [];
    },
    duplicateFacility(id){
      this.showLoader("Duplicating...")
      this.$http
          .post(`venues/facilities/duplicate`, {facility_id:id})
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.showSuccess(response.data.message);
              this.$emit('reload');
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          })
          .finally(() => {
            this.hideLoader()
          });

    },
    viewFacility() {
      this.$router.push({
        name: "ViewFacility",
        params: { data: btoa(this.id) },
      });
    },
    deleteGround(id) {
      this.$emit("remove", id);
    },
    getGameFormation() {
      if (this.game_formations.length > 0) {
        return this.game_formations.map((item) => item.name).join(", ");
      }
      return "--";
    },
  },
};
</script>

<style scoped>

.ground_det_table {
  color: rgb(235, 235, 235);
  width: 100%;
}
.ground_det_table tr td {
  padding: 3px;
}

.ground_det_table td:first-child {
  color: #b0ada8;
}
.ground_card {
  min-height: 200px;
}


.ground_head {
  text-align: center;
  border-radius: 12px;
  width: 310px;
  margin: auto;
}

.scroll {
  overflow-x: hidden;
  overflow-y: auto;
  padding: 15px;
  max-height: 93px;
  width: 100%;
}

.view_more {
  width: 10%;
}

.pricing_option .titledet {
  width: 40%;
}

.pricing_option .timedet {
  width: 30%;
}

.pricing_option .price {
  width: 20%;
}

.g_subheading {
  width: 100%;
  text-align: left;
  padding: 0 0 5px 7px;
  color: #fff;
}

.price_option_container {
  width: 100%;
}

.ground_Img {
  margin: 10px;
  width: 90%;
  border-radius: 4px;
}
.dependencies {
  color: black;
  background-color: #F8FAFB !important;
  thead{
    border: none !important;
    border-bottom: none !important;
    th{
      font-size: 10px !important;
    }
  }
  tbody {
    tr:hover {
      background-color: transparent !important;
    }
  }
}
.dependencies thead th {
  background-color: #F8FAFB !important;
  color: black !important;
  font-style: normal !important;
  font-weight: 500;
  line-height: normal;;
  box-shadow: inset 0 -1px 0 white !important;
}


.dependencies tbody td {
  color: #333;
  font-size: 9px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}


.facility_widget{
  border-radius: 8px;

}

.facility_widget:hover{
  border: 1px solid rgba(17, 42, 70, 0.5);
  box-shadow: 0 8px 24px 0  rgba(70, 76, 136, 0.20) !important;
}

.fill-red{
  fill: red !important;
}

.nodepend{
  color: #A6A6A6;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
</style>

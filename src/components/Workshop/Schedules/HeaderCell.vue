<template>
  <v-sheet
      color="#E9F1F6"
      dark
      width="160"
      height="60px"
      class="title flex-grow-0 flex-shrink-0 text-center pb-1 ts-header-cell"
      style="position: relative; color:black; font-weight: 600"
  >
    <div :class="`header_name ${name}`" :title="name">
      {{ name }}
    </div>
  </v-sheet>
</template>
<script>
export default {
  props: {
    name: { type: String, default: "Pitch Name" },
  },
};
</script>
<style>
.ts-header-cell{
  .c-booking-btn {
    width: 180px;
    font-weight: 400;
    font-size: 10px;
    line-height: normal;
  }
  .c-booking-btn span {
    width: 50% !important;
    display: inline-block;
    font-weight: 600;
  }
  .header_name {
    padding-top: 12px;
    font-size: 14px;
  }
  .header_name.Time {
    padding-top: 20px;
    text-align: center;
  }
}

</style>
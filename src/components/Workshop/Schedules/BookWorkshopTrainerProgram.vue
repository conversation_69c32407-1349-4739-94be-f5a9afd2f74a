<template>
  <v-dialog v-model="addParticipantDialog" scrollable persistent width="60%">
    <v-form ref="form" >
      <v-card v-if="workshop">
        <v-card-text class="">
          <div class="row pt-1">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2 mb-2">
                <SvgIcon class="font-16 font-semibold" text="Add New Reservation" style="color: black"></SvgIcon>
                <div class="d-flex gap-x-1">
                    <v-autocomplete
                        v-if="(promotions.length > 0 && prorateDetails.price) || (promotions.length > 0 && editFlag) || (promotions.length > 0 && package_type == 2)"
                        :items="[{ name: 'None', promotion_code: null }, ...promotions]"
                        item-text="name"
                        label="Promotions"
                        item-value="promotion_code"
                        v-model="registerForm.promotion_code"
                        outlined
                        @change="verifyBenefit('promotion')"
                        dense
                        hide-details="auto"
                        class="q-autocomplete shadow-0"
                        :readonly="discountApplied && editFlag"
                    >
                    </v-autocomplete>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex justify-center align-center pa-3 booking-form-headline mt-2" style="color: #4FAEAF;background-color:#EEF7F7;margin-left: -16px; margin-right: -16px;">
            <div class="pitch font-16"><span class="font-semibold">Service:</span> BasketBall</div>
            <div class="ml-2 font-16">-
              <span class="font-semibold">Total Price:</span> {{ workshopProduct.total_price | toCurrency }}
              <span v-if="workshopProduct.discount!= null && workshopProduct.price !== workshopProduct.discount.actual_price" class="text-decoration-line-through">
                {{ workshopProduct.discount.actual_price | toCurrency }}
              </span>
            </div>
          </div>
          <v-card class="bordered rounded-2 shadow-0 mt-2 program-card">
            <v-row class="customer-heading-bg">
              <div class="col-md-4 col-sm-12"><div class="font-semibold text-base text-blue font-14 text-left">Reservation details</div></div>
              <div class="col-md-4 col-sm-12"><div class="font-12 text-blue text-center">Date: {{ startDate | dateformatDDMMYY }}</div></div>
              <div class="col-md-4 col-sm-12"><div class="font-12 text-blue text-right">Academy Name: {{ workshop? workshop.name : '' }}</div></div>
            </v-row>
            <v-card-text  v-if="workshopData">
              <v-row dense align="center">
                <v-col cols="12" md="4" sm="12">
                  <label>Program*</label>
                  <v-text-field
                      readonly
                      v-model="workshopData.program_name"
                      outlined
                      background-color="#fff"
                      class="q-text-field shadow-0"
                      hide-details="auto"
                      dense
                      disabled
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="12">
                  <label>Product*</label>
                  <v-select
                      v-model="workshopProduct"
                      :rules="[(v) => !!v.product_id || 'Product is required']"
                      :items="productAndSchedules.pricing"
                      item-text="name"
                      item-value="product_id"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      required
                      :readonly="editFlag"
                      return-object
                      background-color="#fff"
                      @change="selectPackageType(),programChange(), checkProrate()"
                      class="q-autocomplete shadow-0"
                      hide-details="auto"
                      dense
                      validate-on-blur
                  ></v-select>
                </v-col>
                <v-col cols="12" md="4" sm="12">
                  <label>Trainer Name</label>
                  <v-text-field
                      readonly
                      v-model="trainer.name"
                      outlined
                      disabled
                      background-color="#fff"
                      class="q-text-field shadow-0"
                      hide-details="auto"
                      dense
                      validate-on-blur
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row dense align="center">
                <v-col cols="12" md="4" sm="6" class="pb-0 pt-0">
                  <label>Class start date*</label>
                  <date-field
                      :backFill="checkBackfillPermission($modules.workshops.schedule.slug)"
                      v-model="startDate"
                      label=""
                      :readonly="package_type === 3"
                      :rules="[(v) => !!v || 'Start date is required']"
                      @change="programChange(), checkProrate()"
                      :minDate="programStartDate"
                      :maxDate="programEndDate"
                      class-name="q-text-field shadow-0"
                      :hide-details="true"
                      :dense="true"
                  >
                  </date-field>
                </v-col>
<!--                <v-col cols="12" md="2" sm="6" class="pb-0 pt-0">-->
<!--                  <label for="">Start Time</label>-->
<!--                  <v-select-->
<!--                      label=""-->
<!--                      outlined-->
<!--                      :menu-props="{ bottom: true, offsetY: true }"-->
<!--                      background-color="#fff"-->
<!--                      item-text="formatted"-->
<!--                      item-value="time"-->
<!--                      dense-->
<!--                      hide-details="auto"-->
<!--                      class="q-autocomplete shadow-0"-->
<!--                  >-->
<!--                  </v-select>-->
<!--                </v-col>-->
<!--                <v-col cols="12" md="2" sm="6" class="pb-0 pt-0">-->
<!--                  <label for="">End Time</label>-->
<!--                  <v-select-->
<!--                      label=""-->
<!--                      outlined-->
<!--                      :menu-props="{ bottom: true, offsetY: true }"-->
<!--                      background-color="#fff"-->
<!--                      item-text="formatted"-->
<!--                      item-value="time"-->
<!--                      dense-->
<!--                      hide-details="auto"-->
<!--                      class="q-autocomplete shadow-0"-->
<!--                  >-->
<!--                  </v-select>-->
<!--                </v-col>-->
                <v-col cols="12" md="2" sm="6" class="pb-0 pt-0" v-if="workshopProduct && workshopProduct.package_type && package_type === 2">
                  <label for="">Weekdays</label>
                  <v-select
                      v-model="selectedWeekDays"
                      :items="availableDays"
                      item-value="value"
                      item-text="label"
                      multiple
                      outlined
                      @change="updateTimeslots"
                      dense
                      hide-details="auto"
                      class="q-autocomplete shadow-0"
                  ></v-select>
                </v-col>
                <v-col md="12">
                </v-col>
                <v-col cols="12">

                  <div class="d-flex gap-x-5 flex-wrap">
                    <span
                        class="d-flex align-center"
                        v-for="(slot) in trainingSlots"
                        :key="slot.id"
                    >
                      <v-checkbox
                          :ripple="false"
                          :value="isChecked(slot)"
                          @change="toggleSelection(slot)"
                      />
                      {{ slot.formatted_date }} {{ slot.start_time }}
                    </span>
                  </div>
                </v-col>
<!--                <v-col cols="12" md="2" sm="6" class="pb-0 pt-0">-->
<!--                  <label for="">Repeat</label>-->
<!--                  <v-select-->
<!--                      label=""-->
<!--                      outlined-->
<!--                      :menu-props="{ bottom: true, offsetY: true }"-->
<!--                      background-color="#fff"-->
<!--                      item-text="formatted"-->
<!--                      item-value="time"-->
<!--                      dense-->
<!--                      hide-details="auto"-->
<!--                      class="q-autocomplete shadow-0"-->
<!--                  >-->
<!--                  </v-select>-->
<!--                </v-col>-->
<!--                <v-col cols="12" md="4" sm="6">-->
<!--                  <label>Price</label>-->
<!--                  <v-text-field-->
<!--                      readonly-->
<!--                      v-model="workshopProduct.price"-->
<!--                      outlined-->
<!--                      :disabled="!workshopProduct.price"-->
<!--                      background-color="#fff"-->
<!--                      append="Discount"-->
<!--                      :prefix="currencyCode"-->
<!--                      class="q-text-field shadow-0"-->
<!--                      hide-details="auto"-->
<!--                      dense-->
<!--                      validate-on-blur-->
<!--                  >-->
<!--                    <template v-slot:append v-if="workshopProduct.discount">-->
<!--                      <div class="text-decoration-line-through">{{ currencyCode }} {{ workshopProduct.discount.actual_price }}</div>-->
<!--                    </template>-->
<!--                  </v-text-field>-->
<!--                </v-col>-->
                <v-col cols="12" md="4" sm="6" v-if="levels.length > 0">
                  <label>Level</label>
                  <v-select
                      v-model="level_id"
                      :items="levels"
                      item-text="name"
                      item-value="id"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                      class="q-autocomplete shadow-0"
                      hide-details="auto"
                      dense
                      validate-on-blur
                  ></v-select>
                </v-col>
                <v-col cols="12" md="4" sm="6" v-if="checkReadPermission($modules.salesTeam.management.slug) && salesTeams.length > 0" class="pb-0 pt-0">
                  <label>Sales Team</label>
                  <v-select
                      v-model="registerForm.sales_team_id"
                      :items="salesTeams"
                      item-text="name"
                      item-value="id"
                      outlined
                      :menu-props="{ bottom: true, offsetY: true }"
                      background-color="#fff"
                      class="q-autocomplete shadow-0"
                      hide-details="auto"
                      dense
                      validate-on-blur
                  ></v-select>
                </v-col>
                <template v-if="workshopProduct && workshopProduct.package_type && package_type === 1 && workshop.workshop_type_id === 1">
                  <v-col sm="12" class="pb-0 pt-0" v-for="(dateRange, drIndex) in productAndSchedules.date_ranges" :key="drIndex">
                    <div>
                      <div class="titles mt-5">Date Range: {{ dateRange.start_date | dateformat }} - {{ dateRange.end_date | dateformat }}</div>
                      <v-card
                          outlined
                          class="mt-2"
                          v-for="(days, index) in dateRange.selectedDays"
                          :key="index"
                          :disabled="dateRange.end_date < startDate ? true : false"
                      >
                        <v-card-text>
                          <v-row sm="12">
                            <v-col sm="6">
                              <label>Day*</label>
                              <v-select
                                  :disabled="dateRange.end_date < startDate ? true : false"
                                  v-model="days.day"
                                  :items="dateRange.weekdays"
                                  item-text="name"
                                  item-value="id"
                                  outlined
                                  :menu-props="{ bottom: true, offsetY: true }"
                                  return-object
                                  @change="daysChange(drIndex, index)"
                                  background-color="#fff"
                                  class="q-autocomplete shadow-0"
                                  hide-details="auto"
                                  dense
                                  validate-on-blur
                              ></v-select>
                            </v-col>
                            <v-col>
                              <label>Select Time slot</label>
                              <v-select
                                  :disabled="dateRange.end_date < startDate ? true : false"
                                  v-model="days.workshop_schedule_id"
                                  :items="days.day.time"
                                  item-text="name"
                                  item-value="workshop_schedule_id"
                                  outlined
                                  :menu-props="{ bottom: true, offsetY: true }"
                                  background-color="#fff"
                                  @change="checkProrate"
                                  class="q-autocomplete shadow-0"
                                  hide-details="auto"
                                  dense
                                  validate-on-blur
                                  :rules=" dateRange.end_date < startDate ? [] : [(v) => !!v || 'Time Slot is required']"
                              >
                                <template slot="item" slot-scope="{ item, on, attrs }">
                                  <v-list-item ripple v-bind="attrs" v-on="on" :disabled="checkStartDatePassFromEndDateOfClass(item)">
                                    <v-list-item-content>
                                      <v-list-item-title>{{ item.name }}</v-list-item-title>
                                      <v-list-item-subtitle>
                                        <div class="text-trancate" style="max-width: 100px">{{ item.facility_name || "NA" }}</div>
                                        <div class="text-trancate mt-1" style="max-width: 100px">{{ item.start_date | dateformat }} -to- {{ item.end_date | dateformat }}</div>
                                      </v-list-item-subtitle>
                                    </v-list-item-content>
                                    <v-list-item-action>
                                      <v-tooltip bottom>
                                        <template v-slot:activator="{ on, attrs }">
                                          <v-btn v-bind="attrs" v-on="on" fab x-small icon><v-icon>mdi-information</v-icon></v-btn>
                                        </template>
                                        Trainers :- {{ item.trainer || "NA" }}
                                      </v-tooltip>
                                    </v-list-item-action>
                                  </v-list-item>
                                </template>
                              </v-select>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                      <div class="add_btn" v-if="checkAddButtonStatus(drIndex) &&  startDate <= dateRange.end_date">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn v-bind="attrs" v-on="on" color="blue-color" fab x-small dark @click="addDay(drIndex)">
                              <v-icon>mdi-plus-circle</v-icon>
                            </v-btn>
                          </template>
                          Add Day
                        </v-tooltip>
                      </div>
                    </div>
                  </v-col>
                </template>
              </v-row>
              <v-row v-if="prorateDetails.dates && prorateDetails.dates.length && package_type == 1" dense align="center">
                <v-alert type="info" outlined text style="margin: 5px auto !important">
                  Customer will be able to attend {{ prorateDetails.available_slots }}/{{ prorateDetails.total_slots }}
                  classes which will start from {{ prorateDetails.dates[0].selected_date | dateformat }} and end
                  by {{ prorateDetails.dates[prorateDetails.dates.length - 1].selected_date | dateformat }}. <br/>
                  <b>Fee: {{ prorateDetails.price | toCurrency }}</b>
                  <span class="text-decoration-line-through" v-if="prorateDetails.product.price > prorateDetails.price">
                    {{ prorateDetails.product.price | toCurrency }}
                  </span>
                  <div class="mt-2">
                    <b>Occurrences: </b>
                    <span v-for="(date, index) in prorateDetails.dates" :key="index">
                      <span v-if="index == 0">{{ date.selected_date | dateformat }}</span>
                      <span v-else-if="index == prorateDetails.dates.length - 1">and {{ date.selected_date | dateformat }}</span>
                      <span v-else>, {{ date.selected_date | dateformat }}</span>
                    </span>
                  </div>
                </v-alert>
            </v-row>
            </v-card-text>
          </v-card>
          <v-card class="bordered rounded-2 shadow-0 mt-2 customer-card">
            <v-row class="">
              <div class="col-md-12">
                <div class="customer-heading-bg">
                  <div class="font-semibold text-base text-blue font-14">Customer details</div>
                </div>
              </div>
            </v-row>
            <v-card-text>
              <v-row dense align="center">
                <v-col md="12">
                  <v-row dense>
                    <v-col cols="12" md="4" sm="12">
                      <label>Mobile Number*</label>
                      <v-mobile-search
                          v-model="registerForm.search"
                          :selected="registerForm.mobile"
                          @updateCustomer="setCustomerData($event)"

                          ref="mobile"
                          hide-details="auto"
                          class-name1="q-text-field shadow-0"
                          class-name2="q-text-field shadow-0 mobile_auto_complete_hide_anchor"
                          background-color=""
                          :dense="true"
                          label=""
                          :show-label="false"
                      ></v-mobile-search>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <label>Customer Name*</label>
                      <v-name-search
                          :selected="registerForm.name"
                          :mobile="registerForm.mobile"
                          :email="registerForm.email"
                          v-model="registerForm.nameSearch"
                          @updateCustomer="setCustomerData($event)"
                          class-name="q-text-field shadow-0"
                          :dense="true"
                          hide-details="auto"
                          label=""
                      ></v-name-search>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <label>Email*</label>
                      <v-text-field
                          v-model="registerForm.email"
                          outlined
                          background-color="#fff"
                          required
                          placeholder="Email"
                          :rules="[
                            (v) => !!v || 'E-mail is required',
                            (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                          ]"
                          class="q-text-field shadow-0"
                          dense
                          hide-details="auto"
                          validate-on-blur
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="4" sm="6" v-if="field.gender.is_visible">
                  <label>Gender {{field.gender.is_required ? '*' : ''}}</label>
                  <v-radio-group v-model="registerForm.gender" class="mt-0 gender-radio" :rules="genderRule()" hide-details="auto" row>
                    <v-radio label="Male" value="Male"></v-radio>
                    <v-radio label="Female" value="Female"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12" md="4" sm="6" v-if="customerAgeRange && field.dob.is_visible">
                  <label>Age Group</label>
                  <v-select
                      v-if="customerAgeRange"
                      v-model="registerForm.age_group"
                      :items="ageRanges"
                      item-text="name"
                      item-value="id"
                      :menu-props="{ bottom: true, offsetY: true }"
                      outlined
                      background-color="#fff"
                      class="q-autocomplete shadow-0"
                      dense
                      hide-details="auto"
                      validate-on-blur
                  ></v-select>
                </v-col>
                <v-col cols="12" md="4" sm="6" v-else-if="field.dob.is_visible">
                  <label>Date Of Birth</label>
                  <date-of-birth-field v-model="registerForm.dob" :dense="true"></date-of-birth-field>
                </v-col>
                <v-col cols="12" md="4" sm="6" v-if="field.nationality.is_visible">
                  <label>Nationality</label>
                  <v-autocomplete
                      v-model="registerForm.country_id"
                      :items="countries"
                      item-value="id"
                      item-text="name"
                      outlined
                      background-color="#fff"
                      class="q-autocomplete shadow-0"
                      dense
                      hide-details="auto"
                      validate-on-blur
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12" sm="6" md="4" v-if="field.tag.is_visible">
                  <label>Tags</label>
                  <v-select
                      :items="tags"
                      :placeholder="`Tags`"
                      outlined
                      item-value="id"
                      item-text="name"
                      :menu-props="{ bottom: true, offsetY: true }"
                      v-model="registerForm.customer_tag"
                      background-color="#fff"
                      class="q-autocomplete shadow-0"
                      dense
                      hide-details="auto"
                      validate-on-blur
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="6" md="4" v-if="field.idProof.is_visible">
                  <label>Document</label>
                  <v-file-input
                      :label="registerForm.document.id != null ? '' : 'Document'"
                      v-model="registerForm.document.file"
                      prepend-inner-icon="mdi-paperclip"
                      prepend-icon
                      outlined
                      background-color="#fff"
                      class="q-text-field shadow-0"
                      dense
                      hide-details="auto"
                  >
                    <template v-slot:label>
                      <span v-if="!registerForm.document.id">Select Document file</span>
                      <span v-if=" registerForm.document.id && !registerForm.document.file" class="font-weight-bold">
                        <span style="width: 70%; display: inline-block" class="text-truncate">{{ registerForm.document.original_file_name }}</span>
                        <span style="width: 20%; display: inline-block" class="text-truncate">.{{ registerForm.document.original_file_name.split(".")[registerForm.document.original_file_name.split(".").length - 1] }}</span>
                      </span>
                    </template>
                    <template v-slot:prepend-inner>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on }">
                          <v-icon color="cyan" v-if=" registerForm.document.id && !registerForm.document.file" @click="openFile(registerForm.document.file_path)" v-on="on">mdi-download-box</v-icon>
                          <v-icon v-else v-on="on">mdi-image</v-icon>
                        </template>
                        <span v-if="registerForm.document.file_path">Download image</span>
                        <span v-else>Upload Image</span>
                      </v-tooltip>
                    </template>
                  </v-file-input>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col md="12">
                  <div class="d-flex gap-x-5">
                    <span class="d-flex align-center"><v-checkbox :ripple="false" v-model="registerForm.opt_marketing"/> Opt In Marketing</span>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
          <v-row class="mt-2 " v-if="workshop.documents.length > 0">
            <div class="col-md-12">
              <div class="d-flex align-center justify-space-between">
                <div class="font-semibold text-base text-blue">Documents</div>
              </div>
            </div>
          </v-row>
          <v-card class="shadow-0 bordered mt-2" v-if="workshop.documents.length > 0">
            <v-card-text>
              <v-row>
                <v-col md="2" sm="2" v-for="document in workshop.documents" :key="document.id">
                  <div align="center" @click="openFile(document.file_path)" style="cursor: pointer">
                    <v-btn large text>
                      <v-icon large>mdi-file-document</v-icon>
                    </v-btn>
                    <div class="title">{{ document.name }}</div>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
          <v-row class="mt-2 " v-if="documentFields.length > 0">
            <div class="col-md-12">
              <div class="d-flex align-center justify-space-between">
                <div class="font-semibold text-base text-blue">Documents</div>
              </div>
            </div>
          </v-row>
          <v-card class="shadow-0 bordered mt-2" elevation="0" v-if="documentFields.length">
            <v-row>
              <v-col cols="12" sm="12" md="12" v-for="(documentField, index) in documentFields" :key="index">
                <v-file-input
                    :label="documentField.name"
                    v-model="documentField.file"
                    prepend-inner-icon="mdi-paperclip"
                    prepend-icon
                    outlined
                    background-color="#fff"
                    :rules="documentUploadRule(documentField)"
                    dense
                    hide-details="auto"
                    class="q-text-field shadow-0"
                >
                  <template v-slot:prepend-inner>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on }">
                        <v-icon
                            color="cyan"
                            v-if=" documentField.document_id && documentField.file_path"
                            @click="openFile(documentField.file_path)"
                            v-on="on"
                        >
                          mdi-download-box
                        </v-icon>
                        <v-icon v-else v-on="on"> mdi-paperclip</v-icon>
                      </template>
                      <span v-if=" documentField.document_id && documentField.file_path">Download uploaded file</span>
                      <span v-else>Upload Document</span>
                    </v-tooltip>
                  </template>

                  <template v-slot:label>
                    <span v-if="!documentField.isEdit">{{ documentField.name }}</span>
                    <span v-if="documentField.isEdit == true && documentField.file == null && documentField.original_file_name" class="font-weight-bold">
                      <span style="width: 70%; display: inline-block" class="text-truncate">{{ documentField.original_file_name }}</span>
                      <span style="width: 20%; display: inline-block" class="text-truncate">.
                        {{ documentField.original_file_name.split(".")[documentField.original_file_name.split(".").length - 1] }}
                      </span>
                    </span>
                  </template>
                  <template v-slot:append v-if="documentField.uploaded_file_path">
                    <v-btn
                        @click="openFile(documentField.uploaded_file_path)"
                        class="text-truncate"
                        depressed
                        color="primary"
                        text
                        style="transform: translate(0, -22%)"
                    >
                      Download {{ documentField.name }}
                    </v-btn>
                  </template>
                </v-file-input>
              </v-col>
            </v-row>
          </v-card>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="" class="ma-2" text @click="close">Close</v-btn>
          <v-btn @click="save" color=" darken-1" class="ma-2 white--text blue-color">{{ editFlag ? "Update" : "Confirm Reservation" }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>
<script>
import moment from "moment";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DateOfBirthField from "@/components/Fields/DateOfBirthField.vue";
// import {pack} from "core-js/internals/ieee754";
export default {
  components: {DateOfBirthField, SvgIcon},
  props: {
    addParticipantDialog: {
      type: Boolean,
      default: false,
    },
    workshop_id:{ type: Number, default: null },
    program_id: {type: Number, default: null},
    program_name:{type: String, default:null},
    start_date:{type: String, default:null},
    end_date:{type: String, default:null},
    trainer:{type: Object, default: null},
    workshopData:{type: Object,default:null},
    date:{ type: String, default: null},
  },
  data() {
    return {
      trainingSlots:[],
      weekdayNames: [
        { value: 1, text: "Monday" },
        { value: 2, text: "Tuesday" },
        { value: 4, text: "Wednesday" },
        { value: 8, text: "Thursday" },
        { value: 16, text: "Friday" },
        { value: 32, text: "Saturday" },
        { value: 64, text: "Sunday" },
      ],
      // Selected weekdays from multiselect
      selectedWeekDays: [],
      field: {
        mobile: {
          slug: process.env.VUE_APP_MOBILE_FIELD,
          is_visible: true,
          is_required: true,
        },
        name: {
          slug: process.env.VUE_APP_NAME_FIELD,
          is_visible: true,
          is_required: true,
        },
        email: {
          slug: process.env.VUE_APP_EMAIL_FIELD,
          is_visible: true,
          is_required: true,
        },
        gender: {
          slug: process.env.VUE_APP_GENDER_FIELD,
          is_visible: true,
          is_required: false,
        },
        dob: {
          slug: process.env.VUE_APP_DOB_FIELD,
          is_visible: true,
          is_required: false,
        },
        nationality: {
          slug: process.env.VUE_APP_NATIONALITY_FIELD,
          is_visible: true,
          is_required: false,
        },
        idProof: {
          slug: process.env.VUE_APP_ID_FIELD,
          is_visible: false,
          is_required: false,
        },
        image: {
          slug: process.env.VUE_APP_IMAGE_FIELD,
          is_visible: true,
          is_required: false,
        },
        tag: {
          slug: process.env.VUE_APP_TAG_FIELD,
          is_visible: false,
          is_required: false,
        },
        documentFields: [],
      },
      field_configurations:[],
      workshop: null,
      workshopPrograms: [],
      productAndSchedules: {
        products: [],
      },
      workshopBookingId: null,
      registerForm: {document: {id: null}, opt_marketing: false},
      editFlag: false,
      programSelected: null,
      workshopProduct: {
        price: null,
      },
      selectedDays: [{day: {time: []}}],
      levels: [],
      schedules: [],
      weekdays: [],
      level_id: null,
      prorateDetails: {},
      discountApplied: false,
      startDate: moment().format("YYYY-MM-DD"),
      documentFields: [],
      package_type: 1,
      selected_dates:[],
      programStartDate: moment().format("YYYY-MM-DD"),
      programEndDate: moment().format("YYYY-MM-DD"),
    };
  },
  watch: {
    selected_dates:{
      immediate:true,
      async handler(val, oldVal){
        console.log(`----------------------------------------`,JSON.stringify(val),JSON.stringify(oldVal))
      }
    },
    addParticipantDialog: {
      immediate: true,
      async handler(val) {
        if (val) {
          console.log("val",val);
          console.log("date",this.date);
          this.startDate = this.date;
          this.workshop = null
          this.reset();
          this.prorateDetails = {};
          console.log("this.workshopData");
          console.log(this.workshopData);
          if (this.workshopData.workshop_id) {
            await this.getWorkshopDetails();
            this.programSelected = this.workshopData.wp_id;
            this.programChange();
          }
          if (this.workshop) {
            let date = moment(this.workshop.start_date, "YYYY-MM-DD").isBefore(moment())? moment().format("YYYY-MM-DD"): this.workshop.start_date;
            this.$store.dispatch("loadPromotions", {
              date: date,
              venue_service_id: this.workshop.venue_service_id,
              product_type: "Academy",
            });
            this.workshopProduct = {price: null};
            this.getLevels();
            this.getConfiguration()
          }
        }
      },
    },
  },
  mounted() {
    if (this.$store.getters.getDocumentTypes.status == false) {
      this.$store.dispatch("loadDocumentTypes");
    }
    if (this.$store.getters.getWeekdays.status == false) {
      this.$store.dispatch("loadWeekdays");
    }
    if (this.$store.getters.getTags.status == false) {
      this.$store.dispatch("loadTags");
    }
    this.checkPermission = this.checkExportPermission(
        this.$modules.salesTeam.dashboard.slug
    );
    if (this.checkPermission) {
      this.$store.dispatch("loadSalesTeams", "Academy");
      this.$forceUpdate();
    }
    if (this.$store.getters.getCustomerAgeRangeConfiguration.status == false) {
      this.$store.dispatch("LoadCustomerAgeRangeConfiguration");
    }
    if (this.$store.getters.getCustomerAgeRange.status == false) {
      this.$store.dispatch("LoadCustomerAgeRange");
    }
    console.log(this.getWeekDays)
  },
  computed: {
    customerAgeRange() {
      return this.$store.getters.getCustomerAgeRangeConfiguration.data;
    },
    ageRanges() {
      return this.$store.getters.getCustomerAgeRange.data;
    },
    tags() {
      return this.$store.getters.getTags.data;
    },
    documentTypes() {
      return this.$store.getters.getDocumentTypes.data;
    },
    countries() {
      return this.$store.getters.getCountries.data;
    },
    promotions() {
      return this.$store.getters.getPromotions.data;
    },
    getWeekDays() {
      return JSON.parse(JSON.stringify(this.$store.getters.getWeekdays.data));
    },
    salesTeams() {
      return this.$store.getters.getSalesTeams.data;
    },
    idProofTypes() {
      return this.$store.getters.getIdProofTypes.data;
    },


    availableDays() {
      if (!this.productAndSchedules?.date_ranges?.length) {
        return [];
      }
      const weekDays = this.$store.getters.getWeekdays.data.reduce((acc, day) => {
        acc[day.bit_value] = day.name;
        return acc;
      }, {});
      const weekdaysSet = new Set(
          this.productAndSchedules.date_ranges.flatMap(range =>
              range.schedules.flatMap(schedule => schedule.weekdays)
          )
      );
      return Array.from(weekdaysSet).map(day => ({
        value: day,
        label: weekDays[day],
      })).sort((a, b) => a.value - b.value);
    }

  },
  methods: {

    isChecked(slot) {
      return this.selected_dates.some(
          (selected) =>
              selected.wps_id === slot.wps_id &&
              selected.date === slot.date &&
              selected.bit_value === slot.bit_value
      );
    },
    toggleSelection(slot) {
      const index = this.selected_dates.findIndex(
          (selected) =>
              selected.wps_id === slot.wps_id &&
              selected.date === slot.date &&
              selected.bit_value === slot.bit_value
      );

      if (index !== -1) {
        // If the slot is already selected, remove it
        this.selected_dates.splice(index, 1);
      } else {
        // If the slot is not selected, add it
        this.selected_dates.push({
          wps_id: slot.wps_id,
          date: slot.date,
          bit_value: slot.bit_value,
        });
      }
    },
    updateTimeslots() {
      const timeslots = [];
      this.productAndSchedules?.date_ranges.forEach((dateRange) => {
        let start_date = moment(this.startDate, "YYYY-MM-DD");
        const range_start_date = moment(dateRange.start_date, "YYYY-MM-DD");

        if (start_date.isBefore(range_start_date)) {
          start_date = range_start_date;
        }

        const end_date = moment(dateRange.end_date, "YYYY-MM-DD").endOf("day");
        const totalDays = end_date.diff(start_date, "days") + 1;
        // Map bit_values to Moment.js `weekday()` values
        const bitValueToWeekday = {
          1: 0,  // Sunday
          2: 1,  // Monday
          4: 2,  // Tuesday
          8: 3,  // Wednesday
          16: 4, // Thursday
          32: 5, // Friday
          64: 6, // Saturday
        };

        this.selectedWeekDays.forEach((weekdayBitValue) => {
          const momentWeekday = bitValueToWeekday[weekdayBitValue];

          if (momentWeekday === undefined) {
            console.warn(`Invalid weekday bit value: ${weekdayBitValue}`);
            return;
          }

          // Generate dates for the selected weekday within the range
          for (let i = 0; i < totalDays; i++) {
            const currentDate = start_date.clone().add(i, "days");
            const dayOfWeek = currentDate.weekday(); // 0 (Sunday) to 6 (Saturday)

            if (dayOfWeek === momentWeekday) {
              dateRange.schedules.forEach((schedule) => {
                if (schedule.weekdays.includes(weekdayBitValue)) {
                  timeslots.push({
                    id:Math.floor(Date.now() / 1000) + Math.floor(Math.random() * 1000),
                    day: currentDate.format("ddd"),
                    bit_value:weekdayBitValue,
                    formatted_date: currentDate.format("ddd, Do MMM"),
                    start_time: moment(schedule.start_time, "HH:mm").format("h:mm A"),
                    end_time: moment(schedule.end_time, "HH:mm").format("h:mm A"),
                    wps_id:schedule.id,
                    date:currentDate.format("YYYY-MM-DD")
                  });
                }
              });
            }
          }
        });
      });

      console.log(timeslots, '---');
      this.trainingSlots = timeslots;
    },
    // pack,
    close() {
      this.documentFields = [];
      this.selected_dates = [];
      this.selectedWeekDays = [];
      this.$emit("close");
    },
    genderRule() {
      const rules = [];
      if (this.field.gender.is_required) {
        const rule = (v) => !!v || "Gender is required";
        rules.push(rule);
      }
      return rules;
    },

    reset() {
      this.workshop = null;
      this.editFlag = false;
      this.registerForm = {document: {id: null}, opt_marketing: false};
      this.workshopProduct = {
        price: null,
      };
      this.level_id = null;
      this.productAndSchedules = {
        products: [],
      };
      this.weekdays = [];
      this.selectedDays = [{day: {time: []}}];
      this.programSelected = null;
      setTimeout(() => {
        // this.$refs["image_upload"].cancel();
        this.$refs.form.resetValidation();
      });
    },
    checkAddButtonStatus(drIndex = null) {
      if (this.productAndSchedules.date_ranges[drIndex]) {
        if (this.productAndSchedules.date_ranges[drIndex].selectedDays) {
          return (this.productAndSchedules.date_ranges[drIndex].selectedDays.length < this.workshopProduct.frequency);
        } else {
          return false;
        }
      }
    },
    addDay(drIndex = null) {
      if (this.checkAddButtonStatus(drIndex)) {
        this.productAndSchedules.date_ranges[drIndex].selectedDays.push({day: {time: []},});
        this.$forceUpdate();
      }
    },
    getTitle(index) {
      let arr = [
        {index: 1, name: 'Periodic Slots'},
        {index: 2, name: 'Session Based'},
        {index: 3, name: 'Program Based'}
      ];
      return this.findNameByIndex(arr, index);
    },
    findNameByIndex(arr, index) {
      const item = arr.find(item => item.index === index);
      return item ? item.name : 'Generic';
    },
    selectPackageType() {
      this.package_type = this.workshopProduct.package_type;
      this.trainingSlots = [];
      this.selected_dates = [];
    },
    programChange() {

      this.trainingSlots = [];
      this.selected_dates = [];
      let program = this.workshop.programs.find((x) => x.id == this.programSelected);
      console.log("caling",program);
      this.setProgramStartAndEndDate(program.date_ranges);
      if (this.startDate < this.programStartDate) {
        this.showError("Program start date is " + this.programStartDate);
        return;
      } else if (this.startDate > this.programEndDate) {
        this.showError("Program end date is " + this.programEndDate);
        return;
      }
      this.productAndSchedules = {
        products: [],
        date_ranges: [],
      };
      this.productAndSchedules.date_ranges = program.date_ranges;
      let pricing = [];
      let groupedPackages = program.pricing.reduce((acc, pkg) => {
        // Find if the package_type_id already exists in the accumulator
        let group = acc.find(g => g.package_type === pkg.package_type);
        if (group) {
          // If it exists, add the package to the items array
          group.items.push(pkg);
        } else {
          // If it doesn't exist, create a new group and add the package to the items array
          acc.push({package_type: pkg.package_type, items: [pkg]});
        }
        return acc;
      }, []);
      groupedPackages.forEach((item) => {
        let title = this.getTitle(item.package_type);
        pricing.push({header: title});
        pricing.push({divider: true});
        item.items.forEach((pkg) => {
          pricing.push(pkg);
        });
      });
      // this.productAndSchedules.pricing = program.pricing.filter((prod) => prod.package_type == this.package_type);
      this.productAndSchedules.pricing = pricing;
      this.productAndSchedules.date_ranges.forEach((dateRange, drIndex) => {
        let weekDays = [];
        if (this.productAndSchedules.date_ranges[drIndex].weekdays) {
          this.productAndSchedules.date_ranges[drIndex].weekdays = null;
        }
        dateRange.schedules.forEach((schedule) => {
          schedule.weekdays.forEach((element) => {
            let findValue = this.getWeekDays.find((y) => y.bit_value === element);
            let obj = {
              trainer: schedule.trainer.map((trainer) => trainer.first_name + trainer.last_name).join(","),
              facility_name: schedule.facility_name ? schedule.facility_name : schedule.location,
              workshop_schedule_id: schedule.id,
              start_time: schedule.start_time,
              end_time: schedule.end_time,
              start_date: dateRange.start_date,
              end_date: dateRange.end_date,
              name: moment(schedule.start_time, "HH:mm").format("h:mm a") + " To " + moment(schedule.end_time, "HH:mm").format("h:mm a"),
            };
            let weekDaysIndex = weekDays.findIndex((x) => x.bit_value == element);
            if (weekDaysIndex !== -1) {
              weekDays[weekDaysIndex].time.push(obj);
            } else {
              findValue.time = [];
              findValue.time.push(obj);
              weekDays.push(findValue);
            }
          });
        });
        if (this.productAndSchedules.date_ranges[drIndex].weekdays) {
          this.productAndSchedules.date_ranges[drIndex].weekdays.push(JSON.parse(JSON.stringify(weekDays)));
        } else {
          this.productAndSchedules.date_ranges[drIndex].weekdays = {};
          this.productAndSchedules.date_ranges[drIndex].weekdays = JSON.parse(JSON.stringify(weekDays));
        }
        // if (!this.orderId) {
        this.productAndSchedules.date_ranges[drIndex].selectedDays = [
          {day: {time: []}},
        ];
        // }
      });
    },
    getLevels() {
      this.$http
          .get(`venues/general/color-codes/academy?venue_service_id=${this.workshop.venue_service_id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.levels = response.data.data;
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    setCustomerData(data) {
      if (!data.customer_id) {
        this.$set(this.registerForm, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.registerForm, "name", data.first_name);
      }

      if (
          this.registerForm.customer_id &&
          !data.customer_id &&
          this.registerForm.name != data.name &&
          this.registerForm.mobile != data.mobile
      ) {
        this.$set(this.registerForm, "mobile", null);
        this.registerForm.search = null;
        this.registerForm.nameSearch = null;
        this.$set(this.registerForm, "email", null);
        this.$set(this.registerForm, "gender", null);
        this.$set(this.registerForm, "name", null);
        this.$set(this.registerForm, "customer_id", null);
        this.$set(this.registerForm, "first_name", null);
        this.$set(this.registerForm, "image_path", null);
        this.$set(this.registerForm, "dob", null);
        this.$set(this.registerForm, "age_group", null);
        this.$set(this.registerForm, "country_id", null);
        this.$set(this.registerForm, "last_name", null);
        this.$set(this.registerForm, "customer_tag", null);
        this.$set(this.registerForm, "opt_marketing", false);
        this.$forceUpdate();
      }

      if (data.mobile) this.$set(this.registerForm, "mobile", data.mobile);
      if (data.email) this.$set(this.registerForm, "email", data.email);
      if (data.country_id) {
        this.$set(this.registerForm, "country_id", data.country_id);
      } else {
        this.$set(this.registerForm, "country_id", null);
      }
      if (data.gender) {
        this.$set(this.registerForm, "gender", data.gender);
      } else {
        this.$set(this.registerForm, "gender", null);
      }
      if (data.dob) {
        this.$set(this.registerForm, "dob", data.dob);
      } else {
        this.$set(this.registerForm, "dob", null);
      }
      if (data.age_group) {
        this.$set(this.registerForm, "age_group", data.age_group);
      } else {
        this.$set(this.registerForm, "age_group", null);
      }
      if (data.customer_tag) {
        this.$set(this.registerForm, "customer_tag", data.customer_tag);
      } else {
        this.$set(this.registerForm, "customer_tag", null);
      }
      if (data.name) this.$set(this.registerForm, "name", data.name);
      if (data.last_name) {
        this.$set(this.registerForm, "last_name", data.last_name);
      } else {
        this.$set(this.registerForm, "last_name", null);
      }
      if (data.first_name)
        this.$set(this.registerForm, "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.registerForm, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.registerForm, "image_path", data.image_path);
      } else {
        this.$set(this.registerForm, "image_path", null);
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.registerForm, "opt_marketing", true);
        } else {
          this.$set(this.registerForm, "opt_marketing", false);
        }
      }
      this.$forceUpdate();
    },
    save() {
      if (this.prorateDetails.available_slots == 0) {
        this.showError("No class available for this day. Please choose another day! ");
        return;
      }
      let checkAllSlotFill = false;
      if (this.package_type == 1 && this.workshop.workshop_type_id == 1) {
        this.productAndSchedules.date_ranges.forEach((dRange, drIndex) => {
          if (dRange.selectedDays && this.startDate <= dRange.end_date) {
            let last = dRange.selectedDays[dRange.selectedDays.length - 1];
            if (
                this.checkAddButtonStatus(drIndex) ||
                !last.day ||
                !last.day.bit_value ||
                !last.workshop_schedule_id
            ) {
              checkAllSlotFill = false;
              this.showError("Please select all schedules");
              return;
            } else {
              checkAllSlotFill = true;
            }
          }
        });
      } else {
        checkAllSlotFill = true;
      }
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (checkAllSlotFill) {
        let formData = new FormData();
        formData.append("workshop_id", this.workshop.id);
        formData.append("workshop_type_id", this.workshop.workshop_type_id);
        formData.append("workshop_program_id", this.programSelected);
        formData.append("product_id", this.workshopProduct.product_id);
        formData.append("package_type", this.package_type);
        if (this.level_id) {
          formData.append("level_id", this.level_id);
        }
        formData.append("venue_service_id", this.workshop.venue_service_id);
        if (this.package_type == 1 && this.workshop.workshop_type_id == 1) {
          this.productAndSchedules.date_ranges.forEach((dRange, dIndex) => {
            if (dRange.selectedDays && this.startDate <= dRange.end_date) {
              formData.append(`date_ranges[${dIndex}][date_range_id]`, dRange.id);
              formData.append(`date_ranges[${dIndex}][start_date]`, dRange.start_date);
              formData.append(`date_ranges[${dIndex}][end_date]`, dRange.end_date);
              dRange.selectedDays.forEach((el, index) => {
                formData.append(`date_ranges[${dIndex}][days][${index}][bit_value]`, el.day.bit_value);
                formData.append(`date_ranges[${dIndex}][days][${index}][workshop_schedule_id]`, el.workshop_schedule_id);
                formData.append(`date_ranges[${dIndex}][days][${index}][start_date]`, el.day.time.find((x) => x.workshop_schedule_id == el.workshop_schedule_id).start_date);
                formData.append(
                    `date_ranges[${dIndex}][days][${index}][end_date]`,
                    el.day.time.find((x) => x.workshop_schedule_id == el.workshop_schedule_id).end_date
                );
                if (this.editFlag && el.id) {
                  formData.append(`date_ranges[${dIndex}][days][${index}][id]`, el.id);
                }
              });
            }
          });
        }

        for (let key in this.registerForm) {
          if (this.registerForm[key] && typeof this.registerForm[key] != "object") {
            formData.append(key, this.registerForm[key]);
          } else if (key == "document" && this.registerForm[key].file) {
            formData.append(key, this.registerForm[key].file);
            if (this.registerForm[key].id) {
              formData.append("document_id", this.registerForm[key].id);
            }
          }
        }
        this.selected_dates.forEach((slot,key)=>{
          formData.append(`selected_date[${key}][date]`, slot?.date);
          formData.append(`selected_date[${key}][wps_id]`, slot?.wps_id);
          formData.append(`selected_date[${key}][bit_value]`, slot?.bit_value);
        })

        formData.append("opt_marketing", this.registerForm.opt_marketing);
        formData.append("start_date", this.startDate);
        let document = [];
        this.documentFields.forEach((documentField) => {
          if (documentField.file || documentField.isEdit) {
            document.push(documentField);
          }
        });

        document.forEach((element, index) => {
          formData.append(`documents[${index}][name]`, element.name);
          formData.append(`documents[${index}][id]`, element.id);
          if (element.isEdit) {
            formData.append(`documents[${index}][file_path]`, element.file_path);
            formData.append(`documents[${index}][document_id]`, element.document_id);
            formData.append(`documents[${index}][original_file_name]`, element.original_file_name);
          }
          if (element.file) {
            formData.append(`documents[${index}][file]`, element.file);
          }
        });
        if (this.registerForm.sales_team_id) {
          formData.append("sales_team_id", this.registerForm.sales_team_id);
        }
        this.showLoader("Saving..");
        this.$http
            .post(`venues/workshops/schedules/booking/v2`, formData, {
              headers: {"Content-Type": "multipart/form-data; boundary=${form._boundary}",},
            })
            .then((response) => {
              if (response.status == 200 && response.data.status) {
                const data = response.data.data;
                this.hideLoader();
                this.$emit("complete", {order_id: data.order_id, workshop_program_id: this.programSelected});
                this.close();
                if (this.editFlag) this.showSuccess("Update successfully");
                else this.showSuccess("Created schedule successfully");
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            });
      }
    },
    checkProrate() {
      // if (this.startDate < this.workshop.start_date) {
      //   // this.showError("Workshop start date is " + this.workshop.start_date);
      //   return;
      // }
      this.prorateDetails = {};
      let isProrateCheck = false;
      if (this.productAndSchedules.date_ranges && this.package_type == 1 && this.workshop.workshop_type_id == 1) {
        this.productAndSchedules.date_ranges.forEach((dRange, drIndex) => {
          if (dRange.selectedDays && this.startDate <= dRange.end_date) {
            let last = dRange.selectedDays[dRange.selectedDays.length - 1];
            if (
                this.checkAddButtonStatus(drIndex) ||
                !last.day ||
                !last.day.bit_value ||
                !last.workshop_schedule_id
            ) {
              isProrateCheck = false;
              return;
            } else {
              isProrateCheck = true;
            }
          }
        });
        if (isProrateCheck) {
          let formData = {};
          formData.workshop_program_id = this.programSelected;
          formData.product_id = this.workshopProduct.product_id;
          formData.start_date = this.startDate;
          formData.days = [];
          this.productAndSchedules.date_ranges.forEach((dRange) => {
            if (dRange.selectedDays) {
              dRange.selectedDays.forEach((el) => {
                if (el.day.bit_value && el.workshop_schedule_id) {
                  let day = {};
                  day.bit_value = el.day.bit_value;
                  day.date_range_id = dRange.id;
                  day.workshop_schedule_id = el.workshop_schedule_id;
                  day.start_date = el.day.time.find(
                      (x) => x.workshop_schedule_id == el.workshop_schedule_id
                  ).start_date;
                  day.end_date = el.day.time.find(
                      (x) => x.workshop_schedule_id == el.workshop_schedule_id
                  ).end_date;
                  formData.days.push(day);
                }
              });
            }
          });

          if (
              !formData.workshop_program_id ||
              !formData.product_id ||
              formData.days.length == 0
          )
            return;
          this.showLoader("Price Calculate ...");
          this.$http
              .post(`venues/workshops/schedules/booking/check`, formData)
              .then((response) => {
                if (response.status == 200 && response.data.status == true) {
                  const data = response.data.data;
                  this.prorateDetails = data;
                  if (data.product.allow_pro_rate == 1) {
                    this.workshopProduct.price = data.price;
                  } else {
                    this.prorateDetails.price = this.workshopProduct.price;
                  }
                  this.$forceUpdate();
                  if (this.registerForm.promotion_code) {
                    this.verifyBenefit("promotion");
                  }
                }
                this.hideLoader();
              })
              .catch((error) => {
                this.hideLoader();
                this.prorateDetails = {};
                this.errorChecker(error);
              });
        }
      }
    },
    verifyBenefit(type) {
      if (!this.workshopProduct.product_id) {
        this.showError("Please select the product");
        return;
      }
      let data = {
        products: [],
      };
      if (this.workshopProduct.discount) {
        this.workshopProduct.price = this.workshopProduct.discount.actual_price;
        delete this.workshopProduct.discount;
      }
      this.workshopProduct.workshop_id = this.workshop.id;
      this.workshopProduct.quantity = 1;
      if (type == "promotion") {
        data.promotion_code = this.registerForm.promotion_code;
        if (data.promotion_code == null) {
          this.discountApplied = false;
          return;
        }
      } else {
        data.card_number = this.registerForm.card_number;
      }
      if (this.registerForm.customer_id) {
        data.customer_id = this.registerForm.customer_id;
      }

      data.products = [this.workshopProduct];

      let url = "venues/benefits/verify";
      this.$http
          .post(url, data)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              if (data.discount) {
                this.workshopProduct = data.products[0];
                this.workshopProduct.product_id = parseInt(data.products[0].product_id);
                this.workshopProduct.name = data.products[0].name;
                this.workshopProduct.discount = data.discount;
                this.workshopProduct.price = data.price;
                console.log(this.workshopProduct);
                this.$forceUpdate();
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    daysChange(drIndex, index) {
      if (
          this.productAndSchedules.date_ranges[drIndex]["selectedDays"] &&
          this.productAndSchedules.date_ranges[drIndex]["selectedDays"][index].day
              .time.length == 1
      ) {
        this.productAndSchedules.date_ranges[drIndex]["selectedDays"][
            index
            ].workshop_schedule_id = this.productAndSchedules.date_ranges[drIndex][
            "selectedDays"
            ][index].day.time[0].workshop_schedule_id;
        this.$forceUpdate();
        this.checkProrate();
      } else {
        this.$forceUpdate();
        this.checkProrate();
      }
    },

    checkStartDatePassFromEndDateOfClass(item) {
      if (
          moment(this.startDate, "YYYY-MM-DD").isBefore(
              moment(item.end_date, "YYYY-MM-DD")
          )
      ) {
        return false; // return false means do not disable this slot
      } else if (
          moment(this.startDate, "YYYY-MM-DD").isSame(
              moment(item.end_date, "YYYY-MM-DD")
          )
      ) {
        // console.log("start date is equal for slot end date");
        let now = moment();
        let currentDate = now.format("YYYY-MM-DD");
        if (
            moment(currentDate, "YYYY-MM-DD").isSame(
                moment(item.end_date, "YYYY-MM-DD")
            )
        ) {
          // console.log("end date is equal for current date");
          let startDateTime = moment(
              this.startDate + " " + now.format("HH:mm"),
              "YYYY-MM-DD HH:mm"
          ).format("YYYY-MM-DD HH:mm");
          let endDateTime = moment(
              item.end_date + " " + item.end_time,
              "YYYY-MM-DD HH:mm"
          ).format("YYYY-MM-DD HH:mm");
          let result = !moment(startDateTime, "YYYY-MM-DD HH:mm").isBefore(
              moment(endDateTime, "YYYY-MM-DD HH:mm")
          );
          return result;
        } else {
          // console.log("current date is before for slot end date");
          return false;
        }
      } else {
        // console.log("start date is grater than end date slot");
        return true;
      }
    },
    setCardData(data) {
      if (!data.customer_id) {
        this.$set(this.registerForm, "customer_id", null);
      }

      if (!data.name && data.first_name) {
        this.$set(this.registerForm, "name", data.first_name);
      }

      if (!data.customer_id && this.registerForm.name != data.name) {
        this.$set(this.registerForm, "mobile", null);
        this.registerForm.search = null;
        this.registerForm.nameSearch = null;
        this.$set(this.registerForm, "email", null);
        this.$set(this.registerForm, "gender", null);
        this.$set(this.registerForm, "name", null);
        this.$set(this.registerForm, "customer_id", null);
        this.$set(this.registerForm, "first_name", null);
        this.$set(this.registerForm, "image_path", null);
        this.$set(this.registerForm, "dob", null);
        this.$set(this.registerForm, "age_group", null);
        this.$set(this.registerForm, "country_id", null);
        this.$set(this.registerForm, "last_name", null);
        this.$set(this.registerForm, "opt_marketing", false);
        this.$forceUpdate();
      }

      if (data.mobile) {
        this.$set(this.registerForm, "mobile", data.mobile);
      }
      if (data.email) this.$set(this.registerForm, "email", data.email);
      if (data.country_id) {
        this.$set(this.registerForm, "country_id", data.country_id);
      } else {
        this.$set(this.registerForm, "country_id", null);
      }
      if (data.country_id) {
        this.$set(this.registerForm, "id_proof_type_id", data.id_proof_type_id);
      }

      if (data.id_proof_number) {
        this.$set(this.registerForm, "id_proof_number", data.id_proof_number);
      }

      if (data.gender) {
        this.$set(this.registerForm, "gender", data.gender);
      } else {
        this.$set(this.registerForm, "gender", null);
      }
      if (data.dob) {
        this.$set(this.registerForm, "dob", data.dob);
      } else {
        this.$set(this.registerForm, "dob", null);
      }
      if (data.age_group) {
        this.$set(this.registerForm, "age_group", data.age_group);
      } else {
        this.$set(this.registerForm, "age_group", null);
      }

      if (data.image) {
        this.$set(this.registerForm, "image", data.image);
      }

      if (data.name) this.$set(this.registerForm, "name", data.name);
      if (data.last_name) {
        this.$set(this.registerForm, "last_name", data.last_name);
      } else {
        this.$set(this.registerForm, "last_name", null);
      }
      if (data.first_name)
        this.$set(this.registerForm, "first_name", data.first_name);
      if (data.customer_id)
        this.$set(this.registerForm, "customer_id", data.customer_id);
      if (data.image_path) {
        this.$set(this.registerForm, "image_path", data.image_path);
      } else {
        this.$set(this.registerForm, "image_path", null);
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.registerForm, "opt_marketing", true);
        } else {
          this.$set(this.registerForm, "opt_marketing", false);
        }
      }
      this.$forceUpdate();
    },
    getConfiguration() {
      this.$http
          .get(`venues/workshops/configuration`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              this.field_configurations=data.field_configurations;
              if (data.field_document_configurations && data.field_document_configurations.length !== 0) {
                this.documentFields = data.field_document_configurations;
              } else {
                this.documentFields = []
              }
              this.field_configurations.forEach( (field) => {
                if (field.slug === this.field.gender.slug) {
                  this.field.gender.is_visible = field.is_visible === 1;
                  this.field.gender.is_required = field.is_required === 1;
                } else if (field.slug === this.field.dob.slug) {
                  this.field.dob.is_visible = field.is_visible === 1;
                  this.field.dob.is_required = field.is_required === 1;
                } else if (field.slug === this.field.tag.slug) {
                  this.field.tag.is_visible = field.is_visible === 1;
                  this.field.tag.is_required = field.is_required === 1;
                } else if (field.slug === this.field.nationality.slug) {
                  this.field.nationality.is_visible = field.is_visible === 1;
                  this.field.nationality.is_required = field.is_required === 1;
                } else if (field.slug === this.field.idProof.slug) {
                  this.field.idProof.is_visible = field.is_visible === 1;
                  this.field.idProof.is_required = field.is_required === 1;
                }
              })
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },

    documentUploadRule(field) {
      const rules = [];
      if (field.is_required && !field.isEdit) {
        const rule = (v) => !!v || "Document is required";
        rules.push(rule);
      }
      return rules;
    },
    async getWorkshopDetails() {
      this.showLoader("Loading");
      await this.$http
          .get(`venues/workshops/details/${this.workshopData.workshop_id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.workshop = response.data.data;
              this.hideLoader();
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    setProgramStartAndEndDate(dateRanges){
      let startDate = dateRanges.reduce((min, range) =>
          min < range.start_date ? min : range.start_date, dateRanges[0].start_date);
      let endDate = dateRanges.reduce((max, range) =>
          max > range.end_date ? max : range.end_date, dateRanges[0].end_date);
      console.log("start: ",startDate);
      console.log("endDate: ",endDate);
      this.programStartDate = startDate;
      this.programEndDate = endDate;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep {
  .gender-radio {
    padding-top: 0 !important;

    .v-radio {
      display: flex;
      justify-content: center;
      border: 1px solid #dadada;
      border-radius: 0.25rem;
      flex-grow: 1;

      label {
        justify-content: center;
      }

      &.v-item--active {
        border: 1px solid #112A46;
        background-color: rgba(17, 42, 70, 0.1);
        color: #112A46;

        label {
          font-weight: 600 !important;
        }
      }
      &:last-child{
        margin-right: 0 !important;
      }
    }

    .v-label {
      min-width: 7.8rem;
      padding: 0.58rem;
    }

    .v-input--selection-controls__input {
      display: none !important;
    }
  }
}
.customer-card .customer-heading-bg,.program-card .customer-heading-bg{
  background-color: #F8FAFB;
  margin: 0px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.customer-card .customer-heading-bg{
  padding: 10px 9px 10px 15px;
}
.program-card .customer-heading-bg{
  padding: 0px 9px 0px 15px;
}

</style>

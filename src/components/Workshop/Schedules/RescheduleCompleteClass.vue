<template>
  <v-dialog v-model="rescheduleDialog" width="680px" scrollable persistent>
    <v-card class="fill-height" v-if="programData">
      <v-row class="bg-teal-light ma-0" dense>
        <v-col cols="12">
          <div class="text-center mt-2 white--text font-semibold" v-if="date">{{ date | dayDateFormat }}</div>
        </v-col>
      </v-row>
      <v-row class="bg-teal-light pl-4 pr-4 pb-4 ma-0" dense>
        <v-col cols="12" md="6" class="v-center">
          <div class="text-left white--text font-semibold">Reschedule</div>
        </v-col>
        <v-col cols="12" md="6" class="v-center">
          <div class="text-right white--text font-semibold">Program: {{ programData.name }}</div>
        </v-col>
      </v-row>
      <v-card-text v-if="reschedule">
        <v-row class="mt-0">
          <v-col sm="6" md="4">
            <date-field
              :backFill="checkBackfillPermission($modules.workshops.schedule.slug)"
              v-model="date"
              label="Reschedule date"
              background-color="#fff"
              class="q-autocomplete shadow-0"
              dense
              :minDate="programStartDate"
              :maxDate="programEndDate"
            >
            </date-field>
          </v-col>
          <v-col lg="4" md="4">
            <v-select
                v-model="reschedule.venue_service_id"
                label="Service*"
                :items="venueServices"
                item-text="name"
                item-value="venue_service_id"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                @change="getFacilities($event)"
                background-color="#fff"
                :rules="[(v) => !!v || 'Service is required']"
                width="200"
                required
                class="q-autocomplete shadow-0"
                hide-details="auto"
                dense
            ></v-select>
          </v-col>
          <v-col sm="4" md="4">
            <v-select
                v-model="reschedule.facility_id"
                item-value="id"
                item-text="name"
                :items="reschedule.facilities"
                label="Facility*"
                outlined
                clearable
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
                @change="getTimesByDuration"
                class="q-autocomplete shadow-0"
                hide-details="auto"
                dense
            />
          </v-col>
        </v-row>
        <v-row class="mt-0">
          <v-col lg="4" md="4">
            <v-select
                :items="timings"
                label="Start Time*"
                item-text="formatted"
                item-value="time"
                v-model="reschedule.start_time"
                :rules="[(v) => !!v || 'Start time is required']"
                validate-on-blur
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                background-color="#fff"
                @change="EndTimeValidator()"
                class="q-autocomplete shadow-0"
                hide-details="auto"
                dense
            ></v-select>
          </v-col>
          <v-col lg="4" md="4">
            <v-select
                :readonly="true"
                :items="timings"
                label="End Time*"
                item-text="formatted"
                item-value="time"
                v-model="reschedule.end_time"
                :rules="[(v) => !!v || 'End time is required']"
                outlined
                :menu-props="{ bottom: true, offsetY: true }"
                validate-on-blur
                background-color="#fff"
                class="q-autocomplete shadow-0"
                hide-details="auto"
                dense
            ></v-select>
          </v-col>
          <v-col lg="4" md="4" v-if="trainer">
            <v-text-field
                v-model="trainer.name"
                outlined
                background-color="#fff"
                required
                class="q-text-field shadow-0"
                dense
                hide-details="auto"
                validate-on-blur
                readonly
            ></v-text-field>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn class="shadow-0" @click="close">Close</v-btn>
        <v-btn dark class="teal-color" @click="rescheduleSlot()">Reschedule</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import moment from "moment";
export default {
  props: {
    rescheduleDialog: { type: Boolean, default: false },
    workshop_schedule_id: { type: Number, default: null },
    scheduleFromDate: { type: String, default: null },
    programId: { type: Number, default: null },
    venueServices:{type: Array, default:()=> []},
    programDuration: {type: Number, default: null },
    facilityId: {type: Number, default: null },
    trainer: { type: Object, default: null },
    slotData: { type: Object, default: null },
    selectedTimeslot: { type: Object,default: null}
  },
  components:{},
  data() {
    return {
      date: this.scheduleFromDate ? this.scheduleFromDate : null,
      times: [],
      timings:[],
      reschedule: {},
      programData: {},
      programStartDate: this.scheduleFromDate,
      programEndDate: this.scheduleFromDate,
    };
  },
  watch: {
    rescheduleDialog: {
      immediate: true,
      handler(val) {
        if (val) {
          if (this.programId && this.date) {
            console.log("ssss",this.selectedTimeslot);
            this.reschedule.facility_id = this.facilityId;
            this.reschedule.venue_service_id = this.slotData.venue_service_id;
            this.reschedule.start_time = this.slotData.start_time;
            this.reschedule.end_time = this.slotData.end_time;
            this.getWorkshopProgram();
            this.getFacilities(this.reschedule.venue_service_id);
          }
        }
      },
    },
    scheduleFromDate: {
      immediate: true,
      handler(val) {
        console.log("val watch",val);
        if (val) {
          this.date = val;
        }
      },
    },
  },
  mounted() {},
  computed: {},
  methods: {
    close() {
      this.$emit("close");
    },
    getWorkshopProgram(){
      this.$http.get(`venues/workshops/program?wp_id=${this.programId}`)
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              if (response.data.data) {
                this.programData = response.data.data;
                this.setProgramStartAndEndDate(response.data.data.date_ranges);
              }
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getTimesByDuration() {
      this.showLoader("Loading facility time...");
      let url = `venues/workshops/time-by-duration?duration=${this.programDuration}&venue_service_id=${this.reschedule.venue_service_id}`;
      if (this.reschedule.facility_id) {
        url += `&facility_id=${this.reschedule.facility_id}`;
      }
      this.$http
          .get(url)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.timings = [];
              this.timings = response.data.data;
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getFacilities(venueServiceId) {
      this.$http
          .get(`venues/facilities/short?venue_service_id=${venueServiceId}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.reschedule.facilities = response.data.data;
              this.getTimesByDuration();
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    EndTimeValidator() {
      const start_time = moment(this.reschedule.start_time, "HH:mm:ss");
      this.reschedule.end_time = moment(start_time)
          .add(this.programDuration, "minutes")
          .format("HH:mm:ss");
      const slotIndex = this.timings.findIndex((x) => x.time == this.reschedule.end_time);
      if (slotIndex == -1) {
        this.showError("Facility not available this time duration");
        this.reschedule.end_time = null;
      }
      this.$forceUpdate();
    },
    rescheduleSlot() {
      let rescheduleData = {
        program_schedule_id: this.slotData.schedule_id,
        from_date: this.slotData.start_date,
        to_date: this.date,
        start_time: this.reschedule.start_time,
        end_time: this.reschedule.end_time,
        trainer_id: this.trainer.id,
        facility_id: this.reschedule.facility_id,
        venue_service_id: this.reschedule.venue_service_id,
        current_start_time: this.slotData.start_time
      };
      if(this.slotData.reschedule_id && this.slotData.reschedule_id > 0){
        console.log("selectedTimeslot",this.selectedTimeslot);
        rescheduleData.reschedule_id = this.slotData.reschedule_id;
        rescheduleData.from_date = this.selectedTimeslot.from_date;
      }
      console.log("res data",rescheduleData);
      if (moment(this.date).isAfter(this.programEndDate)) {
        this.showError("Reschedule date is higher than program date");
        return true;
      }
      if( (this.facilityId === this.reschedule.facility_id) &&
          (this.slotData.venue_service_id === this.reschedule.venue_service_id) &&
          (this.slotData.start_date === this.date) &&
          (this.slotData.start_time === this.reschedule.start_time)
      ){
        this.showError("Please make any chanage for reschedule");
        return true;
      }
      this.showLoader("Rescheduling ...");
      this.$http.post(`venues/workshops/schedules/booking/reschedule`, rescheduleData)
        .then((response) => {
          if (response.status === 200 && response.data.status) {
            this.hideLoader();
            this.$emit("reschedule-done");
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    setProgramStartAndEndDate(dateRanges){
      let startDate = dateRanges.reduce((min, range) =>
          min < range.start_date ? min : range.start_date, dateRanges[0].start_date);
      let endDate = dateRanges.reduce((max, range) =>
          max > range.end_date ? max : range.end_date, dateRanges[0].end_date);
      this.programStartDate = startDate;
      this.programEndDate = endDate;
    },
  },
};
</script>

<style scoped>
.bg-teal-light{
  background-color: #4EAEAF;
}
</style>
<template>
  <v-dialog v-model="ttsDialog" scrollable persistent width="420px">
    <v-form ref="form">
      <v-card>
        <v-card-text class="">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon class="text-lg font-semibold" text="Transfer Trainer Session" style="color: black" >
                </SvgIcon>
                <v-btn  fab x-small class="shadow-0" @click="close">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <v-row class="mt-2" dense>
            <v-col cols="12" sm="12" md="12">
              <label>Transfer From</label>
              <v-text-field
                  v-if="trainer"
                  v-model="trainer.name"
                  outlined
                  background-color="#fff"
                  required
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  validate-on-blur
                  disabled
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="12" md="12">
              <label>Transfer To</label>
              <v-select
                  v-model="transferTo"
                  :rules="[(v) => !!v || 'Trainer is required']"
                  :items="filteredTrainer"
                  item-text="name"
                  item-value="id"
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  required
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  hide-details="auto"
                  dense
                  validate-on-blur
              ></v-select>
            </v-col>

          </v-row>
        </v-card-text>
        <v-divider class="mt-2"></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2" text  @click="close">Close</v-btn>
          <v-btn @click="transfer" color="darken-1" class="ma-2 white--text blue-color">Transfer</v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  components: {SvgIcon},
  props: {
    ttsDialog: {
      type: Boolean,
      default: false,
    },
    trainer:{type: Object, default: null},
    trainersList: {type: Array, default: () => []},
    slotData: { type: Object, default: null},
  },
  data() {
    return {
      transferTo: null,
      wps: null,
      filteredTrainer: [],
    };
  },
  watch: {
    ttsDialog: {
      immediate: true,
      handler(val) {
        console.log("dfddadada",this.trainer);
        if (val) {
          this.filteredTrainer = [];
          let ft = [];
          this.trainersList.forEach( (t) => {
            if(t.id != this.trainer.id) {
              ft.push(t);
            }
          })
          this.filteredTrainer = ft;
        }
      },
    },
  },
  computed: {
    venueServices() {
      return this.$store.getters.getVenueServices.data.filter((service) => service.name != "POS");
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },
    transfer() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      console.log("trainer_id", this.transferTo);
      console.log("trainer", this.trainer.id);
      console.log("slotData", this.slotData);
      if (!this.trainer.id || !this.transferTo) {
        this.showError("Select Trainer");
        return;
      }
      if(!this.slotData.schedule_id){
        this.showError("Schedule Id not found");
        return;
      }
      if(!this.slotData.venue_service_id){
        this.showError("Venue service Id not found");
        return;
      }
      this.showLoader("wait");
      let data = {
        trainer_id: this.trainer.id,
        transfer_to: this.transferTo,
        date: this.slotData.start_date,
        venue_service_id: this.slotData.venue_service_id,
        program_schedule_id:  this.slotData.schedule_id,
      };
      if(this.slotData.reschedule_id){
        data.reschedule_id = this.slotData.reschedule_id;
      }
      console.log(data);
      this.$http.post(`venues/workshops/trainers/session/transfer`, data)
        .then((response) => {
          if (response.status === 200 && response.data.status) {
            this.hideLoader();
            this.showSuccess("Session transferred successfully.");
            this.$emit('transferSuccess');
          }
        })
        .catch((error) => {
          this.hideLoader();
          this.errorChecker(error);
        });
    },
  }
};
</script>

<style></style>

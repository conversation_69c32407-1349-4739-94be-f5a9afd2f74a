<template>
  <v-card
      color="grey"
      :height="height"
      width="160"
      tile
      class="flex-grow-0 flex-shrink-0"
  >
    <template v-for="(slot,i) in timeSlots">
      <v-sheet
          block
          :height="slot.height"
          :class="`d-flex justify-center align-center time_${i}`"
          style="border-right: 1px solid rgba(0, 0, 0, 0.05);border-bottom: 1px solid #f3f3f3;"
          :key="`s-${i}`"
      >
        {{ slot.formated_start_time }} - {{ slot.formated_end_time }}
      </v-sheet>
      <!-- <v-divider :key="`d_${slot.id}`"></v-divider> -->
    </template>
  </v-card>
</template>

<script>
export default {
  props: {
    height: { type: Number, default: 4800 },
    timeSlots:{ type: Array, default: () => [] },
  },
  data() {
    return {
      timeData: [],
    };
  },
  created() {
    this.$store.subscribe((mutation) => {
      if (mutation.type === "HIGHLIGHT") {
        let element = this.$el.getElementsByClassName(
            `time_${mutation.payload}`
        )[0];
        if (element) {
          element.classList.add("highlight");
        }
      } else if (mutation.type === "REMOVE_HIGHLIGHT") {
        let element = this.$el.getElementsByClassName(
            `time_${mutation.payload}`
        )[0];
        if (element) {
          element.classList.remove("highlight");
        }
      }
    });
  },
};
</script>

<style>
.highlight {
  background-color: #009688 !important;
  font-weight: bold;
  color: #fff !important;
}
</style>
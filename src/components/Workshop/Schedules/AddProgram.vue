<template>
  <v-dialog v-model="addProgramDialog" scrollable persistent width="680px">
    <v-form ref="form">
      <v-card>
        <v-card-text class="">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon class="text-lg font-semibold" text="Add Program" style="color: black" >
                </SvgIcon>
                <v-btn  fab x-small class="shadow-0" @click="close">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <v-row class="mt-2" dense>
            <v-col cols="12" sm="6" md="6">
              <label>Trainer Name</label>
              <v-text-field
                  v-model="trainer.name"
                  outlined
                  background-color="#fff"
                  required
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  validate-on-blur
                  disabled
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="6">
              <label>Program Name *</label>
              <v-text-field
                  v-model="program.name"
                  outlined
                  background-color="#fff"
                  required
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  validate-on-blur
                  :rules="[(v) => !!v || 'Program is required',]"
              ></v-text-field>
            </v-col>
            <v-col cols="12"  sm="6" md="6">
              <label>Academy*</label>
              <v-autocomplete
                  :items="workshops"
                  item-text="name"
                  item-value="id"
                  v-model="workshop"
                  return-object
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  :rules="[(v) => !!v || 'Academy is required']"
                  required
                  class="q-autocomplete shadow-0"
                  hide-details="auto"
                  dense
                  validate-on-blur
                  @change="changeAcademy"
              >
              </v-autocomplete>
            </v-col>
            <v-col cols="12"  sm="6" md="6">
              <label>Product*</label>
              <v-select
                  v-model="selectedProduct"
                  :rules="[(v) => !!v || 'Product is required']"
                  :items="workshopProducts"
                  item-text="name"
                  item-value="id"
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  required
                  multiple
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  hide-details="auto"
                  dense
                  validate-on-blur
              ></v-select>
            </v-col>
          </v-row>
          <v-divider class="mt-2" v-if="workshop"></v-divider>
          <v-row class="mt-2" dense v-if="workshop">
            <v-col cols="12" md="4" sm="6">
              <label>Class Start Date*</label>
              <date-picker-field
                  v-model="program.date_ranges[0].start_date"
                  :backFill="checkBackfillPermission($modules.workshops.schedule.slug)"
                  :dense="true"
                  :hide-details="true"
                  :maxDate="workshop.end_date"
                  :minDate="workshop.start_date"
                  :rules="[validateStartDate]"
                  class-name="q-text-field shadow-0"
                  label=""
                  @change="checkBookingsExistsInFacility(0, 0)"
              />
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label>Class End Date*</label>
              <date-picker-field
                  v-model="program.date_ranges[0].end_date"
                  :backFill="checkBackfillPermission($modules.workshops.schedule.slug)"
                  :dense="true"
                  :hide-details="true"
                  :maxDate="workshop.end_date"
                  :minDate="program.date_ranges[0].start_date"
                  :rules="[validateEndDate]"
                  class-name="q-text-field shadow-0"
                  label=""
                  @change="checkBookingsExistsInFacility"
              />
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label>Duration*</label>
              <v-select
                  v-model="program.duration"
                  :items="[
                    { text: '15 min', value: 15 },
                    { text: '30 min', value: 30 },
                    { text: '45 min', value: 45 },
                    { text: '1 hr', value: 60 },
                    { text: '1 hr 30 min', value: 90 },
                    { text: '2 hr', value: 120 },
                    { text: '2 hr 30 min', value: 150 },
                    { text: '3 hr', value: 180 },
                    { text: '3 hr 30 min', value: 210 },
                    { text: '4 hr', value: 240 },
                  ]"
                  :menu-props="{ bottom: true, offsetY: true }"
                  :rules="[(v) => !!v || 'Duration is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  hint="Session duration (default 1 hr)"
                  outlined
                  required
                  @change="programDurationChange"
              ></v-select>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label>Days Applicable*</label>
              <v-autocomplete
                  v-model="program.date_ranges[0].schedules[0].weekdays"
                  :items="weekdays"
                  :rules="[  (v) =>  v.length > 0 || 'Days Applicable is required',]"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="name"
                  item-value="bit_value"
                  multiple
                  outlined
                  validate-on-blur
                  @change="checkBookingsExistsInFacility"
              >
                <template v-if="weekdays.length == program.date_ranges[0].schedules[0].weekdays.length" v-slot:selection="{ index }">
                  <span v-if="index == 0">All Days</span>
                </template>
                <template v-else v-slot:selection="{ item, index }">
                  <span v-if="index == 0">{{ item.name }}</span>
                  <span v-if="index == 1">,{{ item.name }}</span>
                  <span v-if="index == 2">, ...</span>
                </template>
                <template v-slot:prepend-item>
                  <v-list-item ripple @click="toggle">
                    <v-list-item-action>
                      <v-icon :color="program.date_ranges[0].schedules[0].weekdays.length >0? 'indigo darken-4': ''">
                        {{ getIcon()}}
                      </v-icon>
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title>All</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label>Ground Assignment – Service</label>
              <v-select
                  v-model="program.date_ranges[0].schedules[0].venue_service_id"
                  :items="venueServices"
                  :menu-props="{bottom: true,offsetY: true,}"
                  :rules="[(v) =>  !!v || 'Service is required',]"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="name"
                  item-value="venue_service_id"
                  outlined
                  required
                  @change="getFacilities($event),(program.date_ranges[0].schedules[0].is_external = false),getTimesByDuration()"
              ></v-select>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label >{{ program.date_ranges[0].schedules[0].is_external ? 'Ground Assignment - External Location' : 'Ground Assignment – Facility' }}</label>
              <v-select
                  v-model="program.date_ranges[0].schedules[0].facility_id"
                  :items="getServiceFacilities()"
                  :menu-props="{bottom: true,offsetY: true,}"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  clearable
                  dense
                  hide-details="auto"
                  item-text="name"
                  item-value="id"
                  outlined
                  @change="facilityChange('internal')"
              >
                <template v-slot:append-item>
                  <v-divider></v-divider>
                  <v-list-item ripple  @click="facilityChange('external')" >
                    <v-list-item-action>
                      <v-icon color="teal">mdi-open-in-new</v-icon>
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title >External</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label>Start Time*</label>
              <v-select
                  v-model="program.date_ranges[0].schedules[0].start_time"
                  :items="getServiceTimeSlot()"
                  :menu-props="{bottom: true, offsetY: true,}"
                  :rules="[(v) => !!v || 'Start time is required']"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="formatted"
                  item-value="time"
                  outlined
                  validate-on-blur
                  @change="endTimeValidator"
              ></v-select>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label>End Time*</label>
              <v-select
                  v-model="program.date_ranges[0].schedules[0].end_time"
                  :items="getServiceTimeSlot()"
                  :menu-props="{bottom: true,offsetY: true,}"
                  :readonly="true"
                  :rules="[(v) => !!v || 'Facility is not available for this time! Change start time ', ]"
                  background-color="#fff"
                  class="q-autocomplete shadow-0"
                  dense
                  hide-details="auto"
                  item-text="formatted"
                  item-value="time"
                  outlined
                  validate-on-blur
              ></v-select>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <label>Capacity*</label>
              <v-text-field
                  v-model="program.capacity"
                  :rules="[
                    (v) => !!v || 'Capacity is required',
                    (v) => !isNaN(v) || 'Quantity must be Number',
                  ]"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  outlined
                  required
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-switch
                  v-model="workshop.is_public"
                  :false-value="0"
                  :true-value="1"
                  class="my-0"
                  dense
                  hide-details="auto"
                  label="Sell Online"

              ></v-switch>
            </v-col>
          </v-row>
          <v-row>
            <v-col lg="12" md="12">
              <v-card v-if="program.date_ranges[0].schedules[0].is_external" flat outlined>
                <v-card-text>
                  <v-row dense>
                    <v-col class="pr-4" cols="12" md="12" sm="12">
                      <v-row dense no-gutters>
                        <v-col md="12">
                          <label>Location*</label>
                          <v-autocomplete
                              v-model="program.date_ranges[0].schedules[0].location"
                              :items="program.date_ranges[0].schedules[0].locationEntries"
                              :loading="program.date_ranges[0].schedules[0].isLoading"
                              :rules="[(v) =>!!v || 'Location is required',]"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              hide-no-data
                              item-text="value"
                              item-value="value"
                              outlined
                              @change="changeLocation"
                              @keyup="changelocationText($event.target.value)"
                          ></v-autocomplete>
                        </v-col>
                      </v-row>
                      <GmapMap
                          :zoom="12"
                          map-type-id="terrain"
                          style="
                                                      width: 100%;
                                                      height: 200px;
                                                    "
                          v-bind:center="{lat: parseFloat(program.date_ranges[0].schedules[0].latitude),lng: parseFloat(program.date_ranges[0].schedules[0].longitude),}"

                      >
                        <GmapMarker
                            ref="mapRef"
                            :clickable="true"
                            :draggable="true"
                            :position="{lat: parseFloat(program.date_ranges[0].schedules[0].latitude ),lng: parseFloat(program.date_ranges[0].schedules[0].longitude),}"

                        />
                      </GmapMap>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider class="mt-2"></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2" text  @click="close">Close</v-btn>
          <v-btn @click="save" color="darken-1" class="ma-2 white--text blue-color">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
import moment from "moment";
import {GetSuggestions, placeDetails} from "@/utils/placesUtils";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import {pack} from "core-js/internals/ieee754";
import DatePickerField from "@/components/Fields/DatePickerField.vue";

export default {
  components: {DatePickerField, SvgIcon},
  props: {
    addProgramDialog: {
      type: Boolean,
      default: false,
    },
    trainer:{type: Object, default: null},
  },
  data() {
    return {
      workshops: [],
      workshop: null,
      workshopProducts: [],
      selectedProduct: null,
      facilities: [],
      timeSlot: [],
      program:  {
          name: null,
          duration: null,
          capacity: null,
          date_ranges: [
            {
              start_date: null,
              end_date: null,
              schedules: [
                {
                  venue_service_id: null,
                  facility_id: null,
                  weekdays: [],
                  start_date: null,
                  end_date: null,
                  start_time: null,
                  end_time: null,
                  latitude: 24.45342255,
                  longitude: 54.35851069,
                  heatmap: null,
                  is_external: false,
                  autocompleteLocationModel: null,
                  location: null,
                  locationEntries: [],
                  isLoading: false,
                },
              ],
            },
          ]
      },
    };
  },
  watch: {
    addProgramDialog: {
      immediate: true,
      handler(val) {
        if (val) {
          this.reset();
          this.getWorkshops();
        }
      },
    },
  },
  mounted() {
    if (this.$store.getters.getDocumentTypes.status == false) {
      this.$store.dispatch("loadDocumentTypes");
    }
    if (this.$store.getters.getWeekdays.status == false) {
      this.$store.dispatch("loadWeekdays");
    }
    if (this.$store.getters.getTags.status == false) {
      this.$store.dispatch("loadTags");
    }
  },
  computed: {
    customerAgeRange() {
      return this.$store.getters.getCustomerAgeRangeConfiguration.data;
    },
    ageRanges() {
      return this.$store.getters.getCustomerAgeRange.data;
    },
    tags() {
      return this.$store.getters.getTags.data;
    },
    documentTypes() {
      return this.$store.getters.getDocumentTypes.data;
    },
    countries() {
      return this.$store.getters.getCountries.data;
    },
    promotions() {
      return this.$store.getters.getPromotions.data;
    },
    salesTeams() {
      return this.$store.getters.getSalesTeams.data;
    },
    weekdays() {
      return JSON.parse(JSON.stringify(this.$store.getters.getWeekdays.data));
    },
    venueServices() {
      return this.$store.getters.getVenueServices.data.filter((service) => service.name != "POS");
    },
  },
  methods: {
    pack,
    close() {
      this.$emit("close");
    },
    validateStartDate(v){
      if (!v) {
        return "Start date is required";
      }
      if (v) {
        if (this.program.date_ranges[0].start_date < this.workshop.start_date || this.program.date_ranges[0].start_date > this.workshop.end_date) {
          return 'Start date should be between the academy start and end date';
        }if ( this.program.date_ranges[0].end_date < this.program.date_ranges[0].start_date) {
          return 'End date should be greater than Start date';
        }
      }
      return true;
    },
    validateEndDate(v){
      if (!v) {
        return "End date is required";
      }
      if (v) {
        if (this.program.date_ranges[0].start_date < this.workshop.start_date || this.program.date_ranges[0].start_date > this.workshop.end_date) {
          return 'Start date should be between the academy start and end date';
        }
        if (this.program.date_ranges[0].end_date < this.workshop.start_date || this.program.date_ranges[0].end_date > this.workshop.end_date) {
          return 'End date should be between the academy start and end date';
        }
        if (this.program.date_ranges[0].end_date < this.program.date_ranges[0].start_date) {
          return 'End date should be greater than Start date';
        }
      }
      return true;
    },
    endTimeValidator() {
      const start_time = moment(this.program.date_ranges[0].schedules[0].start_time, "HH:mm:ss");
      this.program.date_ranges[0].schedules[0].end_time = moment(start_time).add(this.program.duration, "minutes").format("HH:mm:ss");
      const timeSlot = this.getServiceTimeSlot();
      const solotIndex = timeSlot.findIndex( (x) => x.time == this.program.date_ranges[0].schedules[0].end_time);
      if (solotIndex == -1) {
        this.showError("Facility not available this time duration");
        this.program.date_ranges[0].schedules[0].end_time = null;
      }
       this.checkBookingsExistsInFacility();
    },
    changeLocation() {
      if (this.program.date_ranges[0].schedules[0].location) {
        let placeId = this.program.date_ranges[0].schedules[0].locationEntries.find(
            (val) => val.value == this.program.date_ranges[0].schedules[0].location
        ).id;
        placeDetails(placeId)
            .then((data) => {
              var lat = data[0].geometry.location.lat();
              var lng = data[0].geometry.location.lng();
              this.workshop.latitude = lat;
              this.workshop.longitude = lng;
              this.program.date_ranges[0].schedules[0].latitude = lat;
              this.program.date_ranges[0].schedules[0].longitude = lng;
            })
            .catch((error) => {
              this.errorChecker(error);
            });
      }
    },
    changelocationText(newVal) {
      var _vue = this.program.date_ranges[0].schedules[0];
      var _vue2 = this;
      if (newVal == null || newVal.length == 0 || this.program.date_ranges[0].schedules[0].location == newVal) {
        return;
      }
      this.program.date_ranges[0].schedules[0].locationEntries = [];
      GetSuggestions(newVal)
          .then(function (res) {
            _vue.isLoading = false;
            _vue.locationEntries = res;
            _vue2.$forceUpdate();
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    reset() {
      this.workshops = [];
      this.workshop = null;
      this.workshopProducts = [];
      this.selectedProduct = null;
      this.facilities = [];
      this.timeSlot = [];
      this.program =  {
          name: null,
          duration: null,
          capacity: null,
          date_ranges: [
            {
              start_date: null,
              end_date: null,
              schedules: [
                {
                  venue_service_id: null,
                  facility_id: null,
                  weekdays: [],
                  start_date: null,
                  end_date: null,
                  start_time: null,
                  end_time: null,
                  latitude: 24.45342255,
                  longitude: 54.35851069,
                  heatmap: null,
                  is_external: false,
                  autocompleteLocationModel: null,
                  location: null,
                  locationEntries: [],
                  isLoading: false,
                },
              ],
            },
          ]
      };
      setTimeout(() => {
        this.$refs.form.resetValidation();
      });
    },
    changeAcademy(){
      let workshopProducts = [];
      if(this.workshop && this.workshop.workshop_products){
        this.workshop.workshop_products.forEach((elem) => {
          if(elem.product){
              workshopProducts.push({id: elem.product.id, 'name': elem.product.name});
          }
        })
      }
      this.workshopProducts = workshopProducts;
    },
    getWorkshops() {
      this.$http.get( `venues/workshops/trainers/academies`)
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              this.workshops = response.data.data;
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    save() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (this.venueServices.length === 0) {
        this.showError("No Facilities found. Please add facilities to add academy.");
        return;
      }
      if (!this.program || !this.program.date_ranges || this.program.date_ranges.length < 1 || !this.program.date_ranges[0].schedules || this.program.date_ranges[0].schedules < 1) {
        this.showError(`Please select at least one date range`);
        return;
      }
      if (!this.selectedProduct) {
        this.showError("No select product");
        return;
      }
      console.log("selected products");

      this.showLoader("Saving");
      let trainerId = this.trainer.id;
      let formData = new FormData();
      formData.append(`id`,parseInt(this.workshop.id));
      this.selectedProduct.forEach( (sp,index) => {
        formData.append(`product_ids[${index}]`, parseInt(sp));
      })
      formData.append(`programs[0][name]`,this.program.name);
      formData.append(`programs[0][capacity]`,parseInt(this.program.capacity));
      formData.append(`programs[0][duration]`,parseInt(this.program.duration));
      formData.append(`programs[0][date_ranges][0][schedules][0][trainer_ids][0]`,trainerId);
      formData.append(`programs[0][date_ranges][0][start_date]`,this.program.date_ranges[0].start_date);
      formData.append(`programs[0][date_ranges][0][end_date]`,this.program.date_ranges[0].end_date);

      for (let key_pro_sche in this.program.date_ranges[0].schedules[0]) {
        if (this.program.date_ranges[0]['schedules'][0][key_pro_sche] !== null) {
          if (key_pro_sche === "weekdays") {
            formData.append(`programs[0][date_ranges][0][schedules][0][${key_pro_sche}]`,JSON.stringify(this.program.date_ranges[0]['schedules'][0][key_pro_sche]));
          } else {
            formData.append( `programs[0][date_ranges][0][schedules][0][${key_pro_sche}]`, this.program.date_ranges[0]['schedules'][0][key_pro_sche]);
          }
        }
      }
      this.$http.post(`venues/workshops/trainers/program/add`, formData)
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.hideLoader();
              this.showSuccess("Program added successfully.");
              this.$emit("programAdded");
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    programDurationChange() {
      if (this.program) {
        this.program.date_ranges.forEach((dr) => {
          dr.schedules.forEach((schedule) => {
            schedule.is_external = false;
            schedule.facility_id = null;
            schedule.start_time = null;
            schedule.end_time = null;
          });
        });
      }
    },
    checkBookingsExistsInFacility() {
      var date_range = this.program.date_ranges[0];
      if (date_range.start_date == "" || date_range.start_date == null || date_range.end_date == "" || date_range.end_date == null) {
        return;
      }
      var sc = this.program.date_ranges[0].schedules[0];
      if (sc.end_time && date_range.start_date && sc.start_time && sc.facility_id) {
        let schedule = {
          id: sc.id ? sc.id : null,
          workshop_program_id:  null,
          start_date: date_range.start_date,
          end_date: date_range.end_date,
          start_time: sc.start_time,
          end_time: sc.end_time,
          facility_id: sc.facility_id,
          weekdays: sc.weekdays,
        };
        this.$http
            .post(`venues/workshops/check-booking-exists`, schedule)
            .then((response) => {
              if (response.status == 200 && response.data.status) {
                if (response.data.data) {
                  this.isBookingsExist = response.data.data;
                }else{
                  this.isBookingsExist = false;
                }
                this.$forceUpdate();
                this.hideLoader();
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            });
      }
    },
    getIcon() {
      let icon = "mdi-checkbox-blank-outline";
      if (this.program.date_ranges[0].schedules[0].weekdays.length === this.weekdays.length) {
        icon = "mdi-close-box";
      }
      if (this.program.date_ranges[0].schedules[0].weekdays.length > 0 && this.program.date_ranges[0].schedules[0].weekdays.length !== this.weekdays.length)
        icon = "mdi-minus-box";
      return icon;
    },
    toggle() {
      this.$nextTick(() => {
        if (this.program.date_ranges[0].schedules[0].weekdays.length == this.weekdays.length) {
          this.program.date_ranges[0].schedules[0].weekdays = [];
        } else {
          this.program.date_ranges[0].schedules[0].weekdays = this.weekdays.map((item) => {
            return item.bit_value;
          });
        }
      });
      this.$forceUpdate();
    },
    getTimesByDuration() {
      let venueServiceId = this.program.date_ranges[0].schedules[0].venue_service_id;
      let facilityId = this.program.date_ranges[0].schedules[0].facility_id;
      let duration = 0;
      if (this.program && this.program.duration) {
        duration = this.program.duration;
      }
      if (duration) {
        if (this.timeSlot[venueServiceId + "." + duration + "." + facilityId] && venueServiceId > 0){
          return;
        }
        this.timeSlot[venueServiceId + "." + duration + "." + facilityId] = [];
        let url = "";
        if (facilityId) {
          url = `&facility_id=${facilityId}`;
        }
        this.$http.get(  `venues/workshops/time-by-duration?duration=${duration}&venue_service_id=${venueServiceId}`+url).then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.timeSlot[venueServiceId + "." + duration + "." + facilityId] = response.data.data;
            this.$forceUpdate();
          }
        }).catch((error) => {
          this.errorChecker(error);
        });
      } else {
        this.showError("Please select duration!");
      }
    },
    async getFacilities(venueServiceId) {
      if (this.facilities[venueServiceId] && venueServiceId > 0) return;
      this.facilities[venueServiceId] = [];
      await this.$http
          .get(`venues/facilities/short?venue_service_id=${venueServiceId}`)
          .then((response) => {
            if (
                response.status == 200 &&
                response.data.status == true &&
                response.data.data.length != 0
            ) {
              const data = response.data.data;
              let select = [];
              let type = data[0].type;
              select.push({header: type.toUpperCase()});
              select.push({divider: true});
              data.forEach((facility) => {
                if (facility.type != type) {
                  select.push({divider: true});
                  select.push({header: facility.type.toUpperCase()});
                  select.push({divider: true});
                  type = facility.type;
                }
                select.push(facility);
              });
              this.facilities[venueServiceId] = select;
              this.$forceUpdate();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getServiceTimeSlot() {
      let duration = this.program.duration;
      let venueServiceId = this.program.date_ranges[0].schedules[0].venue_service_id;
      let facilityId = this.program.date_ranges[0].schedules[0].facility_id;
      if (this.timeSlot[venueServiceId + "." + duration + "." + facilityId] != null) {
        return this.timeSlot[venueServiceId + "." + duration + "." + facilityId];
      }
      return [];
    },
    getServiceFacilities() {
      let venue_service_id = null;
      venue_service_id = this.program.date_ranges[0].schedules[0].venue_service_id;
      if (venue_service_id !== null && this.facilities[venue_service_id] != null) {
        return this.facilities[venue_service_id];
      }
      return [];
    },
    facilityChange(type) {
      if (type === "external") {
        this.program.date_ranges[0].schedules[0].is_external = true;
        this.program.date_ranges[0].schedules[0].facility_id = null;
        this.program.date_ranges[0].schedules[0].latitude = 24.45342255;
        this.program.date_ranges[0].schedules[0].longitude = 54.35851069;
      } else {
        this.program.date_ranges[0].schedules[0].is_external = false;
      }
      this.$forceUpdate();
      this.getTimesByDuration();
    },
  }
};
</script>

<style></style>

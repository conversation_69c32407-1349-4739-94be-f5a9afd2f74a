<template>
  <v-hover v-slot="{ hover }">
    <v-sheet
        block
        :height="height"
        :class="`d-flex justify-center align-center cursorstyle ms-border-bottom  ts-schedule-cell ${hover?'hoverstyle' : ''}`"
    >
      <v-row>
        <v-col md="12" :sm="12" v-if="!tslot.wp_id" @click="onClick('addProgram')">
          <div class="tslot">
            <div class="cc-row">
              <PlusIcon />
            </div>
          </div>
        </v-col>
        <v-col md="12" :sm="12" v-if="tslot.wp_id" @click="onClick('addParticipant')">
          <div :class="['tslot', className]">
            <div class="cc-row">{{ tslot.program_name }}</div>
          </div>
        </v-col>
      </v-row>
    </v-sheet>
  </v-hover>
</template>
<script>
import PlusIcon from "@/assets/images/trainers/plus-icon.svg";
export default {
  props: {
    height: { type: Number, default: 50 },
    index: { type: Number, default: 0 },
    tslot: { type: Object, default: null},
    className: { type: String, default: "" },
  },
  components: {PlusIcon},
  methods: {
    onClick(action) {
      this.$emit("onClickSlot",{hour:this.index,slots: this.tslot, action: action});
    },
  },
}
</script>
<style>
.ts-schedule-cell {
  &.ms-border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
  &.hoverstyle {
    background: rgba(79, 174, 175, 0.10);
    cursor: pointer;
  }
  .tslot {
    position: relative;
    text-align: center;
    overflow: hidden;
    width: 100%;
    display: table;
    height:40px;
  }
  .tslot.slot-available,.tslot.slot-rescheduled,.tslot.slot-full{
    background-color: #112A46;
    color: #FFFFFF;
  }
  /* Ribbon for top-left corner */
  .tslot.slot-available::before,.tslot.slot-rescheduled::before,.tslot.slot-full::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 20px 20px 0 0; /* Adjust size for the triangle */
    border-color: green transparent transparent transparent; /* Default for 'slot-available' */
  }
  .tslot.slot-available::before {
    background-color: #112A46;
    border-color: green transparent transparent transparent;
  }
  .tslot.slot-rescheduled::before {
    background-color: #12A46;
    border-color: yellow transparent transparent transparent;
  }
  .tslot.slot-full::before {
    background-color: #112A46;
    border-color: red transparent transparent transparent;
  }

  .tslot span {
    display: inline;
  }
  .tslot .cc-row svg {
    vertical-align: middle;
  }
  .cc-row {
    width: 100%;
  }
  .cc-row .plus-btn{
    width: 20px;
    height: 20px;
  }
  .cc-row span {
    display: block;
  }

}

</style>
<template>
  <v-dialog v-model="tssDialog" scrollable persistent width="420px">
    <v-form ref="form">
      <v-card v-if="student">
        <v-card-text class="">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon class="text-lg font-semibold" text="Transfer Student Session" style="color: black" >
                </SvgIcon>
                <v-btn  fab x-small class="shadow-0" @click="close">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <v-row class="mt-2" dense>
            <v-col cols="12" sm="12" md="12">
              <label>Transfer From</label>
              <v-text-field
                  v-if="student"
                  v-model="student.name"
                  outlined
                  background-color="#fff"
                  required
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  validate-on-blur
                  disabled
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="12" md="12">
              <label>Transfer To</label>
              <v-name-search
                  outlined
                  :selected="name"
                  @updateCustomer="setCustomerData"
                  readonly
                  v-model="nameSearch"
                  hide-details="auto"
                  class-name="q-text-field shadow-0"
                  background-color=""
                  :dense="true"
                  label=""
              >
              </v-name-search>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider class="mt-2"></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="ma-2" text  @click="close">Close</v-btn>
          <v-btn @click="transfer" color="darken-1" class="ma-2 white--text blue-color">Transfer</v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
export default {
  components: {SvgIcon},
  props: {
    tssDialog: {
      type: Boolean,
      default: false,
    },
    student:{type: Object, default: null},
    slotData: { type: Object, default: null},
  },
  data() {
    return {
      transferTo: null,
      wps: null,
      name: null,
      nameSearch:null,
    };
  },
  watch: {
    tssDialog: {
      immediate: true,
      handler(val) {
        if (val) {
          this.name = null;
          this.nameSearch = null;
        }
      },
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },
    transfer() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      if (!this.student.customer_id || !this.transferTo) {
        this.showError("Select Customer");
        return;
      }
      this.showLoader("wait");
      let data = {
        attendance_id: this.student.attendance_id,
        order_id: this.student.order_id,
        customer_id: this.student.customer_id,
        booking_schedule_id: this.student.workshop_booking_schedule_id,
        transfer_to: this.transferTo,
      };
      console.log(data);
      this.$http.post(`venues/workshops/schedules/booking/session/transfer`, data)
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.hideLoader();
              this.showSuccess("Session transferred successfully.");
              this.$emit('transferStudentSessionSuccess');
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },
    setCustomerData(data){
      console.log("data",data);
      if(data && data.customer_id) {
        this.transferTo = data.customer_id;
      }else{
        this.transferTo = null;
      }
    },
  }
};
</script>

<style></style>

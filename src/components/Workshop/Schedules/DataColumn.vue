<template>
  <v-card
      :height="height"
      width="160"
      tile
      class="flex-grow-0 flex-shrink-0"
  >
    <template v-for="(slot, i) in timeSlots">
      <v-hover v-slot="{ hover }" :key="`ss_${i}`">
        <v-sheet
            :key="`ss_${i}`"
            block
            :height="slot.height"
            :class="[
              'd-flex justify-center align-center relative cursorstyle ms-border-bottom ts-schedule-cell',
              hover && isSlotPartiallyAvailable(slot) ? 'hoverstyle' : ''
            ]"
        >
          <div
              style="height:60px;z-index: 1"
              class="w-full"
          >
            <!-- Render multiple overlapping programs -->
            <template v-for="(program, index) in getSortedStackedPrograms(slot)">
              <div
                  :key="program.schedule_id"
                  :class="['tslot', getClassName(program), 'absolute']"
                  :style="calculatePositionInHour(program, slot, index)"
                  @click.stop="handleProgramClick(slot, program)"
              >
                <div class="cc-row">{{ program.program_name }}</div>
              </div>
            </template>
            <!-- Render empty portions of the slot -->
            <template v-for="(emptyRange, emptyIndex) in getEmptyRanges(slot)">
              <div
                  :key="`empty_${emptyIndex}`"
                  class="tslot absolute"
                  :style="calculateEmptyPosition(emptyRange, slot)"
                  :class="[isRangeAvailable(emptyRange, slot) ? 'available-class' : 'unavailable-class']"
                  @click.stop="handleEmptyRangeClick(emptyRange, slot)"
              >
                <div class="cc-row">-</div>
              </div>
            </template>
          </div>
        </v-sheet>
      </v-hover>
    </template>
  </v-card>
</template>


<script>
import moment from "moment/moment";

export default {
  props: {
    increment: { type: Number, default: 60 },
    height: { type: Number, default: 80 },
    date: { type: String, default: null },
    timeSlots: { type: Array, default: () => [] },
    schedules: { type: Array, default: () => [] },
    className: { type: String, default: "" },
    availabilities: { type: Array, default: () => [] },
  },
  computed: {
    sortedSchedules() {
      return [...this.schedules].sort((a, b) =>
          this.timeInMinutes(a.start_time) - this.timeInMinutes(b.start_time) ||
          this.timeInMinutes(a.end_time) - this.timeInMinutes(b.end_time)
      );
    }
  },

  methods: {
    handleProgramClick(slot, program) {
      console.log(`Clicked program: ${program.program_name} at ${program.start_time}-${program.end_time}`);
      if(program.type === "trainer"){
        this.onClickSlot('showTrainerParticipant', slot, program);
      }else if(program.type === "workshop"){
        this.onClickSlot('addParticipant', slot, program);
      }
    },
    handleEmptySlotClick(slot) {
      if (this.isAvailableSlot(slot, this.date) === 'available-class') {
        console.log(`Clicked empty slot: ${slot.start_time}-${slot.end_time}`);
        this.onClickSlot('addProgram', slot);
      }
    },
    // method for empty range clicks
    handleEmptyRangeClick(emptyRange, slot) {
      if (this.isRangeAvailable(emptyRange, slot)) {
        console.log(`Clicked empty range: ${emptyRange.start_time}-${emptyRange.end_time}`);
        this.onClickSlot('addProgram', {...slot, ...emptyRange});
      } else {
        console.log(`Empty range ${emptyRange.start_time}-${emptyRange.end_time} is unavailable`);
      }
    },
    onClickSlot(action, slot, program = null) {
      let d = {start_time: slot.start_time, program: program, action: action, is_open_participants: true};
      if (action === "addProgram") {
        console.log('Emitting addProgram', d);
        this.$emit("addProgram", d);
      } else if (action === "addParticipant") {
        console.log('Emitting addParticipant/showParticipant', d);
        if (program && program.attendance_count == 0 && program.is_reschedule == 0) {
          this.$emit("addParticipant", d);
        } else {
          this.$emit("showParticipant", d);
        }
      }else if(action === "showTrainerParticipant"){
        this.$emit("showTrainerParticipant",d);
      }
    },
    getClassName(selectedProgram) {
      if (selectedProgram.attendance_count >= selectedProgram.capacity) {
        return "slot-full";
      } else if (selectedProgram.is_reschedule) {
        return "slot-rescheduled";
      } else {
        return "slot-available";
      }
    },
    findProgramsForSlot(slot) {
      return this.sortedSchedules.filter((schedule) => {
        const slotStart = this.timeInMinutes(slot.start_time);
        const slotEnd = this.timeInMinutes(slot.end_time);
        const scheduleStart = this.timeInMinutes(schedule.start_time);
        const scheduleEnd = this.timeInMinutes(schedule.end_time);
        return scheduleStart < slotEnd && scheduleEnd > slotStart;
      });
    },
    getSortedStackedPrograms(slot) {
      const programs = this.findProgramsForSlot(slot);
      const groupedByStart = {};
      programs.forEach(program => {
        const startKey = this.timeInMinutes(program.start_time);
        if (!groupedByStart[startKey]) groupedByStart[startKey] = [];
        groupedByStart[startKey].push(program);
      });
      return Object.values(groupedByStart)
          .map(group => group.sort((a, b) => this.timeInMinutes(a.end_time) - this.timeInMinutes(b.end_time)))
          .flat()
          .sort((a, b) => this.timeInMinutes(a.start_time) - this.timeInMinutes(b.start_time));
    },
    calculatePositionInHour(program, slot) {
      const slotStart = this.timeInMinutes(slot.start_time);
      const scheduleStart = this.timeInMinutes(program.start_time);
      const scheduleEnd = this.timeInMinutes(program.end_time);
      const offsetMinutes = scheduleStart - slotStart;
      const durationMinutes = scheduleEnd - scheduleStart;
      const remainingMinutes = 60 - offsetMinutes;
      const programsAtStart = this.getSortedStackedPrograms(slot).filter(p =>
          this.timeInMinutes(p.start_time) === scheduleStart
      ).length;
      const stackHeight = Math.min(remainingMinutes / Math.max(programsAtStart, 1), durationMinutes);
      const stackIndex = this.getSortedStackedPrograms(slot)
          .filter(p => this.timeInMinutes(p.start_time) === scheduleStart)
          .indexOf(program);
      const effectiveHeight = Math.min(stackHeight, remainingMinutes - (stackIndex * stackHeight));
      const effectiveTop = Math.min(offsetMinutes + (stackIndex * stackHeight), 60);
      return {
        height: `${effectiveHeight}px`,
        top: `${effectiveTop}px`,
        left: '0',
        right: '0',
        position: 'absolute'
      };
    },
    timeInMinutes(time) {
      const [hours, minutes] = time.split(":").map(Number);
      return hours * 60 + minutes;
    },
    isAvailableSlot(slot, currentDate = null) {
      if (currentDate && moment(currentDate).isBefore(moment(), 'day')) {
        return 'unavailable-class';
      }
      return this.availabilities.some(availability =>
          slot.start_time >= availability.start_time &&
          slot.end_time <= availability.end_time
      ) ? "available-class" : "unavailable-class";
    },
    // New method: Calculate empty ranges within a slot
    getEmptyRanges(slot) {
      const slotStart = this.timeInMinutes(slot.start_time);
      const slotEnd = this.timeInMinutes(slot.end_time);
      const programs = this.getSortedStackedPrograms(slot);
      const ranges = [];
      let lastEnd = slotStart;

      programs.forEach(program => {
        const programStart = this.timeInMinutes(program.start_time);
        const programEnd = this.timeInMinutes(program.end_time);

        if (programStart > lastEnd) {
          ranges.push({
            start_time: this.minutesToTime(lastEnd),
            end_time: this.minutesToTime(programStart)
          });
        }
        lastEnd = Math.max(lastEnd, programEnd);
      });

      if (lastEnd < slotEnd) {
        ranges.push({
          start_time: this.minutesToTime(lastEnd),
          end_time: this.minutesToTime(slotEnd)
        });
      }

      return ranges;
    },
    // New method: Convert minutes back to HH:MM format
    minutesToTime(minutes) {
      const hours = Math.floor(minutes / 60).toString().padStart(2, '0');
      const mins = (minutes % 60).toString().padStart(2, '0');
      return `${hours}:${mins}`;
    },
    // New method: Calculate position for empty ranges
    calculateEmptyPosition(emptyRange, slot) {
      const slotStart = this.timeInMinutes(slot.start_time);
      const rangeStart = this.timeInMinutes(emptyRange.start_time);
      const rangeEnd = this.timeInMinutes(emptyRange.end_time);
      const top = rangeStart - slotStart;
      const height = rangeEnd - rangeStart;
      return {
        top: `${top}px`,
        height: `${height}px`,
        left: '0',
        right: '0',
        position: 'absolute'
      };
    },
    // Check if a specific range is available
    isRangeAvailable(range) {
      if (this.date && moment(this.date).isBefore(moment(), 'day')) {
        return false;
      }
      const normalizedRange = {
        start_time: range.start_time + ":00",
        end_time: range.end_time + ":00"
      };
      return this.availabilities.some(availability =>
          normalizedRange.start_time >= availability.start_time &&
          normalizedRange.end_time <= availability.end_time
      );
    },
    //Check if slot has any available portion
    isSlotPartiallyAvailable(slot) {
      const emptyRanges = this.getEmptyRanges(slot);
      return emptyRanges.some(range => this.isRangeAvailable(range, slot));
    }
  }
};
</script>


<style>
.ts-schedule-cell {
  &.ms-border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  &.hoverstyle {
    background: rgba(79, 174, 175, 0.10);
    cursor: pointer;
  }

  .tslot {
    position: relative;
    text-align: center;
    overflow: hidden;
    width: 100%;
    display: table;
  }

  .tslot.slot-available, .tslot.slot-rescheduled, .tslot.slot-full {
    background-color: #112A46;
    color: #FFFFFF;
    cursor: pointer;
  }

  .tslot.available-class {
    background: transparent;
    color: #000;
    cursor: pointer;
  }

  .tslot.unavailable-class {
    background: rgba(200, 200, 200, 0.3);
    pointer-events: none;
  }

  /* Ribbon styles unchanged */

  .tslot.slot-available::before, .tslot.slot-rescheduled::before, .tslot.slot-full::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 24px 24px 0 0;
    border-color: green transparent transparent transparent;
  }

  .tslot.slot-available::before {
    border-color: green transparent transparent transparent;
  }

  .tslot.slot-rescheduled::before {
    border-color: yellow transparent transparent transparent;
  }

  .tslot.slot-full::before {
    border-color: red transparent transparent transparent;
  }

  .tslot span {
    display: inline;
  }

  .tslot .cc-row svg {
    vertical-align: middle;
  }

  .cc-row {
    width: 100%;
    height: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cc-row .plus-btn {
    width: 20px;
    height: 20px;
  }

  .cc-row span {
    display: block;
  }

  .slot-blocked {
    background-color: rgba(255, 0, 0, 0.3);
    pointer-events: none;
  }
}
</style>
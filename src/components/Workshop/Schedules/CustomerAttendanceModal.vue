<template>
  <v-dialog v-model="showParticipantDialog" width="60%"  scrollable persistent>
    <v-card class="fill-height wc-attendance-modal" v-if="showStudent">
      <v-row class="bg-teal-light ma-0" dense>
        <v-col cols="12">
          <div class="position-relative" style="position: relative">
            <div class="text-center mt-2 white--text font-semibold">{{ date | dayDateFormat }}</div>
            <v-btn
                fab
                x-small
                class="shadow-0 position-absolute"
                style="top: -10px; right: 0px; position: absolute;"
                @click="closeMainDialog"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>
        </v-col>
      </v-row>
      <v-row class="bg-teal-light pl-4 pr-4 pb-4 ma-0" dense>
        <v-col cols="12" md="4" class="v-center">
          <div class="text-left white--text font-semibold">Attendance</div>
        </v-col>
        <v-col cols="12" md="4" class="v-center">
          <div class="text-center white--text font-semibold">{{ programName }}</div>
        </v-col>
        <v-col cols="12" md="4" class="v-center">
          <div class="text-right" style="max-width: 280px; margin: 0 auto; margin-right: 0;">
            <v-select
              v-model="selectedTimeslot"
              item-text="name"
              :items="times"
              item-value="times_id"
              @change="timeChange()"
              outlined
              :menu-props="{ bottom: true, offsetY: true }"
              required
              return-object
              background-color="#4EAEAF"
              color="#ffff"
              class="q-autocomplete shadow-0 vs"
              hide-details="auto"
              dense
              validate-on-blur
          >
            <template slot="item" slot-scope="data">
              <template>
                <v-list-item-content style="width: 70px; color: #FFFFFF !important;">
                  <v-list-item-title>
                    {{ data.item.name }}
                  </v-list-item-title>
                  <v-list-item-subtitle>
                    {{ data.item.facility_name }}
                  </v-list-item-subtitle>
                </v-list-item-content>
                <v-list-item-action style="width: 50px">
                  {{ data.item.attendance }} / {{ data.item.capacity }}
                </v-list-item-action>
              </template>
            </template>
          </v-select>
          </div>
        </v-col>
      </v-row>
      <v-card-text class="pa-4 pt-1">
        <table class="table table-bordered table-striped ca-table">
            <thead>
              <tr class="opacity-70 tr-neon bordered">
                <th>Name</th>
                <th>Mobile</th>
                <th>Email</th>
                <th>Payment</th>
                <th>Status</th>
                <th>Attendance</th>
                <th>Cancel</th>
<!--                <th>Reschedule</th>-->
                <th>Action</th>
              </tr>
            </thead>
            <tbody v-if="students.length">
              <tr v-for="(student, index) in students" :key="index" class="bordered">
              <td>{{ student.name }}</td>
              <td>{{ student.mobile }}</td>
              <td>{{ student.email }}</td>
              <td style="text-align: center">
                <v-btn text color="warning" plain v-if="student.order_status == 5">Unpaid</v-btn>
                <v-btn text color="primary" v-if="student.order_status == 4">Paid</v-btn>
              </td>
              <td class="md-table-cell text-center">
                <v-btn text :color="student.is_attended == 0 ? 'secondary' : student.is_attended == 1 ? 'success' : 'error'" dark small>
                  {{ !student.is_attended ? "NA" : student.is_attended == 1 ? "Attended" : student.is_attended == 2 ? "No show" : "" }}
                </v-btn>
              </td>
              <td class="md-table-cell text-center">
                <div class="d-flex justify-center">
                  <v-checkbox
                      class="mt-0"
                      v-model="student.is_check"
                      hide-details
                      :indeterminate="student.is_attended === 1 ? true : false"
                      :disabled=" student.is_attended === 1 || (selectedTimeslot && selectedTimeslot.is_cancelled == 1) ? true : false"
                      @select="$forceUpdate"
                  ></v-checkbox>
                </div>
              </td>
              <td class="md-table-cell text-center">
                <div class="d-flex justify-center">
                  <v-checkbox
                      class="mt-0"
                      v-model="student.is_cancel_booking"
                      hide-details
                      :indeterminate="(student.is_cancel_booking === 1 || student.is_attended > 0) ? true : false"
                      :disabled="student.is_cancelled === 1 || student.is_cancel_booking === 1 || student.is_attended > 0 || (selectedTimeslot && selectedTimeslot.is_cancelled) == 1 ? true : false"
                  ></v-checkbox>
                </div>
              </td>
              <td>
                <v-menu content-class="q-menu" >
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn :ripple="false" class="text_capitalize options" elevation="0" v-bind="attrs" v-on="on" tile color="#fff">
                      <DotsIcon />
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item  v-if="student.is_attended === 0 && (selectedTimeslot && selectedTimeslot.is_cancelled == 0)"  @click="rescheduleBtnClick(student)">
                      <SvgIcon class="font-medium text-sm gap-x-2" text="Reschedule">
                        <template #icon>
                          <TimeScheduleIcon opacity="1" stroke="black" />
                        </template>
                      </SvgIcon>
                    </v-list-item>
                    <v-list-item  v-if="student.order_status == 4 && (selectedTimeslot && selectedTimeslot.is_cancelled == 0) && checkWritePermission($modules.sales.refund.slug)"  @click="refund(student)">
                      <SvgIcon class="font-medium text-sm gap-x-2" text="Refund">
                        <template #icon>
                          <RefundIcon opacity="1" stroke="black" />
                        </template>
                      </SvgIcon>
                    </v-list-item>
                    <v-list-item  v-if="student.order_status == 4 && (selectedTimeslot && selectedTimeslot.is_cancelled == 0 && student.is_attended == 0) && checkWritePermission($modules.sales.refund.slug)"  @click="transferStudentSessionModal(student)">
                      <SvgIcon class="font-medium text-sm gap-x-2" text="Transfer Session">
                        <template #icon>
                          <TransferIcon />
                        </template>
                      </SvgIcon>
                    </v-list-item>
                   </v-list>
                </v-menu>
              </td>
            </tr>
            </tbody>
            <tbody v-else>
              <td colspan="9">
              <v-card-text class="pa-8 text-center">
                <h3>No students found!</h3>
              </v-card-text>
            </td>
            </tbody>
        </table>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions>
        <v-menu content-class="q-menu" v-if="students && !disableAttendance && !disableReschedule && (selectedTimeslot && selectedTimeslot.is_cancelled == 0)">
          <template v-slot:activator="{ on, attrs }">
              <v-btn class="shadow-0 text-capitalize mr-2 bordered" v-bind="attrs" v-on="on" ><EditBtnIcon/> More</v-btn>
          </template>
          <v-list>
            <v-list-item v-if="checkWritePermission($modules.memberships.dashboard.slug)" @click="rescheduleCompleteClassBtnClick">
              <SvgIcon class="font-medium text-sm gap-x-2 pointer" text="Reschedule Class">
                <template #icon>
                  <TimeScheduleIcon opacity="1" stroke="black" />
                </template>
              </SvgIcon>
            </v-list-item>
            <v-list-item @click="transferSessionsModal()" >
              <SvgIcon text="Transfer Sessions" class="font-medium text-sm gap-x-2 pointer">
                <template #icon>
                  <TransferIcon />
                </template>
              </SvgIcon>
            </v-list-item>
            <v-divider class="mb-2"/>
            <v-list-item @click="cancelClass()">
              <SvgIcon text="Cancel Class" class="font-medium text-sm gap-x-2 red--text svg-stroke-red pointer">
                <template #icon>
                  <DeleteIcon opacity="1" stroke="red" />
                </template>
              </SvgIcon>
            </v-list-item>
          </v-list>
        </v-menu >
        <v-btn dark class="blue-color shadow-0" v-if="selectedTimeslot && selectedTimeslot.is_cancelled === 0 && selectedTimeslot.capacity > selectedTimeslot.attendance" @click="addStudent"><AddIcon/> &nbsp;Add Student</v-btn>
        <row md="12" sm="12" v-if="selectedTimeslot && selectedTimeslot.is_cancelled === 1">
          <v-col md="12" sm="12">
            <span v-if="selectedTimeslot && selectedTimeslot.is_cancelled === 1" data-v-b74a88c0="" class="red--text" style="font-weight: bold; margin-left: 75px">Class Cancelled.</span>
          </v-col>
        </row>
        <v-spacer></v-spacer>
        <div style="width: 150px; margin-right: 20px" v-if="selectedTimeslot && selectedTimeslot.is_cancelled === 0 && trainer">
          <v-text-field
              v-model="trainer.name"
              outlined
              background-color="#fff"
              required
              class="q-text-field shadow-0"
              dense
              hide-details="auto"
              validate-on-blur
              readonly
          ></v-text-field>
        </div>
        <v-btn v-if="students && !students.length == 0 && selectedTimeslot && selectedTimeslot.is_cancelled == 0 && isCanceledCheckBoxChecked" dark class="red-color" @click="cancelBookings()">Cancel Bookings</v-btn>
        <div style="margin-left: 7px">
          <v-menu offset-y v-if="students && !students.length == 0 && !disableAttendance && selectedTimeslot && selectedTimeslot.is_cancelled == 0 && isAttendanceCheckBoxChecked">
            <template v-slot:activator="{ on, attrs }">
              <v-btn class="default-color" dark v-bind="attrs" v-on="on">Attendance</v-btn>
            </template>
            <v-list>
              <v-list-item @click="markAttendance('present')">
                <v-list-item-title>Mark as attended</v-list-item-title>
              </v-list-item>
              <v-list-item @click="markAttendance('absent')">
                <v-list-item-title>Mark no show</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </v-card-actions>
    </v-card>
    <confirm-model
        v-bind="confirmModel"
        @confirm="confirmActions"
        @close="confirmModel.id = null"
    ></confirm-model>
    <TransferTrainerSession :ttsDialog="ttsDialog" :slotData="slotData" :trainer="trainer" :trainersList="trainersList" @close="ttsDialog = false" @transferSuccess="transferSuccess"/>
    <TransferStudentSession :tssDialog="tssDialog" :slotData="slotData" :student="selectedStudent" @close="tssDialog = false" @transferStudentSessionSuccess="transferStudentSessionSuccess"/>
    <RescheduleCompleteClass
      v-if="date && selectedTimeslot && showRescheduleCompleteClassDialog"
      :rescheduleDialog="showRescheduleCompleteClassDialog"
      :venueServices="venueServices"
      :scheduleFromDate="date"
      :programId="programId"
      :programName="programName"
      :programDuration="programDuration"
      :facilityId="facilityId"
      :trainer="trainer"
      :slotData="slotData"
      :selectedTimeslot="selectedTimeslot"
      :selected_time_slot_id="selectedTimeslot && selectedTimeslot.time_slot_id ? selectedTimeslot.time_slot_id : null"
      @close="closeCompleteRescheduleClass"
      @reschedule-done="(showRescheduleCompleteClassDialog = false), rescheduleDone()"
    />
    <RescheduleClass
        v-if="date && selectedTimeslot && showRescheduleClassDialog"
        :refreshRescheduleClass="refreshRescheduleClass"
        :rescheduleClassDialog="showRescheduleClassDialog"
        v-bind="rescheduleStudentDetails"
        :scheduleFromDate="date"
        :programName="programName"
        :programId="programId"
        :selectedTimeslot="selectedTimeslot"
        :slotData="slotData"
        :selected_time_slot_id="selectedTimeslot && selectedTimeslot.time_slot_id ? selectedTimeslot.time_slot_id : null"
        @close="showRescheduleClassDialog = false"
        @reschedule-done="(showRescheduleClassDialog = false), rescheduleDone()"
    ></RescheduleClass>
    <RefundNew
        v-if="refundModel.invoiceId && refund_dialog"
        v-bind="refundModel"
        :refundInvoiceData="refundInvoiceData"
        :show="refund_dialog"
        :workshopRefundAmount="workshopRefundAmount"
        :workshopCustomerAttendanceId="workshopCustomerAttendanceId"
        @close="refund_dialog = false"
        @refund="refund_dialog = false; timeChange();"
    />
  </v-dialog>
</template>

<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DotsIcon from "@/assets/images/misc/h-options.svg";
import EditBtnIcon from "@/assets/images/misc/more-horizontal-square.svg";
import AddIcon from "@/assets/images/misc/plus-icon.svg";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
import TimeScheduleIcon from "@/assets/images/misc/time-schedule.svg";
import TransferIcon from "@/assets/images/misc/transfer.svg";
import TransferTrainerSession from "@/components/Workshop/Schedules/TransferTrainerSession.vue";
import TransferStudentSession from "@/components/Workshop/Schedules/TransferStudentSession.vue";
import RescheduleClass from "@/views/Workshops/WorkshopDetails/RescheduleClass.vue";
// import OrderDetails from "@/components/Order/OrderDetails.vue";
import moment from "moment";
import RescheduleCompleteClass from "./RescheduleCompleteClass.vue";
import RefundNew from "@/components/Invoice/RefundNew.vue";
import RefundIcon from "@/assets/images/finance/refunds.svg"
// import RescheduleClass from "@/views/Workshops/WorkshopDetails/RescheduleClass.vue";
// import CustomerFormForWalkIn from "@/views/Workshops/WorkshopDetails/CustomerFormForWalkIn.vue";
// import RefundNew from "@/components/Invoice/RefundNew.vue";
// import ArrowLeft from "@/assets/images/misc/arrow-circle-left.svg"
// import ArrowRight from "@/assets/images/misc/arrow-circle-right.svg"

export default {
  props: {
    showParticipantDialog: {
      type: Boolean,
      default: false,
    },
    trainer:{type: Object, default: null},
    workshop: Object,
    sliderChange: Number,
    slotData: { type: Object, default: null},
  },
  components: {
    RefundNew,
    RescheduleCompleteClass,
    RescheduleClass,
    SvgIcon,DotsIcon,EditBtnIcon,AddIcon,DeleteIcon,TimeScheduleIcon,TransferIcon,RefundIcon,
    TransferTrainerSession,TransferStudentSession
  },
  data() {
    return {
      lastSelectedCell: null,
      refreshRescheduleClass: 0,
      order_id_for_payment: null,
      walkInCustomerFormDialog: false,
      refund_dialog: false,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      workshopRefundAmount: null,
      workshopCustomerAttendanceId: null,
      rescheduleStudentDetails: {},
      showRescheduleClassDialog: false,
      showRescheduleCompleteClassDialog: false,
      disableReschedule: false,
      disableAttendance: false,
      selectedWorkshopScheduleId: null,
      timings: [],
      trainersList: [],
      reschedule: {},
      is_public: true,
      date: moment().format("YYYY-MM-DD"),
      selectedDate: moment().format("YYYY-MM-DD"),
      rescheduleDialog: false,
      selectedTrainer: null,
      selectedCellDate: null,
      trainers: [],
      attendanceTrainers: [],
      selectedTimeslot: null,
      times: [],
      students: [],
      availableDays: [],
      events: [],
      products: [],
      schedules: [],
      dateRanges: [],
      showStudent: false,
      programName: null,
      programId: null,
      programDuration: null,
      colors: ["blue", "indigo", "deep-purple", "cyan", "green", "orange", "grey darken-1"],
      refundModel: { invoiceId: null, type: "full", amount: 0 },
      refundInvoiceData: {},
      cancelledStatus: false,
      ttsDialog: false,
      tssDialog: false,
      venueServiceId:null,
      facilityId: null,
      selectedStudent: null,
    };
  },
  computed: {
    venueServices() {
      return this.$store.getters.getVenueServices.data;
    },
    weekdays() {
      return this.$store.getters.getWeekdays.data;
    },
    isCanceledCheckBoxChecked(){
      let cancelledStudents = [];
      this.students.forEach((element) => {
        if (element.is_cancel_booking == true) {
          cancelledStudents.push(element);
        }
      });
      return cancelledStudents.length > 0;
    },
    isAttendanceCheckBoxChecked(){
      let cs = [];
      this.students.forEach((element) => {
        if (element.is_check) {
          cs.push(element);
        }
      });
      return cs.length > 0;
    }
  },
  watch: {
    showParticipantDialog: {
      immediate: true,
      async handler(val) {
        if (val) {
          if(this.slotData) {
            this.selectedTimeslot = null;
            this.programId = this.slotData.wp_id;
            this.date = this.slotData.start_date;
            if (this.slotData.start_date) {
              this.date = this.slotData.start_date;
              this.lastSelectedCell = {
                formattedDate: this.slotData.start_date,
                id: this.slotData.id,
                date: this.slotData.start_date,
                workshop_schedule_id: this.slotData.schedule_id,
                workshop_program_id: this.slotData.wp_id,
              };
              // Convert to Date objects
              const start = new Date(`1970-01-01T${this.slotData.start_time}Z`);
              const end = new Date(`1970-01-01T${this.slotData.end_time}Z`);
              this.programDuration = (end - start) / (1000 * 60);
              this.programName = this.slotData.program_name;
              this.viewStudent(this.lastSelectedCell);
              this.showStudent = true;
            }
          }
        }
      },
    },
  },
  mounted() {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
    this.getTrainers();
  },
  methods: {
    closeMainDialog(){
      this.showStudent = false;
      this.$emit("close");
    },
    closeCompleteRescheduleClass(){
      this.showRescheduleCompleteClassDialog=false;
    },
    addStudent(){
      console.log("slot data");
      console.log(this.slotData);
      this.$emit('addStudent',this.slotData);
    },
    rescheduleBtnClick(student){
      this.rescheduleStudentDetails = student;
      this.date = this.selectedCellDate;
      this.showRescheduleClassDialog = true;
      this.refreshRescheduleClass += 1;
    },
    rescheduleCompleteClassBtnClick(){
      this.date = this.selectedCellDate;
      console.log("selectedTimeslot",this.selectedTimeslot);
      this.showRescheduleCompleteClassDialog = true;
    },
    getTrainers() {
      console.log("calling get trainers");
      this.$http
          .get(`venues/trainers/short`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.trainersList = response.data.data;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    viewStudent(cell) {
      this.lastSelectedCell = cell;
      this.date = cell.date;
      this.times = [];
      this.dayName = moment(cell.date).format("dddd");
      this.selectedCellDate = cell.date;
      this.disableAttendance = false;
      if (this.programId === null) {
        this.programId = cell.wp_id;
      }
      this.showLoader("Loading...");
      this.$http
          .get(`venues/workshops/time_slot?workshop_program_id=${this.programId}&day=${this.dayName}&date=${this.selectedCellDate}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              if (response.data.data.length) {
                this.trainers = [];
                response.data.data.forEach((element, index) => {
                  let selectedTrainer = [];
                  if(element.start_time == this.slotData.start_time && element.end_time == this.slotData.end_time) {
                    this.times.push({
                      name: moment(element.start_time, "HH:mm").format("h:mm a") +
                          " To " +
                          moment(element.end_time, "HH:mm").format("h:mm a"),
                      time: element.start_time,
                      workshopScheduleId: element.id,
                      is_reschedule: element.is_reschedule,
                      from_date: element.from_date,
                      reschedule_id: element.reschedule_id,
                      facility_name: element.facility_name
                          ? element.facility_name
                          : element.location,
                      capacity: element.capacity,
                      attendance: element.attendance,
                      program_name: element.name,
                      venue_service_id: element.venue_service_id,
                      facility_id: element.facility_id,
                      start_date: element.start_date,
                      end_date: element.end_date,
                      time_slot_id: element.time_slot_id,
                      times_id: index + 1,
                      date_range_id: element.date_range_id,
                      trainers: selectedTrainer,
                      is_cancelled: element.is_cancelled,
                    });
                  }
                });

                let selectedCellDate = moment(this.selectedCellDate);
                let todayDate = moment();
                if (todayDate.diff(selectedCellDate, "days") > 1 && !this.checkBackfillPermission(this.$modules.workshops.schedule.slug)) {
                  this.disableAttendance = true;
                }
                this.selectedTimeslot = this.times[0];
                this.is_public = response.data.is_public;
                this.timeChange();
              }
              this.hideLoader();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    timeChange() {
      console.log("selected time s",this.selectedTimeslot);
      const workshopScheduleId = this.selectedTimeslot.workshopScheduleId;
      const weekdays = this.dayName;
      const time = this.selectedTimeslot.time;
      const is_reschedule = this.selectedTimeslot.is_reschedule;
      const from_date = this.selectedTimeslot.from_date;
      this.venueServiceId = this.selectedTimeslot.venue_service_id;
      if (this.selectedTimeslot.facility_id) {
        this.facilityId = this.selectedTimeslot.facility_id;
        //this.getTimesByDuration();
      }

      this.getBookedCustomer(
          workshopScheduleId,
          weekdays,
          time,
          is_reschedule,
          from_date
      );
    },
    rescheduleDone() {
      this.showStudent = false;
      this.$emit('reloadSchedule');
    },
    getBookedCustomer(workshopScheduleId, weekdays, time, is_reschedule, from_date) {
      let reschedule_id = null;
      if (this.selectedTimeslot.is_reschedule) {
        reschedule_id = this.selectedTimeslot.reschedule_id;
      }
      this.disableReschedule = false;
      this.selectedWorkshopScheduleId = workshopScheduleId;
      this.showLoader("Loading");
      this.students = [];
      this.$http
        .get(`venues/workshops/schedules/booking/customer?workshop_schedule_id=${workshopScheduleId}&weekdays=${weekdays}&start_time=${time}&date=${this.selectedCellDate}&is_reschedule=${is_reschedule}&from_date=${from_date}&reschedule_id=${reschedule_id}`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.students = response.data.data.map( (s) => {s.is_check = false;  return s;});
            if (this.students && this.students.findIndex((students) => students.is_attended == true) != -1) {
              this.disableReschedule = true;
            }
            this.selectedTrainer = null;
            this.students.forEach((element) => {
             let selTrainer = null;
              if (element.trainer_id) {
                selTrainer = this.trainers.find((x) => x.trainer_id == element.trainer_id);
                if (selTrainer) {
                  this.selectedTrainer = selTrainer.trainer_id;
                } else {
                  selTrainer = this.trainersList.find((x) => x.id == element.trainer_id);
                  if (selTrainer) {
                    this.selectedTrainer = selTrainer.id;
                    let obj = {
                      trainer_id: selTrainer.id,
                      first_name: selTrainer.name,
                    };
                    this.trainers.push(obj);
                  }
                }
              }
            });
            if(!this.selectedTrainer && this.trainer){
              this.selectedTrainer = this.trainer.id;
            }
            this.hideLoader();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    markAttendance(type) {
      if (!this.selectedTrainer) {
        this.showError("Please select trainer");
        return true;
      }
      var data = [];
      this.students.forEach((student) => {
        let obj = {};
        obj = {
          attendance_id: student.attendance_id,
          order_id: student.order_id,
          date: this.selectedCellDate,
          trainer_id: this.selectedTrainer,
          booking_schedule_id: student.workshop_booking_schedule_id,
          workshop_booking_id: student.workshop_booking_id,
          workshop_schedule_id: student.workshop_schedule_id,
        };
        if (student.is_check) {
          if (type === "present") {
            obj.is_attended = student.is_check ? 1 : 0;
          }
          if (type === "absent") {
            obj.is_attended = student.is_check ? 2 : 0;
          }
        }
        console.log("student.is_check",student.is_check);
        if (student.is_check) data.push(obj);
      });
      if (data.length > 0) {
        this.showLoader("Loading...");
        this.$http.post(`venues/workshops/schedules/booking/attendance`, {attendance: data,})
            .then((response) => {
              if (response.status == 200 && response.data.status) {
                this.timeChange();
                this.hideLoader();
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            });
      }
    },
    downloadFile(path) {
      if (this.$store.getters.checkExportPermission(this.$modules.workshops.management.id)) {
        return;
      }
      window.open(path, "_blank");
    },
    rnd(a, b) {
      return Math.floor((b - a + 1) * Math.random()) + a;
    },
    getWeekdays(days) {
      let dayName = "";
      days.forEach((element) => {
        let findValue = this.$store.getters.getWeekdays.data.find(
            (y) => y.bit_value == element
        );
        if (findValue) {
          dayName = dayName.concat(findValue.name.substring(0, 3), ",");
        }
      });
      return dayName.slice(0, -1);
    },
    refund(student) {
      this.confirmModel = {
        id: student.attendance_id,
        title: `Do you want refund this student booking?`,
        description: `This will refund selected student booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "refund",
        data: student,
      };
    },
    cancelClass() {
      this.confirmModel = {
        id: this.selectedWorkshopScheduleId,
        title: `Do you want cancel this class?`,
        description: `By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel",
        data: this.selectedTimeslot,
      };
    },
    cancelBookings() {
      this.confirmModel = {
        id: this.selectedWorkshopScheduleId,
        title: `Do you want cancel this bookings?`,
        description: `By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel_bookings",
        data: this.selectedTimeslot,
      };
    },
    showReceipt(order_id) {
      this.order_id_for_payment = order_id;
    },
    confirmActions(data) {
      if (data.type == "refund") {
        if (data.id && data.data) {
          this.workshopCustomerAttendanceId = data.id;
          this.showLoader("Loading...");
          this.$http.get(`venues/workshops/schedules/booking/single-class-refund-amount/${data.data.order_id}`)
              .then((response) => {
                if (response.status === 200 && response.data.status) {
                  this.hideLoader();
                  if (response.data.type === "partial" || response.data.type === "normal") {
                    this.payments = [
                      {
                        card_type_id: null,
                        payment_code: null,
                        payment_method_id: null,
                        amount: null,
                        payment_method: null,
                      },
                    ];
                    this.$store.commit("setOrderPayments", this.payments);
                    this.workshopRefundAmount = response.data.refund_amount;
                    this.refund_dialog = true;
                    this.refundModel.invoiceId = 1;
                    this.refundModel.amount = response.data.refund_amount;
                    this.refundModel.type = "partial";
                  }
                }
              })
              .catch((error) => {
                this.errorChecker(error);
              });
        }
      } else if (data.type == "cancel") {
        if (data.id) {
          this.showLoader("Loading...");
          var cancel = {
            workshop_schedule_id: data.id,
            date: this.date,
          };
          this.showLoader("Cancelling class...");
          this.$http
              .post(`venues/workshops/schedules/class-cancel`, cancel)
              .then((response) => {
                if (response.status == 200 && response.data.status == true) {
                  this.selectedTimeslot.is_cancelled=1;
                  this.hideLoader();
                  this.$forceUpdate();
                }
              })
              .catch((error) => {
                this.errorChecker(error);
              });
        }
      } else if (data.type == "cancel_bookings") {
        if (data.id) {
          this.showLoader("Loading...");

          let cancelledStudents = [];
          this.students.forEach((element) => {
            if (element.is_cancel_booking == true) {
              cancelledStudents.push(element);
            }
          });
          if (cancelledStudents.length == 0) {
            this.showError("Please select at least one customer.");
            this.hideLoader();
            this.$forceUpdate();
          } else {
            var cancel_booking = {
              workshop_schedule_id: data.id,
              date: this.date,
              students: cancelledStudents,
            };
            console.log("cancel Bookings");
            console.log(cancel_booking);
            this.showLoader("Cancelling class...");
            this.$http
                .post(
                    `venues/workshops/schedules/class-bookings-cancel`,
                    cancel_booking
                )
                .then((response) => {
                  if (response.status == 200 && response.data.status == true) {
                    const workshopScheduleId =
                        this.selectedTimeslot.workshopScheduleId;
                    const weekdays = this.dayName;
                    const time = this.selectedTimeslot.time;
                    const is_reschedule = this.selectedTimeslot.is_reschedule;
                    const from_date = this.selectedTimeslot.from_date;
                    this.getBookedCustomer(
                        workshopScheduleId,
                        weekdays,
                        time,
                        is_reschedule,
                        from_date
                    );
                    this.hideLoader();
                    this.$forceUpdate();
                  }
                })
                .catch((error) => {
                  this.errorChecker(error);
                });
          }
        }
      }
      this.confirmModel.id = null;
    },
    transferSessionsModal(){
      console.log("tts calling", this.trainersList);
      console.log("slotdata", this.slotData);
      this.ttsDialog = true;
      this.$forceUpdate();
    },
    transferSuccess(){
      this.ttsDialog = false;
      this.showStudent = false;
      this.$emit("reloadSchedule");
    },
    transferStudentSessionModal(student){
      console.log("student", student);
      if(student && student.is_attended == 0 && student.order_status == 4){
        this.tssDialog = true;
        this.selectedStudent = student;
      }else{
        this.selectedStudent = null;
        this.showError("You can't transfer this sesison");
      }
    },
    transferStudentSessionSuccess(){
      this.tssDialog = false;
      this.selectedStudent = null;
      this.timeChange();
    }
  },
};
</script>
<style scoped >
.bg-teal-light{
  background-color: #4EAEAF;
}
.headline-new {
  background-color: rgb(233, 241, 246);
  color: black;
}
.v-center {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content:center;
}
.ca-table{
  th{
    font-size: 14px;
    font-weight: bold;
    color: #2b2b2b;
  }
  td{
    font-size: 12px;
    color: #000;
  }
}
.wc-attendance-modal .q-autocomplete ::v-deep .vs .v-select__selections{
  color:#fff !important;
}
</style>
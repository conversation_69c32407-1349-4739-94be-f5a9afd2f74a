<script>
export default {
  name: "B2bRequestRevision",
  props: {
    revisionModal: {
      type: Boolean,
      required: true
    },
    b2bId: {
      type: Number,
      required: true
    }
  },
  data(){
    return {
      valid:true,
      reason: '',
    }
  },
  methods:{
    closeConfigModel(){
      this.reason = '';
      this.$emit('closeModal', false);
    },
    submitRevision() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }

      this.showLoader("Requesting Revision");
      this.$http.post('/venues/b2b/request-revision', {
        reason: this.reason,
        b2b_id: this.b2bId
      }).then(response => {
        if (response.status == 200 && response.data.status == true) {
          this.$emit('closeModal', false);
          this.reason = '';
          this.showSuccess(response.data.message);
        }
      }).catch(error => {
        this.errorChecker(error)
      }).then(() => {
        this.hideLoader();
      });
    },
  }
}
</script>

<template>
  <v-dialog :value="revisionModal" max-width="500px" persistent scrollable width="40%">
    <v-form ref="form" v-model="valid">
      <v-card>
        <v-card-title class="border-bottom">
          <div class="w-full">
            <div class="d-flex justify-space-between align-center">
              <p class="mb-0 font-medium">
                Revision Request
              </p>
              <v-btn class="shadow-0" fab x-small @click="closeConfigModel">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </v-card-title>
        <v-card-text>
          <v-container>
            <div>
              <label for="">
                Reason for Revision
              </label>
              <v-textarea
                  v-model="reason"
                  :rules="[
                      (v) => !!v || 'Reason is required'
                    ]"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  hide-details="auto"
                  outlined
                  required
              ></v-textarea>
            </div>
            <div class="d-flex justify-end">
              <v-btn
                  v-if="checkWritePermission($modules.b2b.partner.slug)"
                  class="white--text teal-color mt-4"
                  height="40"
                  @click="submitRevision"
              >
                Request
              </v-btn>
            </div>
          </v-container>
        </v-card-text>
      </v-card>
    </v-form>
  </v-dialog>

</template>

<style scoped>

</style>
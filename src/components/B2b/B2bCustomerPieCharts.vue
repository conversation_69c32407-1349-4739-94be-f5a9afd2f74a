<template>
  <v-container fluid>
    <v-row>
      <v-col cols="4">
        <v-card height="420px">
          <v-card-title>
            <v-icon class="mr-1">mdi-chart-pie</v-icon>Nationality
            <v-spacer></v-spacer>
            <v-btn
              v-if="exportPermission && countryDataExist"
              small
              @click="exportPieData('country')"
              >Export</v-btn
            >
          </v-card-title>
          <pie-chart
            :data="countryPieChart"
            v-if="countryDataExist"
          ></pie-chart>
          <div v-else class="d-flex fill-height align-center justify-center">
            <div class="pb-4">No data exist</div>
          </div>
        </v-card>
      </v-col>
      <v-col cols="4">
        <v-card height="420px">
          <v-card-title>
            <v-icon class="mr-1">mdi-chart-pie</v-icon>Age
            <v-spacer></v-spacer>
            <v-btn
              v-if="exportPermission && ageDataExist"
              small
              @click="exportPieData('age')"
              >Export</v-btn
            >
          </v-card-title>
          <pie-chart v-if="ageDataExist" :data="agePieChart"></pie-chart>
          <div v-else class="d-flex fill-height align-center justify-center">
            <div class="pb-4">No data exist</div>
          </div>
        </v-card>
      </v-col>
      <v-col cols="4">
        <v-card height="420px">
          <v-card-title>
            <v-icon class="mr-1">mdi-chart-pie</v-icon>Gender
            <v-spacer></v-spacer>
            <v-btn
              v-if="exportPermission && genderDataExist"
              small
              @click="exportPieData('gender')"
              >Export</v-btn
            >
          </v-card-title>
          <pie-chart v-if="genderDataExist" :data="genderPieChart"></pie-chart>
          <div v-else class="d-flex fill-height align-center justify-center">
            <div class="pb-4">No data exist</div>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import PieChart from "./../Chart/PieChart";
export default {
  components: {
    PieChart,
  },
  props: {
    date1: { type: String, default: null },
    date2: { type: String, default: null },
    params: {
      type: Object,
      default: () => {
        return {
          venue_service_ids: [],
          product_ids: [],
          product_type_ids: [],
          b2b_id: null,
        };
      },
    },
    productTypeIds: { type: Array, default: () => [] },
    venueServiceIds: { type: Array, default: () => [] },

    "export-permission": { type: Boolean, default: true },
    "show-venue-service": { type: Boolean, default: true },
    "show-product-type": { type: Boolean, default: false },
    "show-chart-headder": { type: Boolean, default: true },
    "stack-type": { type: String, default: "all" }, //service,type,all
    reload: { type: Boolean, default: false },
    "date-toggle": { type: String, default: "DATE" }, //service,type,all
  },
  watch: {
    productTypeIds: {
      handler(val) {
        if (val) {
          this.getCustomerGraphData();
        }
      },
    },
    venueServiceIds: {
      handler(val) {
        if (val) {
          this.getCustomerGraphData();
        }
      },
    },
    date1: {
      handler(val) {
        if (val) {
          this.getCustomerGraphData();
        }
      },
    },
    date2: {
      handler(val) {
        if (val) {
          this.getCustomerGraphData();
        }
      },
    },
    params: {
      immediate: true,
      handler(params) {
        // if (params.venue_service_ids.length > 0) {
        //   this.venueServiceIds = params.venue_service_ids;
        // } else {
        //   this.venueServiceIds = [];
        // }
        // if (params.product_type_ids.length > 0) {
        //   this.productTypeIds = params.product_type_ids;
        // } else {
        //   this.productTypeIds = [];
        // }
        if (params.product_ids.length > 0) {
          this.productIds = params.product_ids;
        } else {
          this.productIds = [];
        }
        if (params.b2b_id && params.b2b_id != null) {
          this.b2b_id = params.b2b_id;
        }
        this.getCustomerGraphData();
      },
    },
    reload: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getCustomerGraphData();
        }
      },
    },
  },
  data() {
    return {
      agePieChart: null,
      genderPieChart: null,
      countryPieChart: null,
      b2b_id: null,
      productIds: [],

      genderDataExist: true,
      countryDataExist: true,
      ageDataExist: true,
    };
  },
  mounted() {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
    if (this.$store.getters.getProductTypes.status == false) {
      this.$store.dispatch("loadProductTypes");
    }
  },
  computed: {
    venueServices() {
      return this.$store.getters.getVenueServices.data;
    },
    productTypes() {
      return this.$store.getters.getProductTypes.data;
    },
  },
  methods: {
    dateChange(data) {
      this.date1 = data.date1;
      this.date2 = data.date2;
      this.getCustomerGraphData();
    },
    getCustomerGraphData() {
      let url = `?date_type=${this.dateToggle.toLowerCase()}&start_date=${
        this.date1
      }&end_date=${this.date2}`;

      if (
        this.venueServiceIds.length != this.venueServices.length &&
        this.venueServiceIds.length > 0
      ) {
        url += `&${this.venueServiceIds
          .map((n, index) => `venue_service_ids[${index}]=${n}`)
          .join("&")}`;
      }

      if (
        this.productTypeIds.length != this.productTypes.length &&
        this.productTypeIds.length > 0
      ) {
        url += `&${this.productTypeIds
          .map((n, index) => `product_type_ids[${index}]=${n}`)
          .join("&")}`;
      }

      if (this.productIds.length > 0) {
        url += `&${this.productIds
          .map((n, index) => `product_ids[${index}]=${n}`)
          .join("&")}`;
      }
      let lastUrl = "";
      if (this.b2b_id && this.b2b_id != null) {
        url += `&b2b=${this.b2b_id}`;
        lastUrl = `venues/b2b/customers${url}`;
      } else {
        lastUrl = `venues/graphs/customers${url}`;
      }
      this.$http
        .get(lastUrl)
        .then((response) => {
          if (response.status == 200) {
            const data = response.data.data;
            this.genderPieChart = data.gender;
            this.countryPieChart = data.country;
            this.agePieChart = data.age;

            this.ageDataExist = false;
            for (let key in this.agePieChart) {
              if (this.agePieChart[key]) {
                this.ageDataExist = true;
              }
            }

            this.countryDataExist = false;
            for (let key in this.countryPieChart) {
              if (this.countryPieChart[key] > 0) {
                this.countryDataExist = true;
              }
            }

            this.genderDataExist = false;
            for (let key in this.genderPieChart) {
              if (this.genderPieChart[key]) {
                this.genderDataExist = true;
              }
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    exportPieData(type) {
      let data = this[type + "PieChart"];
      let csvContent = "data:text/csv;charset=utf-8,Key,Count\r\n";
      for (let key in data) {
        let row = key + "," + (data[key] ? data[key] : 0) + "\r\n";
        csvContent += row;
      }
      var encodedUri = encodeURI(csvContent);
      var link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", type + ".csv");
      document.body.appendChild(link);
      link.click();
    },
  },
};
</script>

<style></style>

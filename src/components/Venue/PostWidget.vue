<template>
  <v-card mt-3 class="mx-auto" max-width="400">
    <view-image :height="200" :image="image">
      <template v-slot:overlay>
        <v-row align="end" class="lightbox white--text pa-2 fill-height">
          <v-col class="post_headings">
            <div class="subheading text-left">{{ title }}</div>
            <div class="heading font-italic font-weight-thin">
              <v-icon dark x-small>mdi-clock-outline</v-icon>
              {{ created | timeStamp }}
            </div>
          </v-col>
        </v-row>
      </template>
    </view-image>
    <v-bottom-navigation>
      <v-btn @click="getLikes">
        <div class="badge">
          <v-badge
            color="red"
            :content="likes_count"
            v-if="likes_count > 0"
          ></v-badge>
        </div>
        <v-img style="height: 0px" src="@/assets/images/krews/like.png"></v-img>
      </v-btn>
      <v-spacer></v-spacer>
      <v-btn @click="getComments">
        <v-icon>mdi-comment-text-outline</v-icon>
        <v-badge
          color="red"
          :content="comments_count"
          v-if="comments_count > 0"
        ></v-badge>
      </v-btn>
    </v-bottom-navigation>
    <v-tooltip bottom>
      <template v-slot:activator="{ on, attrs }">
        <v-btn
          v-bind="attrs"
          v-on="on"
          v-if="checkDeletePermission($modules.general.posts.slug)"
          fab
          dark
          x-small
          absolute
          top
          right
          color="red"
          value="nearby"
          @click="deletePost"
        >
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
      Delete
    </v-tooltip>
  </v-card>
</template>

<script>
export default {
  props: {
    id: { type: Number, default: 0 },
    index: { type: Number, default: 0 },
    likes_count: { type: String, default: "0" },
    comments_count: { type: String, default: "0" },
    image: { type: String, default: null },
    created: { type: String, default: null },
    title: { type: String, default: null },
  },

  methods: {
    deletePost() {
      this.$emit("delete", { id: this.id, index: this.index });
    },
    getLikes() {
      this.$emit("like", this.id);
    },
    getComments() {
      this.$emit("comment", this.id);
    },
  },
};
</script>

<style scoped>
.post_title {
  text-shadow: #fcfcfc 0px 0px 1px;
  color: #000000;
}
.theme--light.v-icon {
  color: rgb(6, 106, 140) !important;
}
.add_btn {
  width: 100px;
  height: 100px;
}
.add_post .theme--light.v-card {
  background-color: transparent;
  color: none;
}
.add_post .v-card {
  -webkit-box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0),
    0px 1px 1px 0px rgba(0, 0, 0, 0), 0px 1px 3px 0px rgba(0, 0, 0, 0);
  box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0),
    0px 1px 1px 0px rgba(0, 0, 0, 0), 0px 1px 3px 0px rgba(0, 0, 0, 0);
}
.badge {
  position: absolute;
  top: 5px;
}
.post_headings {
  background: rgb(0, 0, 0);
  background: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.11) 0%,
    rgba(0, 0, 0, 0.4) 23%,
    rgba(0, 0, 0, 0.5) 72%,
    rgba(0, 0, 0, 0) 99%
  );
  color: #fff;
}
.v-btn--fab.v-btn--fixed,
.v-btn--fab.v-btn--absolute {
  z-index: 0 !important;
}
</style>
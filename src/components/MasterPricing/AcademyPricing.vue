<template>
  <v-container fluid>
    <v-row>
      <!-- Academies List -->
      <v-col cols="3" md="3" class="pr-0">
        <v-list class="fixed-height py-0  bordered rounded-t-2">
          <v-list-item class="list-item list-heading">
            <v-list-item-content>
              <v-list-item-title >Academies List</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item
              v-for="(workshop, index) in workshops"
              :key="index"
              @click="selectWorkshop(workshop)"
              :class="{ 'blue--text': selectedWorkshop.id === workshop.id }"
              class="list-item border-bottom"
          >
            <v-list-item-content>
              <v-list-item-title>{{ workshop.name }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-col>

      <!-- Packages Table -->
      <v-col cols="9"  md="9">
        <v-card class="card-background-silver rounded-2 shadow-0">
          <v-card-title>
            {{ selectedWorkshop.name }}
          </v-card-title>

          <v-card-text class="rounded-2 fixed-height">
            <v-simple-table class="table p-4 table-bordered overflow-y-auto" style="background-color: #FFFFFF; border-radius: 12px ;">
              <thead>
              <tr class="">
                <td>Product Name</td>
<!--                <td>Type</td>-->
                <td>Price</td>
                <td>Tax</td>
                <td>Price (Inc Vat)</td>
                <td>Actions</td>
              </tr>
              </thead>
              <tbody>
              <tr
                  v-for="(product,
                    tp) in selectedWorkshop.products"
                  :key="tp"
              >
                <td>{{ product.name }}</td>
<!--                <td>{{ getPackageType(product.ticket_type) }}</td>-->
                <td>{{ product.price  | toCurrency }}</td>
                <td>{{ product.tax_amount  | toCurrency }}</td>
                <td>{{ product.total_price  | toCurrency }}</td>
                <td class="text-center">
                  <v-btn
                      icon
                      @click="editProduct(product)"
                  >
                    <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
                  </v-btn>
                </td>
              </tr>
              </tbody>
            </v-simple-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import EditIcon from "@/assets/images/tables/edit.svg";

export default {
  components: {EditIcon},
  mounted() {
    this.loadAcademies();
  },
  props:{
    refresh:{ type:Boolean, default: false},
  },
  watch: {
    refresh(){
      this.loadAcademies();
    }
  },
  data() {
    return {
      workshops: [],
      selectedWorkshop: {
        name:'',
        products: [],
      },
      types:[
        {
          name:'Individual',
          key:'I'
        },
        {
          name:'Couple',
          key:'C'
        },
        {
          name:'Group',
          key:'G'
        },
      ],
    };
  },
  methods: {
    editProduct(product) {
      this.$emit('editProduct', product);
    },
    getPackageType(key){
      const type = this.types.find(type => type.key === key);
      return type ? type.name : 'NA';
    },
    selectWorkshop(workshop) {
      this.selectedWorkshop = workshop;
      this.loadWorkshopProducts();
    },
    loadAcademies(){
      this.showLoader('Loading Academies');
      this.$http.get(`venues/master-pricing/get/workshops`)
          .then((response) => {
            if (response.status == 200 && response.data.status) {
              this.workshops = response.data.data;
              if(this.workshops.length > 0){
                this.selectWorkshop(this.workshops[0]);
              }
            }
          }).catch(error => {
        this.errorChecker(error);
        return false;
      }).finally(() => {
        this.hideLoader()
      })
    },
    loadWorkshopProducts(){
      if(!this.selectedWorkshop.id){
        return;
      }
      this.selectedWorkshop.products = [];
      this.showLoader("Loading products");
      this.$http.get(`venues/master-pricing/get/products/workshops/${this.selectedWorkshop.id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status) {
              this.selectedWorkshop.products = response.data.data;
              console.log(this.selectedWorkshop)
              this.$forceUpdate()
            }
          }).catch(error => {
        this.errorChecker(error);
        return false;
      }).finally(() => {
        this.hideLoader()
      })
    },
  },
}
</script>

<style scoped>
.list-item {
  cursor: pointer;
  padding: 5px 10px 5px 10px;
  text-align: center;
}

.list-item.blue--text {
  background-color: #112A46;
}

.card-background-silver{
  background-color: #F0F5F9;
}

.list-item.v-list-item.v-list-item--link.theme--light.blue--text {
  color:white !important;
}

.rounded-t-2{
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.bordered {
  border: 1px solid #EAEAEA !important;
  overflow: hidden;
}

.list-heading{
  background: #E9F1F6 !important;
  color: black;
  font-weight: bold;
}

.fixed-height {
  max-height: calc(100vh - 200px);
  overflow: auto;
}

</style>
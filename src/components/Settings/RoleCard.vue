
<template>
  <v-card class="shadow-0 bordered">
    <v-card-title class="border-bottom pa-3">
      <div class=" w-full">
        <div class="d-flex justify-space-between align-center">
          <p class="mb-0 font-medium font-16">
            <b>{{ title }}</b>
          </p>
          <v-menu
              content-class="q-menu"
              offset-y
          >
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                  class="text_capitalize px-0 no-hover-effect"
                  elevation="0"
                  height="24"
                  style="background-color: transparent; min-width: 24px !important; "
                  v-bind="attrs"
                  v-on="on"
              >
                <DotsIcon height="24" width="24"/>
              </v-btn>
            </template>
            <v-list class="pointer">
              <v-list-item v-if="checkWritePermission($modules.settings.roles.slug)"
                           @click.stop="$emit('edit', id)">
                <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
                  <template #icon>
                    <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
                  </template>
                </SvgIcon>
              </v-list-item>
              <v-list-item
                  v-if="venue_id && checkDeletePermission($modules.settings.roles.slug)"
                  @click.stop="deleteRole">
                <SvgIcon class="font-medium text-sm gap-x-2 text-red" text="Delete">
                  <template #icon>
                    <DeleteIcon stroke="#E50000"/>
                  </template>
                </SvgIcon>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
    </v-card-title>
    <v-card-text class="pa-3 border-bottom">
      <div class="d-flex justify-space-between align-center px-10">
        <div>
          <span class="text-black font-14">Modules: {{ Number(modules) | numberFormatter }}</span>
        </div>
        <div>
          <span class="text-black font-14">Submodules: {{ Number(sub_modules) | numberFormatter }}</span>
        </div>
      </div>
    </v-card-text>
<!--    <v-card-actions class="pa-1">-->
<!--      <v-spacer></v-spacer>-->
<!--      <v-btn-->
<!--          class="ma-2 text-capitalize" text-->
<!--          v-if="checkWritePermission($modules.settings.roles.slug)"-->
<!--          @click="$emit('edit', id)"-->
<!--      >-->
<!--        <SvgIcon class="font-medium text-sm gap-x-2" text=" ">-->
<!--          <template #icon>-->
<!--            <EditIcon height="16" viewBox="0 0 20 20" width="16"/>-->
<!--          </template>-->
<!--        </SvgIcon>-->
<!--      </v-btn>-->
<!--      <v-spacer></v-spacer>-->
<!--      <v-btn-->
<!--          @click="deleteRole"-->
<!--          class="ma-2 text-capitalize" text-->
<!--          v-if="venue_id && checkDeletePermission($modules.settings.roles.slug)"-->
<!--      >-->
<!--        <SvgIcon class="font-medium text-sm gap-x-2 text-red" text="">-->
<!--          <template #icon>-->
<!--            <DeleteIcon stroke="#E50000"/>-->
<!--          </template>-->
<!--        </SvgIcon>-->
<!--      </v-btn>-->
<!--      <v-spacer v-if="venue_id"></v-spacer>-->
<!--    </v-card-actions>-->
  </v-card>
<!--  <v-card color="#3e4c55" class="mt-2 mr-5 ml-2">-->
<!--    <v-row align="end" class="fill-height">-->
<!--      <v-col class="pt-0">-->
<!--        <v-list-item color="rgba(0, 0, 0, .4)" dark>-->
<!--          <v-list-item-content>-->
<!--            <v-list-item-title class="title">{{ title }}</v-list-item-title>-->
<!--          </v-list-item-content>-->
<!--        </v-list-item>-->
<!--        <v-row class="pl-2 pr-2">-->
<!--          <v-col col="12" md="6" sm="6">-->
<!--            <v-btn small block color="#008483" dark-->
<!--              >Modules: {{ Number(modules) | numberFormatter }}</v-btn-->
<!--            >-->
<!--          </v-col>-->
<!--          <v-col col="12" md="6" sm="6">-->
<!--            <v-btn small block color="#008483" dark-->
<!--              >Submodules:{{ Number(sub_modules) | numberFormatter }}</v-btn-->
<!--            >-->
<!--          </v-col>-->
<!--        </v-row>-->
<!--      </v-col>-->
<!--    </v-row>-->

<!--    <v-card-actions></v-card-actions>-->

<!--    <v-bottom-navigation>-->
<!--      <v-spacer></v-spacer>-->
<!--      <v-btn-->
<!--        text-->
<!--        v-if="checkWritePermission($modules.settings.roles.slug)"-->
<!--        @click="$emit('edit', id)"-->
<!--      >-->
<!--        <span>Edit</span>-->
<!--        <v-icon medium>mdi-pencil</v-icon>-->
<!--      </v-btn>-->
<!--      <v-spacer></v-spacer>-->
<!--      <v-btn-->
<!--        @click="deleteRole"-->
<!--        text-->
<!--        v-if="venue_id && checkDeletePermission($modules.settings.roles.slug)"-->
<!--      >-->
<!--        <span>{{ status_id == 1 ? "Delete" : "Restore" }}</span>-->
<!--        <v-icon>-->
<!--          {{ status_id == 1 ? "mdi-delete" : "mdi-delete-restore" }}-->
<!--        </v-icon>-->
<!--      </v-btn>-->
<!--      <v-spacer v-if="venue_id"></v-spacer>-->
<!--    </v-bottom-navigation>-->
<!--  </v-card>-->
</template>
<script>
import EditIcon from "@/assets/images/tables/edit.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
import DotsIcon from "@/assets/images/misc/dots.svg";

export default {
  components: {DotsIcon, DeleteIcon, SvgIcon, EditIcon},
  props: {
    id: { type: Number, default: 0 },
    status_id: { type: Number, default: 1 },
    venue_id: { type: Number, default: null },
    title: { type: String, default: "" },
    modules: { type: Number, default: 0 },
    sub_modules: { type: Number, default: 0 },
  },

  methods: {
    deleteRole() {
      this.$emit("delete", {
        id: this.id,
        status: this.status_id == 1 ? "delete" : "restore",
      });
    },
  },
};
</script>

<style scoped></style>

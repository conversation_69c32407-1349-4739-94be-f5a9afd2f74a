<template>
  <v-dialog :value="viewConfig" width="1000" @input="close" scrollable>
    <v-card>
      <v-card-title class="headline">Sun System Configuration</v-card-title>
      <!-- <v-card-text> -->
      <p class="mb-0 pl-4 pt-4"><b>Sun System JV</b></p>
      <!-- </v-card-text> -->
      <v-card-text class="pa-4">
        <v-card rounded outlined class="pa-2 mt-0">
          <v-row>
            <v-col cols="12" v-if="isEnableSunSystem">
              <template>
                <p class="mb-0 pl-4 pt-2">
                  <b> Version</b>
                </p>
                <v-col cols="3" md="3" sm="3">
                  <v-text-field
                    outlined
                    background-color="#fff"
                    v-model="version"
                    hide-details="auto"
                    class="q-text-field shadow-0"
                    dense
                  ></v-text-field>
                </v-col>
              </template>
              <template v-for="(method, index) in paymentMethods">
                <p class="mb-0 pl-4 pt-2" :key="`head_${index}`">
                  <b> {{ method.name }}</b>
                </p>
                <v-card-text class="" :key="`card_${index}`">
                  <v-card rounded outlined class="pa-4 mt-0">
                    <v-row :key="`${index}`">
                      <v-col cols="3" md="3" sm="3">
                        <label for="">Account code</label>
                        <v-text-field
                          outlined
                          background-color="#fff"
                          v-model="method.sunSystemAccountCode"
                          hide-details="auto"
                          class="q-text-field shadow-0"
                          dense
                          maxlength="10"
                        ></v-text-field>
                      </v-col>

                      <v-col cols="3" md="3" sm="3">
                        <label for="">GL code</label>
                        <v-text-field
                          outlined
                          background-color="#fff"
                          v-model="method.sunSystemGLCode"
                          hide-details="auto"
                          class="q-text-field shadow-0"
                          dense
                          maxlength="15"
                        ></v-text-field>
                      </v-col>

                      <!-- <v-col cols="3" md="3" sm="3">
                        <label for="">Analysis code 0</label>
                        <v-text-field
                          outlined
                          background-color="#fff"
                          v-model="method.sunSystemAnalysisCode0"
                          hide-details="auto"
                          class="q-text-field shadow-0"
                          dense
                          maxlength="15"
                        ></v-text-field>
                      </v-col> -->

                      <v-col cols="3" md="3" sm="3">
                        <label for="">Analysis code 1</label>
                        <v-text-field
                          outlined
                          background-color="#fff"
                          v-model="method.sunSystemAnalysisCode1"
                          hide-details="auto"
                          class="q-text-field shadow-0"
                          dense
                          maxlength="15"
                        ></v-text-field>
                      </v-col>

                      <v-col cols="3" md="3" sm="3">
                        <label for="">Analysis code 2</label>
                        <v-text-field
                          outlined
                          background-color="#fff"
                          v-model="method.sunSystemAnalysisCode2"
                          hide-details="auto"
                          class="q-text-field shadow-0"
                          dense
                          maxlength="15"
                        ></v-text-field>
                      </v-col>

                      <v-col cols="3" md="3" sm="3">
                        <label for="">Analysis code 7</label>
                        <v-text-field
                          outlined
                          background-color="#fff"
                          v-model="method.sunSystemAnalysisCode7"
                          hide-details="auto"
                          class="q-text-field shadow-0"
                          dense
                          maxlength="15"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-card-text>
              </template>
            </v-col>
          </v-row>
        </v-card>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn class="blue-color" dark @click="close">Close</v-btn>
        <v-btn class="teal-color" @click="saveConfiguration" dark>Save</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    viewConfig: { type: Boolean, default: false },
  },
  data() {
    return {
      isEnableSunSystem: false,
      sunSystemConfig: [],
      version: null,
    };
  },
  watch: {
    viewConfig: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getConfiguration();
        }
      },
    },
  },
  mounted() {
    if (this.$store.getters.getSunSystemPaymentMethods.status == false) {
      this.$store.dispatch("loadSunSystemPaymentMethods");
    }

    if (this.$store.getters.venueInfo.enable_sun_system) {
      this.isEnableSunSystem = true;
    } else {
      this.isEnableSunSystem = false;
    }
    this.getVenueDetails();
  },
  computed: {
    paymentMethods() {
      return this.$store.getters.getSunSystemPaymentMethods.data;
    },
  },
  methods: {
    getVenueDetails() {
      this.$http
        .get(`venues/profile`)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            const data = response.data.data;
            this.version = data.jv_version;
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    close() {
      this.operator_email = "";
      this.$emit("close");
    },
    saveConfiguration() {
      const formData = new FormData();
      if (this.isEnableSunSystem) {
        if (this.version) {
          formData.append(`version`, this.version);
        }
        this.paymentMethods.forEach((method) => {
          // Check and append data for each payment method using hierarchical structure

          if (method.sunSystemAccountCode) {
            formData.append(
              `sun_system[${method.source_type}][${method.id}][sun_system_account_code]`,
              method.sunSystemAccountCode
            );
          }
          if (method.sunSystemGLCode) {
            formData.append(
              `sun_system[${method.source_type}][${method.id}][sun_system_gl_code]`,
              method.sunSystemGLCode
            );
          }
          // if (method.sunSystemAnalysisCode0) {
          //   formData.append(
          //     `sun_system[${method.source_type}][${method.id}][sun_system_analysis_code_0]`,
          //     method.sunSystemAnalysisCode0
          //   );
          // }
          if (method.sunSystemAnalysisCode1) {
            formData.append(
              `sun_system[${method.source_type}][${method.id}][sun_system_analysis_code_1]`,
              method.sunSystemAnalysisCode1
            );
          }
          if (method.sunSystemAnalysisCode2) {
            formData.append(
              `sun_system[${method.source_type}][${method.id}][sun_system_analysis_code_2]`,
              method.sunSystemAnalysisCode2
            );
          }
          if (method.sunSystemAnalysisCode7) {
            formData.append(
              `sun_system[${method.source_type}][${method.id}][sun_system_analysis_code_7]`,
              method.sunSystemAnalysisCode7
            );
          }
        });
      }

      console.log("Form Data Entries:");
      formData.forEach((value, key) => {
        console.log(key, value);
      });
      this.$http
        .post(`venues/configurations/sun-system`, formData)
        .then((response) => {
          if (response.status == 200 && response.data.status == true) {
            this.showSuccess("Configuration Saved");
            this.close();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    getConfiguration() {
      this.$http
        .get(`venues/configurations/sun-system`)
        .then((response) => {
          if (response.status === 200 && response.data.status === true) {
            let data = response.data.data;
            if (data) {
              data.forEach((config) => {
                // Determine source_type based on card_type_id
                const sourceType = config.card_type_id ? "card" : "method";
                const matchId = config.card_type_id || config.payment_method_id;

                const method = this.paymentMethods.find(
                  (m) => m.id === matchId && m.source_type === sourceType
                );

                if (method) {
                  this.$set(
                    method,
                    "sunSystemAccountCode",
                    config.account_code
                  );
                  this.$set(method, "sunSystemGLCode", config.gl_code);
                  this.$set(
                    method,
                    "sunSystemAnalysisCode1",
                    config.analysis_code_1
                  );
                  this.$set(
                    method,
                    "sunSystemAnalysisCode2",
                    config.analysis_code_2
                  );
                  this.$set(
                    method,
                    "sunSystemAnalysisCode7",
                    config.analysis_code_7
                  );
                }
              });
            }
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
  },
};
</script>

<style></style>

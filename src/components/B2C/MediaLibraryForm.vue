<template>
  <v-container fluid>
    <BackButton :handler="goToB2CMediaLibrary"/>
    <v-container class="form_container" fluid no-gutters style="max-width: 95% !important;">
      <v-form ref="form" v-model="valid">
        <div class="d-flex justify-space-between align-center">
          <h3 class="text-base font-semibold black-text ml-2">Resource Details</h3>
          <v-switch
              v-model="enable_arabic"
              :false-value="0"
              :true-value="1"
              class="mx-4 my-0"
              dense
              hide-details="auto"
              label="Arabic"
          ></v-switch>
        </div>
        <v-card class="rounded shadow-0 bordered mt-2">
          <v-card-text>
            <v-row dense>
              <v-col class="pr-8" cols="12" md="7" sm="12">
                <v-row dense>
                  <v-col cols="12" md="6" sm="12">
                    <label for="">Type*</label>
                    <v-select
                        v-model="resource.type"
                        :error-messages="errors.type"
                        :items="resourceTypes"
                        :rules="[(v) => !!v || 'Type is required']"
                        class="q-autocomplete shadow-0"
                        dense
                        hide-details="auto"
                        item-text="label"
                        item-value="value"
                        outlined
                        required
                        validate-on-blur
                        @change="onTypeChange"
                    ></v-select>
                  </v-col>
                  <v-col cols="12" md="6" sm="12">
                    <label for="">Tag*</label>
                    <v-select
                        v-model="resource.b2c_media_library_tags_id"
                        :error-messages="errors.b2c_media_library_tags_id"
                        :items="tags"
                        :rules="[(v) => !!v || 'Tag is required']"
                        class="q-autocomplete shadow-0"
                        dense
                        hide-details="auto"
                        item-text="name"
                        item-value="id"
                        outlined
                        required
                        validate-on-blur
                    ></v-select>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <label for="">Title*</label>
                    <v-text-field
                        v-model="resource.title"
                        :error-messages="errors.title"
                        :rules="[(v) => !!v || 'Title is required']"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                        required
                        validate-on-blur
                    ></v-text-field>
                  </v-col>
                  <v-col v-if="enable_arabic" cols="12" md="12" sm="12">
                    <label for="">Title(AR)</label>
                    <v-text-field
                        v-model="resource.ar_title"
                        :error-messages="errors.ar_title"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                        validate-on-blur
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <label for="">Description</label>
                    <v-textarea
                        v-model="resource.description"
                        :error-messages="errors.description"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                        rows="4"
                        validate-on-blur
                    ></v-textarea>
                  </v-col>
                  <v-col v-if="enable_arabic" cols="12" md="12" sm="12">
                    <label for="">Description(AR)</label>
                    <v-textarea
                        v-model="resource.ar_description"
                        :error-messages="errors.ar_description"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                        rows="4"
                        validate-on-blur
                    ></v-textarea>
                  </v-col>
                  <v-col cols="6">
                    <label for="">
                      Publish Date
                    </label>
                    <date-field
                        v-model="resource.published_at"
                        :back-fill="true"
                        hide-details="auto"
                        :rules="[(v) => !!v || 'Date is required']"
                        :dense="true"
                        label=""
                    >
                    </date-field>
                  </v-col>
                  <v-col cols="6">
                    <div class="d-flex align-items-center gap-x-4">
                      <v-switch
                          v-model="resource.is_public"
                          :false-value="0"
                          :true-value="1"
                          class="mx-4 my-0 v-input--reverse"
                          dense
                          hide-details="auto"
                      >
                        <template #label>
                          Enable Online
                        </template>
                      </v-switch>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col lg="5" md="5" sm="12">
                <div style="height: 200px">
                  <span style="text-transform: capitalize">
                    {{ resource.type }}
                  </span>
                  <image-uploader
                      v-if="['image', 'video'].includes(resource.type)"
                      :accept="accept"
                      :allow-video="resource.type === 'video'"
                      :height="200"
                      :image_path="documentImage"
                      defaultImage="blog"
                      messagePosition="inside"
                      messageText="Images and Videos are allowed"
                      text="Resource Media"
                      @remove="() => {resource.document = null;}"
                      @upload="(data) => {resource.document = data;}"
                  ></image-uploader>
                  <div v-else-if="['audio','document'].includes(resource.type)">
                    <label for="">{{
                        resource.document ? resource.document.original_file_name : 'Upload Media'
                      }}</label>
                    <v-file-input
                        v-model="resource.document"
                        :accept="accept"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                        prepend-icon
                        prepend-inner-icon="mdi-paperclip"
                    />
                  </div>
                  <div v-else>
                    <v-textarea
                        v-model="resource.embedded_link"
                        background-color="#fff"
                        class="q-text-field shadow-0"
                        dense
                        hide-details="auto"
                        outlined
                    />
                  </div>
                  <small v-if="errors.embedded_link" class="error--text">
                    {{ errors.embedded_link }}
                  </small>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <v-row class="mt-6">
          <v-btn v-if="resource.id && checkDeletePermission($modules.b2cconfig.mediaLibrary.slug)"
                 class="ma-2 shadow-0 text-white" color="red"
                 @click="deleteResource">
            Delete
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn v-if="checkWritePermission($modules.b2cconfig.mediaLibrary.slug)"
                 class="ma-2 white--text blue-color shadow-0"
                 @click="addOrEditResource">
            {{ resource.id != null ? "Update" : "Add" }} resoruce
          </v-btn>
        </v-row>
      </v-form>

      <confirm-model
          v-bind="confirmModel"
          @close="confirmModel.id = null"
          @confirm="confirmActions"
      ></confirm-model>
    </v-container>
  </v-container>
</template>
<script>
import BackButton from "@/components/Common/BackButton.vue";
import ImageUploader from "@/components/Image/ImageUploader.vue";
import moment from 'moment'

export default {
  components: {
    ImageUploader,
    BackButton,
  },
  mounted() {
    this.resourceId = this.$route.params.id ? atob(this.$route.params.id) : null;
    this.getMediaLibraryTags()
    if (this.resourceId) {
      this.getBlog(this.resourceId);
    }
  },
  computed: {
    documentImage() {
      if (this.resource.document && this.resource.document.file_path) {
        return this.resource.document.file_path;
      }
      return this.resource.document && typeof this.resource.document === 'string' ? this.resource.document : null;
    },
    accept() {
      switch (this.resource.type) {
        case 'image':
          return 'image/*';
        case 'video':
          return 'video/*';
        case 'audio':
          return 'audio/*';
        case 'document':
          return 'application/pdf';
        case 'embedded':
          return null;
      }
      return 'image/*';
    }
  },
  data() {
    return {
      tags: [],
      valid: true,
      resourceId: null,
      resourceTypes: [
        {
          label: 'Image',
          value: 'image'
        },
        {
          label: 'Video',
          value: 'video'
        },
        {
          label: 'Audio',
          value: 'audio'
        },
        {
          label: 'Document',
          value: 'document'
        },
        {
          label: 'Embedded',
          value: 'embedded'
        }
      ],
      resource: {
        type: 'image',
        title: null,
        ar_title: null,
        description: '',
        ar_description: '',
        document: null,
        is_public: 1,
        b2c_media_library_tags_id: null,
        embedded_link:null,
        published_at:moment().format('YYYY-MM-DD')
      },
      form: null,
      errors: {},
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      enable_arabic: 0,
      initialType: {
        type: null,
        document: null
      }
    }
  },
  methods: {
    getMediaLibraryTags() {
      this.$http
          .get(`venues/b2c/media-library/tags`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              if (data.length) {
                this.tags = data;
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getBlog(id) {
      this.showLoader("Loading")
      this.$http
          .get(`venues/b2c/media-library/${id}`)
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.resource = response.data?.data || {};
              this.enable_arabic = (!!this.resource.ar_title || !!this.resource.ar_description) ? 1 : 0;
              this.initialType.type = this.resource.type;
              this.initialType.document = this.resource.document;
            }
          })
          .catch((error) => {
            this.showError(error)
          })
          .finally(() => {
            this.hideLoader()
          });
    },
    goToB2CMediaLibrary() {
      this.$router.push({name: "B2CMediaLibraries"});
    },
    addOrEditResource() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all fields");
        return;
      }
      this.showLoader("Please wait...")
      this.errors = {};
      const formData = new FormData();
      Object.keys(this.resource).forEach((key) => {
        if (this.resource[key] !== null && this.resource[key] !== undefined && key !== 'document') {
          formData.append(key, this.resource[key]);
        }
      });

      if (this.resourceId && this.resource.document?.id) {
        formData.append('document', this.resource.document.id);
      } else if (this.resource.document) {
        formData.append('document', this.resource.document);
      }
      formData.append('enable_arabic', this.enable_arabic);


      this.$http
          .post(`venues/b2c/media-library` + (this.resourceId ? '/' + this.resourceId : ''), formData)
          .then((response) => {
            if (response.status === 200 && response.data.status) {
              this.showSuccess(response.data.message);
              this.$router.push({name: "B2CMediaLibraries"});
            }
          })
          .catch((error) => {
            if (error.response?.status === 422) {
              Object.keys(error.response.data.data).forEach((key) => {
                this.errors = {
                  ...this.errors,
                  [key]: error.response.data.data[key][0]
                }
              })
            }
            this.showError(error.response.data.message || error)
          })
          .finally(() => {
            this.hideLoader()
          });
    },
    deleteResource() {
      this.confirmModel = {
        id: this.resourceId,
        title: "Delete Resource",
        description: "Are you sure you want to delete this resource?",
        type: 'delete'
      }
    },
    confirmActions(data) {
      if (data.type === 'delete') {
        this.$http
            .delete(`venues/b2c/media-library/${data.id}`)
            .then((response) => {
              if (response.status === 200 && response.data.status) {
                this.showSuccess(response.data.message);
                this.$router.push({name: "B2CLibraries"});
              }
            })
            .catch((error) => {
              this.showError(error)
            })
      }
    },
    onTypeChange(type) {
      if (this.resourceId) {
        if (this.initialType.type !== type) {
          this.resource.document = null;
        } else if (this.initialType.document) {
          this.resource.document = this.initialType.document;
        }
      } else {
        this.resource.document = null;
      }
      this.$forceUpdate()
    }
  }
  ,
}

</script>

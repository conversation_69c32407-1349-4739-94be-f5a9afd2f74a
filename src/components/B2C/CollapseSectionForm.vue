<template>
    <div>
        <v-dialog
        scrollable
        persistent
        v-model="collapseSectionPopup"
        width="70%"
        max-width="100%"
        :retain-focus="false"
        >
            <v-card height="100%">
                <v-form ref="form"  v-model="valid" lazy-validation>
                    <v-card-title class="headline">Add New Section
                        <v-btn
                            text
                            x-small
                            class="ma-2 white--text blue-color"
                            href="https://devapi-qportal-uae.s3.me-central-1.amazonaws.com/documents/section-types.pdf"
                            target="_blank"
                            >View Section Types</v-btn
                        >

                    </v-card-title>
                    <v-card-text class="pa-8">
                        <v-row>
                            <v-col cols="3" class="pb-0">
                                <v-switch
                                    class="mx-0 my-0"
                                    v-model="collapseSection.enable_section"
                                    label="Enable Section"
                                ></v-switch>
                            </v-col>
                            <v-col cols="4" class="pb-0">
                                <v-select
                                    v-model="collapseSection.section_type"
                                    :items="sectionTypes"
                                    item-value="id"
                                    outlined
                                    :menu-props="{ bottom: true, offsetY: true }"
                                    background-color="#fff"
                                    item-text="name"
                                    :label="`Section Type*`"
                                    required
                                    :rules="[(v) => !!v || 'Section type is required']"
                                    ></v-select>
                            </v-col>
                            <v-col cols="6" class="pb-0" v-if="[11,14,15].includes(collapseSection.section_type)">
                                <v-select
                                    v-model="collapseSection.event_ids"
                                    :items="filteredEvents"
                                    item-value="id"
                                    outlined
                                    :menu-props="{ bottom: true, offsetY: true }"
                                    background-color="#fff"
                                    item-text="name"
                                    :label="`Events*`"
                                    required
                                    :rules="[(v) => !!v || 'Events is required']"
                                    multiple
                                    >
                                </v-select>
                            </v-col>
                            <v-col cols="6" class="pb-0" v-if="[12,14,16].includes(collapseSection.section_type)">
                                <v-select
                                    v-model="collapseSection.academies_ids"
                                    :items="filteredAcademies"
                                    item-value="id"
                                    outlined
                                    :menu-props="{ bottom: true, offsetY: true }"
                                    background-color="#fff"
                                    item-text="name"
                                    :label="`Academy*`"
                                    required
                                    :rules="[(v) => !!v || 'Academy is required']"
                                    multiple
                                    >
                                </v-select>
                            </v-col>
                        </v-row>
                        <v-row v-if="[2,4,5,6,7,11,12,14,15,16].includes(collapseSection.section_type)">
                            <v-col cols="6" class="pb-0">
                                <v-text-field
                                    label="Title"
                                    outlined
                                    v-model="collapseSection.title"
                                    class="mx-0 my-0"
                                    required
                                ></v-text-field>
                            </v-col>
                            <v-col cols="6" class="pb-0" v-if="is_arabic_enabled">
                                <v-text-field
                                    label="Title (Ar)"
                                    outlined
                                    v-model="collapseSection.ar_title"
                                    class="mx-0 my-0"
                                    required
                                    dir="rtl"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row v-if="collapseSection.section_type == 4">
                            <v-col cols="6" class="pb-0">
                                <v-text-field
                                    label="Subtitle"
                                    outlined
                                    v-model="collapseSection.sub_title"
                                    class="mx-0 my-0"
                                ></v-text-field>
                            </v-col>
                            <v-col cols="6" class="pb-0" v-if="is_arabic_enabled">
                                <v-text-field
                                    label="Subtitle (Ar)"
                                    outlined
                                    v-model="collapseSection.ar_sub_title"
                                    class="mx-0 my-0"
                                    dir="rtl"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row v-if="![11,12,13,14,6,15,16].includes(collapseSection.section_type)">
                          <v-col>
                            <h3 class="pb-4">Description 123</h3>

                            <RichEditor
                                key="en_editior"
                                v-model="description"
                                contents-lang-direction="ltr"
                                language="en"
                            />
                            <!--                                 <text-editor-->
                            <!--                                    @complete="setDescriptionContent"-->
                            <!--                                    :message="description"-->
                            <!--                                    style="width: 100%"-->
                            <!--                                />-->
                          </v-col>
                        </v-row>
                        <v-row v-if="is_arabic_enabled && ![11,12,13,14,6,15,16].includes(collapseSection.section_type)">
                          <v-col>
                            <h3 class="pb-4">Description (Ar)</h3>
                            <RichEditor
                                key="ar_editior"
                                v-model="ar_description"
                                contents-lang-direction="rtl"
                                language="ar"
                            />
                            <!--                                 <text-editor-->
                            <!--                                    @complete="setArDescriptionContent"-->
                            <!--                                    :message="ar_description"-->
                            <!--                                    style="width: 100%"-->
                            <!--                                    dir="rtl"-->
                            <!--                                />-->
                          </v-col>
                        </v-row>
                        <v-row v-if="[3,4,5].includes(collapseSection.section_type)">
                          <v-col cols="6" class="pb-0">
                            <image-upload
                                @upload="
                                (data) => {
                                    collapseSection.cover_image = data;
                                }
                                "
                                @remove="
                                () => {
                                    collapseSection.cover_image = null;
                                }
                                "
                                :image_path="collapseSection.cover_path"
                                :height="300"
                                class="mr-5"
                                defaultImage="event"
                                :maxAspectRatio="16 / 9"
                            ></image-upload>
                            <p class="text-center mt-0 mb-8"><b>Section image</b></p>
                        </v-col>
                        </v-row>
                        <v-row v-if="collapseSection.section_type === 6">
                            <v-col cols="12" md="6" class="pb-0" v-for="(card,index) in collapseSection.cards" :key="index">
                                <image-upload
                                    @upload="
                                    (data) => {
                                        card.cover_image = data;
                                    }
                                    "
                                    @remove="
                                    () => {
                                        card.cover_image = null;
                                    }
                                    "
                                    :image_path="card.cover_path"
                                    :height="300"
                                    class="mr-5"
                                    defaultImage="event"
                                    :maxAspectRatio="16 / 9"
                                ></image-upload>
                              <div class="d-flex justify-space-between align-center gap-x-2">
                                <v-select
                                    class="my-0"
                                    v-model="card.page"
                                    :items="pagesPath"
                                    item-value="id"
                                    outlined
                                    :menu-props="{ bottom: true, offsetY: true }"
                                    background-color="#fff"
                                    item-text="name"
                                    :label="`Linked Page*`"
                                    required
                                    hide-details="auto"
                                    return-object
                                    :rules="[(v) => !!v || 'Linked Page is required']"
                                ></v-select>
                                <v-tooltip
                                    bottom
                                    v-if="collapseSection.cards.length>1"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-btn v-bind="attrs" v-on="on" icon @click="deleteCard(index)" x-small class="svg-stroke-red">
                                      <DeleteIcon/>
                                    </v-btn>
                                  </template>
                                  <span>Delete</span>
                                </v-tooltip>
                              </div>
                            </v-col>
                            <v-col cols="12" md="6" class="d-flex rounded-3 flex-column justify-center align-center min-h-300">
                                <v-btn
                                    color="#ffffff"
                                    elevation="0"
                                    fab
                                    style="color:#4FAEAF;"
                                    @click.stop="addCard"
                                >
                                  <v-icon>mdi-plus</v-icon>
                                </v-btn>
                            </v-col>
                        </v-row>

                    </v-card-text>
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                            text
                            class="ma-2 white--text blue-color"
                            @click="closeCollapseSectionPopupHandler"
                            >Close</v-btn
                        >
                        <v-btn
                            text
                            class="ma-2 white--text teal-color"
                            @click="saveOrUpdateCollapseSection">{{ collapseId? "Update" : "Save" }}</v-btn
                        >
                    </v-card-actions>
                </v-form>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
// import TextEditor from "@/components/Marketing/TextEditor";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
import RichEditor from "@/components/Common/RichEditor.vue";
export default {
    name: "CollapseSectionForm",
    components: {
      RichEditor,
      DeleteIcon,
        // TextEditor
    },
    props: {
        collapseId: { type: Number, default: null },
        sectionId: { type: Number, default: null },
        collapseSectionPopup: { type: Boolean, default: false },
        is_arabic_enabled: { type: Number, default: 0 },
        facilities: { type: Array, default: function() { return [] } },
        pagesPath: { type: Array, default: function() { return [] } },
    },
    watch: {
        collapseSectionPopup: {
            immediate: true,
            handler(val) {
                this.description = "";
                this.ar_description = "";
                console.log(val);
                if (this.sectionId && this.collapseSectionPopup) {
                    this.getCollapseSection();
                }
            },
        }
    },
  mounted() {
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch("loadVenueServices");
    }
    this.loadEvents(true);
    this.loadAcademies(true);

  },
  data() {
        return {
            valid: true,
            sectionTypes: [
                { id: 1, name: "Description" },
                { id: 2, name: "Title & Description" },
                { id: 3, name: "Description & Image" },
                { id: 4, name: "Title, Description & Image" },
                { id: 5, name: "Card : Title, Description & Image" },
                { id: 6, name: "Clickable Image" },
                { id: 7, name: "Accordion" },
                { id: 11, name: "Event Module" },
                { id: 12, name: "Workshop Module" },
                { id: 13, name: "Gallery Module" },
                { id: 14, name: "Events and Workshops" },
                { id: 15, name: "Past Events" },
                { id: 16, name: "Past Workshops" },
            ],
            allFacilities: [],
            collapseSection: {
                id: null,
                collapse_id: null,
                section_type: 1,
                event_ids:[],
                academies_ids:[],
                title: "",
                ar_title: "",
                sub_title: "",
                ar_sub_title: "",
                description: "",
                ar_description: "",
                enable_section: false,
                is_arabic_enabled: 0,
                cover_image: null,
                cover_path: null,
                cards: [
                  {
                    page: null,
                    cover_image: null,
                    cover_path: null,
                  }
                ],
            },
            description: "",
            ar_description: "",
            events: [],
            academies:[],
            past_events: [],
            past_academies:[],
        }
    },
    computed:{
      venueServices() {
        console.log(this.$store.getters.getVenueServices.data)
        return this.$store.getters.getVenueServices.data;
      },
      filteredEvents(){
        if (this.collapseSection.section_type === 11 || this.collapseSection.section_type === 14) {
          return this.events;
        }else if(this.collapseSection.section_type === 15){
          return this.past_events;
        }
        return [];
      },
      filteredAcademies(){
        if (this.collapseSection.section_type === 12 || this.collapseSection.section_type === 14) {
          return this.academies;
        }else if(this.collapseSection.section_type === 16){
          return this.past_academies;
        }
        return [];
      }
    },
    methods: {
        saveOrUpdateCollapseSection() {
            if (!this.$refs.form.validate()) {
                this.showError("Please fill all required fields");
                return;
            }
            this.showLoader("Saving...");
            let formData = new FormData();

            if (this.collapseSection && this.collapseId) {
                formData.append("collapse_id", this.collapseId);
                if (this.collapseSection.id) {
                    formData.append("id", this.collapseSection.id ? this.collapseSection.id : null);
                }
                if (this.collapseSection.event_ids && this.collapseSection.event_ids.length > 0) {
                   this.collapseSection.event_ids.forEach((event_id,index) => {
                    formData.append(`event_ids[${index}]`, event_id);
                   });
                }
                if (this.collapseSection.academies_ids && this.collapseSection.academies_ids.length > 0) {
                  this.collapseSection.academies_ids.forEach((academy_id,index) => {
                    formData.append(`workshop_ids[${index}]`, academy_id);
                  });
                }
                if (this.collapseSection.venue_service_id) {
                    formData.append("venue_service_id", this.collapseSection.venue_service_id);
                }
                if (this.collapseSection.title) {
                    formData.append("title", this.collapseSection.title);
                }else{
                    formData.append("title", "");
                }
                if (this.collapseSection.sub_title) {
                    formData.append("sub_title", this.collapseSection.sub_title);
                }else{
                    formData.append("sub_title", "");
                }
                if (this.description) {
                    formData.append("description", this.description);
                }
                formData.append("section_type", this.collapseSection.section_type);
                formData.append("enable_section", this.collapseSection.enable_section ? true : false);

                if (this.is_arabic_enabled) {
                    formData.append("is_arabic_enabled", true);
                    if (this.collapseSection.ar_title) {
                        formData.append("ar_title", this.collapseSection.ar_title);
                    }
                    if (this.collapseSection.ar_sub_title) {
                        formData.append("ar_sub_title", this.collapseSection.ar_sub_title);
                    }
                    if (this.ar_description) {
                        formData.append("ar_description", this.ar_description);
                    }
                }

                if (this.collapseSection.cover_image) {
                    formData.append("cover_image", this.collapseSection.cover_image);
                }

                if (this.collapseSection.cards && this.collapseSection.cards.length > 0) {
                  this.collapseSection.cards.forEach((card,index) => {
                    if (card.page) {
                      Object.keys(card.page).forEach(key => {
                        formData.append(`cards[${index}][page][${key}]`, card.page[key]);
                      });
                    }
                    if (card.cover_image) {
                      formData.append(`cards[${index}][cover_image]`, card.cover_image);
                    }else{
                      formData.append(`cards[${index}][cover_image]`, card.cover_path);
                    }
                  });
                }

                this.$http.post(`venues/b2c/additional/page/collapse/section`, formData,{
                    headers: {
                        "Content-Type": "multipart/form-data; boundary=${form._boundary}",
                    },
                }).then((response) => {
                    this.hideLoader();
                    this.collapseSection = {};
                    if (response.status == 200 && response.data.status == true) {
                        this.showSuccess("Collapse saved successfully.");
                        this.resetFormFields();
                        this.closeCollapseSectionPopupHandler();
                    }
                })
                .catch((error) => {
                    this.hideLoader();
                    this.errorChecker(error);
                });
            } else {
                this.hideLoader();
                this.showError("Data not found");
            }
        },
        closeCollapseSectionPopupHandler() {
            this.resetFormFields();
            this.$emit("closeCollapseSection");
        },
        setDescriptionContent(content) {
          this.description = content;
        },
        setArDescriptionContent(content) {
          this.ar_description= content;
        },
        getCollapseSection() {
        if (this.sectionId) {
          this.showLoader("Loading Section");
          this.$http.get(`venues/b2c/additional/page/collapse/section/${this.sectionId}`).then((response) => {
            this.hideLoader();
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data;
              this.collapseSection = data;
              this.collapseSection.academies_ids = (data.workshops || []).map(workshop => workshop.id);
              this.collapseSection.event_ids = (data.events || []).map(event => event.id);
              if (data.description) {
                this.description = data.description;
              }
              if (data.ar_description) {
                this.ar_description = data.ar_description;
              }
              if (data.cover_image_url) {
                this.collapseSection.cover_path = data.cover_image_url;
              }
              if (data.cards && data.cards.length > 0) {
                this.collapseSection.cards = data.cards.map(card => {
                  return {
                    page: {...card.page,id:parseInt(card.page.id)},
                    cover_path: card.cover_image,
                    cover_image: null,
                  }
                });
              }else{
                this.collapseSection.cards = [
                  {
                    page: null,
                    cover_image: null,
                    cover_path: null,
                  }
                ]
              }
            }
          })
              .catch((error) => {
                this.hideLoader();
                this.errorChecker(error);
              });
        }
      },
        resetFormFields() {
            let ob = {
                id: null,
                collapse_id: null,
                section_type: 1,
                title: "",
                ar_title: "",
                sub_title: "",
                ar_sub_title: "",
                description: "",
                ar_description: "",
                enable_section: false,
                is_arabic_enabled: 0,
                cover_image: null,
                cover_path: null,
                event_ids:[],
                academies_ids:[],
                cards:[]
            }
            this.description = "";
            this.ar_description = "";
            this.collapseSection = Object.assign({},ob);
        },
        loadEvents(allowPast=false){
            this.$http.get(`venues/b2c/events`,{
              params:{
                allowPast
              }
            }).then((response) => {
                if (response.status == 200 && response.data.status == true) {
                  this.events = [];
                  this.past_events = [];
                  const data = response.data.data
                  this.events = data.filter(event => event.status_id == 1);
                  this.past_events = data.filter(event => event.status_id == 12);
                }
            })
            .catch((error) => {
                this.errorChecker(error);
            });
        },
        loadAcademies(allowPast=false){
          this.$http.get(`venues/b2c/academies`,{
            params:{
              allowPast
            }
          }).then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.academies = [];
              this.past_academies = [];
              const data = response.data.data
              this.academies = data.filter(academy => academy.status_id == 1);
              this.past_academies = data.filter(academy => academy.status_id == 12);
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
        },
        addCard(){
          console.log(this.collapseSection)
          if (!this.collapseSection.cards) {
            this.collapseSection.cards = [];
          }
          this.collapseSection.cards.push({
            page: null,
            cover_image: null,
            cover_path: null,
          });
        },
        deleteCard(index){
          this.collapseSection.cards.splice(index, 1);
        }
    },
};
</script>

<style lang="scss" scoped>
.min-h-300{
  min-height: 300px !important;
}
</style>

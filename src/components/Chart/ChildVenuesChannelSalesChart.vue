<template>
  <v-container fluid>
    <v-row class="mt-3">
      <v-col cols="12">
        <div class="d-flex justify-space-between">
          <v-row class="align-center">
            <v-col cols="5">
              <v-select
                placeholder="Venues"
                required
                v-model="subVenueIds"
                outlined
                background-color="#fff"
                item-value="id"
                item-text="name"
                :menu-props="{ bottom: true, offsetY: true }"
                :items="subVenues"
                @change="callGraph"
                multiple
                class="q-autocomplete shadow-0"
                hide-details
                dense
                style="max-width: 240px !important"
              >
                <template
                  v-if="subVenueIds.length === subVenues.length"
                  v-slot:selection="{ index }"
                >
                  <span v-if="index === 0">All Venues</span>
                </template>
                <template v-else v-slot:selection="{ item, index }">
                  <span v-if="index === 0" class="text-elepsis">{{
                    item.name
                  }}</span>
                  <span v-if="index === 1" class="text-elepsis">, ..</span>
                </template>
                <template v-slot:prepend-item>
                  <v-list-item ripple @click="toggle">
                    <v-list-item-action>
                      <v-icon
                        :color="subVenueIds.length > 0 ? 'indigo darken-4' : ''"
                        >{{ icon() }}</v-icon
                      >
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title>Select All</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <v-divider class="mt-2"></v-divider>
                </template>
              </v-select>
            </v-col>
            <v-col cols="3">
              <v-btn-toggle
                borderless
                class="q-tabs shadow-0 bordered"
                style="height: 40px !important"
                v-model="dateToggle"
                :mandatory="mandatory"
                tile
              >
                <v-btn value="DATE" height="40">Day</v-btn>
                <v-btn value="MONTH" height="40">Month</v-btn>
                <v-btn value="YEAR" height="40">Year</v-btn>
              </v-btn-toggle>
            </v-col>
            <v-col cols="4">
              <date-range-field
                :dateType="dateToggle"
                :date1="date1"
                :date2="date2"
                @periodChange="dateChange"
                class-name="q-text-field shadow-0"
                :outlined="true"
                :dense="true"
              />
            </v-col>
          </v-row>
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="6">
        <v-card class="shadow rounded-5" height="380px">
          <div
            v-if="bookingsLoading"
            class="d-flex fill-height align-center justify-center"
          >
            <div class="pb-4">Loading...</div>
          </div>
          <div
            v-else-if="!bookingDataExist"
            class="d-flex fill-height align-center justify-center"
          >
            <div class="pb-4">No data exist</div>
          </div>
          <div class="d-flex justify-space-between p-5">
            <SvgIcon class="text-base font-semibold" text="Bookings">
              <template v-slot:icon> </template>
            </SvgIcon>
          </div>
          <pie-chart v-if="bookingDataExist" :data="bookingsPieChart" />
        </v-card>
      </v-col>

      <v-col cols="6">
        <v-card class="shadow rounded-5" height="380px">
          <div
            v-if="bookingsLoading"
            class="d-flex fill-height align-center justify-center"
          >
            <div class="pb-4">Loading...</div>
          </div>
          <div
            v-else-if="!bookingDataExist"
            class="d-flex fill-height align-center justify-center"
          >
            <div class="pb-4">No data exist</div>
          </div>
          <div class="d-flex justify-space-between p-5">
            <SvgIcon class="text-base font-semibold" text="Sales">
              <template v-slot:icon> </template>
            </SvgIcon>
          </div>
          <pie-chart v-if="salesDataExist" :data="salesPieChart" />
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import PieChart from "./PieChart";
import moment from "moment";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DateRangeField from "@/components/Fields/DateRangeField.vue";
export default {
  components: {
    DateRangeField,
    PieChart,
    SvgIcon,
  },
  props: {
    "export-permission": { type: Boolean, default: true },
    "show-venue-service": { type: Boolean, default: true },
    "show-product-type": { type: Boolean, default: false },
    "stack-type": { type: String, default: "all" }, //service,type,all
    reload: { type: Boolean, default: false },
  },
  watch: {
    subVenues: {
      immediate: true,
      handler(val) {
        if (val.length > 0) {
          this.subVenueIds = val.map((v) => v.id);
          this.callGraph(); // 🔁 move callGraph here after subVenueIds are set
        }
      },
    },
    reload: {
      immediate: true,
      handler(val) {
        if (val) {
          this.callGraph();
        }
      },
    },
  },
  data() {
    return {
      subVenueIds: [],
      sub_venue_ids: [],
      bookingsPieChart: null,
      bookingDataExist: false,
      salesPieChart: null,
      salesDataExist: false,
      dateToggle: "DATE",
      productIds: [],
      bookingsLoading: false,
      salesLoading: false,
      date1: moment().subtract(15, "days").format("YYYY-MM-DD"),
      date2: moment().format("YYYY-MM-DD"),
      stack: "all",
      mandatory: false,
    };
  },
  mounted() {
    if (this.$store.getters.getSubVenues.status == false) {
      this.$store.dispatch("loadSubVenues");
    }
  },
  computed: {
    subVenues() {
      const subVenues = this.$store.getters.getSubVenues || [];
      const currentVenueId = this.$store.getters.userInfo?.venue_id;
      return subVenues.data.filter((venue) => venue.id !== currentVenueId);
    },
  },
  methods: {
    toggle() {
      this.$nextTick(() => {
        if (this.subVenueIds.length == this.subVenues.length) {
          this.subVenueIds = [];
        } else {
          this.subVenueIds = this.subVenues.map((item) => item.id);
        }
      });
      setTimeout(() => {
        this.callGraph();
      });
    },
    icon() {
      if (this.subVenueIds.length == this.subVenues) return "mdi-close-box";
      if (this.subVenueIds.length == 0) return "mdi-checkbox-blank-outline";
      return "mdi-minus-box";
    },
    callGraph() {
      this.getOrderSalesChartData();
      this.getOrderBookingChartData();
    },
    async getGraphStartDate() {
      let url = "?type=sales";
      url += this.getApiFilter();
      await this.$http
        .get(`venues/graphs/get-start-date${url}`)
        .then((response) => {
          if (response.status == 200) {
            this.date1 = moment(response.data.data)
              .subtract(2, "days")
              .format("YYYY-MM-DD");
            this.date2 = moment(response.data.data)
              .add(13, "days")
              .format("YYYY-MM-DD");
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },
    dateChange(data) {
      this.date1 = data.date1;
      this.date2 = data.date2;
      this.callGraph();
    },
    getOrderSalesChartData() {
      this.salesLoading = true;
      this.salesDataExist = false;
      let url = "?type=sales";
      url += this.getApiFilter();
      this.$http
        .get(`venues/graphs/child-venues-channel-orders${url}`)
        .then((response) => {
          if (response.status == 200) {
            const transformedData = {};

            response.data.data.forEach((item) => {
              transformedData[item.channel] = parseFloat(item.total_value);
            });

            // Assign transformed object to bookingsPieChart.data
            this.salesPieChart = transformedData;
            if (response.data.data.length == 0) this.salesDataExist = false;
            else this.salesDataExist = true;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.salesLoading = false;
        });
    },
    getOrderBookingChartData() {
      this.bookingsLoading = true;
      this.bookingDataExist = false;
      let url = "?type=bookings";
      url += this.getApiFilter();
      this.$http
        .get(`venues/graphs/child-venues-channel-orders${url}`)
        .then((response) => {
          if (response.status == 200) {
            const transformedData = {};
            response.data.data.forEach((item) => {
              transformedData[item.channel] = parseFloat(item.total_value);
            });

            // Assign transformed object to bookingsPieChart.data
            this.bookingsPieChart = transformedData;
            if (response.data.data.length == 0) this.bookingDataExist = false;
            else this.bookingDataExist = true;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        })
        .finally(() => {
          this.bookingsLoading = false;
        });
    },
    getApiFilter() {
      let venueIdsParam =
        this.subVenueIds.length > 0
          ? `&venue_ids=${this.subVenueIds.join(",")}`
          : "";
      let url = `&date_type=${this.dateToggle.toLowerCase()}&start_date=${
        this.date1
      }&end_date=${this.date2}&stack_type=${this.stackType}${venueIdsParam}`;

      return url;
    },
  },
};
</script>

<style>
/* TODO:: Move to global once design completed */
.theme--light.v-btn-toggle:not(.v-btn-toggle--group) .v-btn.v-btn {
  border-color: unset !important;
}

.text-elepsis {
  white-space: nowrap; /* Prevents the text from wrapping */
  text-overflow: ellipsis; /* Adds ellipsis (...) to indicate text overflow */
  max-width: 85%;
  overflow: hidden;
}
</style>
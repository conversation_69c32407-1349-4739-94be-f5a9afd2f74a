<template>
    <column-chart
        :colors="colors"
        :data="chartData"
        :dataset="{ borderWidth: 0 }"
        :library="chartOptions"
        :stacked="true"
        :ytitle="yTitle"
        legend="bottom"
    ></column-chart>
</template>

<script>

export default {
  components: {},
  props: {
    type: String,
    heading: String,
    chartData: Array,
    yTitle: String,
    xTitle: String,
    exportData: Array,
    loader: {type: Boolean, default: true},
    colors: {
      type: Array,
      default: function () {
        return [
          "rgb(44, 59, 70)",
          "rgb(0, 177, 176)",
          "rgb(255, 192, 0)",
          "rgb(123, 127, 129)",
          "rgb(126, 110, 77)",
          "rgb(14, 103, 171)",
          "rgb(109, 139, 163)",
          "rgb(44, 76, 70)",
          "rgb(0, 134, 176)",
          "rgb(255, 152, 0)",
          "rgb(123, 187, 129)",
          "rgb(126, 53, 77)",
          "rgb(14, 87, 171)",
          "rgb(109, 39, 163)",
          "rgb(23, 46, 70)",
          "rgb(43, 67, 176)",
          "rgb(255, 43, 0)",
          "rgb(23, 43, 129)",
          "rgb(233, 45, 77)",
          "rgb(234, 103, 171)",
          "rgb(109, 154, 163)",
          "rgb(32, 76, 70)",
          "rgb(32, 134, 176)",
          "rgb(34, 34, 0)",
          "rgb(123, 23, 129)",
          "rgb(32, 53, 77)",
          "rgb(87, 45, 171)",
          "rgb(198, 45, 34)",
          "rgb(32, 21, 124)",
          "rgb(0, 43, 21)",
          "rgb(56, 192, 89)",
          "rgb(123, 21, 43)",
          "rgb(12, 110, 45)",
          "rgb(14, 54, 171)",
          "rgb(23, 139, 76)",
          "rgb(44, 76, 70)",
          "rgb(0, 134, 67)",
          "rgb(255, 34, 0)",
          "rgb(123, 187, 12)",
          "rgb(34, 53, 77)",
          "rgb(14, 45, 76)",
          "rgb(34, 39, 34)",
          "rgb(23, 56, 70)",
          "rgb(43, 23, 176)",
          "rgb(255, 76, 0)",
          "rgb(23, 23, 129)",
          "rgb(233, 45, 77)",
          "rgb(234, 32, 171)",
          "rgb(109, 154, 163)",
          "rgb(32, 67, 70)",
          "rgb(32, 79, 176)",
          "rgb(34, 34, 0)",
          "rgb(123, 23, 129)",
          "rgb(32, 87, 77)",
          "rgb(5, 245, 171)",
          "rgb(198, 34, 34)",
        ];
      },
    },

    exportPermission: {type: Boolean, default: true},
  },
  watch: {

  },
  mounted() {
  },
  data() {
    return {
      chartOptions: {

        tooltips: {enabled: true},
        scales: {
          yAxes: [
            {
              ticks: {
                suggestedMax: 2,
              },
            },
          ],
          xAxes: [
            {
              id: "xAxis1",
              type: "category",
              ticks: {
                callback: function (label) {
                  return (
                      (label.split(" ")[1] ? label.split(" ")[1] : "") +
                      " " +
                      (label.split(" ")[2] ? label.split(" ")[2] : "") +
                      " " +
                      (label.split(" ")[3] ? label.split(" ")[3] : "")
                  );
                },
              },
            },
            {
              id: "xAxis2",
              type: "category",
              offset: true,
              gridLines: {
                display: false,
                drawOnChartArea: false,
                drawBorder: false,
                lineWidth: 0.1,
              },
              ticks: {
                callback: function (label) {
                  return label.split(" ")[0];
                },
                scaleBeginAtZero: false,
                autoSkip: false,
              },

              scaleLabel: {
                display: true,
                labelString: this.xTitle,
                fontStyle: "bold",
              },
            },
          ],
        },
        plugins: {
          datalabels: {
            color: "#fff",
            align: "center",
            anchor: "center",
            font: {
              size: 12,
              weight: 700,
            },
            offset: 8,
          },
        },
      },
    };
  },

  computed: {

  },
  methods: {

  },
};
</script>
<style scoped>

</style>

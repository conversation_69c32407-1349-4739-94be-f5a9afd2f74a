<template>
  <v-container fluid>
    <v-row class="mt-4">
      <v-col md="4" class="ml-3 mt-3">
        <date-period
          :dense="true"
          :dateType="dateToggle"
          @periodChange="dateChange"
          class-name="q-text-field shadow-0"
        ></date-period>
      </v-col>
      <v-spacer></v-spacer>
      <v-col md="3" style="text-align: center">
        <v-btn-toggle
          borderless
          class="q-tabs"
          v-model="dateToggle"
          :mandatory="mandatory"
        >
          <v-btn height="54" value="DATE">Day</v-btn>
          <v-btn height="54" value="MONTH">Month</v-btn>
          <v-btn height="54" value="YEAR">Year</v-btn>
        </v-btn-toggle>
      </v-col>
      <v-spacer></v-spacer>
    </v-row>
    <v-row>
      <v-col cols="12">
        <BarChart
          :exportPermission="exportPermission"
          v-bind="visits"
        ></BarChart>
      </v-col>

      <v-col cols="12">
        <BarChart
          :exportPermission="exportPermission"
          v-bind="sales1"
        ></BarChart>
      </v-col>

      <v-col cols="12">
        <BarChart
          :exportPermission="exportPermission"
          v-bind="sales2"
        ></BarChart>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import BarChart from "./BarChart";
import moment from "moment";
export default {
  components: {
    BarChart,
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return {};
      },
    },
    "export-permission": { type: Boolean, default: true },
    "stack-type": { type: String, default: "all" }, //service,type,all
    reload: { type: Boolean, default: false },
  },
  watch: {

    stackType: {
      immediate: true,
      handler() {
        this.callGraph();
      },
    },
    stack(val) {
      this.$emit("stackChange", val);
    },
    reload: {
      immediate: true,
      handler(val) {
        if (val) {
          this.callGraph();
        }
      },
    },
  },
  data() {
    return {
      agePieChart: null,
      genderPieChart: null,
      countryPieChart: null,
      dateToggle: "DATE",
      date1: moment()
        .subtract(15, "days")
        .format("YYYY-MM-DD"),
      date2: moment().format("YYYY-MM-DD"),
      visits: {
        heading: "Top 10 Members | Visits",
        type: "totalVisits",
        fullscreenFlag: true,
        yTitle: "Visits",
        xTitle: "Member",
        chartData: [],
        exportData: [],
        loader: true,
        heighest: 0,
      },
      sales1: {
        heading: "Top 10 Members | Membership Sales",
        type: "totalSales",
        fullscreenFlag: true,
        yTitle: "Sales",
        xTitle: "Member",
        chartData: [],
        exportData: [],
        loader: true,
        heighest: 0,
      },
      sales2: {
        heading: "Top 10 Members | Non-membership Sales",
        type: "totalSales",
        fullscreenFlag: true,
        yTitle: "Sales",
        xTitle: "Member",
        chartData: [],
        exportData: [],
        loader: true,
        heighest: 0,
      },
      stack: "all",
      mandatory: false,
    };
  },

  methods: {
    callGraph() {
      this.getMemberAnalyticsData();
    },
    dateChange(data) {
      this.date1 = data.date1;
      this.date2 = data.date2;
      this.callGraph();
    },
    getMemberAnalyticsData() {
      this.sales1.loader = true;
      let url = "?type=member_sales";
      url += this.getApiFilter();
      this.$http
        .get(`venues/graphs/membership-analytics${url}`)
        .then((response) => {
          if (response.status == 200) {
            const {
              top_membership_sales,
              top_non_membership_sales,
              top_member_visits,
            } = response.data.data;

            let membersChartData = this.getGraphData(
              top_membership_sales,
              "Sales"
            );

            this.sales1.heighest = Math.ceil(membersChartData.largest * 1.1);
            this.sales1.chartData = membersChartData.data;
            this.sales1.exportData = top_membership_sales;
            this.sales1.loader = false;
            this.mandatory = true;

            let nonmembersChartData = this.getGraphData(
              top_non_membership_sales,
              "Sales"
            );
            this.sales2.heighest = Math.ceil(nonmembersChartData.largest * 1.1);
            this.sales2.chartData = nonmembersChartData.data;
            this.sales2.exportData = top_non_membership_sales;
            this.sales2.loader = false;
            this.mandatory = true;

            //top member visits
            let chartData = this.getGraphData(top_member_visits, "Visits");
            this.visits.heighest = Math.ceil(chartData.largest * 1.1);
            this.visits.chartData = chartData.data;
            this.visits.exportData = top_member_visits;
            this.visits.loader = false;
            this.mandatory = true;
          }
        })
        .catch((error) => {
          this.errorChecker(error);
        });
    },

    getApiFilter() {
      let url = `&date_type=${this.dateToggle.toLowerCase()}&start_date=${
        this.date1
      }&end_date=${this.date2}`;
      return url;
    },
    getGraphData(data, type) {
      var graphData = [];
      let largest = 0;
      if (data.length > 0) {
        let keys = Object.keys(data[0]).filter(
          (ele) => ele.toLowerCase() != "customer"
        );
        let keysLength = keys.length;
        data.forEach((element, index) => {
          let di = 0;
          let lar = 0;
          for (let i = 0; i <= keysLength; i++) {
            if (index == 0) {
              graphData[i] = {};
              graphData[i].data = {};
              graphData[i].name = keys[i];
              if (element[keys[i]]) {
                lar = lar + parseInt(element[keys[i]]);
              }
              graphData[i].data[element.Customer] = element[keys[i]];
              graphData[keysLength] = {};
              graphData[keysLength].name = "Total";
              graphData[keysLength].data = {};
              graphData[keysLength].data[element.Customer] = 0;
              di = di + 1;
            } else {
              if (element[keys[i]]) {
                lar = lar + parseInt(element[keys[i]]);
              }
              graphData[i].data[element.Customer] = element[keys[i]];
              di = di + 1;
            }
          }
          graphData[keysLength].data[element.Customer] = 0;
          if (largest < lar) {
            largest = lar;
          }
        });
        if (keysLength == 1 && keys[0] == type) {
          graphData[0].library = {};
          graphData[0].library.datalabels = {
            color: "rgba(0,0,49)",
            align: "end",
            anchor: "end",
            font: {
              size: 12,
              weight: 700,
            },
          };
        }
      }

      return { data: graphData, largest: largest };
    },
  },
};
</script>

<style></style>

<template>
  <v-row>
    <v-col :cols="12">
      <v-row dense>
        <v-col cols="12">
          <div class="d-flex justify-space-between">
            <v-row>
              <child-venues-sales
                @stackChange="stackChangeCapture"
                :stackType="stackType"
                :showProductType="false"
                :showVenueService="false"
                :exportPermission="false"
              ></child-venues-sales>
            </v-row>
          </div>
        </v-col>
      </v-row>

      <v-row dense>
        <v-col cols="12">
          <div class="d-flex justify-space-between">
            <v-row>
              <child-venues-channel-sales
                @stackChange="stackChangeCapture"
                :stackType="stackType"
                :showProductType="false"
                :showVenueService="false"
                :exportPermission="false"
              ></child-venues-channel-sales>
            </v-row>
          </div>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>
<script>
//import SvgIcon from "@/components/Image/SvgIcon.vue";
import ChildVenuesSales from "@/components/Chart/ChildVenuesSalesGraph";
import ChildVenuesChannelSales from "@/components/Chart/ChildVenuesChannelSalesChart";
export default {
  components: {
    ChildVenuesSales,
    ChildVenuesChannelSales,
  },
  data() {
    return {
      btnShow: false,
      stackType: "all",
      configDialog: false,
    };
  },

  mounted() {
    setTimeout(() => {
      this.btnShow = true;
    }, 10);
  },
  methods: {
    stackChangeCapture(data) {
      this.stackType = data;
    },
  },
};
</script>
<style scoped>
</style>
<template>
  <v-card class=" shadow  membership_card"
          style="border-radius: 8px"
          @click="viewSales"
          :ripple="false"
  >
    <v-card-text class="relative">
      <v-menu
          absolute
          content-class="q-menu"
          right
          top
      >
        <template v-slot:activator="{ on, attrs }">
          <v-btn
              :ripple="false"
              absolute
              class="text_capitalize options"
              elevation="0"
              right
              top
              v-bind="attrs"
              v-on="on"
              text
          >
            <DotsIcon/>
          </v-btn>
        </template>
        <v-list>
          <v-list-item
              v-if="checkWritePermission($modules.vouchers.management.slug) && status_id == 1"
              @click="$emit('edit', id)"
          >
            <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
              <template #icon>
                <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
              </template>
            </SvgIcon>
          </v-list-item>
          <v-list-item
              v-if="checkDeletePermission($modules.vouchers.management.slug)"
              @click="toggleStatus"
          >
            <SvgIcon class="font-medium text-sm gap-x-2 text-red" text="Delete" v-if="status_id == 1">
              <template #icon>
                <DeleteIcon stroke="#E50000"/>
              </template>
            </SvgIcon>

            <SvgIcon class="font-medium text-sm gap-x-2 text-green" text="Reactivate" v-else>

            </SvgIcon>
          </v-list-item>
        </v-list>
      </v-menu>
      <v-row class="border-bottom pb-2 align-center" dense>
        <v-col cols="5" style="">
          <simple-image
              :image="image"
              defaultImage="workshop"
              style="border-radius: 8px ; min-height: 165px"
          ></simple-image>
        </v-col>
        <v-col cols="7">
          <v-tooltip bottom >
            <template v-slot:activator="{ on, attrs }">
              <p v-on="on" v-bind="attrs" class="text-lg black-text font-bold text-truncate ml-2 membership_heading" style="max-width: 80%">
                {{ name }}
              </p>
            </template>
            {{name}}
          </v-tooltip>
          <v-divider/>
          <v-card
              class="overflow-y-hidden  mt-1 shadow-0  "
              style="min-height: 130px; max-height: 130px"
          >
            <div v-if="description" class="p-1" v-html="description"></div>
            <div v-else class="p-1">
              <p>No Description found</p>
            </div>
          </v-card>
        </v-col>
      </v-row>

      <v-row dense class="mt-4">
        <v-col cols="6" class="justify-center text-center" >
          <div class="membership_stats">
            <p class="text-base text-dark-gray">Card Issues</p>
            <p class="text-lg font-medium text-blue  ">{{ sales | numberFormatter }}</p>
          </div>
        </v-col>
        <v-col cols="6" class="justify-center text-center" >
          <div class="membership_stats">
            <p class="text-base text-dark-gray">Revenue</p>
            <p class="text-lg font-medium text-blue  ">{{ Number(revenue) | toCurrency }}</p>
          </div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>

</template>

<script>
import DotsIcon from "@/assets/images/misc/h-options.svg";
import SvgIcon from "../Image/SvgIcon.vue";
import EditIcon from "@/assets/images/tables/edit.svg";
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
import SimpleImage from "../Image/SimpleImage.vue";

export default {
  components:{SimpleImage, SvgIcon, DotsIcon, EditIcon,DeleteIcon},
  props: {
    name: { type: String, default: "" },
    type: { type: String, default: "" },
    voucher_type: { type: String, default: "" },
    id: { type: Number, default: 0 },
    image: { type: String, default: "0" },
    description: { type: String, default: "0" },
    revenue: { type: Number, default: 0 },
    sales: { type: Number, default: 0 },
    status_id: { type: Number, default: 0 },
  },

  methods: {
    edit() {
      this.$emit("edit", this.id);
    },
    toggleStatus() {
      this.$emit("delete", {
        id: this.id,
        type: this.status_id == 1 ? "delete" : "activate",
      });
    },
    viewSales() {
      this.$router.push({
        name: "VoucherSales",
        params: { data: btoa(this.id) },
      });
    },
  },
};
</script>

<style scoped>


.membership_card {
  border: 1px solid rgba(17, 42, 70, 0);
  cursor: pointer;
}

.membership_card:hover {
  border: 1px solid rgba(17, 42, 70, 0.5);
  box-shadow: 0 8px 24px 0 rgba(70, 76, 136, 0.20) !important;

}

.membership_stats {
  p {
    margin: 0;
    padding: 0;
  }
}
</style>

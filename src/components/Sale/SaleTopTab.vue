<template>
    <v-row
        justify="center"
        v-if="
        checkReadPermission($modules.sales.graph.slug) ||
        checkReadPermission($modules.sales.logs.slug) ||
        checkReadPermission($modules.sales.credits.slug) ||
        checkReadPermission($modules.sales.refunds.slug) ||
        checkReadPermission($modules.sales.void.slug)
        "
    >
        <transition name="slide-fade">
            <v-col
                align="center"
                cols="6"
                md="1"
                :class="[salesClass ? 'btn_bg' : '']"
                v-if="checkReadPermission($modules.sales.graph.slug)"
            >
                <router-link :to="`/sales`">
                    <v-icon color="#066a8c">mdi-finance</v-icon>
                    <div class="salesBtn">Sales</div>
                </router-link>
            </v-col>
        </transition>
        <v-col
            align="center"
            cols="6"
            md="1"
            :class="[logClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.logs.slug)"
            >
            <router-link :to="`/logs`">
                <v-icon color="#066a8c">mdi-history</v-icon>
                <div class="salesBtn">Orders</div>
            </router-link>
        </v-col>
        <v-col
            align="center"
            cols="6"
            md="1"
            :class="[invoiceClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.logs.slug)"
            >
            <router-link :to="`/invoices`">
                <v-icon color="#066a8c">mdi-file-document-edit</v-icon>
                <div class="salesBtn">Invoices</div>
            </router-link>
        </v-col>
        <v-col
            align="center"
            cols="6"
            md="1"
            :class="[creditClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.credits.slug)"
            >
            <router-link :to="`/credits`">
                <v-icon color="#066a8c">mdi-account-clock</v-icon>
                <div class="salesBtn">Credits</div>
            </router-link>
        </v-col>
        <v-col
            align="center"
            cols="6"
            md="1"
            :class="[refundClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.refunds.slug)"
            >
            <router-link :to="`/refunds`">
                <v-icon color="#066a8c">mdi-cash-refund</v-icon>
                <div class="salesBtn">Refunds</div>
            </router-link>
        </v-col>
        <v-col
            align="center"
            cols="6"
            md="1"
            :class="[cancellationClass ? 'btn_bg' : '']"
            v-if="checkReadPermission($modules.sales.void.slug)"
            >
            <router-link :to="`/cancellations`">
                <v-icon color="#066a8c">mdi-file-cancel</v-icon>
                <div class="salesBtn">Cancel</div>
            </router-link>
        </v-col>
    </v-row>
</template>
<script>
export default {
    props: {
        salesClass:{ type: Boolean, default: false },
        logClass: { type: Boolean, default: false },
        invoiceClass : { type: Boolean, default: false },
        creditClass: { type: Boolean, default: false },
        refundClass: { type: Boolean, default: false },
        cancellationClass: { type: Boolean, default: false },

    }
}
</script>
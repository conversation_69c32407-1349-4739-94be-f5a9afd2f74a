<template>
  <span>
    <v-btn v-if="defaultBtn" class="teal-color" dark small @click="pickFile">
      {{ !image_path && !uploadedImage ? "Upload Image" : "Change Image" }}
      <v-icon right dark>{{
          !image_path && !uploadedImage ? "mdi-plus-circle" : "mdi-pencil"
        }}</v-icon>
    </v-btn>
    <v-card flat class="d-flex" elevation="2">
      <v-btn
          v-if="
          (uploadedImage != null && defaultBtn == false && !isB2cCarousel) ||
            isRemoveIcon
        "
          fab
          absolute
          top
          right
          x-small
          color="error"
          @click="cancel"
      >
        <v-icon>mdi-close</v-icon>
      </v-btn>
      <v-hover v-slot="{ hover }" v-if="defaultBtn == false">
        <div class="relative" v-if="fileType ==='video'" style="width: 100%">
          <video :src="getImage()" autoplay muted :style="`object-fit: cover; width:100%; height:${height}px`"  />
          <v-row
              v-if="hover"
              class="fill-height ma-0 hoverImage absolute"
              align="center"
              @click="pickFile"
              justify="center"
              style="cursor: pointer;top:0; left:0;width: 100%;height: 100%"
          >
            <div align="center" justify="center" class="white--text">
              <v-icon color="#fff" large>mdi-pencil</v-icon>
              <div class="pa-2 .title">{{ text }}</div>
            </div>
          </v-row>
        </div>
        <v-img :src="getImage()" v-else :aspect-ratio="aspect_ratio" :height="height">
          <v-row
              v-if="hover"
              class="fill-height ma-0 hoverImage"
              align="center"
              @click="pickFile"
              justify="center"
              style="cursor: pointer"
          >
            <div align="center" justify="center" class="white--text">
              <v-icon color="#fff" large>mdi-pencil</v-icon>
              <div class="pa-2 .title">{{ text }}</div>
            </div>
          </v-row>
        </v-img>

      </v-hover>
      <input
          type="file"
          style="display: none"
          ref="image"
          :accept="accept"
          @change="onFilePicked"
      />
      <v-dialog v-model="cropDialog" width="900" @input="cancelCrop">
        <v-card>
          <v-card-title class="headline">Crop</v-card-title>
          <v-card-text class="mt-2" style="min-height: 450px">
            <v-row>
              <v-col md="6">
                <v-card tile flat class="fill-height">
                  <cropper
                      ref="cropper"
                      @change="onChange"
                      :debounce="false"
                      :width="400"
                      :height="400"
                      :src="uploadedImage"
                      :stencil-props="{
                      minAspectRatio: 1,
                      maxAspectRatio: maxAspectRatio,
                    }"
                  />
                </v-card>
              </v-col>
              <v-col md="6">
                <v-card tile flat width="400" class="fill-height">
                  <preview
                      :width="result.width"
                      :height="result.height"
                      :image="result.image"
                      :coordinates="result.coordinates"
                  />
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-col md="6">
              <div class="d-flex justify-space-around mt-2">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                        v-bind="attrs"
                        v-on="on"
                        small
                        fab
                        @click="flip(false, true)"
                    ><v-icon>mdi-flip-vertical</v-icon></v-btn
                    >
                  </template>
                  <span> Flip vertical </span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                        v-bind="attrs"
                        v-on="on"
                        small
                        fab
                        @click="flip(true, false)"
                    ><v-icon>mdi-flip-horizontal</v-icon></v-btn
                    >
                  </template>
                  <span> Flip horizontal </span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                        v-bind="attrs"
                        v-on="on"
                        small
                        fab
                        @click="rotate(90)"
                    ><v-icon>mdi-rotate-left</v-icon></v-btn
                    >
                  </template>
                  <span> Rotate Left </span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                        v-bind="attrs"
                        v-on="on"
                        small
                        fab
                        @click="rotate(-90)"
                    ><v-icon>mdi-rotate-right</v-icon></v-btn
                    >
                  </template>
                  <span> Rotate right </span>
                </v-tooltip>
              </div>
            </v-col>
            <v-spacer></v-spacer>
            <v-btn class="blue-color" text dark @click="cancelCrop"
            >Cancel</v-btn
            >
            <v-btn class="teal" text dark @click="closeCropper">
              <v-icon left>mdi-crop-free</v-icon>Fullsize</v-btn
            >
            <v-btn class="teal" text dark @click="cropImage">
              <v-icon left>mdi-crop</v-icon> Crop</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card>
    <v-dialog v-model="chooseFileDialog" width="600" @input="chooseFileDialog = false">
      <v-card>
        <v-card-text class="pb-0">
          <div class="row pt-1 border-bottom">
            <div class="col-md-12">
              <div class="d-flex justify-space-between align-center mt-2">
                <SvgIcon class="text-xl font-normal"
                         text="Upload or Select" style="color: black">
                </SvgIcon>
                <v-btn fab x-small class="shadow-0" @click="chooseFileDialog = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
          <div class="d-flex justify-space-between pa-4 gap-x-2 mt-4">
            <div class="d-flex align-center justify-center pointer bordered pa-4 rounded-2 choose-file-source" @click="MediaListingModal = true" >
              <SvgIcon class="font-normal gap-x-2" text="Choose from library">
                  <template #icon>
                    <ChooseIcon/>
                  </template>
                </SvgIcon>
            </div>
            <div class="d-flex align-center justify-center pointer bordered pa-4 rounded-2 choose-file-source" @click="pickImgFile" >
              <SvgIcon class="font-normal gap-x-2" text="Upload File">
                  <template #icon>
                    <UploadIcon/>
                  </template>
                </SvgIcon>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <MediaListingModal
        @close="MediaListingModal = false"
        @selectTarget="selectFileFromLibrary"
        :MediaListingModal="MediaListingModal"
        :allow-video="allowVideo"
        :width="200"
        :height="200"
    />
  </span>
</template>

<script>
import {Cropper, Preview} from "vue-advanced-cropper";
import "vue-advanced-cropper/dist/style.css";
import images from "@/utils/images";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import ChooseIcon from "@/assets/images/misc/upload-cloud.svg";
import UploadIcon from "@/assets/images/misc/upload-pc.svg";
import MediaListingModal from "@/components/MediaLibrary/MediaListingModal.vue";

export default {
  components: {
    MediaListingModal,
    UploadIcon,
    ChooseIcon, SvgIcon,
    Cropper,
    Preview,
  },
  props: {
    image_path: {default: null},
    aspect_ratio: {type: String, default: "1.7"},
    defaultImage: {type: String, default: "default"},
    height: {type: Number, default: 100},
    text: {type: String, default: "Click to change the image"},
    defaultBtn: {type: Boolean, default: false},
    maxAspectRatio: {type: Number, default: 1},
    isRemoveIcon: {type: Boolean, default: false},
    isB2cCarousel: {type: Boolean, default: false},
    allowVideo: { type: Boolean, default: false }
  },
  data() {
    return {
      MediaListingModal:false,
      chooseFileDialog: false,
      uploadedImage: null,
      currentImage: null,
      cropDialog: false,
      result: {
        coordinates: null,
        image: null,
        height: 400,
        width: 400,
      },
      file: null,
      fileType:'image'
    };
  },
  watch: {
    image() {
      this.uploadedImage = null;
    },
    image_path: {
      immediate: true,
      handler() {
        this.uploadedImage = null;
        this.getFileType()
      },
    },
  },
  mounted() {
    this.getFileType()
  },
  computed: {
    accept() {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp'];
      const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.mkv', '.webm'];

      if (this.allowVideo) {
        return [...imageExtensions, ...videoExtensions].join(',');
      }

      return imageExtensions.join(',');
    },
  },
  methods: {
    getImage () {
      if (this.uploadedImage) {
        return this.uploadedImage;
      }
      if (this.image_path && typeof this.image_path !== 'object') {
        return this.s3BucketURL + this.image_path;
      }
      if (this.image_path && typeof this.image_path === 'object') {
        return window.URL.createObjectURL(this.image_path);
      }
      return images[this.defaultImage];
    },
    pickImgFile () {
      this.chooseFileDialog = false;
      this.$refs.image.click();
    },
    pickFile () {
      if (this.checkReadPermission(this.$modules.mediaLibrary.management.slug)) {
        this.chooseFileDialog = true;
      } else {
        this.pickImgFile();
      }
    },
    selectFileFromLibrary (file) {
      this.showLoader('loading your image');
      let type = 'image';
      let fileName = 'image.jpg';
      if (file.mime_type.includes('video')) {
        type = 'video';
        fileName = 'video.' + file.extension;
      }
      let filePath = this.s3BucketURL + file.path;
      this.createFileFromImageUrl(filePath, fileName,type).then(file => {
       if (type === 'video'){
         this.$emit("upload", file);
         this.$emit("result", URL.createObjectURL(file));
         this.uploadedImage = URL.createObjectURL(file);
         this.MediaListingModal = false
         this.getFileType()
       }else{
         this.onFilePicked({target:{files:[file]}})
       }
        this.chooseFileDialog = false;
      }).catch(error => {
        console.error(error);
      }).finally(() => {
        this.hideLoader();
      });
    },
    createFileFromImageUrl (imageUrl, fileName,type) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        if (type === 'image') {
          const img = new Image();
          img.crossOrigin = 'Anonymous';

          img.onload = () => {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);

            canvas.toBlob((blob) => {
              if (blob) {
                const file = new File([blob], fileName, { type: blob.type });
                resolve(file);
              } else {
                reject(new Error('Failed to create file from image.'));
              }
            }, 'image/jpeg'); // Specify the MIME type here
          };

          img.onerror = (error) => {
            reject(new Error('Failed to load image: ' + error.message));
          };
          img.src = imageUrl;

        } else if (type === 'video') {
          try {
            const response = await fetch(imageUrl);
            const blob = await response.blob();

            const file = new File([blob], fileName, { type: blob.type });
            resolve(file);
          } catch (error) {
            reject(new Error('Failed to load video: ' + error.message));
          }
        } else {
          reject(new Error('Unsupported type: ' + type));
        }
      });
    },
    // pickFile() {
    //   this.$refs.image.click();
    // },
    cancel () {
      if (this.defaultBtn == false) {
        this.uploadedImage = null;
        this.$emit("remove");
        this.$refs.image.value = null;
      }
    },
    cancelCrop () {
      this.cropDialog = false;
      this.resul = {
        coordinates: null,
        image: null,
      };
      this.cancel();
    },
    onFilePicked (e) {
      let files = e.target.files;
      if (!files.length) {
        return;
      }
      const file = files[0];
      if (file.type.includes('video/')) {
        this.$emit("upload", file);
        this.$emit("result", URL.createObjectURL(file));
        this.uploadedImage = URL.createObjectURL(file);
        this.getFileType()
      } else {
        let reader = new FileReader();
        reader.onload = (e) => {
          this.uploadedImage = e.target.result;
          this.file = files[0];
          this.$emit("upload", files[0]);
          this.$emit("result", e.target.result);
          this.$refs.image.value = null;
          this.cropDialog = true;
          this.getFileType()
        };
        reader.readAsDataURL(files[0]);
      }
    },
    cropImage () {
      const { coordinates, canvas } = this.$refs.cropper.getResult();
      this.coordinates = coordinates;
      let imageDataUrl = canvas.toDataURL();
      this.uploadedImage = imageDataUrl;
      this.$emit("upload", this.dataURLtoFile(imageDataUrl, this.file.name));
      this.$emit("result", this.uploadedImage);
      this.$refs.image.value = null;
      this.cropDialog = false;
      this.MediaListingModal = false
    },
    closeCropper () {
      this.cropDialog = false
      this.MediaListingModal = false
    },
    onChange ({ coordinates, image }) {
      let ratio = coordinates.height / coordinates.width;
      this.result = {
        coordinates,
        image,
      };
      this.result.height = 400 * ratio;
      this.result.width = 400;
    },
    flip (x, y) {
      if (this.$refs.cropper.customImageTransforms.rotate % 180 !== 0) {
        this.$refs.cropper.flip(!x, !y);
      } else {
        this.$refs.cropper.flip(x, y);
      }
    },
    rotate (angle) {
      this.$refs.cropper.rotate(angle);
    },
    dataURLtoFile (dataurl, filename) {
      //Convert base64 to file
      var arr = dataurl.split(","),
          mime = arr[0].match(/:(.*?);/)[1],
          bstr = atob(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, {
        type: mime,
      });
    },
    async getFileType () {
      let type = 'image';
      try {
        let file = null;

        // Check if uploadedImage is a Blob URL and fetch its Blob object
        if (this.uploadedImage) {
          type = this.uploadedImage.startsWith('blob:') ? 'video' : 'image';
          this.fileType = type;
          return;
        } else if (this.image_path && typeof this.image_path === 'object') {
          file = this.image_path;
        }

        if (file) {
          const fileType = file.type;

          if (fileType.startsWith('image/')) {
            type = 'image';
          } else if (fileType.startsWith('video/')) {
            type = 'video';
          }
        }

        if (this.image_path && typeof this.image_path === 'string') {
          const fileExtension = this.image_path.split('.').pop().toLowerCase();
          const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
          const videoExtensions = ['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'webm'];

          if (imageExtensions.includes(fileExtension)) {
            type = 'image';
          } else if (videoExtensions.includes(fileExtension)) {
            type = 'video';
          }
        } else {
          type = 'image';
        }

      } catch (error) {
        console.error('Error determining file type:', error);
        type = 'image';
      }
      this.fileType = type;
    },
  },
};
</script>

<style>
.choose-file-source {
  background: #F2F2F2 !important;
  width: 50% !important;
  color: black !important;
}
</style>

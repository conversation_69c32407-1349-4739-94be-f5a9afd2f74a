trigger:
- feature/QP-3839

pool:
  vmImage: 'windows-latest'

variables:
  nodeVersion: '14.x' 
  azureSubscription: 'KREWS-UAT-SUBSCRIPTION(0b7488aa-df3b-4985-b4b9-17e70ed2f18b)'
  storageAccount: 'krewsdevafhnewqportal'
  containerName: '$web' 
  sourceFolder: '$(Build.ArtifactStagingDirectory)\dist' 
  sasToken: $(token)
  resourceGroup: 'KREWS-UAT-RG'
  profileName: 'KREWS-QPORTAL-UAT-2'
  endpointName: 'devqportalafh'

steps:
- task: NodeTool@0
  inputs:
    versionSpec: '$(nodeVersion)'
  displayName: 'Install Node.js'

- powershell: |
    # npm install -g @quasar/cli
    npx browserslist@latest --update-db
    del .env
    copy .env.devafh.local .env
    npm install
    npm run dev_build
    # quasar build
  displayName: 'Build Node App'



- task: CopyFiles@2
  inputs:
    SourceFolder: 'dist'
    Contents: '**' 
    TargetFolder: '$(Build.ArtifactStagingDirectory)/dist'
  displayName: 'Copy files to Artifact Staging Directory'



- task: AzureCLI@2
  inputs:
    azureSubscription: '$(azureSubscription)'
    scriptType: 'ps'
    scriptLocation: 'inlineScript'
    inlineScript: |
      az storage blob delete-batch --account-name ${env:storageAccount} --source ${env:containerName}
  displayName: 'Delete Existing Blobs in Azure Blob Storage Container'

- task: AzureCLI@2
  inputs:
    azureSubscription: '$(azureSubscription)'
    scriptType: 'ps'
    scriptLocation: 'inlineScript'
    inlineScript: |
      az storage blob upload-batch -d ${env:containerName} --account-name ${env:storageAccount} -s ${env:sourceFolder} --pattern '**'
  displayName: 'Upload to Azure Blob Storage'

- task: AzureCLI@2
  inputs:
    azureSubscription: '$(azureSubscription)'
    scriptType: 'ps'
    scriptLocation: 'inlineScript'
    inlineScript: |
      az extension add --name front-door
      az afd endpoint purge --resource-group ${env:resourceGroup} --profile-name ${env:profileName} --endpoint-name ${env:endpointName} --domains devqportalafh-hub8h8b3fwapescd.a03.azurefd.net devqportalafh.qportal.io  --content-paths '/*'
  displayName: 'Purge endpoint from frontdoor'